# 🧪 Local Testing Guide - Iframe Security Implementation

This guide walks you through setting up and testing the iframe embedding security measures locally before deploying to production.

## 📋 Prerequisites

- Node.js (v18 or higher)
- npm or yarn
- Firebase CLI (`npm install -g firebase-tools`)
- Two terminal windows/tabs

## 🚀 Step 1: Local Environment Setup

### 1.1 Set up prod-designer-core (Embedded App)

```bash
cd prod-designer-core

# Install dependencies
npm install

# Install Cloud Functions dependencies
cd functions
npm install
cd ..

# Create local environment file
cp .env.example .env.local
```

Edit `.env.local` with local testing configuration:
```bash
# Local Testing Configuration
NODE_ENV=development
SECURITY_ENABLED=true
DEVELOPMENT_MODE=true

# Local IPs (your machine's IP)
ALLOWED_IPS=127.0.0.1,::1,localhost,***********/24

# Local domains
ALLOWED_DOMAINS=localhost,127.0.0.1,*************

# Local origins for testing
ALLOWED_ORIGINS=http://localhost:8080,http://localhost:3000,http://localhost:8084,http://127.0.0.1:8080

# Admin key for testing
ADMIN_KEY=local-test-admin-key

# Enable debug logging
LOG_LEVEL=debug
ENABLE_ACCESS_LOGS=true
```

### 1.2 Set up spark-oasis-main (Parent App)

```bash
cd spark-oasis-main

# Install dependencies
npm install

# Create local environment file
cp .env.example.security .env.local
```

Edit `.env.local` with local testing configuration:
```bash
# Local Testing Configuration
VITE_CARPORT_DESIGNER_URL_PROD=http://localhost:8084/
VITE_CARPORT_DESIGNER_URL_DEV=http://localhost:8084/
VITE_CARPORT_DESIGNER_URL_FALLBACK=http://localhost:8084/

# Local iframe origins
VITE_ALLOWED_IFRAME_ORIGINS=http://localhost:8084,http://127.0.0.1:8084

# Development settings
VITE_DEVELOPMENT_MODE=true
VITE_ENABLE_DEBUG_LOGS=true
```

## 🔧 Step 2: Start Local Development Servers

### 2.1 Start prod-designer-core (Terminal 1)

```bash
cd prod-designer-core

# Start the Expo web server
npm run web

# This should start on http://localhost:8084
```

### 2.2 Start Firebase Functions Emulator (Terminal 2)

```bash
cd prod-designer-core

# Start Firebase emulators
firebase emulators:start --only functions

# Functions will be available at:
# http://localhost:5001/designmycarport/us-central1/[function-name]
```

### 2.3 Start spark-oasis-main (Terminal 3)

```bash
cd spark-oasis-main

# Start the Vite development server
npm run dev

# This should start on http://localhost:3000
```

## 🧪 Step 3: Local Security Testing

### 3.1 Basic Functionality Test

1. **Open spark-oasis-main in browser:**
   ```
   http://localhost:3000/carport-designer
   ```

2. **Verify iframe loads:**
   - The iframe should display the prod-designer-core application
   - Check browser console for any errors
   - Verify no CORS or security errors

### 3.2 Security Middleware Testing

Test the Cloud Functions security middleware:

```bash
# Test health check
curl http://localhost:5001/designmycarport/us-central1/healthCheck

# Test security validation with allowed origin
curl -X GET \
  -H "Origin: http://localhost:3000" \
  -H "X-Forwarded-For: 127.0.0.1" \
  http://localhost:5001/designmycarport/us-central1/secureApp

# Test security validation with blocked origin
curl -X GET \
  -H "Origin: http://malicious.com" \
  -H "X-Forwarded-For: ***********" \
  http://localhost:5001/designmycarport/us-central1/secureApp
```

### 3.3 CORS Policy Testing

Test CORS headers:

```bash
# Test preflight request
curl -X OPTIONS \
  -H "Origin: http://localhost:3000" \
  -H "Access-Control-Request-Method: GET" \
  -H "Access-Control-Request-Headers: Content-Type" \
  http://localhost:8084/

# Test actual request
curl -X GET \
  -H "Origin: http://localhost:3000" \
  http://localhost:8084/
```

### 3.4 Automated Security Tests

Run the security test suite:

```bash
# Test via Cloud Function
curl -X GET \
  -H "X-Admin-Key: local-test-admin-key" \
  http://localhost:5001/designmycarport/us-central1/testSecurity
```

## 📊 Step 4: Manual Testing Scenarios

### 4.1 Open the Local Test Page

1. **Open `test-security.html` in your browser:**
   ```
   file:///path/to/your/project/test-security.html
   ```

2. **Update the test URLs to local:**
   - Change iframe URLs to `http://localhost:8084/`
   - Update Cloud Function URLs to local emulator URLs

### 4.2 Test Scenarios to Verify

#### ✅ Should Work (Authorized Access):
1. **Iframe Embedding:**
   - Load spark-oasis-main at `http://localhost:3000`
   - Navigate to CarportDesigner page
   - Verify iframe loads prod-designer-core successfully

2. **Direct Access:**
   - Open `http://localhost:8084/` directly
   - Should load without security restrictions in development mode

3. **CORS Requests:**
   - From localhost:3000 to localhost:8084
   - Should include proper CORS headers

#### ❌ Should Block (Test Security):

To test blocking, temporarily modify your local configuration:

1. **Edit `.env.local` in prod-designer-core:**
   ```bash
   DEVELOPMENT_MODE=false
   ALLOWED_ORIGINS=http://localhost:9999  # Different port
   ```

2. **Restart the servers and test:**
   - Iframe should fail to load
   - CORS requests should be blocked
   - Security middleware should return 403

### 4.3 Browser Developer Tools Testing

1. **Open DevTools (F12) in spark-oasis-main:**
   - **Console Tab:** Check for CORS errors, CSP violations
   - **Network Tab:** Verify security headers in responses
   - **Security Tab:** Check for mixed content warnings

2. **Check Response Headers:**
   ```
   X-Frame-Options: ALLOWALL
   Content-Security-Policy: frame-ancestors 'self' http://localhost:*
   Access-Control-Allow-Origin: http://localhost:3000
   ```

## 🔍 Step 5: Debugging Common Issues

### 5.1 Iframe Not Loading

**Symptoms:**
- Blank iframe
- Console errors about frame loading

**Debug Steps:**
```bash
# Check if prod-designer-core is running
curl http://localhost:8084/

# Check security headers
curl -I http://localhost:8084/

# Check CORS
curl -H "Origin: http://localhost:3000" http://localhost:8084/
```

### 5.2 CORS Errors

**Symptoms:**
- "Access blocked by CORS policy" in console

**Debug Steps:**
1. Verify origins in `cors-config.ts`
2. Check preflight OPTIONS handling
3. Ensure development mode is enabled

### 5.3 Security Middleware Issues

**Symptoms:**
- 403 Forbidden responses
- Security validation failures

**Debug Steps:**
```bash
# Check Cloud Functions logs
firebase emulators:logs

# Test with curl
curl -v -H "Origin: http://localhost:3000" \
  http://localhost:5001/designmycarport/us-central1/secureApp
```

## ✅ Step 6: Validation Checklist

Before proceeding to production deployment, verify:

- [ ] prod-designer-core loads at http://localhost:8084
- [ ] spark-oasis-main loads at http://localhost:3000
- [ ] Iframe embedding works from spark-oasis-main to prod-designer-core
- [ ] Security headers are present in responses
- [ ] CORS policies allow legitimate requests
- [ ] Security middleware blocks unauthorized access (when not in dev mode)
- [ ] Automated security tests pass
- [ ] No console errors in browser
- [ ] Manual test scenarios work as expected

## 🚀 Step 7: Prepare for Production

Once local testing is complete:

1. **Update environment variables for production**
2. **Test with production-like configuration locally**
3. **Run the deployment script:** `./deploy-security.sh`

## 📝 Notes

- **Development Mode:** Security restrictions are relaxed for easier testing
- **IP Detection:** May not work perfectly in local environment
- **HTTPS:** Production will use HTTPS; local uses HTTP
- **Ports:** Ensure no port conflicts with other services

## 🛠️ Quick Start Commands

For your convenience, use these automated scripts:

### Automated Setup
```bash
# Run the setup script to configure everything
./setup-local-testing.sh

# Start all services at once
./start-local-development.sh

# Run automated tests
./run-local-tests.sh
```

### Manual Commands
```bash
# Terminal 1: Start prod-designer-core
cd prod-designer-core
npm run web

# Terminal 2: Start Firebase Functions
cd prod-designer-core
firebase emulators:start --only functions

# Terminal 3: Start spark-oasis-main
cd spark-oasis-main
npm run dev
```

## 🧪 Testing Tools

### Interactive Testing
- **Local Test Page:** Open `test-security-local.html` in your browser
- **Manual Testing:** Navigate to `http://localhost:3000/carport-designer`
- **Direct Access:** Visit `http://localhost:8084/` directly

### Command Line Testing
```bash
# Test basic connectivity
curl http://localhost:8084/
curl http://localhost:3000/
curl http://localhost:5001/designmycarport/us-central1/healthCheck

# Test CORS
curl -H "Origin: http://localhost:3000" http://localhost:8084/

# Test security headers
curl -I http://localhost:8084/

# Test Cloud Function security
curl -H "X-Admin-Key: local-test-admin-key" \
  http://localhost:5001/designmycarport/us-central1/testSecurity
```

## 🔧 Common Port Configurations

If you need to use different ports, update these files:

### prod-designer-core
- **Default Port:** 8084 (Expo web)
- **Change in:** `package.json` scripts or use `--port` flag
- **Environment:** Update `ALLOWED_ORIGINS` in `.env.local`

### spark-oasis-main
- **Default Port:** 3000 (Vite dev server)
- **Change in:** `vite.config.ts` or use `--port` flag
- **Environment:** Update iframe URLs in `.env.local`

### Firebase Functions
- **Default Port:** 5001 (Functions emulator)
- **Change in:** `firebase.json` emulator configuration

## 📱 Testing from Other Devices

To test from mobile devices or other computers on your network:

1. **Find your local IP address:**
   ```bash
   # Mac/Linux
   ifconfig | grep "inet " | grep -v 127.0.0.1

   # Windows
   ipconfig | findstr "IPv4"
   ```

2. **Update environment files with your IP:**
   ```bash
   # Add to ALLOWED_IPS and ALLOWED_ORIGINS
   ALLOWED_IPS=127.0.0.1,::1,localhost,YOUR_IP_HERE
   ALLOWED_ORIGINS=http://localhost:8084,http://YOUR_IP_HERE:8084
   ```

3. **Access from other devices:**
   - prod-designer-core: `http://YOUR_IP:8084/`
   - spark-oasis-main: `http://YOUR_IP:3000/`

---

**Next Steps:** After successful local testing, proceed with production deployment using the `deploy-security.sh` script.
