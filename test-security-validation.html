<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Security Implementation Validation Test</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            border-left: 5px solid #4CAF50;
        }
        .test-section h2 {
            margin-top: 0;
            color: #4CAF50;
        }
        .test-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .test-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .test-item h3 {
            margin-top: 0;
            color: #FFD700;
        }
        .iframe-container {
            width: 100%;
            height: 600px;
            border: 2px solid #4CAF50;
            border-radius: 8px;
            overflow: hidden;
            background: white;
        }
        .iframe-container iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background-color: #4CAF50; }
        .status-warning { background-color: #FF9800; }
        .status-error { background-color: #F44336; }
        .test-button {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: all 0.3s ease;
        }
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        .security-info {
            background: rgba(255, 193, 7, 0.2);
            border: 1px solid #FFC107;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .security-info h4 {
            margin-top: 0;
            color: #FFC107;
        }
        .console-output {
            background: #1a1a1a;
            color: #00ff00;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 200px;
            overflow-y: auto;
            margin: 10px 0;
        }
        .link-button {
            display: inline-block;
            background: linear-gradient(45deg, #2196F3, #1976D2);
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 6px;
            margin: 5px;
            transition: all 0.3s ease;
        }
        .link-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔒 Security Implementation Validation</h1>
        
        <div class="security-info">
            <h4>🛡️ Security Implementation Overview</h4>
            <p>This test validates the enhanced security implementation for blocking direct access to prod-designer-core while preserving iframe embedding functionality.</p>
            <ul>
                <li><strong>Application-level Security Middleware:</strong> AccessControlProvider, ReferrerGuard, SecurityHeaders</li>
                <li><strong>Environment-based Configuration:</strong> Development mode allows direct access with warnings</li>
                <li><strong>Production Behavior:</strong> Would block direct access while allowing iframe embedding</li>
                <li><strong>Security Headers:</strong> CSP with frame-ancestors, X-Frame-Options, CORS policies</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>🌐 Service Status Check</h2>
            <div class="test-grid">
                <div class="test-item">
                    <h3><span class="status-indicator status-success"></span>prod-designer-core</h3>
                    <p>Port 8081 - Main application with security middleware</p>
                    <a href="http://localhost:8081" target="_blank" class="link-button">Open Direct Access</a>
                </div>
                <div class="test-item">
                    <h3><span class="status-indicator status-success"></span>spark-oasis-main</h3>
                    <p>Port 8080 - Host application for iframe embedding</p>
                    <a href="http://localhost:8080" target="_blank" class="link-button">Open Host App</a>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>🔍 Security Test Scenarios</h2>
            
            <div class="test-item">
                <h3>1. Direct Access Test (Development Mode)</h3>
                <p>In development mode, direct access is allowed but security warnings are logged to console.</p>
                <a href="http://localhost:8081" target="_blank" class="link-button">Test Direct Access</a>
                <p><strong>Expected:</strong> Application loads with security warnings in browser console</p>
            </div>

            <div class="test-item">
                <h3>2. Iframe Embedding Test</h3>
                <p>Testing the carport designer embedded within the spark-oasis-main application.</p>
                <a href="http://localhost:8080/carport-designer" target="_blank" class="link-button">Test Iframe Embedding</a>
                <p><strong>Expected:</strong> Application loads normally within iframe without security warnings</p>
            </div>
        </div>

        <div class="test-section">
            <h2>🖼️ Live Iframe Embedding Demo</h2>
            <p>This demonstrates the prod-designer-core application running within an iframe, which should work seamlessly:</p>
            <div class="iframe-container">
                <iframe src="http://localhost:8081" title="Carport Designer - Iframe Embedded"></iframe>
            </div>
        </div>

        <div class="test-section">
            <h2>📋 Security Implementation Details</h2>
            <div class="test-grid">
                <div class="test-item">
                    <h3>🛡️ AccessControlProvider</h3>
                    <p>Main security middleware that validates access patterns, detects iframe embedding, and enforces security policies.</p>
                </div>
                <div class="test-item">
                    <h3>🔍 ReferrerGuard</h3>
                    <p>Validates HTTP referrer headers to ensure requests come from authorized sources.</p>
                </div>
                <div class="test-item">
                    <h3>🔒 SecurityHeaders</h3>
                    <p>Manages CSP headers with frame-ancestors directive and other security headers.</p>
                </div>
                <div class="test-item">
                    <h3>⚙️ SecurityManager</h3>
                    <p>Orchestrates all security components with environment-based configuration.</p>
                </div>
            </div>
        </div>

        <div class="security-info">
            <h4>🚀 Production Deployment Notes</h4>
            <p>When deployed to production:</p>
            <ul>
                <li>Set <code>BLOCK_DIRECT_ACCESS=true</code> to enforce security restrictions</li>
                <li>Set <code>DEVELOPMENT_MODE=false</code> to disable development overrides</li>
                <li>Configure <code>ALLOWED_REFERRERS</code> with production domain URLs</li>
                <li>Direct access will show security fallback component</li>
                <li>Iframe embedding from authorized origins will continue to work</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>🔧 Testing Instructions</h2>
            <ol>
                <li><strong>Open Browser Console:</strong> Press F12 to open developer tools</li>
                <li><strong>Test Direct Access:</strong> Click "Test Direct Access" and check console for security warnings</li>
                <li><strong>Test Iframe Embedding:</strong> Click "Test Iframe Embedding" and verify it loads without warnings</li>
                <li><strong>Compare Behavior:</strong> Notice the difference in console output between direct access and iframe embedding</li>
                <li><strong>Verify Security Headers:</strong> Check Network tab for CSP and security headers</li>
            </ol>
        </div>
    </div>

    <script>
        // Add some interactive functionality
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔒 Security Validation Test Page Loaded');
            console.log('📋 Security Implementation Status:');
            console.log('   • Application-level security middleware: ✅ IMPLEMENTED');
            console.log('   • Referrer-based access control: ✅ IMPLEMENTED');
            console.log('   • Security headers management: ✅ IMPLEMENTED');
            console.log('   • Environment-based configuration: ✅ IMPLEMENTED');
            console.log('   • Development mode: ✅ ACTIVE');
            
            // Test service connectivity
            const services = [
                { name: 'prod-designer-core', url: 'http://localhost:8081', port: 8081 },
                { name: 'spark-oasis-main', url: 'http://localhost:8080', port: 8080 },
                { name: 'Firebase Functions', url: 'http://localhost:5001/designmycarport/us-central1/healthCheck', port: 5001 }
            ];
            
            console.log('🌐 Testing service connectivity...');
            services.forEach(service => {
                fetch(service.url, { mode: 'no-cors' })
                    .then(() => console.log(`✅ ${service.name}: ACCESSIBLE (port ${service.port})`))
                    .catch(() => console.log(`❌ ${service.name}: FAILED (port ${service.port})`));
            });
        });
    </script>
</body>
</html>
