#!/bin/bash

# Local Development Startup Script
# Starts all required services for local testing

echo "🚀 Starting Local Development Environment"
echo "========================================"

# Function to start a service in background
start_service() {
    local name=$1
    local command=$2
    local dir=$3
    
    echo "Starting $name..."
    cd "$dir"
    $command &
    local pid=$!
    echo "$name started with PID $pid"
    cd - > /dev/null
}

# Start prod-designer-core web server
start_service "prod-designer-core" "npm run web" "prod-designer-core"

# Wait a bit for the first service to start
sleep 3

# Start Firebase Functions emulator
start_service "Firebase Functions" "firebase emulators:start --only functions" "prod-designer-core"

# Wait a bit for functions to start
sleep 3

# Start spark-oasis-main
start_service "spark-oasis-main" "npm run dev" "spark-oasis-main"

echo ""
echo "🎉 All services started!"
echo ""
echo "Services:"
echo "- prod-designer-core: http://localhost:8084"
echo "- spark-oasis-main: http://localhost:3000"
echo "- Firebase Functions: http://localhost:5001"
echo ""
echo "Test iframe embedding at: http://localhost:3000/carport-designer"
echo ""
echo "Press Ctrl+C to stop all services"

# Wait for user to stop
wait
