<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Local Security Testing Suite - Iframe Embedding</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .iframe-container {
            width: 100%;
            height: 400px;
            border: 2px solid #ddd;
            border-radius: 5px;
            overflow: hidden;
            position: relative;
        }
        .iframe-container iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .test-button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background-color: #0056b3;
        }
        .test-results {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-top: 10px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .url-input {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .info-box {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Local Security Testing Suite - Iframe Embedding</h1>
        <div class="info-box">
            <strong>Local Testing Environment</strong><br>
            This page tests the security measures for iframe embedding in your local development environment.
            Make sure both applications are running:
            <ul>
                <li>prod-designer-core: <a href="http://localhost:8084" target="_blank">http://localhost:8084</a></li>
                <li>spark-oasis-main: <a href="http://localhost:3000" target="_blank">http://localhost:3000</a></li>
                <li>Firebase Functions: <a href="http://localhost:5001" target="_blank">http://localhost:5001</a></li>
            </ul>
        </div>
    </div>

    <div class="container">
        <div class="test-section">
            <h3>1. Local Iframe Embedding Test</h3>
            <p>Testing iframe embedding from local development server:</p>
            <div class="iframe-container">
                <iframe 
                    id="local-iframe"
                    src="http://localhost:8084/"
                    title="Local Embedding Test"
                    sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-presentation">
                </iframe>
            </div>
            <div id="local-status" class="status warning">Loading...</div>
        </div>

        <div class="test-section">
            <h3>2. Custom URL Test</h3>
            <p>Test embedding with different local URLs:</p>
            <input type="text" id="custom-url" class="url-input" 
                   value="http://localhost:8084/" 
                   placeholder="Enter local URL to test">
            <button class="test-button" onclick="testCustomURL()">Test URL</button>
            <div class="iframe-container">
                <iframe 
                    id="custom-iframe"
                    title="Custom URL Test"
                    sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-presentation">
                </iframe>
            </div>
            <div id="custom-status" class="status warning">Enter a URL and click Test</div>
        </div>

        <div class="test-section">
            <h3>3. Local Security Headers Test</h3>
            <p>Test security headers and CORS policies on local server:</p>
            <button class="test-button" onclick="testLocalHeaders()">Test Headers</button>
            <button class="test-button" onclick="testLocalCORS()">Test CORS</button>
            <div id="headers-results" class="test-results"></div>
        </div>

        <div class="test-section">
            <h3>4. Local Cloud Function Test</h3>
            <p>Test the security validation Cloud Function locally:</p>
            <input type="text" id="admin-key" class="url-input" 
                   value="local-test-admin-key"
                   placeholder="Enter admin key">
            <button class="test-button" onclick="testLocalCloudFunction()">Run Security Tests</button>
            <button class="test-button" onclick="testHealthCheck()">Health Check</button>
            <div id="function-results" class="test-results"></div>
        </div>

        <div class="test-section">
            <h3>5. Network Connectivity Test</h3>
            <p>Test basic connectivity to all local services:</p>
            <button class="test-button" onclick="testConnectivity()">Test All Services</button>
            <div id="connectivity-results" class="test-results"></div>
        </div>
    </div>

    <script>
        // Local URLs for testing
        const LOCAL_URLS = {
            prodDesigner: 'http://localhost:8084/',
            sparkOasis: 'http://localhost:3000/',
            functions: 'http://localhost:5001/designmycarport/us-central1'
        };

        // Track iframe loading status
        function setupIframeMonitoring() {
            const localIframe = document.getElementById('local-iframe');
            const localStatus = document.getElementById('local-status');
            
            localIframe.onload = function() {
                localStatus.className = 'status success';
                localStatus.textContent = '✅ Local iframe loaded successfully';
            };
            
            localIframe.onerror = function() {
                localStatus.className = 'status error';
                localStatus.textContent = '❌ Failed to load local iframe - check if prod-designer-core is running';
            };

            // Timeout check
            setTimeout(() => {
                if (localStatus.textContent.includes('Loading')) {
                    localStatus.className = 'status error';
                    localStatus.textContent = '⏰ Timeout loading iframe - check if prod-designer-core is running on port 8084';
                }
            }, 10000);
        }

        // Test custom URL
        function testCustomURL() {
            const url = document.getElementById('custom-url').value;
            const iframe = document.getElementById('custom-iframe');
            const status = document.getElementById('custom-status');
            
            if (!url) {
                status.className = 'status error';
                status.textContent = '❌ Please enter a URL';
                return;
            }
            
            status.className = 'status warning';
            status.textContent = '⏳ Loading...';
            
            iframe.src = url;
            
            iframe.onload = function() {
                status.className = 'status success';
                status.textContent = `✅ Successfully loaded: ${url}`;
            };
            
            iframe.onerror = function() {
                status.className = 'status error';
                status.textContent = `❌ Failed to load: ${url}`;
            };
            
            // Timeout after 10 seconds
            setTimeout(() => {
                if (status.textContent.includes('Loading')) {
                    status.className = 'status error';
                    status.textContent = `⏰ Timeout loading: ${url}`;
                }
            }, 10000);
        }

        // Test local security headers
        async function testLocalHeaders() {
            const results = document.getElementById('headers-results');
            results.textContent = 'Testing local security headers...\n';
            
            try {
                const response = await fetch(LOCAL_URLS.prodDesigner, {
                    method: 'HEAD',
                    mode: 'cors'
                });
                
                const headers = {};
                for (let [key, value] of response.headers.entries()) {
                    headers[key] = value;
                }
                
                results.textContent += 'Local Response Headers:\n';
                results.textContent += JSON.stringify(headers, null, 2);
                
                // Check for security headers
                const securityHeaders = [
                    'x-frame-options',
                    'content-security-policy',
                    'x-content-type-options',
                    'referrer-policy'
                ];
                
                results.textContent += '\n\nSecurity Headers Check:\n';
                securityHeaders.forEach(header => {
                    const value = headers[header];
                    results.textContent += `${header}: ${value || 'NOT SET'}\n`;
                });
                
            } catch (error) {
                results.textContent += `Error: ${error.message}\n`;
                results.textContent += 'Make sure prod-designer-core is running on localhost:8084\n';
            }
        }

        // Test local CORS
        async function testLocalCORS() {
            const results = document.getElementById('headers-results');
            results.textContent += '\n\nTesting local CORS...\n';
            
            try {
                const response = await fetch(LOCAL_URLS.prodDesigner, {
                    method: 'GET',
                    mode: 'cors',
                    headers: {
                        'Origin': window.location.origin
                    }
                });
                
                results.textContent += `CORS Test: ${response.ok ? 'PASSED' : 'FAILED'}\n`;
                results.textContent += `Status: ${response.status} ${response.statusText}\n`;
                
                const corsHeaders = [
                    'access-control-allow-origin',
                    'access-control-allow-methods',
                    'access-control-allow-headers',
                    'access-control-allow-credentials'
                ];
                
                corsHeaders.forEach(header => {
                    const value = response.headers.get(header);
                    results.textContent += `${header}: ${value || 'NOT SET'}\n`;
                });
                
            } catch (error) {
                results.textContent += `CORS Error: ${error.message}\n`;
            }
        }

        // Test local Cloud Function
        async function testLocalCloudFunction() {
            const adminKey = document.getElementById('admin-key').value || 'local-test-admin-key';
            const results = document.getElementById('function-results');
            results.textContent = 'Running local security tests...\n';
            
            try {
                const response = await fetch(`${LOCAL_URLS.functions}/testSecurity`, {
                    method: 'GET',
                    headers: {
                        'X-Admin-Key': adminKey,
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    results.textContent = 'Local Security Test Results:\n';
                    results.textContent += JSON.stringify(data, null, 2);
                } else {
                    results.textContent = `Error: ${data.error}\n`;
                    if (data.details) {
                        results.textContent += `Details: ${data.details}\n`;
                    }
                }
                
            } catch (error) {
                results.textContent += `Network Error: ${error.message}\n`;
                results.textContent += 'Make sure Firebase Functions emulator is running on localhost:5001\n';
                results.textContent += 'Start with: firebase emulators:start --only functions\n';
            }
        }

        // Test health check
        async function testHealthCheck() {
            const results = document.getElementById('function-results');
            results.textContent += '\n\nTesting health check...\n';
            
            try {
                const response = await fetch(`${LOCAL_URLS.functions}/healthCheck`);
                const data = await response.json();
                
                results.textContent += `Health Check: ${response.ok ? 'PASSED' : 'FAILED'}\n`;
                results.textContent += JSON.stringify(data, null, 2);
                
            } catch (error) {
                results.textContent += `Health Check Error: ${error.message}\n`;
            }
        }

        // Test connectivity to all services
        async function testConnectivity() {
            const results = document.getElementById('connectivity-results');
            results.textContent = 'Testing connectivity to local services...\n\n';
            
            const services = [
                { name: 'prod-designer-core', url: LOCAL_URLS.prodDesigner },
                { name: 'spark-oasis-main', url: LOCAL_URLS.sparkOasis },
                { name: 'Firebase Functions', url: `${LOCAL_URLS.functions}/healthCheck` }
            ];
            
            for (const service of services) {
                try {
                    const response = await fetch(service.url, { method: 'HEAD' });
                    results.textContent += `✅ ${service.name}: ${response.status} ${response.statusText}\n`;
                } catch (error) {
                    results.textContent += `❌ ${service.name}: ${error.message}\n`;
                }
            }
            
            results.textContent += '\n🎯 Quick Start Commands:\n';
            results.textContent += 'prod-designer-core: cd prod-designer-core && npm run web\n';
            results.textContent += 'spark-oasis-main: cd spark-oasis-main && npm run dev\n';
            results.textContent += 'Firebase Functions: cd prod-designer-core && firebase emulators:start --only functions\n';
        }

        // Initialize monitoring when page loads
        document.addEventListener('DOMContentLoaded', setupIframeMonitoring);
    </script>
</body>
</html>
