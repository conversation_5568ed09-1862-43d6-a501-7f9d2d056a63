#!/bin/bash

# Security Implementation Deployment Script
# Deploys security measures for iframe embedding

set -e

echo "🔒 Deploying Security Implementation for Iframe Embedding"
echo "========================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required tools are installed
check_requirements() {
    print_status "Checking requirements..."
    
    if ! command -v firebase &> /dev/null; then
        print_error "Firebase CLI is not installed. Please install it first:"
        echo "npm install -g firebase-tools"
        exit 1
    fi
    
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js first."
        exit 1
    fi
    
    print_success "All requirements met"
}

# Deploy prod-designer-core security
deploy_prod_designer_core() {
    print_status "Deploying prod-designer-core security measures..."
    
    if [ ! -d "prod-designer-core" ]; then
        print_error "prod-designer-core directory not found"
        exit 1
    fi
    
    cd prod-designer-core
    
    # Install and deploy Cloud Functions
    print_status "Installing Cloud Functions dependencies..."
    cd functions
    npm install
    
    print_status "Deploying Cloud Functions..."
    firebase deploy --only functions
    
    if [ $? -eq 0 ]; then
        print_success "Cloud Functions deployed successfully"
    else
        print_error "Failed to deploy Cloud Functions"
        exit 1
    fi
    
    cd ..
    
    # Build and deploy hosting
    print_status "Building web application..."
    npm run build:web
    
    print_status "Deploying Firebase Hosting with security headers..."
    firebase deploy --only hosting
    
    if [ $? -eq 0 ]; then
        print_success "Firebase Hosting deployed successfully"
    else
        print_error "Failed to deploy Firebase Hosting"
        exit 1
    fi
    
    cd ..
}

# Deploy spark-oasis-main updates
deploy_spark_oasis_main() {
    print_status "Deploying spark-oasis-main updates..."
    
    if [ ! -d "spark-oasis-main" ]; then
        print_error "spark-oasis-main directory not found"
        exit 1
    fi
    
    cd spark-oasis-main
    
    # Install dependencies
    print_status "Installing dependencies..."
    npm install
    
    # Build application
    print_status "Building application..."
    npm run build
    
    # Deploy to Firebase
    print_status "Deploying to Firebase Hosting..."
    firebase deploy
    
    if [ $? -eq 0 ]; then
        print_success "spark-oasis-main deployed successfully"
    else
        print_error "Failed to deploy spark-oasis-main"
        exit 1
    fi
    
    cd ..
}

# Test security implementation
test_security() {
    print_status "Testing security implementation..."
    
    # Test prod-designer-core accessibility
    print_status "Testing prod-designer-core accessibility..."
    if curl -s -o /dev/null -w "%{http_code}" https://designmycarport.firebaseapp.com/ | grep -q "200"; then
        print_success "prod-designer-core is accessible"
    else
        print_warning "prod-designer-core may not be accessible"
    fi
    
    # Test spark-oasis-main accessibility
    print_status "Testing spark-oasis-main accessibility..."
    if curl -s -o /dev/null -w "%{http_code}" https://website-463606.firebaseapp.com/ | grep -q "200"; then
        print_success "spark-oasis-main is accessible"
    else
        print_warning "spark-oasis-main may not be accessible"
    fi
    
    # Test security headers
    print_status "Testing security headers..."
    headers=$(curl -s -I https://designmycarport.firebaseapp.com/)
    
    if echo "$headers" | grep -q "x-frame-options"; then
        print_success "X-Frame-Options header is present"
    else
        print_warning "X-Frame-Options header is missing"
    fi
    
    if echo "$headers" | grep -q "content-security-policy"; then
        print_success "Content-Security-Policy header is present"
    else
        print_warning "Content-Security-Policy header is missing"
    fi
    
    print_status "Security testing completed"
}

# Main deployment process
main() {
    echo "Starting deployment process..."
    echo
    
    check_requirements
    echo
    
    # Ask for confirmation
    read -p "Do you want to proceed with the deployment? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_status "Deployment cancelled"
        exit 0
    fi
    
    echo
    deploy_prod_designer_core
    echo
    
    deploy_spark_oasis_main
    echo
    
    test_security
    echo
    
    print_success "🎉 Security implementation deployment completed!"
    echo
    echo "Next steps:"
    echo "1. Open test-security.html in your browser to run manual tests"
    echo "2. Verify iframe embedding works from spark-oasis-main"
    echo "3. Test unauthorized access is properly blocked"
    echo "4. Monitor Cloud Function logs for any issues"
    echo
    echo "Documentation: See SECURITY_IMPLEMENTATION.md for detailed information"
}

# Run main function
main "$@"
