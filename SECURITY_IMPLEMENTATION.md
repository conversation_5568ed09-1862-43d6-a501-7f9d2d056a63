# 🔒 Security Implementation Guide
## Iframe Embedding Security for prod-designer-core → spark-oasis-main

This document outlines the comprehensive security measures implemented to securely embed the prod-designer-core application into spark-oasis-main using iframes.

## 📋 Table of Contents

1. [Security Overview](#security-overview)
2. [Implementation Details](#implementation-details)
3. [Configuration](#configuration)
4. [Testing](#testing)
5. [Deployment](#deployment)
6. [Troubleshooting](#troubleshooting)

## 🛡️ Security Overview

### Security Measures Implemented

1. **X-Frame-Options Headers** - Control iframe embedding permissions
2. **Content Security Policy (CSP)** - Define allowed frame ancestors
3. **IP Address Whitelisting** - Restrict access by IP address
4. **Domain Name Whitelisting** - Restrict access by domain/origin
5. **CORS Policies** - Secure cross-origin resource sharing
6. **Request Validation Middleware** - Server-side security validation
7. **Iframe Sandboxing** - Client-side iframe security restrictions

### Architecture

```
spark-oasis-main (Parent App)
    ↓ iframe request
prod-designer-core (Embedded App)
    ↓ security validation
Cloud Functions (Security Middleware)
    ↓ validation result
Firebase Hosting (Secure Headers)
```

## 🔧 Implementation Details

### 1. Firebase Hosting Security Headers

**File:** `prod-designer-core/firebase.json`

```json
{
  "hosting": {
    "headers": [
      {
        "source": "**",
        "headers": [
          {
            "key": "X-Frame-Options",
            "value": "ALLOWALL"
          },
          {
            "key": "Content-Security-Policy",
            "value": "frame-ancestors 'self' https://*.firebaseapp.com https://*.web.app https://localhost:* http://localhost:*;"
          },
          {
            "key": "X-Content-Type-Options",
            "value": "nosniff"
          },
          {
            "key": "Referrer-Policy",
            "value": "strict-origin-when-cross-origin"
          }
        ]
      }
    ]
  }
}
```

### 2. Cloud Functions Security Middleware

**Files:**
- `prod-designer-core/functions/src/security-middleware.ts`
- `prod-designer-core/functions/src/cors-config.ts`
- `prod-designer-core/functions/src/secure-app.ts`

**Key Features:**
- IP address validation with CIDR support
- Domain/origin whitelisting with wildcard support
- Development mode bypass
- Comprehensive CORS handling
- Request logging and monitoring

### 3. Client-Side Security (spark-oasis-main)

**File:** `spark-oasis-main/src/pages/CarportDesigner.tsx`

**Features:**
- Iframe sandboxing with restricted permissions
- Origin validation for postMessage communication
- Error handling and fallback URLs
- Loading states and retry mechanisms
- Referrer policy enforcement

## ⚙️ Configuration

### Environment Variables

#### prod-designer-core (.env)
```bash
# Security Configuration
NODE_ENV=production
SECURITY_ENABLED=true

# Allowed IPs (comma-separated)
ALLOWED_IPS=127.0.0.1,::1,YOUR_PRODUCTION_IP

# Allowed Domains (comma-separated)
ALLOWED_DOMAINS=localhost,designmycarport.firebaseapp.com,website-463606.firebaseapp.com,yourdomain.com

# Allowed Origins (comma-separated)
ALLOWED_ORIGINS=https://designmycarport.firebaseapp.com,https://website-463606.firebaseapp.com,https://yourdomain.com

# Admin Configuration
ADMIN_KEY=your-secure-admin-key-here
```

#### spark-oasis-main (.env)
```bash
# Iframe Security Configuration
VITE_CARPORT_DESIGNER_URL_PROD=https://designmycarport.firebaseapp.com/
VITE_CARPORT_DESIGNER_URL_DEV=http://localhost:8084/
VITE_ALLOWED_IFRAME_ORIGINS=https://designmycarport.firebaseapp.com,https://designmycarport.web.app
```

### Customizing Security Settings

#### Adding New Allowed Domains
1. Update `ALLOWED_DOMAINS` in prod-designer-core environment
2. Update `allowedOrigins` in `cors-config.ts`
3. Update CSP `frame-ancestors` in `firebase.json`
4. Redeploy both applications

#### Adding New IP Addresses
1. Update `ALLOWED_IPS` in prod-designer-core environment
2. Update `allowedIPs` in `security-middleware.ts`
3. Redeploy Cloud Functions

## 🧪 Testing

### Automated Testing

Run the security test suite:

```bash
# Deploy functions first
cd prod-designer-core/functions
npm run deploy

# Run tests via Cloud Function
curl -X GET \
  -H "X-Admin-Key: dev-admin-key" \
  https://us-central1-designmycarport.cloudfunctions.net/testSecurity
```

### Manual Testing

1. Open `test-security.html` in a web browser
2. Test authorized embedding
3. Test custom URLs
4. Verify security headers
5. Run Cloud Function tests

### Test Scenarios

#### ✅ Should Allow:
- Embedding from whitelisted domains
- Requests from whitelisted IP addresses
- CORS requests from allowed origins
- Development mode (localhost) access

#### ❌ Should Block:
- Embedding from non-whitelisted domains
- Requests from blocked IP addresses
- CORS requests from unauthorized origins
- Malicious iframe attempts

## 🚀 Deployment

### 1. Deploy prod-designer-core Security

```bash
cd prod-designer-core

# Install dependencies
cd functions
npm install

# Deploy Cloud Functions
npm run deploy

# Deploy hosting with new headers
cd ..
npm run build:web
firebase deploy --only hosting
```

### 2. Deploy spark-oasis-main Updates

```bash
cd spark-oasis-main

# Install dependencies
npm install

# Build and deploy
npm run build
firebase deploy
```

### 3. Verify Deployment

1. Check iframe embedding works from spark-oasis-main
2. Verify security headers are present
3. Test unauthorized access is blocked
4. Run security test suite

## 🔍 Troubleshooting

### Common Issues

#### Iframe Not Loading
**Symptoms:** Blank iframe or loading errors
**Solutions:**
1. Check browser console for CSP violations
2. Verify allowed origins in CORS configuration
3. Ensure Firebase hosting headers are deployed
4. Check network connectivity

#### CORS Errors
**Symptoms:** "Access blocked by CORS policy"
**Solutions:**
1. Add origin to `allowedOrigins` in cors-config.ts
2. Verify origin format (include protocol and port)
3. Check preflight OPTIONS handling
4. Redeploy Cloud Functions

#### Security Validation Failures
**Symptoms:** 403 Forbidden responses
**Solutions:**
1. Check IP address is in whitelist
2. Verify domain is in allowed domains
3. Check development mode settings
4. Review Cloud Function logs

### Debug Commands

```bash
# Check Firebase hosting headers
curl -I https://designmycarport.firebaseapp.com/

# Test CORS
curl -X OPTIONS \
  -H "Origin: https://website-463606.firebaseapp.com" \
  -H "Access-Control-Request-Method: GET" \
  https://designmycarport.firebaseapp.com/

# View Cloud Function logs
firebase functions:log --only testSecurity

# Test security validation
curl -X GET \
  -H "X-Admin-Key: your-admin-key" \
  -H "Origin: https://website-463606.firebaseapp.com" \
  https://us-central1-designmycarport.cloudfunctions.net/secureApp
```

### Security Monitoring

Monitor these metrics:
- Failed authentication attempts
- Blocked IP addresses
- CORS violations
- Iframe embedding attempts from unauthorized domains

## 📚 Additional Resources

- [Firebase Hosting Headers Documentation](https://firebase.google.com/docs/hosting/full-config#headers)
- [Content Security Policy Guide](https://developer.mozilla.org/en-US/docs/Web/HTTP/CSP)
- [CORS Documentation](https://developer.mozilla.org/en-US/docs/Web/HTTP/CORS)
- [Iframe Security Best Practices](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/iframe#security)

## 🔐 Security Best Practices

1. **Regular Updates:** Keep security configurations updated
2. **Monitoring:** Monitor access logs for suspicious activity
3. **Testing:** Regularly test security measures
4. **Documentation:** Keep security documentation current
5. **Principle of Least Privilege:** Only allow necessary access
6. **Defense in Depth:** Use multiple security layers

---

**Last Updated:** 2025-01-06
**Version:** 1.0.0
