#!/bin/bash

# Local Testing Runner Script
# Runs automated tests for the security implementation

echo "🧪 Running Local Security Tests"
echo "==============================="

# Test 1: Basic connectivity
echo "Testing basic connectivity..."
curl -s http://localhost:8081/ > /dev/null && echo "✅ prod-designer-core is accessible" || echo "❌ prod-designer-core is not accessible"
curl -s http://localhost:8080/ > /dev/null && echo "✅ spark-oasis-main is accessible" || echo "❌ spark-oasis-main is not accessible"

# Test 2: Security headers
echo -e "\nTesting security headers..."
HEADERS=$(curl -s -I http://localhost:8081/)
echo "$HEADERS" | grep -qi "x-frame-options" && echo "✅ X-Frame-Options header present" || echo "❌ X-Frame-Options header missing"
echo "$HEADERS" | grep -qi "content-security-policy" && echo "✅ CSP header present" || echo "❌ CSP header missing"

# Test 3: CORS
echo -e "\nTesting CORS..."
curl -s -H "Origin: http://localhost:8080" http://localhost:8081/ > /dev/null && echo "✅ CORS allows localhost:8080" || echo "❌ CORS blocks localhost:8080"

# Test 4: Cloud Functions (if running)
echo -e "\nTesting Cloud Functions..."
if curl -s http://localhost:5001/designmycarport/us-central1/healthCheck > /dev/null 2>&1; then
    echo "✅ Cloud Functions emulator is running"
    curl -s -H "X-Admin-Key: local-test-admin-key" http://localhost:5001/designmycarport/us-central1/testSecurity > /dev/null && echo "✅ Security tests endpoint accessible" || echo "❌ Security tests endpoint not accessible"
else
    echo "⚠️  Cloud Functions emulator not running"
fi

echo -e "\n🎉 Local testing complete!"
echo "Open http://localhost:8080/carport-designer to test iframe embedding"
