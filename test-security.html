<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Security Testing Suite - Iframe Embedding</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .iframe-container {
            width: 100%;
            height: 400px;
            border: 2px solid #ddd;
            border-radius: 5px;
            overflow: hidden;
            position: relative;
        }
        .iframe-container iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .test-button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background-color: #0056b3;
        }
        .test-results {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-top: 10px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .url-input {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔒 Security Testing Suite - Iframe Embedding</h1>
        <p>This page tests the security measures implemented for iframe embedding of the prod-designer-core application.</p>
    </div>

    <div class="container">
        <div class="test-section">
            <h3>1. Authorized Embedding Test</h3>
            <p>Testing iframe embedding from an authorized domain (should work):</p>
            <div class="iframe-container">
                <iframe 
                    id="authorized-iframe"
                    src="https://designmycarport.firebaseapp.com/"
                    title="Authorized Embedding Test"
                    sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-presentation">
                </iframe>
            </div>
            <div id="authorized-status" class="status warning">Loading...</div>
        </div>

        <div class="test-section">
            <h3>2. Custom URL Test</h3>
            <p>Test embedding with a custom URL:</p>
            <input type="text" id="custom-url" class="url-input" 
                   value="https://designmycarport.firebaseapp.com/" 
                   placeholder="Enter URL to test">
            <button class="test-button" onclick="testCustomURL()">Test URL</button>
            <div class="iframe-container">
                <iframe 
                    id="custom-iframe"
                    title="Custom URL Test"
                    sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-presentation">
                </iframe>
            </div>
            <div id="custom-status" class="status warning">Enter a URL and click Test</div>
        </div>

        <div class="test-section">
            <h3>3. Security Headers Test</h3>
            <p>Test security headers and CORS policies:</p>
            <button class="test-button" onclick="testSecurityHeaders()">Test Headers</button>
            <button class="test-button" onclick="testCORS()">Test CORS</button>
            <div id="headers-results" class="test-results"></div>
        </div>

        <div class="test-section">
            <h3>4. Cloud Function Security Test</h3>
            <p>Test the security validation Cloud Function:</p>
            <input type="text" id="admin-key" class="url-input" 
                   placeholder="Enter admin key (default: dev-admin-key)">
            <button class="test-button" onclick="testCloudFunction()">Run Security Tests</button>
            <div id="function-results" class="test-results"></div>
        </div>
    </div>

    <script>
        // Track iframe loading status
        function setupIframeMonitoring() {
            const authorizedIframe = document.getElementById('authorized-iframe');
            const authorizedStatus = document.getElementById('authorized-status');
            
            authorizedIframe.onload = function() {
                authorizedStatus.className = 'status success';
                authorizedStatus.textContent = '✅ Authorized iframe loaded successfully';
            };
            
            authorizedIframe.onerror = function() {
                authorizedStatus.className = 'status error';
                authorizedStatus.textContent = '❌ Failed to load authorized iframe';
            };
        }

        // Test custom URL
        function testCustomURL() {
            const url = document.getElementById('custom-url').value;
            const iframe = document.getElementById('custom-iframe');
            const status = document.getElementById('custom-status');
            
            if (!url) {
                status.className = 'status error';
                status.textContent = '❌ Please enter a URL';
                return;
            }
            
            status.className = 'status warning';
            status.textContent = '⏳ Loading...';
            
            iframe.src = url;
            
            iframe.onload = function() {
                status.className = 'status success';
                status.textContent = `✅ Successfully loaded: ${url}`;
            };
            
            iframe.onerror = function() {
                status.className = 'status error';
                status.textContent = `❌ Failed to load: ${url}`;
            };
            
            // Timeout after 10 seconds
            setTimeout(() => {
                if (status.textContent.includes('Loading')) {
                    status.className = 'status error';
                    status.textContent = `⏰ Timeout loading: ${url}`;
                }
            }, 10000);
        }

        // Test security headers
        async function testSecurityHeaders() {
            const results = document.getElementById('headers-results');
            results.textContent = 'Testing security headers...\n';
            
            try {
                const response = await fetch('https://designmycarport.firebaseapp.com/', {
                    method: 'HEAD',
                    mode: 'cors'
                });
                
                const headers = {};
                for (let [key, value] of response.headers.entries()) {
                    headers[key] = value;
                }
                
                results.textContent += 'Response Headers:\n';
                results.textContent += JSON.stringify(headers, null, 2);
                
                // Check for security headers
                const securityHeaders = [
                    'x-frame-options',
                    'content-security-policy',
                    'x-content-type-options',
                    'referrer-policy'
                ];
                
                results.textContent += '\n\nSecurity Headers Check:\n';
                securityHeaders.forEach(header => {
                    const value = headers[header];
                    results.textContent += `${header}: ${value || 'NOT SET'}\n`;
                });
                
            } catch (error) {
                results.textContent += `Error: ${error.message}\n`;
            }
        }

        // Test CORS
        async function testCORS() {
            const results = document.getElementById('headers-results');
            results.textContent += '\n\nTesting CORS...\n';
            
            try {
                const response = await fetch('https://designmycarport.firebaseapp.com/', {
                    method: 'GET',
                    mode: 'cors',
                    headers: {
                        'Origin': window.location.origin
                    }
                });
                
                results.textContent += `CORS Test: ${response.ok ? 'PASSED' : 'FAILED'}\n`;
                results.textContent += `Status: ${response.status} ${response.statusText}\n`;
                
                const corsHeaders = [
                    'access-control-allow-origin',
                    'access-control-allow-methods',
                    'access-control-allow-headers',
                    'access-control-allow-credentials'
                ];
                
                corsHeaders.forEach(header => {
                    const value = response.headers.get(header);
                    results.textContent += `${header}: ${value || 'NOT SET'}\n`;
                });
                
            } catch (error) {
                results.textContent += `CORS Error: ${error.message}\n`;
            }
        }

        // Test Cloud Function security
        async function testCloudFunction() {
            const adminKey = document.getElementById('admin-key').value || 'dev-admin-key';
            const results = document.getElementById('function-results');
            results.textContent = 'Running security tests...\n';
            
            try {
                const response = await fetch('https://us-central1-designmycarport.cloudfunctions.net/testSecurity', {
                    method: 'GET',
                    headers: {
                        'X-Admin-Key': adminKey,
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    results.textContent = 'Security Test Results:\n';
                    results.textContent += JSON.stringify(data, null, 2);
                } else {
                    results.textContent = `Error: ${data.error}\n`;
                    if (data.details) {
                        results.textContent += `Details: ${data.details}\n`;
                    }
                }
                
            } catch (error) {
                results.textContent += `Network Error: ${error.message}\n`;
                results.textContent += 'Make sure the Cloud Function is deployed and accessible.\n';
            }
        }

        // Initialize monitoring when page loads
        document.addEventListener('DOMContentLoaded', setupIframeMonitoring);
    </script>
</body>
</html>
