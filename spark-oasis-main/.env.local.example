# Local Development Environment Configuration
# Copy this file to .env.local and modify as needed

# Iframe URLs for Local Testing
VITE_CARPORT_DESIGNER_URL_PROD=http://localhost:8084/
VITE_CARPORT_DESIGNER_URL_DEV=http://localhost:8084/
VITE_CARPORT_DESIGNER_URL_FALLBACK=http://localhost:8084/

# Allowed iframe origins for security validation
VITE_ALLOWED_IFRAME_ORIGINS=http://localhost:8084,http://127.0.0.1:8084

# Content Security Policy Configuration
VITE_CSP_FRAME_SRC=http://localhost:8084 http://127.0.0.1:8084
VITE_CSP_CONNECT_SRC=http://localhost:8084 http://127.0.0.1:8084

# Security Headers
VITE_ENABLE_SECURITY_HEADERS=true
VITE_X_FRAME_OPTIONS=SAMEORIGIN
VITE_X_CONTENT_TYPE_OPTIONS=nosniff

# Development Settings
VITE_DEVELOPMENT_MODE=true
VITE_ENABLE_DEBUG_LOGS=true

# Error Reporting (disabled for local development)
VITE_ENABLE_ERROR_REPORTING=false

# Local Firebase Emulator URLs (if using)
VITE_FIREBASE_FUNCTIONS_URL=http://localhost:5001/designmycarport/us-central1

# Testing Configuration
VITE_TEST_MODE=true
VITE_ADMIN_KEY=local-test-admin-key
