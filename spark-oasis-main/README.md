# Spark Oasis - XYZ Company Website

A modern, responsive website for XYZ Company, specializing in COLD FORMED steel building solutions. Built with React, TypeScript, and Tailwind CSS.

## 🚀 Features

### Core Pages

- **Home** (`/`) - Landing page with company overview and key services
- **Building** (`/building`) - 3D carport designer integration and building solutions
- **Buying** (`/buying`) - Information for customers looking to purchase
- **Selling** (`/selling`) - Resources for dealers and partners
- **Manufacturing** (`/manufacturing`) - Manufacturing capabilities and processes
- **Engineering** (`/engineering`) - Engineering services and expertise
- **Contact** (`/contact`) - Contact form with location detection and privacy policy links
- **Blog** (`/blog`) - Company blog and news

### Legal Pages

- **Privacy Policy** (`/privacy`) - Privacy policy and data handling
- **Email Policy** (`/email-policy`) - Email consent and communication policy
- **Terms of Service** (`/terms`) - Terms and conditions
- **Cookie Policy** (`/cookies`) - Cookie usage and preferences

### Key Features

- **Responsive Design** - Mobile-first approach with Tailwind CSS
- **3D Integration** - React Three Fiber for 3D carport designer
- **Form Handling** - Contact forms with Firebase integration
- **Location Detection** - Automatic user location detection for better UX
- **Modern UI Components** - Radix UI components with custom styling
- **TypeScript** - Full type safety throughout the application
- **Firebase Backend** - Real-time data and form submissions

## 🛠️ Tech Stack

### Frontend

- **React 18** - UI framework
- **TypeScript** - Type safety
- **Vite** - Build tool and dev server
- **Tailwind CSS** - Utility-first CSS framework
- **Radix UI** - Accessible UI components
- **React Router** - Client-side routing
- **React Hook Form** - Form handling
- **Framer Motion** - Animations

### 3D Graphics

- **React Three Fiber** - React renderer for Three.js
- **Three.js** - 3D graphics library
- **@react-three/drei** - Useful helpers for React Three Fiber

### Backend & Services

- **Firebase** - Backend services (Firestore, Functions, Hosting)
- **Firebase Functions** - Serverless backend functions

### Development Tools

- **Prettier** - Code formatting
- **ESLint** - Code linting
- **Vitest** - Unit testing

## 📦 Installation

### Prerequisites

- Node.js (v18 or higher)
- npm or yarn
- Firebase CLI (for deployment)

### Setup Instructions

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd spark-oasis
   ```

2. **Install dependencies**

   ```bash
   npm install
   ```

3. **Environment Setup**

   ```bash
   cp env.example .env.local
   ```

   **IMPORTANT**: For `.env` credentials, see shared document "IMPORTANT/Credentials"

   Edit `.env.local` and add your Firebase configuration:

   ```env
   VITE_FIREBASE_API_KEY=your_api_key
   VITE_FIREBASE_AUTH_DOMAIN=your_auth_domain
   VITE_FIREBASE_PROJECT_ID=your_project_id
   VITE_FIREBASE_STORAGE_BUCKET=your_storage_bucket
   VITE_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
   VITE_FIREBASE_APP_ID=your_app_id
   ```

4. **Firebase Setup**
   - Follow the instructions in `FIREBASE_SETUP.md` for detailed Firebase configuration
   - Or use `FIREBASE_QUICK_SETUP.md` for a quick setup guide

### Firebase Configuration for Contact Form

The contact form uses Firebase Firestore for data storage. To configure Firebase for the contact form:

1. **Firebase Functions Configuration**

   - Navigate to `functions/` directory
   - Use `firebase config:get` to view current configuration
   - Ensure Firestore is enabled in your Firebase project

2. **Contact Form Setup**

   - The contact form automatically saves submissions to Firestore
   - Check `src/lib/firebase.ts` for Firebase configuration
   - Contact form data is stored in the `contacts` collection
   - **IP and Browser Language Auto-Detection**: The form automatically detects and captures user's IP address and browser language preferences for localization and analytics purposes

3. **Firebase Database Structure**

   - **Project Name**: "website"
   - **Database Collection**: `contactSubmission`
   - Contact form submissions are stored in the `contactSubmission` collection
   - Each submission includes user details and timestamp

4. **Confirmation Email Feature**

   - Users receive a confirmation email after submitting the contact form
   - Email is automatically sent using Firebase Functions
   - Email includes submission details and confirmation message
   - **Firebase Function**: `sendContactEmail` - Handles the email sending process and confirmation logic

5. **Environment Variables for Contact Form**
   ```env
   # Firebase configuration (already included above)
   VITE_FIREBASE_API_KEY=your_api_key
   VITE_FIREBASE_AUTH_DOMAIN=your_auth_domain
   VITE_FIREBASE_PROJECT_ID=your_project_id
   VITE_FIREBASE_STORAGE_BUCKET=your_storage_bucket
   VITE_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
   VITE_FIREBASE_APP_ID=your_app_id
   ```

## 🚀 Development

### Start Development Server

```bash
npm run dev
```

### Build for Production

```bash
npm run build
```

### Type Checking

```bash
npm run typecheck
```

### Code Formatting

```bash
npm run format.fix
```

### Testing

```bash
npm run test
```

## 📁 Project Structure

```
src/
├── components/          # Reusable UI components
├── pages/              # Page components
│   ├── Index.tsx       # Home page
│   ├── Building.tsx    # Building solutions page
│   ├── Contact.tsx     # Contact form page
│   ├── Privacy.tsx     # Privacy policy
│   └── ...            # Other pages
├── lib/                # Utility libraries
│   ├── firebase.ts     # Firebase configuration
│   └── logger.ts       # Logging utilities
├── hooks/              # Custom React hooks
├── contexts/           # React contexts
└── App.tsx             # Main app component
```

## 🎨 Styling

The project uses Tailwind CSS with a custom design system:

- **Brand Colors**: Custom red color scheme (`brand-red`)
- **Components**: Radix UI components with custom styling
- **Responsive**: Mobile-first responsive design
- **Animations**: Framer Motion for smooth transitions

## 🔧 Configuration

### Tailwind CSS

Configuration is in `tailwind.config.ts` with custom:

- Color palette
- Typography settings
- Animation utilities
- Component variants

### Vite

Configuration is in `vite.config.ts` with:

- React SWC plugin for fast compilation
- Path aliases for clean imports
- Build optimizations

## 📱 Responsive Design

The website is fully responsive with breakpoints:

- **Mobile**: Default (320px+)
- **Tablet**: `md:` (768px+)
- **Desktop**: `lg:` (1024px+)
- **Large Desktop**: `xl:` (1280px+)

## 🔒 Privacy & Legal

The website includes comprehensive legal pages:

- **Privacy Policy**: Data collection and usage
- **Email Policy**: Email consent and communication
- **Terms of Service**: Usage terms and conditions
- **Cookie Policy**: Cookie usage and preferences

All forms include proper consent checkboxes linking to these policies.

## 🚀 Deployment

### Firebase Hosting

```bash
npm run build
firebase deploy
```

### Environment Variables

Make sure to set up environment variables for production:

- Firebase configuration
- API keys
- Feature flags

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is private and proprietary to XYZ Company.

## 🆘 Support

For support and questions:

- Email: <EMAIL>
- Phone: +****************
- Business Hours: Monday - Friday: 8:00 AM - 6:00 PM

## 🔄 Updates

Stay updated with the latest changes:

- Check the `CHANGELOG.md` file
- Follow the release notes in GitHub
- Subscribe to company newsletters

---

**XYZ Company** - Transforming businesses with COLD FORMED steel building solutions.
