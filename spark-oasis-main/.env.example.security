# Spark Oasis Main - Security Configuration

# Iframe Security Configuration
VITE_CARPORT_DESIGNER_URL_PROD=https://designmycarport.firebaseapp.com/
VITE_CARPORT_DESIGNER_URL_DEV=http://localhost:8084/
VITE_CARPORT_DESIGNER_URL_FALLBACK=https://designmycarport.web.app/

# Allowed iframe origins for security validation
VITE_ALLOWED_IFRAME_ORIGINS=https://designmycarport.firebaseapp.com,https://designmycarport.web.app,http://localhost:8084

# Content Security Policy Configuration
VITE_CSP_FRAME_SRC=https://designmycarport.firebaseapp.com https://designmycarport.web.app http://localhost:8084
VITE_CSP_CONNECT_SRC=https://designmycarport.firebaseapp.com https://designmycarport.web.app http://localhost:8084

# Security Headers
VITE_ENABLE_SECURITY_HEADERS=true
VITE_X_FRAME_OPTIONS=SAMEORIGIN
VITE_X_CONTENT_TYPE_OPTIONS=nosniff

# Development Settings
VITE_DEVELOPMENT_MODE=false
VITE_ENABLE_DEBUG_LOGS=false

# Error Reporting
VITE_ENABLE_ERROR_REPORTING=true
VITE_ERROR_REPORTING_URL=https://your-error-reporting-service.com/api/errors
