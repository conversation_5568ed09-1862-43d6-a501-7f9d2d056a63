<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Local Security Testing Tool</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔒 Local Security Testing Tool</h1>
        <p>This tool helps validate the security implementation for iframe embedding.</p>
    </div>

    <div class="container">
        <div class="test-section">
            <h2>1. Service Connectivity Tests</h2>
            <button onclick="testConnectivity()">Test All Services</button>
            <div id="connectivity-results"></div>
        </div>

        <div class="test-section">
            <h2>2. Security Headers Test</h2>
            <button onclick="testSecurityHeaders()">Test Security Headers</button>
            <div id="headers-results"></div>
        </div>

        <div class="test-section">
            <h2>3. CORS Policy Test</h2>
            <button onclick="testCORS()">Test CORS Policies</button>
            <div id="cors-results"></div>
        </div>

        <div class="test-section">
            <h2>4. Cloud Functions Security Test</h2>
            <button onclick="testCloudFunctions()">Test Cloud Functions</button>
            <div id="functions-results"></div>
        </div>

        <div class="test-section">
            <h2>5. Iframe Embedding Test</h2>
            <button onclick="testIframeEmbedding()">Load Iframe</button>
            <div id="iframe-results"></div>
            <div id="iframe-container"></div>
        </div>

        <div class="test-section">
            <h2>6. Test Logs</h2>
            <button onclick="clearLogs()">Clear Logs</button>
            <div id="test-logs" class="log"></div>
        </div>
    </div>

    <script>
        const PROD_DESIGNER_URL = 'http://localhost:8081';
        const SPARK_OASIS_URL = 'http://localhost:8080';
        const FUNCTIONS_URL = 'http://localhost:5001/designmycarport/us-central1';
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logDiv = document.getElementById('test-logs');
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logDiv.textContent += logEntry;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(logEntry);
        }

        function showResult(containerId, message, type) {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.textContent = message;
            container.appendChild(resultDiv);
        }

        function clearResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }

        async function testConnectivity() {
            clearResults('connectivity-results');
            log('Starting connectivity tests...');

            const services = [
                { name: 'prod-designer-core', url: PROD_DESIGNER_URL },
                { name: 'spark-oasis-main', url: SPARK_OASIS_URL },
                { name: 'Firebase Functions', url: `${FUNCTIONS_URL}/healthCheck` }
            ];

            for (const service of services) {
                try {
                    const response = await fetch(service.url, { 
                        method: 'GET',
                        mode: 'cors'
                    });
                    if (response.ok) {
                        showResult('connectivity-results', `✅ ${service.name} is accessible`, 'success');
                        log(`${service.name} connectivity: SUCCESS`);
                    } else {
                        showResult('connectivity-results', `❌ ${service.name} returned ${response.status}`, 'error');
                        log(`${service.name} connectivity: FAILED (${response.status})`);
                    }
                } catch (error) {
                    showResult('connectivity-results', `❌ ${service.name} is not accessible: ${error.message}`, 'error');
                    log(`${service.name} connectivity: ERROR - ${error.message}`);
                }
            }
        }

        async function testSecurityHeaders() {
            clearResults('headers-results');
            log('Testing security headers...');

            try {
                const response = await fetch(PROD_DESIGNER_URL, { method: 'HEAD' });
                const headers = response.headers;
                
                const securityHeaders = [
                    'x-frame-options',
                    'content-security-policy',
                    'x-content-type-options'
                ];

                securityHeaders.forEach(header => {
                    if (headers.has(header)) {
                        showResult('headers-results', `✅ ${header}: ${headers.get(header)}`, 'success');
                        log(`Security header ${header}: PRESENT`);
                    } else {
                        showResult('headers-results', `⚠️ ${header}: Not present`, 'warning');
                        log(`Security header ${header}: MISSING`);
                    }
                });
            } catch (error) {
                showResult('headers-results', `❌ Failed to test headers: ${error.message}`, 'error');
                log(`Security headers test: ERROR - ${error.message}`);
            }
        }

        async function testCORS() {
            clearResults('cors-results');
            log('Testing CORS policies...');

            try {
                const response = await fetch(PROD_DESIGNER_URL, {
                    method: 'GET',
                    mode: 'cors',
                    headers: {
                        'Origin': SPARK_OASIS_URL
                    }
                });
                
                if (response.ok) {
                    showResult('cors-results', '✅ CORS allows cross-origin requests', 'success');
                    log('CORS test: SUCCESS - Cross-origin requests allowed');
                } else {
                    showResult('cors-results', `⚠️ CORS response: ${response.status}`, 'warning');
                    log(`CORS test: WARNING - Response status ${response.status}`);
                }
            } catch (error) {
                if (error.message.includes('CORS')) {
                    showResult('cors-results', '❌ CORS policy blocks cross-origin requests', 'error');
                    log('CORS test: FAILED - Cross-origin requests blocked');
                } else {
                    showResult('cors-results', `❌ CORS test failed: ${error.message}`, 'error');
                    log(`CORS test: ERROR - ${error.message}`);
                }
            }
        }

        async function testCloudFunctions() {
            clearResults('functions-results');
            log('Testing Cloud Functions security...');

            // Test health check
            try {
                const healthResponse = await fetch(`${FUNCTIONS_URL}/healthCheck`);
                if (healthResponse.ok) {
                    const healthData = await healthResponse.json();
                    showResult('functions-results', '✅ Health check endpoint working', 'success');
                    log('Cloud Functions health check: SUCCESS');
                } else {
                    showResult('functions-results', '❌ Health check failed', 'error');
                    log('Cloud Functions health check: FAILED');
                }
            } catch (error) {
                showResult('functions-results', `❌ Health check error: ${error.message}`, 'error');
                log(`Cloud Functions health check: ERROR - ${error.message}`);
            }

            // Test security endpoint
            try {
                const securityResponse = await fetch(`${FUNCTIONS_URL}/testSecurity`, {
                    headers: {
                        'X-Admin-Key': 'local-test-admin-key'
                    }
                });
                if (securityResponse.ok) {
                    const securityData = await securityResponse.json();
                    showResult('functions-results', '✅ Security test endpoint working', 'success');
                    log('Cloud Functions security test: SUCCESS');
                } else {
                    showResult('functions-results', '❌ Security test failed', 'error');
                    log('Cloud Functions security test: FAILED');
                }
            } catch (error) {
                showResult('functions-results', `❌ Security test error: ${error.message}`, 'error');
                log(`Cloud Functions security test: ERROR - ${error.message}`);
            }
        }

        function testIframeEmbedding() {
            clearResults('iframe-results');
            log('Testing iframe embedding...');

            const container = document.getElementById('iframe-container');
            container.innerHTML = '';

            const iframe = document.createElement('iframe');
            iframe.src = PROD_DESIGNER_URL;
            iframe.style.width = '100%';
            iframe.style.height = '600px';
            iframe.style.border = '1px solid #ddd';
            iframe.style.borderRadius = '4px';

            iframe.onload = function() {
                showResult('iframe-results', '✅ Iframe loaded successfully', 'success');
                log('Iframe embedding: SUCCESS - Content loaded');
            };

            iframe.onerror = function() {
                showResult('iframe-results', '❌ Iframe failed to load', 'error');
                log('Iframe embedding: FAILED - Content failed to load');
            };

            // Timeout check
            setTimeout(() => {
                if (!iframe.contentDocument && !iframe.contentWindow) {
                    showResult('iframe-results', '⚠️ Iframe may be blocked by security policies', 'warning');
                    log('Iframe embedding: WARNING - May be blocked by security policies');
                }
            }, 5000);

            container.appendChild(iframe);
            log('Iframe embedding: Started loading content...');
        }

        function clearLogs() {
            document.getElementById('test-logs').textContent = '';
        }

        // Auto-run basic tests on page load
        window.onload = function() {
            log('Security testing tool loaded');
            log('Ready to run tests...');
        };
    </script>
</body>
</html>
