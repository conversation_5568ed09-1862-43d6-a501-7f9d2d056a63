# Firebase Functions - Gmail Email Configuration

This Firebase Functions project uses Gmail for sending email notifications from contact forms.

## Configuration Setup

### 1. Gmail Setup

1. Enable 2-Factor Authentication on your Gmail account
2. Generate an App Password:
   - Go to Google Account settings
   - Security → 2-Step Verification → App passwords
   - Generate a password for "Mail"

### 2. Firebase Configuration

Set up the following Firebase configuration values:

```bash
# Set Gmail email address
firebase functions:config:set email.user="<EMAIL>"

# Set Gmail app password
firebase functions:config:set email.password="your-app-password"

# Set admin email for notifications
firebase functions:config:set email.admin="<EMAIL>"
```

### 3. Verify Configuration

You can verify your configuration with:

```bash
firebase functions:config:get
```

## Functions

### sendContactEmail

- **Type**: Callable Function
- **Purpose**: Sends confirmation email to customer and notification email to admin
- **Trigger**: Called from client application when contact form is submitted

### testEmail

- **Type**: HTTP Function
- **Purpose**: Test endpoint for email functionality
- **URL**: `https://your-region-your-project.cloudfunctions.net/testEmail`

## Email Templates

The functions include HTML email templates for:

- Customer confirmation emails
- Admin notification emails

Both templates are styled with a professional design and include all relevant contact form information.

## Deployment

Deploy the functions with:

```bash
firebase deploy --only functions
```

## Troubleshooting

- Ensure your Gmail app password is correct
- Verify 2-Factor Authentication is enabled on your Gmail account
- Check Firebase Functions logs for any errors
- Test with the `testEmail` function before going live
