import { BrowserRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import Manufacturing from "./pages/Manufacturing";
import Selling from "./pages/Selling";
import Engineering from "./pages/Engineering";
import Building from "./pages/Building";
import Buying from "./pages/Buying";
import Contact from "./pages/Contact";
import Blog from "./pages/Blog";
import CarportDesigner from "./pages/CarportDesigner";
import Privacy from "./pages/Privacy";
import Terms from "./pages/Terms";
import Cookies from "./pages/Cookies";
import EmailPolicy from "./pages/EmailPolicy";
import NotFound from "./pages/NotFound";
import "./App.css";

function App() {
  return (
    <BrowserRouter>
      <Routes>
        <Route path="/" element={<Index />} />
        <Route path="/building" element={<Building />} />
        <Route path="/manufacturing" element={<Manufacturing />} />
        <Route path="/selling" element={<Selling />} />
        <Route path="/engineering" element={<Engineering />} />
        <Route path="/buying" element={<Buying />} />
        <Route path="/contact" element={<Contact />} />
        <Route path="/blog" element={<Blog />} />
        <Route path="/carport-designer" element={<CarportDesigner />} />
        <Route path="/privacy" element={<Privacy />} />
        <Route path="/terms" element={<Terms />} />
        <Route path="/cookies" element={<Cookies />} />
        <Route path="/email-policy" element={<EmailPolicy />} />
        {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
        <Route path="*" element={<NotFound />} />
      </Routes>
    </BrowserRouter>
  );
}

export default App;
