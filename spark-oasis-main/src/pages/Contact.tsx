import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import Navigation from "@/components/Navigation";
import Footer from "@/components/Footer";
import {
  Phone,
  Mail,
  MapPin,
  Globe,
  Clock,
  Send,
  CheckCircle,
  AlertCircle,
  Loader2,
} from "lucide-react";
import {
  handleContactFormSubmission,
  ContactFormData,
  LocationInfo,
} from "@/lib/firebase";
import { logger } from "@/lib/logger";

const Contact = () => {
  const [formData, setFormData] = useState<ContactFormData>({
    name: "",
    email: "",
    phone: "",
    state: "",
    country: "",
    city: "",
    message: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [submitMessage, setSubmitMessage] = useState("");
  const [submitError, setSubmitError] = useState("");
  const [acceptedTerms, setAcceptedTerms] = useState(false);
  const [locationInfo, setLocationInfo] = useState<LocationInfo>({
    detected: false,
    country: "",
    region: "",
    city: "",
    timezone: "",
    currency: "",
    userCountry: "",
    userCity: "",
  });

  // Detect user location on component mount
  useEffect(() => {
    const detectLocation = async () => {
      try {
        const response = await fetch("https://ipapi.co/json/");
        const data = await response.json();

        setLocationInfo({
          detected: true,
          country: data.country_name || "",
          region: data.region || "",
          city: data.city || "",
          timezone: data.timezone || "",
          currency: data.currency || "",
          userCountry: data.country_name || "",
          userCity: data.city || "",
        });

        // Auto-populate form fields with detected location
        setFormData((prev) => ({
          ...prev,
          country: data.country_code || "",
          city: data.city || "",
        }));
      } catch (error) {
        logger.warn("Location detection failed", error, "Contact");
        setLocationInfo((prev) => ({
          ...prev,
          detected: false,
        }));
      }
    };

    detectLocation();
  }, []);

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >,
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitError("");
    setSubmitMessage("");

    try {
      const result = await handleContactFormSubmission(
        formData,
        locationInfo,
        navigator.language,
      );

      if (result.success) {
        setIsSubmitted(true);
        setSubmitMessage(result.message);

        // Reset form after showing success
        setTimeout(() => {
          setIsSubmitted(false);
          setFormData({
            name: "",
            email: "",
            phone: "",
            state: "",
            country: "",
            city: "",
            message: "",
          });
          setSubmitMessage("");
        }, 5000);
      } else {
        setSubmitError(result.message);
      }
    } catch (error) {
      setSubmitError("An unexpected error occurred. Please try again.");
      logger.error("Form submission error", error, "Contact");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <div className="min-h-screen bg-white">
        <Navigation />

        {/* Hero Section */}
        <section className="py-20 bg-gradient-to-br from-gray-50 to-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <Badge className="mb-6 bg-brand-red/10 text-brand-red border-brand-red/20">
                Contact Us
              </Badge>
              <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                Get in Touch with
                <span className="text-brand-red"> XYZ Company</span>
              </h1>
              <p className="text-xl text-gray-600 max-w-4xl mx-auto mb-8">
                Ready to transform your business with COLD FORMED steel building
                solutions? Our expert team is here to help you get started.
              </p>
            </div>
          </div>
        </section>

        {/* Contact Form & Info Section */}
        <section className="py-12 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid lg:grid-cols-2 gap-8">
              {/* Contact Form */}
              <Card className="border-0 shadow-xl hover:shadow-2xl transition-all duration-300 bg-gradient-to-br from-white to-gray-50/30">
                <CardContent className="p-6">
                  <div className="flex items-center mb-6">
                    <div className="w-10 h-10 bg-brand-red/10 rounded-full flex items-center justify-center mr-3">
                      <Send className="h-5 w-5 text-brand-red" />
                    </div>
                    <div>
                      <h2 className="text-xl font-bold text-gray-900">
                        Send Us a Message
                      </h2>
                      <p className="text-gray-600 text-sm">
                        We'll get back to you within 24 hours
                      </p>
                    </div>
                  </div>

                  {/* Location Detection Status */}
                  {locationInfo.detected && (
                    <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                      <div className="flex items-center text-sm text-blue-700">
                        <MapPin className="h-4 w-4 mr-2" />
                        <span>
                          Detected location: {locationInfo.city},{" "}
                          {locationInfo.country}
                        </span>
                      </div>
                    </div>
                  )}

                  {/* Error Message */}
                  {submitError && (
                    <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                      <div className="flex items-center text-sm text-red-700">
                        <AlertCircle className="h-4 w-4 mr-2" />
                        <span>{submitError}</span>
                      </div>
                    </div>
                  )}

                  {/* Success Message */}
                  {isSubmitted && (
                    <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg">
                      <div className="flex items-center text-sm text-green-700">
                        <CheckCircle className="h-4 w-4 mr-2" />
                        <span>{submitMessage}</span>
                      </div>
                    </div>
                  )}

                  {isSubmitted ? (
                    <div className="text-center py-8">
                      <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <CheckCircle className="h-6 w-6 text-green-600" />
                      </div>
                      <h3 className="text-lg font-bold text-gray-900 mb-1">
                        Message Sent!
                      </h3>
                      <p className="text-gray-600 text-sm">
                        Thank you for contacting us. We'll respond soon.
                      </p>
                    </div>
                  ) : (
                    <form onSubmit={handleSubmit} className="space-y-4">
                      <div className="grid md:grid-cols-2 gap-3">
                        <div className="space-y-1">
                          <label className="block text-sm font-semibold text-gray-700">
                            Name *
                          </label>
                          <input
                            type="text"
                            name="name"
                            required
                            value={formData.name}
                            onChange={handleInputChange}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-red focus:border-transparent transition-all duration-200 hover:border-gray-400"
                            placeholder="Your Name"
                          />
                        </div>
                        <div className="space-y-1">
                          <label className="block text-sm font-semibold text-gray-700">
                            Email *
                          </label>
                          <input
                            type="email"
                            name="email"
                            required
                            value={formData.email}
                            onChange={handleInputChange}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-red focus:border-transparent transition-all duration-200 hover:border-gray-400"
                            placeholder="<EMAIL>"
                          />
                        </div>
                      </div>
                      <div className="grid md:grid-cols-3 gap-3">
                        <div className="space-y-1">
                          <label className="block text-sm font-semibold text-gray-700">
                            Contact Number
                          </label>
                          <input
                            type="tel"
                            name="phone"
                            value={formData.phone}
                            onChange={handleInputChange}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-red focus:border-transparent transition-all duration-200 hover:border-gray-400"
                            placeholder="Phone Number"
                          />
                        </div>
                        <div className="space-y-1">
                          <label className="block text-sm font-semibold text-gray-700">
                            City
                          </label>
                          <input
                            type="text"
                            name="city"
                            value={formData.city}
                            onChange={handleInputChange}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-red focus:border-transparent transition-all duration-200 hover:border-gray-400"
                            placeholder="City"
                          />
                        </div>
                        <div className="space-y-1">
                          <label className="block text-sm font-semibold text-gray-700">
                            State/Province
                          </label>
                          <input
                            type="text"
                            name="state"
                            value={formData.state}
                            onChange={handleInputChange}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-red focus:border-transparent transition-all duration-200 hover:border-gray-400"
                            placeholder="State"
                          />
                        </div>
                      </div>
                      <div className="space-y-1">
                        <label className="block text-sm font-semibold text-gray-700">
                          Country
                        </label>
                        <select
                          name="country"
                          value={formData.country}
                          onChange={handleInputChange}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-red focus:border-transparent transition-all duration-200 hover:border-gray-400 bg-white"
                        >
                          <option value="">Select Country</option>
                          <option value="US">United States</option>
                          <option value="CA">Canada</option>
                          <option value="AU">Australia</option>
                          <option value="GB">United Kingdom</option>
                          <option value="DE">Germany</option>
                          <option value="FR">France</option>
                          <option value="JP">Japan</option>
                          <option value="CN">China</option>
                          <option value="IN">India</option>
                          <option value="BR">Brazil</option>
                          <option value="MX">Mexico</option>
                          <option value="ZA">South Africa</option>
                          <option value="AE">United Arab Emirates</option>
                          <option value="SG">Singapore</option>
                          <option value="AU">Australia</option>
                          <option value="NZ">New Zealand</option>
                        </select>
                      </div>
                      <div className="space-y-1">
                        <label className="block text-sm font-semibold text-gray-700">
                          Message *
                        </label>
                        <textarea
                          name="message"
                          required
                          rows={4}
                          value={formData.message}
                          onChange={handleInputChange}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-red focus:border-transparent resize-none transition-all duration-200 hover:border-gray-400"
                          placeholder="Tell us about your inquiry..."
                        ></textarea>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id="acceptedTerms"
                          checked={acceptedTerms}
                          onChange={(e) => setAcceptedTerms(e.target.checked)}
                          className="h-4 w-4 text-brand-red"
                        />
                        <label
                          htmlFor="acceptedTerms"
                          className="text-sm text-gray-700"
                        >
                          I agree to the{" "}
                          <a
                            href="/privacy"
                            className="text-brand-red hover:underline"
                            target="_blank"
                            rel="noopener noreferrer"
                          >
                            Privacy Policy
                          </a>{" "}
                          and{" "}
                          <a
                            href="/email-policy"
                            className="text-brand-red hover:underline"
                            target="_blank"
                            rel="noopener noreferrer"
                          >
                            consent to be contacted
                          </a>{" "}
                          regarding my enquiry. (Required)
                        </label>
                      </div>
                      <Button
                        type="submit"
                        disabled={isSubmitting || !acceptedTerms}
                        className="w-full bg-brand-red hover:bg-brand-red-dark text-white py-2 text-base font-semibold transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                        size="lg"
                      >
                        {isSubmitting ? (
                          <div className="flex items-center justify-center">
                            <Loader2 className="animate-spin h-4 w-4 mr-2" />
                            Sending...
                          </div>
                        ) : (
                          "Send Inquiry"
                        )}
                      </Button>
                    </form>
                  )}
                </CardContent>
              </Card>

              {/* Contact Information */}
              <div className="space-y-4">
                <div>
                  <div className="flex items-center mb-6">
                    <div className="w-10 h-10 bg-brand-red/10 rounded-full flex items-center justify-center mr-3">
                      <MapPin className="h-5 w-5 text-brand-red" />
                    </div>
                    <div>
                      <h2 className="text-xl font-bold text-gray-900">
                        Contact Information
                      </h2>
                      <p className="text-gray-600 text-sm">
                        Get in touch with our team
                      </p>
                    </div>
                  </div>
                  <div className="space-y-3">
                    <div className="flex items-start p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                      <MapPin className="h-5 w-5 text-brand-red mr-3 mt-0.5 flex-shrink-0" />
                      <div>
                        <h3 className="font-semibold text-gray-900 mb-1 text-sm">
                          Global Headquarters
                        </h3>
                        <p className="text-gray-600 text-sm">
                          123 Innovation Drive
                          <br />
                          Steel City, SC 12345
                          <br />
                          United States
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                      <Phone className="h-5 w-5 text-brand-red mr-3 mt-0.5 flex-shrink-0" />
                      <div>
                        <h3 className="font-semibold text-gray-900 mb-1 text-sm">
                          Phone
                        </h3>
                        <p className="text-gray-600 text-sm">
                          +****************
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                      <Mail className="h-5 w-5 text-brand-red mr-3 mt-0.5 flex-shrink-0" />
                      <div>
                        <h3 className="font-semibold text-gray-900 mb-1 text-sm">
                          Email
                        </h3>
                        <p className="text-gray-600 text-sm">
                          <EMAIL>
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                      <Clock className="h-5 w-5 text-brand-red mr-3 mt-0.5 flex-shrink-0" />
                      <div>
                        <h3 className="font-semibold text-gray-900 mb-1 text-sm">
                          Business Hours
                        </h3>
                        <p className="text-gray-600 text-sm">
                          Monday - Friday: 8:00 AM - 6:00 PM
                          <br />
                          Saturday: 9:00 AM - 2:00 PM
                          <br />
                          Sunday: Closed
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                      <Globe className="h-5 w-5 text-brand-red mr-3 mt-0.5 flex-shrink-0" />
                      <div>
                        <h3 className="font-semibold text-gray-900 mb-1 text-sm">
                          Service Areas
                        </h3>
                        <p className="text-gray-600 text-sm">
                          United States, Canada, Australia
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <Card className="border-brand-red/20 bg-gradient-to-br from-brand-red/5 to-transparent hover:shadow-lg transition-all duration-300">
                  <CardContent className="p-4">
                    <div className="flex items-center mb-3">
                      <div className="w-8 h-8 bg-brand-red/10 rounded-full flex items-center justify-center mr-2">
                        <Phone className="h-4 w-4 text-brand-red" />
                      </div>
                      <h3 className="font-bold text-gray-900 text-sm">
                        Need Immediate Assistance?
                      </h3>
                    </div>
                    <p className="text-gray-600 mb-3 text-sm">
                      For urgent inquiries or technical support, our team is
                      available 24/7.
                    </p>
                    <Button
                      className="bg-brand-red hover:bg-brand-red-dark text-white w-full text-sm"
                      size="sm"
                    >
                      Emergency Support
                    </Button>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </section>
      </div>
      <Footer />
    </>
  );
};

export default Contact;
