import { useState, useEffect, useRef } from "react";
import Navigation from "@/components/Navigation";

// Configuration for iframe sources
const IFRAME_CONFIG = {
  production: "https://designmycarport.firebaseapp.com/",
  development: "http://localhost:8081/",
  fallback: "https://designmycarport.web.app/"
};

// Determine which URL to use based on environment
const getIframeUrl = () => {
  const isDevelopment = import.meta.env.DEV || window.location.hostname === 'localhost';
  return isDevelopment ? IFRAME_CONFIG.development : IFRAME_CONFIG.production;
};

const CarportDesigner = () => {
  const [iframeUrl, setIframeUrl] = useState(getIframeUrl());
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const maxRetries = 2;

  // Handle iframe load success
  const handleIframeLoad = () => {
    setIsLoading(false);
    setHasError(false);
    console.log('Carport Designer loaded successfully');
  };

  // Handle iframe load error
  const handleIframeError = () => {
    console.error('Failed to load Carport Designer from:', iframeUrl);
    setIsLoading(false);

    if (retryCount < maxRetries) {
      // Try fallback URL or retry
      if (iframeUrl === IFRAME_CONFIG.production && retryCount === 0) {
        console.log('Trying fallback URL...');
        setIframeUrl(IFRAME_CONFIG.fallback);
        setRetryCount(1);
        setIsLoading(true);
      } else if (retryCount === 1) {
        console.log('Trying development URL as last resort...');
        setIframeUrl(IFRAME_CONFIG.development);
        setRetryCount(2);
        setIsLoading(true);
      } else {
        setHasError(true);
      }
    } else {
      setHasError(true);
    }
  };

  // Retry loading
  const handleRetry = () => {
    setRetryCount(0);
    setHasError(false);
    setIsLoading(true);
    setIframeUrl(getIframeUrl());
  };

  // Security: Add message listener for iframe communication
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      // Verify origin for security
      const allowedOrigins = [
        'https://designmycarport.firebaseapp.com',
        'https://designmycarport.web.app',
        'http://localhost:8081'
      ];

      if (!allowedOrigins.includes(event.origin)) {
        console.warn('Received message from unauthorized origin:', event.origin);
        return;
      }

      // Handle messages from iframe
      if (event.data.type === 'CARPORT_DESIGNER_READY') {
        console.log('Carport Designer is ready');
        setIsLoading(false);
      } else if (event.data.type === 'CARPORT_DESIGNER_ERROR') {
        console.error('Carport Designer error:', event.data.error);
        handleIframeError();
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, []);

  return (
    <div className="min-h-screen bg-white">
      <Navigation />
      {/* 3D Designer Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
            <div className="h-[calc(100vh-24rem)] min-h-[600px] relative">
              {/* Loading State */}
              {isLoading && (
                <div className="absolute inset-0 flex items-center justify-center bg-gray-50 z-10">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                    <p className="text-gray-600">Loading 3D Carport Designer...</p>
                  </div>
                </div>
              )}

              {/* Error State */}
              {hasError && (
                <div className="absolute inset-0 flex items-center justify-center bg-gray-50 z-10">
                  <div className="text-center max-w-md mx-auto p-6">
                    <div className="text-red-500 mb-4">
                      <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      Unable to Load Designer
                    </h3>
                    <p className="text-gray-600 mb-4">
                      The 3D Carport Designer is temporarily unavailable. This might be due to network issues or maintenance.
                    </p>
                    <button
                      onClick={handleRetry}
                      className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
                    >
                      Try Again
                    </button>
                  </div>
                </div>
              )}

              {/* Iframe */}
              <iframe
                ref={iframeRef}
                src={iframeUrl}
                title="3D Carport Designer"
                className="w-full h-full border-0"
                allowFullScreen
                loading="lazy"
                onLoad={handleIframeLoad}
                onError={handleIframeError}
                sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-presentation"
                referrerPolicy="strict-origin-when-cross-origin"
                style={{ display: hasError ? 'none' : 'block' }}
              />
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default CarportDesigner;
