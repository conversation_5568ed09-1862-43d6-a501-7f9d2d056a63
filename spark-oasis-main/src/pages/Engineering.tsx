import { <PERSON>, useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import Navigation from "@/components/Navigation";
import Footer from "@/components/Footer";
import {
  Calculator,
  TrendingUp,
  Globe,
  FileText,
  DollarSign,
  Zap,
  CheckCircle,
} from "lucide-react";

const Engineering = () => {
  const navigate = useNavigate();

  return (
    <>
      <div className="min-h-screen bg-white">
        <Navigation />

        {/* Hero Section */}
        <section className="py-20 bg-gradient-to-br from-gray-50 to-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <Badge className="mb-6 bg-brand-red/10 text-brand-red border-brand-red/20">
                For Engineers
              </Badge>
              <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                Grow Your Business With
                <span className="text-brand-red"> COLD FORMED</span>
              </h1>
              <p className="text-xl text-gray-600 max-w-4xl mx-auto mb-8">
                Imagine having a partner who sends your engineering team a
                steady stream of work. A partner who can save you money on
                marketing. A partner who makes stamping and detailing
                construction documents easy.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button
                  className="bg-brand-red hover:bg-brand-red-dark text-white"
                  size="lg"
                  onClick={() => navigate("/contact")}
                >
                  Partner With Us
                </Button>
                <Button
                  variant="outline"
                  size="lg"
                  onClick={() =>
                    window.open(
                      "https://buildsteel.org/category/why-steel/",
                      "_blank",
                    )
                  }
                >
                  Learn More
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* What We Give You Section */}
        <section className="py-20 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                Engineering Partnership Benefits
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                The 3D Designer software engineers every steel building to local
                codes. We make steel buildings easy to design AND easy to
                engineer.
              </p>
            </div>

            <div className="grid lg:grid-cols-2 gap-12 items-start">
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-8">
                  We Give You:
                </h3>
                <div className="grid gap-6">
                  {[
                    {
                      icon: <TrendingUp className="h-4 w-4 text-brand-red" />,
                      title: "Steady, Consistent Work",
                    },
                    {
                      icon: <Zap className="h-4 w-4 text-brand-red" />,
                      title: "Streamlined Process",
                    },
                    {
                      icon: <Globe className="h-4 w-4 text-brand-red" />,
                      title: "Accurate Code Compliance",
                    },
                    {
                      icon: <FileText className="h-4 w-4 text-brand-red" />,
                      title: "Easy Plan Export",
                    },
                    {
                      icon: <DollarSign className="h-4 w-4 text-brand-red" />,
                      title: "Additional Income",
                    },
                    {
                      icon: <CheckCircle className="h-4 w-4 text-brand-red" />,
                      title: "Easy Market Entry",
                    },
                  ].map((feature, index) => (
                    <Card
                      key={index}
                      className="flex items-center shadow-sm hover:shadow-md transition-all duration-300 border-none h-12"
                    >
                      <CardContent className="flex items-center w-full px-3 py-0">
                        <div className="flex items-center space-x-3">
                          <div className="flex-shrink-0 p-1 bg-brand-red/10 rounded">
                            {feature.icon}
                          </div>
                          <div>
                            <h4 className="font-medium text-sm text-gray-900">
                              {feature.title}
                            </h4>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
              <div className="relative">
                <div className="bg-gradient-to-br from-brand-red to-brand-red-dark rounded-2xl p-8 text-white">
                  <Calculator className="h-16 w-16 mb-6" />
                  <h4 className="text-2xl font-bold mb-4">
                    Engineering Benefits
                  </h4>
                  <div className="space-y-4">
                    <div className="flex justify-between">
                      <span>Work Volume Increase</span>
                      <span className="font-bold">+200%</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Marketing Cost Savings</span>
                      <span className="font-bold">-80%</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Project Turnaround</span>
                      <span className="font-bold">2-3 Days</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Revenue Growth</span>
                      <span className="font-bold">+150%</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gray-900 text-white relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-brand-red/20 to-transparent"></div>
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative text-center">
            <h2 className="text-3xl lg:text-4xl font-bold mb-4">
              Ready to Grow Your Engineering Business?
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-8">
              Join engineering firms worldwide who have expanded their business
              with our COLD FORMED steel building partnership program.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                className="bg-brand-red hover:bg-brand-red-dark text-white"
                size="lg"
                onClick={() => navigate("/contact")}
              >
                Become a Partner
              </Button>
              <Link to="/contact"></Link>
            </div>
          </div>
        </section>
      </div>
      <Footer />
    </>
  );
};

export default Engineering;
