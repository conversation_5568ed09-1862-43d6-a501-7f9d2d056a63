import * as React from "react";
import { Link, useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import Navigation from "@/components/Navigation";
import Footer from "@/components/Footer";
import {
  Building,
  Clock,
  DollarSign,
  CheckCircle,
  FileText,
  Truck,
  Wrench,
  Shield,
} from "lucide-react";

const BuildingPage = () => {
  const navigate = useNavigate();
  return (
    <>
      <div className="min-h-screen bg-white">
        <Navigation />

        {/* Hero Section */}
        <section className="py-20 bg-gradient-to-br from-gray-50 to-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <Badge className="mb-6 bg-brand-red/10 text-brand-red border-brand-red/20">
                For Builders & Erectors
              </Badge>
              <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                Build COLD FORMED Steel Buildings
                <span className="text-brand-red"> Faster & Easier</span>
              </h1>
              <p className="text-xl text-gray-600 max-w-4xl mx-auto mb-8">
                Everything you need to successfully build COLD FORMED steel
                buildings. From delivery to documentation, we make building
                easier.
              </p>
            </div>
          </div>
        </section>

        {/* 3D Design Tool Section */}
        <section className="py-20 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <Badge className="mb-6 bg-brand-red/10 text-brand-red border-brand-red/20">
                Professional Builder Tools
              </Badge>
              <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
                Web-Based 3D Design Tool for Builders
              </h2>
              <p className="text-xl text-gray-600 max-w-4xl mx-auto">
                A web-based 3D design tool that helps builders attract qualified
                leads, reduce time wasted on non-serious enquiries, and build
                trust faster with customers—right from their website or in the
                field. Designed for rural building businesses.
              </p>
            </div>

            {/* Customer Pain Points */}
            <div className="mb-20">
              <h3 className="text-2xl font-bold text-gray-900 mb-8 text-center">
                Customer Pain Points (Before Using the Tool)
              </h3>
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                {[
                  {
                    painPoint: "Repetitive calls with vague customer ideas",
                    consequence: "Wasted time, poor quality leads",
                  },
                  {
                    painPoint: "Customers unsure of what they want",
                    consequence: "Slower decisions, lost confidence",
                  },
                  {
                    painPoint: "High admin load quoting every minor enquiry",
                    consequence: "Burnout, inefficient sales process",
                  },
                  {
                    painPoint: "Visiting site with no design or info",
                    consequence: "Poor first impression, lower conversion",
                  },
                  {
                    painPoint:
                      "Customers expect visuals, but builder can't produce them quickly",
                    consequence: "Lost trust, lost business",
                  },
                  {
                    painPoint: "Website is static and not generating leads",
                    consequence: "Missed opportunities, no automation",
                  },
                ].map((item, index) => (
                  <div
                    key={index}
                    className="bg-red-50 border border-red-200 rounded-lg p-6"
                  >
                    <div className="flex items-start mb-3">
                      <span className="text-red-500 mr-2 text-lg">❌</span>
                      <h4 className="font-semibold text-gray-900 text-sm leading-tight">
                        {item.painPoint}
                      </h4>
                    </div>
                    <div className="ml-6">
                      <p className="text-red-700 text-sm font-medium">
                        Builder Consequence:
                      </p>
                      <p className="text-red-600 text-sm">{item.consequence}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Strategic Benefits */}
            <div className="mb-20">
              <h3 className="text-2xl font-bold text-gray-900 mb-8 text-center">
                Strategic Benefits for Builders
              </h3>
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                {[
                  {
                    outcome: "Fewer Wasted Hours",
                    impact:
                      "Spend time only with serious, design-engaged customers",
                    emoji: "🎯",
                  },
                  {
                    outcome: "More Sales Conversions",
                    impact:
                      "Clients trust builders more when they can see what they're getting",
                    emoji: "📈",
                  },
                  {
                    outcome: "Professional Presentation",
                    impact:
                      "Competes visually with bigger players; improves image of small builder",
                    emoji: "🧰",
                  },
                  {
                    outcome: "Digital Presence Upgrade",
                    impact:
                      "Turns a static website into an interactive lead-generating machine",
                    emoji: "🌐",
                  },
                  {
                    outcome: "Easy Admin & Follow-Up",
                    impact:
                      "Submitted designs include contact info and ready-to-quote visuals",
                    emoji: "📧",
                  },
                  {
                    outcome: "Improved Customer Engagement",
                    impact:
                      "Customers better understand what's possible—and get more involved",
                    emoji: "💬",
                  },
                ].map((benefit, index) => (
                  <div
                    key={index}
                    className="bg-green-50 border border-green-200 rounded-lg p-6"
                  >
                    <div className="flex items-start mb-3">
                      <span className="text-2xl mr-3">{benefit.emoji}</span>
                      <h4 className="font-bold text-gray-900 text-base leading-tight">
                        {benefit.outcome}
                      </h4>
                    </div>
                    <p className="text-green-700 text-sm leading-relaxed ml-11">
                      {benefit.impact}
                    </p>
                  </div>
                ))}
              </div>
            </div>

            {/* Call to Action */}
            <div className="text-center bg-gradient-to-r from-brand-red to-brand-red-dark rounded-2xl p-8 text-white">
              <h3 className="text-2xl font-bold mb-4">
                Ready to Transform Your Building Business?
              </h3>
              <p className="text-xl mb-6 opacity-90">
                Join builders worldwide who are using our 3D design tool to
                attract better leads and close more sales.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link to="/designer">
                  <Button
                    className="bg-white text-brand-red hover:bg-gray-100"
                    size="lg"
                  >
                    Try 3D Designer
                  </Button>
                </Link>
                <Link to="/contact">
                  <Button
                    variant="outline"
                    className="border-white text-black hover:bg-white hover:text-brand-red"
                    size="lg"
                  >
                    Contact Sales
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </section>

        {/* We Got You - Easy Setup Section */}
        <section className="py-20 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <Badge className="mb-6 bg-green-100 text-green-800 border-green-200">
                We Got You Covered
              </Badge>
              <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
                Easy Setup - Just Copy & Paste
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                No technical skills required. Get your 3D designer running on
                your website in under 5 minutes with our simple iframe embed
                code.
              </p>
            </div>

            <div className="grid lg:grid-cols-2 gap-12 items-center mb-16">
              {/* Left side - Steps */}
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-8">
                  3 Simple Steps to Get Started
                </h3>
                <div className="space-y-6">
                  {[
                    {
                      step: "1",
                      title: "Get Your Embed Code",
                      description:
                        "We provide you with a simple iframe code customized for your business.",
                    },
                    {
                      step: "2",
                      title: "Copy & Paste",
                      description:
                        "Just copy the code and paste it anywhere on your website where you want the designer to appear.",
                    },
                    {
                      step: "3",
                      title: "Start Converting Leads",
                      description:
                        "Your customers can now design carports directly on your website and submit qualified leads.",
                    },
                  ].map((item, index) => (
                    <div key={index} className="flex items-start">
                      <div className="flex-shrink-0 w-10 h-10 bg-brand-red text-white rounded-full flex items-center justify-center font-bold mr-4">
                        {item.step}
                      </div>
                      <div>
                        <h4 className="font-bold text-gray-900 mb-2">
                          {item.title}
                        </h4>
                        <p className="text-gray-600">{item.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Right side - Code Example */}
              <div>
                <div className="bg-gray-900 rounded-lg p-4 sm:p-6 text-green-400 font-mono text-xs sm:text-sm overflow-x-auto">
                  <div className="mb-4 text-gray-400">
                    <span className="text-orange-400">//</span> Your custom
                    embed code:
                  </div>
                  <div className="bg-gray-800 p-3 sm:p-4 rounded border-l-4 border-brand-red">
                    <div className="text-blue-400 break-all">
                      {"<"}
                      <span className="text-purple-400">iframe</span>
                      {">"}
                    </div>
                    <div className="ml-2 sm:ml-4 text-yellow-300 break-all">
                      <span className="text-blue-400">src</span>=
                      <span className="text-green-400">
                        "https://designer.xyzcompany.com/*****/embed"
                      </span>
                    </div>
                    <div className="ml-2 sm:ml-4 text-yellow-300 break-all">
                      <span className="text-blue-400">width</span>=
                      <span className="text-green-400">"100%"</span>
                    </div>
                    <div className="ml-2 sm:ml-4 text-yellow-300 break-all">
                      <span className="text-blue-400">height</span>=
                      <span className="text-green-400">"600"</span>
                    </div>
                    <div className="ml-2 sm:ml-4 text-yellow-300 break-all">
                      <span className="text-blue-400">frameborder</span>=
                      <span className="text-green-400">"0"</span>
                    </div>
                    <div className="ml-2 sm:ml-4 text-yellow-300 break-all">
                      <span className="text-blue-400">title</span>=
                      <span className="text-green-400">
                        "3D Carport Designer"
                      </span>
                    </div>
                    <div className="text-blue-400 break-all">
                      {"<"}/<span className="text-purple-400">iframe</span>
                      {">"}
                    </div>
                  </div>
                  <div className="mt-4 text-gray-400">
                    <span className="text-orange-400">//</span> That's it! Ready
                    to go.
                  </div>
                </div>
              </div>
            </div>

            {/* Features Grid */}
            <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6 mb-16">
              {[
                {
                  icon: "⚡",
                  title: "5-Minute Setup",
                  description: "Get running in minutes, not hours",
                },
                {
                  icon: "📱",
                  title: "Mobile Responsive",
                  description: "Works perfectly on all devices",
                },
                {
                  icon: "",
                  title: "Multilingual UI",
                  description:
                    "Language toggle to support culturally diverse areas",
                },
                {
                  icon: "💰",
                  title: "No Upfront Fee",
                  description: "We get paid when you get paid",
                },
                {
                  icon: "🎨",
                  title: "Custom Branding",
                  description: "Match your website's look and feel",
                },
                {
                  icon: "🔧",
                  title: "No Maintenance",
                  description: "We handle updates and hosting",
                },
              ].map((feature, index) => (
                <div
                  key={index}
                  className="text-center p-6 bg-white rounded-lg shadow-sm"
                >
                  <div className="text-3xl mb-3">{feature.icon}</div>
                  <h4 className="font-bold text-gray-900 mb-2">
                    {feature.title}
                  </h4>
                  <p className="text-gray-600 text-sm">{feature.description}</p>
                </div>
              ))}
            </div>

            {/* Final CTA */}
            <div className="text-center mt-12">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                Ready to Add 3D Designer to Your Website?
              </h3>
              <p className="text-gray-600 mb-6">
                Get your custom embed code and start converting visitors into
                qualified leads today.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button
                  className="bg-brand-red hover:bg-brand-red-dark text-white"
                  size="lg"
                  onClick={() => navigate("/contact")}
                >
                  Get Embed Code
                </Button>
                <Link to="/designer">
                  <Button variant="outline" size="lg">
                    Try Designer First
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </section>

        {/* Building Benefits Section */}
        <section className="py-20 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                Everything You Need to Build Successfully
              </h2>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  icon: <Truck className="h-12 w-12 text-brand-red" />,
                  title: "Fast Delivery",
                  description: "On-site delivery in as little as 2-3 weeks.",
                },
                {
                  icon: <Wrench className="h-12 w-12 text-brand-red" />,
                  title: "Easier Material Handling",
                  description:
                    "Less heavy equipment required for lighter components.",
                },
                {
                  icon: <DollarSign className="h-12 w-12 text-brand-red" />,
                  title: "Foundation Savings",
                  description: "Save 25% on foundation costs.",
                },
                {
                  icon: <FileText className="h-12 w-12 text-brand-red" />,
                  title: "Stamped Drawings",
                  description:
                    "Engineered to local codes with stamped drawings.",
                },
                {
                  icon: <CheckCircle className="h-12 w-12 text-brand-red" />,
                  title: "No Welding Required",
                  description:
                    "Bolt and screw connections with no welding needed.",
                },
                {
                  icon: <Shield className="h-12 w-12 text-brand-red" />,
                  title: "Complete Documentation",
                  description:
                    "Install manuals, training videos, and easy-to-follow docs.",
                },
              ].map((benefit, index) => (
                <div key={index} className="text-center p-6">
                  <div className="mb-6 flex justify-center">{benefit.icon}</div>
                  <h3 className="text-xl font-bold text-gray-900 mb-4">
                    {benefit.title}
                  </h3>
                  <p className="text-gray-600">{benefit.description}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gray-900 text-white relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-brand-red/20 to-transparent"></div>
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative text-center">
            <h2 className="text-3xl lg:text-4xl font-bold mb-4">
              Ready to Start Building?
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-8">
              Join the growing COLD FORMED building market with confidence.
            </p>
            <Link to="/contact">
              <Button
                className="bg-brand-red hover:bg-brand-red-dark text-white"
                size="lg"
              >
                Get Started
              </Button>
            </Link>
          </div>
        </section>
      </div>
      <Footer />
    </>
  );
};

export default BuildingPage;
