import { Link, useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import Navigation from "@/components/Navigation";
import Footer from "@/components/Footer";
import { Card, CardContent } from "@/components/ui/card";
import {
  Globe,
  ShoppingCart,
  DollarSign,
  Clock,
  Shield,
  Leaf,
  CheckCircle,
  Building,
  Calculator,
  Wrench,
  Zap,
} from "lucide-react";

const Buying = () => {
  const navigate = useNavigate();
  return (
    <>
      <div className="min-h-screen bg-white">
        <Navigation />

        {/* Hero Section */}
        <section className="py-20 bg-gradient-to-br from-gray-50 to-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <Badge className="mb-6 bg-brand-red/10 text-brand-red border-brand-red/20">
                For Buyers & Consumers
              </Badge>
              <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                Your Steel Building
                <span className="text-brand-red"> Solution</span>
              </h1>
              <p className="text-xl text-gray-600 max-w-4xl mx-auto mb-8">
                We do not sell, design, or erect buildings. We created a
                building design software called 3D Designer and partnered with
                manufacturers across the globe to make steel buildings available
                to buyers like you.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button
                  className="bg-brand-red hover:bg-brand-red-dark text-white"
                  size="lg"
                  onClick={() =>
                    window.open("http://localhost:8084/", "_blank")
                  }
                >
                  Try Free 3D Designer
                </Button>
                <Button
                  variant="outline"
                  size="lg"
                  onClick={() =>
                    window.open(
                      "https://buildsteel.org/category/why-steel/",
                      "_blank",
                    )
                  }
                >
                  Learn More
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* COLD FORMED Benefits Section */}
        <section className="py-20 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                What Are COLD FORMED Steel Buildings?
              </h2>
              <p className="text-xl text-gray-600 max-w-4xl mx-auto">
                COLD FORMED buildings are pre-engineered metal buildings (PEMB)
                that are bolted and screwed together with no welding. They are
                versatile enough to include lean-tos and mezzanines yet simple
                enough to provide significant savings of both{" "}
                <strong>TIME</strong> and <strong>MONEY</strong>.
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  icon: <Calculator className="h-12 w-12 text-brand-red" />,
                  title: "Lower Foundation Costs",
                  description:
                    "Spend less on foundation with lighter weight components.",
                },
                {
                  icon: <Wrench className="h-12 w-12 text-brand-red" />,
                  title: "Less Equipment Needed",
                  description:
                    "Spend less on construction equipment and heavy machinery.",
                },
                {
                  icon: <Zap className="h-12 w-12 text-brand-red" />,
                  title: "Time & Money Savings",
                  description:
                    "Save time and money on maintenance over the long term.",
                },
                {
                  icon: <Shield className="h-12 w-12 text-brand-red" />,
                  title: "Superior Protection",
                  description:
                    "Greater protection from fire, termites, and mold.",
                },
                {
                  icon: <Globe className="h-12 w-12 text-brand-red" />,
                  title: "Engineered to Code",
                  description:
                    "Engineered to local wind and snow loads in Australia, US and Canada.",
                },
                {
                  icon: <Building className="h-12 w-12 text-brand-red" />,
                  title: "No Welding Required",
                  description:
                    "Bolted and screwed together with no welding needed.",
                },
              ].map((benefit, index) => (
                <Card
                  key={index}
                  className="border-0 shadow-sm hover:shadow-md transition-shadow"
                >
                  <CardContent className="p-8 text-center">
                    <div className="mb-6 flex justify-center">
                      {benefit.icon}
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 mb-4">
                      {benefit.title}
                    </h3>
                    <p className="text-gray-600">{benefit.description}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>
        {/* Why Choose COLD FORMED Section */}
        <section className="py-20 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                Why Should You Choose COLD FORMED?
              </h2>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
              {[
                {
                  icon: <DollarSign className="h-12 w-12 text-brand-red" />,
                  title: "Save Money",
                  description:
                    "Cold formed steel buildings use 25% less concrete than red iron. Lighter components reduce heavy equipment needs.",
                },
                {
                  icon: <Clock className="h-12 w-12 text-brand-red" />,
                  title: "Save Time",
                  description:
                    "Materials delivered in as little as 2-3 weeks. Easy construction documents for quick assembly.",
                },
                {
                  icon: <Shield className="h-12 w-12 text-brand-red" />,
                  title: "Save Worry",
                  description:
                    "Engineered to local codes. More resistant to fire, water, mold, and termites than timber.",
                },
                {
                  icon: <Leaf className="h-12 w-12 text-brand-red" />,
                  title: "Save the Planet",
                  description:
                    "Made with 100% recyclable materials. Easily engineered to hold solar panels.",
                },
              ].map((benefit, index) => (
                <div key={index} className="text-center p-6">
                  <div className="mb-6 flex justify-center">{benefit.icon}</div>
                  <h3 className="text-xl font-bold text-gray-900 mb-4">
                    {benefit.title}
                  </h3>
                  <p className="text-gray-600">{benefit.description}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gray-900 text-white relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-brand-red/20 to-transparent"></div>
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative text-center">
            <h2 className="text-3xl lg:text-4xl font-bold mb-4">
              Ready to Design Your Building?
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-8">
              Use our free 3D Designer to create your perfect COLD FORMED steel
              building.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                className="bg-brand-red hover:bg-brand-red-dark text-white"
                size="lg"
              >
                Try 3D Designer Free
              </Button>
              <Link to="/contact">
                <Button
                  variant="outline"
                  className="border-white text-red-600 hover:bg-white hover:text-gray-900"
                  size="lg"
                >
                  Find a Dealer
                </Button>
              </Link>
            </div>
          </div>
        </section>
      </div>
      <Footer />
    </>
  );
};

export default Buying;
