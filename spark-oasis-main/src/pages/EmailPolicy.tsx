import { Badge } from "@/components/ui/badge";
import Navigation from "@/components/Navigation";
import Footer from "@/components/Footer";
import {
  FileText,
  Calendar,
  Shield,
  AlertTriangle,
  CheckCircle,
  Mail,
  Settings,
  Eye,
  Lock,
  XCircle,
  Bell,
  Users,
  Globe,
  Gavel,
} from "lucide-react";

const EmailPolicy = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      <Navigation />

      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 relative overflow-hidden">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <Badge className="mb-6 bg-brand-red/20 text-brand-red border-brand-red/30 backdrop-blur-sm">
              Email Policy
            </Badge>
            <h1 className="text-4xl lg:text-6xl font-bold text-white mb-6 leading-tight">
              Email Consent &
              <span className="text-brand-red block"> Marketing Policy</span>
            </h1>
            <p className="text-xl text-gray-300 max-w-4xl mx-auto mb-8 leading-relaxed">
              Learn about our email marketing practices, your consent rights,
              and how to manage your email preferences.
            </p>
            <div className="flex items-center justify-center gap-8 text-sm text-gray-400">
              <div className="flex items-center gap-2 bg-white/10 backdrop-blur-sm px-4 py-2 rounded-full">
                <Calendar className="h-4 w-4" />
                <span>Last updated: March 15, 2025</span>
              </div>
              <div className="flex items-center gap-2 bg-white/10 backdrop-blur-sm px-4 py-2 rounded-full">
                <FileText className="h-4 w-4" />
                <span>Version 1.0</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Email Policy Content */}
      <section className="py-20 bg-white">
        <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="space-y-16">
            {/* Consent to Receive Communications */}
            <div className="bg-gradient-to-br from-white to-gray-50/50 rounded-2xl p-8 shadow-sm border border-gray-100">
              <div className="flex items-center gap-4 mb-6">
                <div className="w-12 h-12 bg-gradient-to-br from-brand-red/20 to-brand-red/10 rounded-xl flex items-center justify-center">
                  <CheckCircle className="h-6 w-6 text-brand-red" />
                </div>
                <h2 className="text-3xl font-bold text-gray-900">
                  Consent to Receive Communications
                </h2>
              </div>
              <div className="space-y-6">
                <p className="text-gray-700 leading-relaxed text-lg">
                  By submitting your contact information through our platform or
                  by interacting with our services (including the "Get a Quote"
                  form or subscribing to our updates), you consent to receive:
                </p>

                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-brand-red rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-gray-700">
                      Transactional emails relating to your use of the platform
                      or lead submissions
                    </p>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-brand-red rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-gray-700">
                      Important service notifications, such as updates, outages,
                      or changes to our policies
                    </p>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-brand-red rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-gray-700">
                      Occasional promotional or informational content, such as
                      new features, case studies, or service updates
                    </p>
                  </div>
                </div>

                <div className="bg-blue-50 border border-blue-200 rounded-xl p-6">
                  <p className="text-blue-800">
                    We will always seek your express consent for marketing
                    unless an existing business relationship or implied consent
                    applies (e.g., as defined under the Australian Spam Act
                    2003).
                  </p>
                </div>
              </div>
            </div>

            {/* Types of Emails We Send */}
            <div className="bg-gradient-to-br from-white to-gray-50/50 rounded-2xl p-8 shadow-sm border border-gray-100">
              <h2 className="text-3xl font-bold text-gray-900 mb-6">
                Types of Emails We Send
              </h2>
              <div className="bg-white rounded-xl overflow-hidden border border-gray-200 shadow-sm">
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900 border-b border-gray-200">
                          Type
                        </th>
                        <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900 border-b border-gray-200">
                          Description
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      <tr>
                        <td className="px-6 py-4 text-sm font-medium text-gray-900">
                          Transactional
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-700">
                          Account access, form submission confirmation, service
                          issues
                        </td>
                      </tr>
                      <tr>
                        <td className="px-6 py-4 text-sm font-medium text-gray-900">
                          Service Notices
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-700">
                          Policy updates, technical changes, or planned
                          maintenance
                        </td>
                      </tr>
                      <tr>
                        <td className="px-6 py-4 text-sm font-medium text-gray-900">
                          Marketing & Updates
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-700">
                          New features, product enhancements, promotions,
                          insights
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
              <div className="mt-4 bg-yellow-50 border border-yellow-200 rounded-xl p-4">
                <p className="text-yellow-800 text-sm">
                  <strong>Note:</strong> You may not opt out of essential
                  transactional or system-related emails.
                </p>
              </div>
            </div>

            {/* Unsubscribing from Marketing Emails */}
            <div className="bg-gradient-to-br from-white to-gray-50/50 rounded-2xl p-8 shadow-sm border border-gray-100">
              <div className="flex items-center gap-4 mb-6">
                <div className="w-12 h-12 bg-gradient-to-br from-brand-red/20 to-brand-red/10 rounded-xl flex items-center justify-center">
                  <XCircle className="h-6 w-6 text-brand-red" />
                </div>
                <h2 className="text-3xl font-bold text-gray-900">
                  Unsubscribing from Marketing Emails
                </h2>
              </div>

              <div className="space-y-6">
                <p className="text-gray-700 leading-relaxed">
                  You may unsubscribe from marketing emails at any time by:
                </p>

                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <div className="w-6 h-6 bg-brand-red/10 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                      <span className="text-brand-red font-semibold text-sm">
                        1
                      </span>
                    </div>
                    <div>
                      <p className="text-gray-700">
                        Clicking the "Unsubscribe" link at the bottom of any
                        promotional email
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-6 h-6 bg-brand-red/10 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                      <span className="text-brand-red font-semibold text-sm">
                        2
                      </span>
                    </div>
                    <div>
                      <p className="text-gray-700">
                        Emailing us <NAME_EMAIL> with
                        the subject line "Unsubscribe"
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-6 h-6 bg-brand-red/10 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                      <span className="text-brand-red font-semibold text-sm">
                        3
                      </span>
                    </div>
                    <div>
                      <p className="text-gray-700">
                        Updating your communication preferences, if such a
                        feature is available on your account portal
                      </p>
                    </div>
                  </div>
                </div>

                <div className="bg-green-50 border border-green-200 rounded-xl p-6">
                  <h3 className="font-semibold text-green-900 mb-3 flex items-center gap-2">
                    <CheckCircle className="h-5 w-5" />
                    Processing Time
                  </h3>
                  <p className="text-green-800">
                    Your unsubscribe request will be processed within 5 business
                    days.
                  </p>
                </div>
              </div>
            </div>

            {/* Data Handling After Unsubscribe */}
            <div className="bg-gradient-to-br from-white to-gray-50/50 rounded-2xl p-8 shadow-sm border border-gray-100">
              <div className="flex items-center gap-4 mb-6">
                <div className="w-12 h-12 bg-gradient-to-br from-brand-red/20 to-brand-red/10 rounded-xl flex items-center justify-center">
                  <Shield className="h-6 w-6 text-brand-red" />
                </div>
                <h2 className="text-3xl font-bold text-gray-900">
                  Data Handling After Unsubscribe
                </h2>
              </div>

              <div className="space-y-4">
                <p className="text-gray-700 leading-relaxed">
                  When you unsubscribe from marketing communications:
                </p>

                <div className="space-y-3">
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-brand-red rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-gray-700">
                      You will no longer receive promotional or general updates
                    </p>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-brand-red rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-gray-700">
                      You may still receive transactional or service-related
                      emails where necessary
                    </p>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-brand-red rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-gray-700">
                      Your contact details will remain in our system for audit,
                      compliance, or service continuity purposes unless you
                      request full deletion under applicable privacy laws
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Compliance Statement */}
            <div className="bg-gradient-to-br from-white to-gray-50/50 rounded-2xl p-8 shadow-sm border border-gray-100">
              <div className="flex items-center gap-4 mb-6">
                <div className="w-12 h-12 bg-gradient-to-br from-brand-red/20 to-brand-red/10 rounded-xl flex items-center justify-center">
                  <Gavel className="h-6 w-6 text-brand-red" />
                </div>
                <h2 className="text-3xl font-bold text-gray-900">
                  Compliance Statement
                </h2>
              </div>

              <div className="space-y-6">
                <p className="text-gray-700 leading-relaxed">We comply with:</p>

                <div className="space-y-3">
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-brand-red rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-gray-700">
                      The Spam Act 2003 (Cth) (Australia)
                    </p>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-brand-red rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-gray-700">
                      Australian Privacy Principles (APPs) under the Privacy Act
                      1988
                    </p>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-brand-red rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-gray-700">
                      Applicable international privacy standards including GDPR
                      (where applicable)
                    </p>
                  </div>
                </div>

                <div className="bg-blue-50 border border-blue-200 rounded-xl p-6">
                  <p className="text-blue-800 font-medium">
                    We never sell, rent, or share your email address with
                    unrelated third parties for marketing purposes.
                  </p>
                </div>
              </div>
            </div>

            {/* Contact Information */}
            <div className="bg-gradient-to-br from-white to-gray-50/50 rounded-2xl p-8 shadow-sm border border-gray-100">
              <h2 className="text-3xl font-bold text-gray-900 mb-6">
                Contact Us
              </h2>
              <p className="text-gray-600 mb-6 text-lg">
                If you have any questions about our email practices or need help
                managing your preferences:
              </p>
              <div className="bg-gradient-to-br from-gray-900 to-gray-800 text-white rounded-xl p-8">
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <p className="text-gray-300 mb-2">
                      <strong className="text-white">Unsubscribe Email:</strong>{" "}
                      <EMAIL>
                    </p>
                    <p className="text-gray-300 mb-2">
                      <strong className="text-white">Privacy Email:</strong>{" "}
                      <EMAIL>
                    </p>
                    <p className="text-gray-300 mb-2">
                      <strong className="text-white">Phone:</strong> +1 (555)
                      123-4567
                    </p>
                  </div>
                  <div>
                    <p className="text-gray-300 mb-2">
                      <strong className="text-white">Address:</strong> 123
                      Business Street, Suite 100, City, State 12345
                    </p>
                    <p className="text-gray-300">
                      <strong className="text-white">Response Time:</strong>{" "}
                      Within 5 business days
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default EmailPolicy;
