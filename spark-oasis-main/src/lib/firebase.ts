// Firebase configuration
import { initializeApp } from "firebase/app";
import {
  getFirestore,
  collection,
  addDoc,
  serverTimestamp,
  connectFirestoreEmulator,
} from "firebase/firestore";
import { getFunctions, httpsCallable } from "firebase/functions";

// Your Firebase configuration
// Replace with your actual Firebase config
const firebaseConfig = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: import.meta.env.VITE_FIREBASE_APP_ID,
};

// Validate required configuration
const requiredConfig = [
  "apiKey",
  "authDomain",
  "projectId",
  "storageBucket",
  "messagingSenderId",
  "appId",
];
const missingConfig = requiredConfig.filter(
  (key) => !firebaseConfig[key as keyof typeof firebaseConfig],
);

// Check if we're in development mode and allow graceful degradation
const isDevelopment = process.env.NODE_ENV === 'development' ||
                     import.meta.env?.DEV ||
                     typeof window !== 'undefined' && window.location.hostname === 'localhost';

// Check if Firebase configuration is complete
const hasCompleteConfig = missingConfig.length === 0;

if (!hasCompleteConfig) {
  if (isDevelopment) {
    console.warn(
      `Firebase configuration missing in development mode: ${missingConfig.join(", ")}. Firebase features will be disabled.`
    );
  } else {
    throw new Error(
      `Missing Firebase configuration: ${missingConfig.join(", ")}`,
    );
  }
}

// Initialize Firebase only if configuration is complete
let app: any = null;
if (hasCompleteConfig) {
  app = initializeApp(firebaseConfig);
}

// Initialize Firestore and Functions
export const db = hasCompleteConfig ? getFirestore(app) : null;
export const functions = hasCompleteConfig ? getFunctions(app) : null;

// Connect to emulator in development if Firebase is configured
if (hasCompleteConfig && isDevelopment && import.meta.env.VITE_FIREBASE_FUNCTIONS_URL && db) {
  try {
    const functionsUrl = new URL(import.meta.env.VITE_FIREBASE_FUNCTIONS_URL);
    connectFirestoreEmulator(db, functionsUrl.hostname, parseInt(functionsUrl.port) || 8080);
  } catch (error) {
    console.warn('Failed to connect to Firebase emulator:', error);
  }
}

// Contact form data interface
export interface ContactFormData {
  name: string;
  email: string;
  phone: string;
  state: string;
  country: string;
  city: string;
  message: string;
}

// Location info interface
export interface LocationInfo {
  detected: boolean;
  country: string;
  region: string;
  city: string;
  timezone: string;
  currency: string;
  userCountry: string;
  userCity: string;
}

// Complete submission data interface
export interface ContactSubmission {
  formData: ContactFormData;
  locationInfo: LocationInfo;
  userAgent: string;
  language: string;
  timestamp: any; // Firestore timestamp
  ipAddress?: string;
  referrer?: string;
  utmSource?: string;
  utmMedium?: string;
  utmCampaign?: string;
  status: "pending" | "processed" | "failed";
  emailSent: boolean;
  emailSentAt?: any;
}

// Helper function to remove undefined values from object
const removeUndefinedValues = (obj: any): any => {
  const cleaned: any = {};
  Object.keys(obj).forEach((key) => {
    if (obj[key] !== undefined && obj[key] !== null) {
      cleaned[key] = obj[key];
    }
  });
  return cleaned;
};

// Function to save contact form data to Firestore
export const saveContactFormToFirestore = async (
  formData: ContactFormData,
  locationInfo: LocationInfo,
  language: string,
): Promise<{ success: boolean; docId?: string; error?: string }> => {
  // If Firebase is not configured, return mock success
  if (!hasCompleteConfig || !db) {
    console.log('Mock contact form submission (Firebase not configured):', formData);
    return { success: true, docId: 'mock-doc-id' };
  }

  try {
    // Get UTM parameters and other analytics data
    const urlParams = new URLSearchParams(window.location.search);
    const utmSource = urlParams.get("utm_source");
    const utmMedium = urlParams.get("utm_medium");
    const utmCampaign = urlParams.get("utm_campaign");

    // Get client IP address
    let ipAddress: string | undefined;
    try {
      const response = await fetch("https://api.ipify.org?format=json");
      const data = await response.json();
      ipAddress = data.ip;
    } catch (error) {
      // IP address fetch failed, continue without it
    }

    // Prepare submission data with only defined values
    const submissionData = removeUndefinedValues({
      formData,
      locationInfo,
      userAgent: navigator.userAgent,
      language,
      ipAddress,
      referrer: document.referrer || null,
      utmSource,
      utmMedium,
      utmCampaign,
      status: "pending",
      emailSent: false,
    });

    // Add document to Firestore
    try {
      const docRef = await addDoc(collection(db, "contactSubmissions"), {
        ...submissionData,
        timestamp: serverTimestamp(),
      });

      return {
        success: true,
        docId: docRef.id,
      };
    } catch (firestoreError) {
      // Re-throw to be caught by outer try-catch
      throw firestoreError;
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
};

// Function to send email notifications via Cloud Functions
export const sendEmailNotifications = async (
  docId: string,
  formData: ContactFormData,
  locationInfo: LocationInfo,
): Promise<{ success: boolean; error?: string }> => {
  // If Firebase is not configured, return mock success
  if (!hasCompleteConfig || !functions) {
    console.log('Mock email notification (Firebase not configured):', { docId, formData, locationInfo });
    return { success: true };
  }

  try {
    // Call Cloud Function to send emails
    const sendContactEmail = httpsCallable(functions, "sendContactEmail");

    const result = await sendContactEmail({
      docId,
      formData,
      locationInfo,
    });

    return {
      success: true,
    };
  } catch (error) {
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to send email notifications",
    };
  }
};

// Combined function for easier use
export const submitContactForm = async (
  formData: ContactFormData,
  locationInfo: LocationInfo,
  language: string,
): Promise<{ success: boolean; docId?: string; error?: string }> => {
  // First save to Firestore
  const saveResult = await saveContactFormToFirestore(formData, locationInfo, language);

  if (!saveResult.success) {
    return saveResult;
  }

  // Then send email notifications
  if (saveResult.docId) {
    const emailResult = await sendEmailNotifications(saveResult.docId, formData, locationInfo);
    if (!emailResult.success) {
      console.warn('Email notification failed:', emailResult.error);
      // Don't fail the entire submission if email fails
    }
  }

  return saveResult;
};

// Function to handle complete contact form submission
export const handleContactFormSubmission = async (
  formData: ContactFormData,
  locationInfo: LocationInfo,
  language: string,
): Promise<{ success: boolean; message: string; docId?: string }> => {
  try {
    // Step 1: Save to Firestore
    const firestoreResult = await saveContactFormToFirestore(
      formData,
      locationInfo,
      language,
    );

    if (!firestoreResult.success) {
      return {
        success: false,
        message: `Failed to save form data: ${firestoreResult.error}`,
      };
    }

    // Step 2: Send email notifications
    const emailResult = await sendEmailNotifications(
      firestoreResult.docId!,
      formData,
      locationInfo,
    );

    if (!emailResult.success) {
      // Form was saved but email failed - still consider it a success
      return {
        success: true,
        message: "Your message has been saved. We will contact you soon.",
        docId: firestoreResult.docId,
      };
    }

    return {
      success: true,
      message:
        "Your message has been sent successfully! We will contact you soon.",
      docId: firestoreResult.docId,
    };
  } catch (error) {
    return {
      success: false,
      message: "An unexpected error occurred. Please try again.",
    };
  }
};

// Utility function to format contact data for email
export const formatContactDataForEmail = (
  formData: ContactFormData,
  locationInfo: LocationInfo,
) => {
  return {
    customerName: formData.name,
    customerEmail: formData.email,
    customerPhone: formData.phone || "Not provided",
    customerLocation: `${formData.city ? formData.city + ", " : ""}${formData.state ? formData.state + ", " : ""}${formData.country}`,
    detectedLocation: locationInfo.detected
      ? `${locationInfo.city ? locationInfo.city + ", " : ""}${locationInfo.country}`
      : "Not detected",
    message: formData.message,
    timezone: locationInfo.timezone,
    currency: locationInfo.currency,
    timestamp: new Date().toLocaleString(),
  };
};
