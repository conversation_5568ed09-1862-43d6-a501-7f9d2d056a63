"""
Pytest configuration and shared fixtures for all tests.

This file provides common test fixtures and configuration.
"""

import sys
import os
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

import pytest
from src.geometry.primitives import Vec2, Vec3
from src.geometry.matrix import Mat4
from src.materials.base import FrameMaterial, FrameMaterialType
from src.business.building_input import BuildingInput, BuildingType, CarportRoofType


@pytest.fixture
def vec2_test_data():
    """Provide test Vec2 instances."""
    return {
        'origin': Vec2(0, 0),
        'unit_x': Vec2(1, 0),
        'unit_y': Vec2(0, 1),
        'point': Vec2(3, 4),
        'negative': Vec2(-2, -3)
    }


@pytest.fixture
def vec3_test_data():
    """Provide test Vec3 instances."""
    return {
        'origin': Vec3(0, 0, 0),
        'unit_x': Vec3(1, 0, 0),
        'unit_y': Vec3(0, 1, 0),
        'unit_z': Vec3(0, 0, 1),
        'point': Vec3(1, 2, 3),
        'negative': Vec3(-1, -2, -3)
    }


@pytest.fixture
def test_frame_material():
    """Provide a test FrameMaterial instance."""
    return FrameMaterial(
        name="C10015",
        material_type=FrameMaterialType.C,
        width=102.0,
        height=51.0,
        thickness=1.5,
        lip=13.5,
        flipped=False,
        section=0
    )


@pytest.fixture
def test_building_input():
    """Provide a test BuildingInput instance."""
    return BuildingInput(
        building_type=BuildingType.CARPORT,
        name="Test Carport",
        roof_type=CarportRoofType.GABLE,
        span=6000,
        length=6000,
        height=2700,
        bays=2,
        wind_speed=32,
        pitch=10.0,
        slab=True,
        slab_thickness=100.0,
        soil="M"
    )


@pytest.fixture
def identity_matrix():
    """Provide an identity matrix."""
    return Mat4.identity()


@pytest.fixture
def temp_output_dir(tmp_path):
    """Provide a temporary directory for output files."""
    output_dir = tmp_path / "output"
    output_dir.mkdir()
    return output_dir


# Test markers
def pytest_configure(config):
    """Configure custom pytest markers."""
    config.addinivalue_line(
        "markers", "slow: marks tests as slow (deselect with '-m \"not slow\"')"
    )
    config.addinivalue_line(
        "markers", "integration: marks tests as integration tests"
    )
    config.addinivalue_line(
        "markers", "unit: marks tests as unit tests"
    )