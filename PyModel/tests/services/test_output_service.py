"""
Test suite for output generation service.

Tests output service functionality including file generation,
management, and the output manager.
"""

import pytest
import asyncio
import json
import shutil
from pathlib import Path
from datetime import datetime, timedelta
from unittest.mock import Mock, AsyncMock, patch, MagicMock
import tempfile
import zipfile

# Import after modifying path
import sys
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from src.services.output_service import OutputService, OutputManager
from src.bim.shed_bim import ShedBim, ShedBimPartMain
from src.output import OutputFormat, OutputResult


@pytest.fixture
def temp_dirs():
    """Create temporary directories for testing."""
    output_dir = Path(tempfile.mkdtemp())
    temp_dir = Path(tempfile.mkdtemp())
    
    yield output_dir, temp_dir
    
    # Cleanup
    shutil.rmtree(output_dir, ignore_errors=True)
    shutil.rmtree(temp_dir, ignore_errors=True)


@pytest.fixture
def output_service(temp_dirs):
    """Create output service instance."""
    output_dir, temp_dir = temp_dirs
    return OutputService(output_dir=output_dir, temp_dir=temp_dir)


@pytest.fixture
def mock_bim():
    """Create mock BIM model."""
    bim = Mock(spec=ShedBim)
    bim.main = Mock(spec=ShedBimPartMain)
    return bim


@pytest.fixture
def mock_generators():
    """Mock output generators."""
    with patch.multiple(
        'src.services.output_service',
        GLTFGenerator=Mock,
        DXFGenerator=Mock,
        IFCGenerator=Mock
    ) as mocks:
        yield mocks


class TestOutputService:
    """Test OutputService class."""
    
    def test_initialization(self, temp_dirs):
        """Test service initialization."""
        output_dir, temp_dir = temp_dirs
        service = OutputService(output_dir=output_dir, temp_dir=temp_dir)
        
        assert service.output_dir == output_dir
        assert service.temp_dir == temp_dir
        assert output_dir.exists()
        assert temp_dir.exists()
        
        # Check generators are initialized
        assert len(service.generators) == 5  # GLTF, GLB, DXF, IFC, IFC4
        assert OutputFormat.GLTF in service.generators
        assert OutputFormat.DXF in service.generators
    
    def test_initialization_default_dirs(self):
        """Test initialization with default directories."""
        service = OutputService()
        
        assert service.output_dir == Path("output")
        assert service.temp_dir == Path("temp/output")
        
        # Cleanup
        shutil.rmtree("output", ignore_errors=True)
        shutil.rmtree("temp", ignore_errors=True)
    
    @pytest.mark.asyncio
    async def test_generate_output_success(self, output_service, mock_bim):
        """Test successful output generation."""
        # Mock generator
        mock_result = OutputResult(
            success=True,
            format=OutputFormat.GLTF,
            file_path=output_service.temp_dir / "test.gltf",
            file_size=1024,
            metadata={"vertices": 100}
        )
        
        mock_generator = Mock()
        mock_generator.generate.return_value = mock_result
        output_service.generators[OutputFormat.GLTF] = mock_generator
        
        # Generate output
        result = await output_service.generate_output(
            mock_bim, "test_model", OutputFormat.GLTF
        )
        
        assert result.success is True
        assert result.format == OutputFormat.GLTF
        assert result.file_path == output_service.temp_dir / "test.gltf"
        
        # Verify generator was called
        mock_generator.generate.assert_called_once_with(
            mock_bim, "test_model", {}
        )
        
        # Verify file is tracked
        assert str(result.file_path) in output_service._temp_files
    
    @pytest.mark.asyncio
    async def test_generate_output_string_format(self, output_service, mock_bim):
        """Test generation with string format."""
        mock_result = OutputResult(success=True, format=OutputFormat.DXF)
        
        mock_generator = Mock()
        mock_generator.generate.return_value = mock_result
        output_service.generators[OutputFormat.DXF] = mock_generator
        
        # Use string format
        result = await output_service.generate_output(
            mock_bim, "test", "dxf"
        )
        
        assert result.success is True
        assert result.format == OutputFormat.DXF
    
    @pytest.mark.asyncio
    async def test_generate_output_invalid_format(self, output_service, mock_bim):
        """Test generation with invalid format."""
        result = await output_service.generate_output(
            mock_bim, "test", "invalid_format"
        )
        
        assert result.success is False
        assert result.format is None
        assert "Unsupported format: invalid_format" in result.errors
    
    @pytest.mark.asyncio
    async def test_generate_output_no_generator(self, output_service, mock_bim):
        """Test generation when no generator available."""
        # Remove a generator
        del output_service.generators[OutputFormat.GLTF]
        
        result = await output_service.generate_output(
            mock_bim, "test", OutputFormat.GLTF
        )
        
        assert result.success is False
        assert result.format == OutputFormat.GLTF
        assert "No generator available" in result.errors[0]
    
    @pytest.mark.asyncio
    async def test_generate_output_with_options(self, output_service, mock_bim):
        """Test generation with custom options."""
        mock_generator = Mock()
        mock_generator.generate.return_value = OutputResult(success=True)
        output_service.generators[OutputFormat.GLTF] = mock_generator
        
        options = {"binary": True, "optimize": True}
        await output_service.generate_output(
            mock_bim, "test", OutputFormat.GLTF, **options
        )
        
        # Verify options were passed
        mock_generator.generate.assert_called_once_with(
            mock_bim, "test", options
        )
    
    @pytest.mark.asyncio
    async def test_generate_multiple_success(self, output_service, mock_bim):
        """Test generating multiple formats."""
        # Mock generators
        for format in [OutputFormat.GLTF, OutputFormat.DXF]:
            mock_generator = Mock()
            mock_generator.generate.return_value = OutputResult(
                success=True,
                format=format,
                file_path=output_service.temp_dir / f"test.{format.value}"
            )
            output_service.generators[format] = mock_generator
        
        # Generate multiple
        formats = [OutputFormat.GLTF, OutputFormat.DXF]
        results = await output_service.generate_multiple(
            mock_bim, "test", formats
        )
        
        assert len(results) == 2
        assert "gltf" in results
        assert "dxf" in results
        assert results["gltf"].success is True
        assert results["dxf"].success is True
    
    @pytest.mark.asyncio
    async def test_generate_multiple_concurrent(self, output_service, mock_bim):
        """Test concurrent generation."""
        call_order = []
        
        async def mock_generate(bim, filename, format, options):
            call_order.append(format)
            await asyncio.sleep(0.01)  # Simulate work
            return OutputResult(success=True, format=format)
        
        # Patch generate_output to track concurrency
        with patch.object(output_service, 'generate_output', side_effect=mock_generate):
            formats = ["gltf", "dxf", "ifc"]
            await output_service.generate_multiple(mock_bim, "test", formats)
            
            # All formats should be processed
            assert len(call_order) == 3
    
    def test_move_to_permanent(self, output_service):
        """Test moving file to permanent storage."""
        # Create temp file
        temp_file = output_service.temp_dir / "temp_test.gltf"
        temp_file.write_text("test content")
        
        # Track it
        output_service._temp_files[str(temp_file)] = datetime.now()
        
        # Move to permanent
        perm_path = output_service.move_to_permanent(temp_file)
        
        assert perm_path.exists()
        assert perm_path.parent == output_service.output_dir
        assert perm_path.read_text() == "test content"
        assert not temp_file.exists()
        
        # Should be removed from tracking
        assert str(temp_file) not in output_service._temp_files
    
    def test_move_to_permanent_with_new_name(self, output_service):
        """Test moving with new filename."""
        temp_file = output_service.temp_dir / "temp.gltf"
        temp_file.write_text("content")
        
        perm_path = output_service.move_to_permanent(temp_file, "final.gltf")
        
        assert perm_path.name == "final.gltf"
        assert perm_path.exists()
    
    def test_move_to_permanent_missing_file(self, output_service):
        """Test moving non-existent file."""
        missing_file = output_service.temp_dir / "missing.gltf"
        
        with pytest.raises(FileNotFoundError):
            output_service.move_to_permanent(missing_file)
    
    @pytest.mark.asyncio
    async def test_cleanup_temp_files(self, output_service):
        """Test temporary file cleanup."""
        # Create old and new files
        old_file = output_service.temp_dir / "old.gltf"
        old_file.write_text("old")
        recent_file = output_service.temp_dir / "recent.gltf"
        recent_file.write_text("recent")
        
        # Track with different ages
        old_time = datetime.now() - timedelta(hours=25)
        recent_time = datetime.now() - timedelta(hours=1)
        
        output_service._temp_files[str(old_file)] = old_time
        output_service._temp_files[str(recent_file)] = recent_time
        
        # Run cleanup
        await output_service.cleanup_temp_files(max_age_hours=24)
        
        # Old file should be deleted
        assert not old_file.exists()
        assert str(old_file) not in output_service._temp_files
        
        # Recent file should remain
        assert recent_file.exists()
        assert str(recent_file) in output_service._temp_files
    
    @pytest.mark.asyncio
    async def test_cleanup_handles_errors(self, output_service):
        """Test cleanup handles file deletion errors."""
        # Track non-existent file
        missing = str(output_service.temp_dir / "missing.gltf")
        output_service._temp_files[missing] = datetime.now() - timedelta(hours=25)
        
        with patch.object(output_service.logger, 'error') as mock_logger:
            await output_service.cleanup_temp_files()
            
            # Should log error but not crash
            mock_logger.assert_called()
    
    def test_get_available_formats(self, output_service):
        """Test getting available formats."""
        formats = output_service.get_available_formats()
        
        assert "gltf" in formats
        assert "glb" in formats
        assert "dxf" in formats
        assert "ifc" in formats
        assert "ifc4" in formats
        assert len(formats) == len(OutputFormat)
    
    def test_validate_format(self, output_service):
        """Test format validation."""
        assert output_service.validate_format("gltf") is True
        assert output_service.validate_format("GLTF") is True
        assert output_service.validate_format("dxf") is True
        assert output_service.validate_format("invalid") is False
        assert output_service.validate_format("") is False
    
    @pytest.mark.asyncio
    async def test_generate_preview(self, output_service, mock_bim):
        """Test preview generation."""
        mock_result = OutputResult(
            success=True,
            format=OutputFormat.GLB,
            file_path=output_service.temp_dir / "preview.glb"
        )
        
        mock_generator = Mock()
        mock_generator.generate.return_value = mock_result
        output_service.generators[OutputFormat.GLB] = mock_generator
        
        # Generate preview
        result = await output_service.generate_preview(mock_bim, size="medium")
        
        assert result.success is True
        assert result.format == OutputFormat.GLB
        
        # Check options
        call_args = mock_generator.generate.call_args[0]
        options = call_args[2]
        assert options["binary"] is True
        assert options["optimize"] is True
        assert options["max_vertices"] == 50000
        assert options["texture_size"] == 1024
    
    @pytest.mark.asyncio
    async def test_generate_preview_sizes(self, output_service, mock_bim):
        """Test different preview sizes."""
        mock_generator = Mock()
        mock_generator.generate.return_value = OutputResult(success=True)
        output_service.generators[OutputFormat.GLB] = mock_generator
        
        # Test each size
        sizes = {
            "small": 10000,
            "medium": 50000,
            "large": 200000
        }
        
        for size, expected_vertices in sizes.items():
            await output_service.generate_preview(mock_bim, size=size)
            
            call_args = mock_generator.generate.call_args[0]
            options = call_args[2]
            assert options["max_vertices"] == expected_vertices
    
    def test_create_export_manifest(self, output_service):
        """Test manifest creation."""
        results = {
            "gltf": OutputResult(
                success=True,
                format=OutputFormat.GLTF,
                file_path=Path("test.gltf"),
                file_size=1024,
                metadata={"vertices": 100}
            ),
            "dxf": OutputResult(
                success=False,
                format=OutputFormat.DXF,
                errors=["Generation failed"]
            )
        }
        
        manifest = output_service.create_export_manifest(results)
        
        assert "timestamp" in manifest
        assert len(manifest["files"]) == 1
        assert len(manifest["errors"]) == 1
        
        # Check successful file
        file_info = manifest["files"][0]
        assert file_info["format"] == "gltf"
        assert file_info["path"] == "test.gltf"
        assert file_info["size"] == 1024
        assert file_info["metadata"]["vertices"] == 100
        
        # Check error
        error_info = manifest["errors"][0]
        assert error_info["format"] == "dxf"
        assert error_info["errors"] == ["Generation failed"]
    
    @pytest.mark.asyncio
    async def test_export_with_manifest(self, output_service, mock_bim):
        """Test export with manifest generation."""
        # Mock generators
        for format in [OutputFormat.GLTF, OutputFormat.DXF]:
            mock_generator = Mock()
            mock_generator.generate.return_value = OutputResult(
                success=True,
                format=format,
                file_path=output_service.temp_dir / f"test.{format.value}"
            )
            output_service.generators[format] = mock_generator
        
        # Export with manifest
        manifest_path = await output_service.export_with_manifest(
            mock_bim, "project1", ["gltf", "dxf"]
        )
        
        assert manifest_path.exists()
        assert manifest_path.name == "project1_manifest.json"
        
        # Check manifest content
        with open(manifest_path) as f:
            manifest = json.load(f)
        
        assert "timestamp" in manifest
        assert len(manifest["files"]) == 2


class TestOutputManager:
    """Test OutputManager class."""
    
    @pytest.fixture
    def output_manager(self, output_service):
        """Create output manager instance."""
        return OutputManager(output_service)
    
    @pytest.mark.asyncio
    async def test_process_order_success(self, output_manager, mock_bim):
        """Test processing customer order."""
        # Mock service methods
        mock_results = {
            "gltf": OutputResult(
                success=True,
                format=OutputFormat.GLTF,
                file_path=Path("/tmp/order_123.gltf")
            ),
            "dxf": OutputResult(
                success=True,
                format=OutputFormat.DXF,
                file_path=Path("/tmp/order_123.dxf")
            )
        }
        
        output_manager.service.generate_multiple = AsyncMock(return_value=mock_results)
        output_manager.service.move_to_permanent = Mock(side_effect=lambda p, n: Path(f"/perm/{n}"))
        
        # Process order
        result = await output_manager.process_order(
            mock_bim, "order_123", ["gltf", "dxf", "invalid"]
        )
        
        assert result["order_id"] == "order_123"
        assert result["status"] == "partial"  # Due to invalid format
        assert result["invalid_formats"] == ["invalid"]
        assert "gltf" in result["files"]
        assert "dxf" in result["files"]
        assert result["files"]["gltf"] == "/perm/order_123_gltf.gltf"
    
    @pytest.mark.asyncio
    async def test_process_order_all_invalid(self, output_manager, mock_bim):
        """Test order with all invalid formats."""
        result = await output_manager.process_order(
            mock_bim, "order_456", ["invalid1", "invalid2"]
        )
        
        assert result["status"] == "partial"
        assert result["invalid_formats"] == ["invalid1", "invalid2"]
        assert result["files"] == {}
    
    @pytest.mark.asyncio
    async def test_create_download_package(self, output_manager, mock_bim, temp_dirs):
        """Test creating ZIP download package."""
        # Create mock files
        output_dir, temp_dir = temp_dirs
        
        mock_results = {}
        for format in ["gltf", "dxf"]:
            file_path = temp_dir / f"test.{format}"
            file_path.write_text(f"{format} content")
            
            mock_results[format] = OutputResult(
                success=True,
                format=OutputFormat(format),
                file_path=file_path,
                metadata={"format": format}
            )
        
        output_manager.service.generate_multiple = AsyncMock(return_value=mock_results)
        output_manager.service.temp_dir = temp_dir
        
        # Create package
        zip_path = await output_manager.create_download_package(
            mock_bim, "package1", ["gltf", "dxf"]
        )
        
        assert zip_path.exists()
        assert zip_path.suffix == ".zip"
        
        # Verify ZIP contents
        with zipfile.ZipFile(zip_path, 'r') as zf:
            names = zf.namelist()
            assert "test.gltf" in names
            assert "test.dxf" in names
            assert "manifest.json" in names
            assert "README.txt" in names
            
            # Check file content
            assert zf.read("test.gltf").decode() == "gltf content"
            
            # Check manifest
            manifest = json.loads(zf.read("manifest.json"))
            assert len(manifest["files"]) == 2
            
            # Check README
            readme = zf.read("README.txt").decode()
            assert "BIM Export Package: package1" in readme
            assert "GLTF: test.gltf" in readme
    
    @pytest.mark.asyncio
    async def test_create_download_package_default_formats(
        self, output_manager, mock_bim, temp_dirs
    ):
        """Test package creation with default formats."""
        output_dir, temp_dir = temp_dirs
        
        # Mock empty results
        output_manager.service.generate_multiple = AsyncMock(return_value={})
        output_manager.service.temp_dir = temp_dir
        
        zip_path = await output_manager.create_download_package(
            mock_bim, "package2"
        )
        
        # Verify default formats were used
        call_args = output_manager.service.generate_multiple.call_args
        assert call_args[0][2] == ["gltf", "dxf", "ifc"]
    
    def test_create_readme(self, output_manager):
        """Test README content generation."""
        results = {
            "gltf": OutputResult(
                success=True,
                format=OutputFormat.GLTF,
                file_path=Path("model.gltf"),
                metadata={"vertices": 1000, "size": "1.2MB"}
            ),
            "dxf": OutputResult(
                success=False,
                format=OutputFormat.DXF,
                errors=["Failed"]
            )
        }
        
        readme = output_manager._create_readme("TestPackage", results)
        
        assert "BIM Export Package: TestPackage" in readme
        assert "GLTF: model.gltf" in readme
        assert "vertices: 1000" in readme
        assert "size: 1.2MB" in readme
        assert "DXF" not in readme  # Failed format not included
        assert "Generated by BIM Backend System" in readme


if __name__ == "__main__":
    pytest.main([__file__, "-v"])