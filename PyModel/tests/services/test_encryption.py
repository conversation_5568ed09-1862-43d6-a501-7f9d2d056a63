"""
Test suite for AES encryption service.

Tests encryption, decryption, and JSON convenience methods.
"""

import pytest
import base64
import json
from cryptography.hazmat.primitives import padding
from src.services.encryption import AESEncryption


class TestAESEncryption:
    """Test AESEncryption class."""
    
    @pytest.fixture
    def encryption_key(self):
        """Standard test encryption key."""
        return "test-encryption-key-123"
    
    @pytest.fixture
    def aes(self, encryption_key):
        """Create AES encryption instance."""
        return AESEncryption(encryption_key)
    
    def test_initialization(self, encryption_key):
        """Test AES initialization."""
        aes = AESEncryption(encryption_key)
        
        # Key should be hashed to 32 bytes
        assert len(aes.key) == 32
        assert aes.backend is not None
    
    def test_key_hashing_consistency(self):
        """Test that same key produces same hash."""
        key = "my-secret-key"
        aes1 = AESEncryption(key)
        aes2 = AESEncryption(key)
        
        assert aes1.key == aes2.key
    
    def test_different_keys_different_hashes(self):
        """Test different keys produce different hashes."""
        aes1 = AESEncryption("key1")
        aes2 = AESEncryption("key2")
        
        assert aes1.key != aes2.key
    
    def test_encrypt_basic(self, aes):
        """Test basic encryption."""
        plaintext = "Hello, World!"
        encrypted = aes.encrypt(plaintext)
        
        # Should return base64 encoded string
        assert isinstance(encrypted, str)
        
        # Should be valid base64
        try:
            decoded = base64.b64decode(encrypted)
            assert len(decoded) > 16  # At least IV + some data
        except Exception:
            pytest.fail("Invalid base64 output")
    
    def test_encrypt_empty_string(self, aes):
        """Test encrypting empty string."""
        encrypted = aes.encrypt("")
        
        assert isinstance(encrypted, str)
        assert len(encrypted) > 0
    
    def test_encrypt_unicode(self, aes):
        """Test encrypting unicode text."""
        plaintext = "Hello 世界 🌍"
        encrypted = aes.encrypt(plaintext)
        
        assert isinstance(encrypted, str)
    
    def test_encrypt_long_text(self, aes):
        """Test encrypting long text."""
        plaintext = "A" * 10000
        encrypted = aes.encrypt(plaintext)
        
        assert isinstance(encrypted, str)
        # Encrypted should be longer due to base64 encoding
        assert len(encrypted) > len(plaintext)
    
    def test_decrypt_basic(self, aes):
        """Test basic decryption."""
        plaintext = "Hello, World!"
        encrypted = aes.encrypt(plaintext)
        decrypted = aes.decrypt(encrypted)
        
        assert decrypted == plaintext
    
    def test_decrypt_empty_string(self, aes):
        """Test decrypting empty string."""
        encrypted = aes.encrypt("")
        decrypted = aes.decrypt(encrypted)
        
        assert decrypted == ""
    
    def test_decrypt_unicode(self, aes):
        """Test decrypting unicode text."""
        plaintext = "Unicode test: 你好世界 🚀"
        encrypted = aes.encrypt(plaintext)
        decrypted = aes.decrypt(encrypted)
        
        assert decrypted == plaintext
    
    def test_decrypt_long_text(self, aes):
        """Test decrypting long text."""
        plaintext = "Lorem ipsum " * 1000
        encrypted = aes.encrypt(plaintext)
        decrypted = aes.decrypt(encrypted)
        
        assert decrypted == plaintext
    
    def test_decrypt_invalid_base64(self, aes):
        """Test decrypting invalid base64."""
        with pytest.raises(ValueError) as exc_info:
            aes.decrypt("not-valid-base64!!!")
        
        assert "Decryption failed" in str(exc_info.value)
    
    def test_decrypt_too_short(self, aes):
        """Test decrypting data that's too short."""
        # Less than 16 bytes (IV size)
        short_data = base64.b64encode(b"short").decode()
        
        with pytest.raises(ValueError) as exc_info:
            aes.decrypt(short_data)
        
        assert "Invalid encrypted data: too short" in str(exc_info.value)
    
    def test_decrypt_corrupted_data(self, aes):
        """Test decrypting corrupted data."""
        # Encrypt something
        encrypted = aes.encrypt("test data")
        
        # Corrupt the encrypted data
        encrypted_bytes = base64.b64decode(encrypted)
        corrupted_bytes = encrypted_bytes[:-5] + b"xxxxx"
        corrupted = base64.b64encode(corrupted_bytes).decode()
        
        with pytest.raises(ValueError) as exc_info:
            aes.decrypt(corrupted)
        
        assert "Decryption failed" in str(exc_info.value)
    
    def test_decrypt_wrong_key(self):
        """Test decrypting with wrong key."""
        aes1 = AESEncryption("key1")
        aes2 = AESEncryption("key2")
        
        encrypted = aes1.encrypt("secret message")
        
        # Should fail with wrong key
        with pytest.raises(ValueError):
            aes2.decrypt(encrypted)
    
    def test_encrypt_decrypt_special_characters(self, aes):
        """Test special characters handling."""
        special_chars = "!@#$%^&*()_+-=[]{}|;':\",./<>?\n\t\r"
        encrypted = aes.encrypt(special_chars)
        decrypted = aes.decrypt(encrypted)
        
        assert decrypted == special_chars
    
    def test_multiple_encrypt_different_results(self, aes):
        """Test that encrypting same text twice gives different results."""
        plaintext = "test message"
        
        encrypted1 = aes.encrypt(plaintext)
        encrypted2 = aes.encrypt(plaintext)
        
        # Should be different due to random IV
        assert encrypted1 != encrypted2
        
        # But both should decrypt to same plaintext
        assert aes.decrypt(encrypted1) == plaintext
        assert aes.decrypt(encrypted2) == plaintext
    
    def test_generate_key_static_method(self):
        """Test key generation."""
        key1 = AESEncryption.generate_key()
        key2 = AESEncryption.generate_key()
        
        # Should be 32 characters (base64 encoded 24 bytes)
        assert len(key1) == 32
        assert len(key2) == 32
        
        # Should be different
        assert key1 != key2
        
        # Should be valid base64
        base64.urlsafe_b64decode(key1)
        base64.urlsafe_b64decode(key2)
    
    def test_encrypt_json(self, aes):
        """Test JSON encryption convenience method."""
        data = {
            "name": "Test User",
            "age": 25,
            "active": True,
            "tags": ["tag1", "tag2"],
            "metadata": {"key": "value"}
        }
        
        encrypted = aes.encrypt_json(data)
        
        assert isinstance(encrypted, str)
        
        # Should be able to decrypt manually
        decrypted_json = aes.decrypt(encrypted)
        decrypted_data = json.loads(decrypted_json)
        
        assert decrypted_data == data
    
    def test_decrypt_json(self, aes):
        """Test JSON decryption convenience method."""
        data = {
            "user_id": 123,
            "settings": {
                "theme": "dark",
                "notifications": False
            },
            "items": [1, 2, 3]
        }
        
        encrypted = aes.encrypt_json(data)
        decrypted = aes.decrypt_json(encrypted)
        
        assert decrypted == data
    
    def test_encrypt_decrypt_json_empty_dict(self, aes):
        """Test JSON methods with empty dictionary."""
        data = {}
        
        encrypted = aes.encrypt_json(data)
        decrypted = aes.decrypt_json(encrypted)
        
        assert decrypted == data
    
    def test_encrypt_json_complex_nested(self, aes):
        """Test JSON encryption with complex nested data."""
        data = {
            "level1": {
                "level2": {
                    "level3": {
                        "values": [1, 2.5, None, True, False],
                        "unicode": "Hello 世界"
                    }
                }
            },
            "array": [
                {"id": 1, "name": "Item 1"},
                {"id": 2, "name": "Item 2"}
            ]
        }
        
        encrypted = aes.encrypt_json(data)
        decrypted = aes.decrypt_json(encrypted)
        
        assert decrypted == data
    
    def test_decrypt_json_invalid(self, aes):
        """Test decrypting invalid JSON."""
        # Encrypt non-JSON string
        encrypted = aes.encrypt("not valid json {]")
        
        with pytest.raises(json.JSONDecodeError):
            aes.decrypt_json(encrypted)
    
    def test_padding_correctness(self, aes):
        """Test PKCS7 padding is applied correctly."""
        # Test various lengths to ensure padding works
        for length in [0, 1, 15, 16, 17, 31, 32, 33]:
            plaintext = "A" * length
            encrypted = aes.encrypt(plaintext)
            decrypted = aes.decrypt(encrypted)
            
            assert decrypted == plaintext
    
    def test_iv_uniqueness(self, aes):
        """Test that IVs are unique for each encryption."""
        plaintext = "test"
        
        # Encrypt multiple times
        encrypted_list = [aes.encrypt(plaintext) for _ in range(10)]
        
        # Extract IVs
        ivs = []
        for encrypted in encrypted_list:
            decoded = base64.b64decode(encrypted)
            iv = decoded[:16]
            ivs.append(iv)
        
        # All IVs should be unique
        assert len(set(ivs)) == len(ivs)
    
    def test_encryption_deterministic_with_same_iv(self):
        """Test that encryption is deterministic with same key and IV."""
        # This is more of an implementation detail test
        # In practice, IV should always be random
        import os
        from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
        
        key = b"a" * 32
        iv = b"b" * 16
        plaintext = b"test message"
        
        # Encrypt twice with same key and IV
        for _ in range(2):
            cipher = Cipher(algorithms.AES(key), modes.CBC(iv))
            encryptor = cipher.encryptor()
            
            padder = padding.PKCS7(128).padder()
            padded = padder.update(plaintext) + padder.finalize()
            
            result = encryptor.update(padded) + encryptor.finalize()
            
            assert result == result  # Same each time
    
    def test_large_data_encryption(self, aes):
        """Test encrypting large data."""
        # 1MB of data
        large_data = "X" * (1024 * 1024)
        
        encrypted = aes.encrypt(large_data)
        decrypted = aes.decrypt(encrypted)
        
        assert decrypted == large_data
    
    def test_binary_safe(self, aes):
        """Test that all byte values are handled correctly."""
        # Create string with all possible byte values
        all_bytes = "".join(chr(i) for i in range(256) if chr(i).isprintable())
        
        encrypted = aes.encrypt(all_bytes)
        decrypted = aes.decrypt(encrypted)
        
        assert decrypted == all_bytes


if __name__ == "__main__":
    pytest.main([__file__, "-v"])