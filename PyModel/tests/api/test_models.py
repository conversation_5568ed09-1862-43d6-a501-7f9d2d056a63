"""
Test suite for API request and response models.

Tests all Pydantic models used in the API.
"""

import pytest
from pydantic import ValidationError
import json
from src.api.models import (
    ResponseStatus, CarportRequest, CarportResponse, 
    HealthResponse, ErrorResponse
)


class TestResponseStatus:
    """Test ResponseStatus enum."""
    
    def test_enum_values(self):
        """Test all enum values."""
        assert ResponseStatus.SUCCESS == "success"
        assert ResponseStatus.ERROR == "error"
        assert ResponseStatus.VALIDATION_ERROR == "validation_error"
    
    def test_enum_members(self):
        """Test enum membership."""
        assert "success" in ResponseStatus._value2member_map_
        assert "error" in ResponseStatus._value2member_map_
        assert "validation_error" in ResponseStatus._value2member_map_


class TestCarportRequest:
    """Test CarportRequest model."""
    
    def test_valid_request(self):
        """Test creating valid request."""
        request = CarportRequest(encrypted_data="base64_encoded_data")
        assert request.encrypted_data == "base64_encoded_data"
    
    def test_missing_encrypted_data(self):
        """Test request without encrypted data."""
        with pytest.raises(ValidationError) as exc_info:
            CarportRequest()
        
        errors = exc_info.value.errors()
        assert len(errors) == 1
        assert errors[0]["loc"] == ("encrypted_data",)
        assert errors[0]["type"] == "missing"
    
    def test_empty_encrypted_data(self):
        """Test request with empty encrypted data."""
        request = CarportRequest(encrypted_data="")
        assert request.encrypted_data == ""  # Empty is technically valid
    
    def test_model_dump(self):
        """Test model serialization."""
        request = CarportRequest(encrypted_data="test_data")
        dumped = request.model_dump()
        assert dumped == {"encrypted_data": "test_data"}
    
    def test_model_json(self):
        """Test JSON serialization."""
        request = CarportRequest(encrypted_data="test_data")
        json_str = request.model_dump_json()
        assert json.loads(json_str) == {"encrypted_data": "test_data"}
    
    def test_from_dict(self):
        """Test creating from dictionary."""
        data = {"encrypted_data": "from_dict"}
        request = CarportRequest(**data)
        assert request.encrypted_data == "from_dict"
    
    def test_schema_example(self):
        """Test schema includes example."""
        schema = CarportRequest.model_json_schema()
        assert "example" in schema
        assert schema["example"]["encrypted_data"] == "base64_encoded_encrypted_json_string"


class TestCarportResponse:
    """Test CarportResponse model."""
    
    def test_success_response(self):
        """Test creating success response."""
        response = CarportResponse(
            success=True,
            status=ResponseStatus.SUCCESS,
            message="Generated successfully",
            file_url="/api/download/123"
        )
        assert response.success is True
        assert response.status == ResponseStatus.SUCCESS
        assert response.message == "Generated successfully"
        assert response.file_url == "/api/download/123"
        assert response.error_details is None
    
    def test_error_response(self):
        """Test creating error response."""
        response = CarportResponse(
            success=False,
            status=ResponseStatus.ERROR,
            message="Generation failed",
            error_details={"reason": "Invalid parameters"}
        )
        assert response.success is False
        assert response.status == ResponseStatus.ERROR
        assert response.message == "Generation failed"
        assert response.file_url is None
        assert response.error_details == {"reason": "Invalid parameters"}
    
    def test_minimal_response(self):
        """Test response with only required fields."""
        response = CarportResponse(
            success=True,
            status=ResponseStatus.SUCCESS,
            message="OK"
        )
        assert response.success is True
        assert response.status == ResponseStatus.SUCCESS
        assert response.message == "OK"
        assert response.file_url is None
        assert response.error_details is None
    
    def test_validation_error_response(self):
        """Test validation error response."""
        response = CarportResponse(
            success=False,
            status=ResponseStatus.VALIDATION_ERROR,
            message="Validation failed",
            error_details={
                "field": "span",
                "reason": "Must be between 3000 and 12000"
            }
        )
        assert response.status == ResponseStatus.VALIDATION_ERROR
        assert response.error_details["field"] == "span"
    
    def test_missing_required_fields(self):
        """Test missing required fields."""
        with pytest.raises(ValidationError) as exc_info:
            CarportResponse(success=True)
        
        errors = exc_info.value.errors()
        assert any(e["loc"] == ("status",) for e in errors)
        assert any(e["loc"] == ("message",) for e in errors)
    
    def test_invalid_status(self):
        """Test invalid status value."""
        with pytest.raises(ValidationError):
            CarportResponse(
                success=True,
                status="invalid_status",
                message="Test"
            )
    
    def test_model_dump_exclude_none(self):
        """Test serialization excludes None values."""
        response = CarportResponse(
            success=True,
            status=ResponseStatus.SUCCESS,
            message="OK"
        )
        dumped = response.model_dump(exclude_none=True)
        assert "file_url" not in dumped
        assert "error_details" not in dumped
    
    def test_schema_example(self):
        """Test schema includes example."""
        schema = CarportResponse.model_json_schema()
        assert "example" in schema
        example = schema["example"]
        assert example["success"] is True
        assert example["status"] == "success"
        assert example["file_url"] == "/api/download/abc123.gltf"


class TestHealthResponse:
    """Test HealthResponse model."""
    
    def test_valid_health_response(self):
        """Test creating valid health response."""
        response = HealthResponse(
            status="healthy",
            version="1.0.0",
            timestamp="2024-01-15T10:30:00Z"
        )
        assert response.status == "healthy"
        assert response.version == "1.0.0"
        assert response.timestamp == "2024-01-15T10:30:00Z"
    
    def test_unhealthy_status(self):
        """Test unhealthy status."""
        response = HealthResponse(
            status="degraded",
            version="1.0.0",
            timestamp="2024-01-15T10:30:00Z"
        )
        assert response.status == "degraded"
    
    def test_missing_fields(self):
        """Test missing required fields."""
        with pytest.raises(ValidationError) as exc_info:
            HealthResponse()
        
        errors = exc_info.value.errors()
        assert len(errors) == 3  # All fields are required
    
    def test_model_json_serialization(self):
        """Test JSON serialization."""
        response = HealthResponse(
            status="healthy",
            version="2.0.0",
            timestamp="2024-01-15T10:30:00Z"
        )
        json_str = response.model_dump_json()
        data = json.loads(json_str)
        assert data["status"] == "healthy"
        assert data["version"] == "2.0.0"
        assert data["timestamp"] == "2024-01-15T10:30:00Z"
    
    def test_schema_example(self):
        """Test schema includes example."""
        schema = HealthResponse.model_json_schema()
        assert "example" in schema
        example = schema["example"]
        assert example["status"] == "healthy"
        assert example["version"] == "1.0.0"
        assert "timestamp" in example


class TestErrorResponse:
    """Test ErrorResponse model."""
    
    def test_simple_error(self):
        """Test simple error response."""
        error = ErrorResponse(
            error="not_found",
            message="Resource not found"
        )
        assert error.error == "not_found"
        assert error.message == "Resource not found"
        assert error.details is None
        assert error.request_id is None
    
    def test_detailed_error(self):
        """Test error with details."""
        error = ErrorResponse(
            error="validation_error",
            message="Invalid input",
            details={"field": "span", "value": 2000, "min": 3000}
        )
        assert error.error == "validation_error"
        assert error.message == "Invalid input"
        assert error.details["field"] == "span"
        assert error.details["value"] == 2000
        assert error.details["min"] == 3000
    
    def test_error_with_request_id(self):
        """Test error with request ID."""
        error = ErrorResponse(
            error="internal_error",
            message="Something went wrong",
            request_id="req_123456"
        )
        assert error.error == "internal_error"
        assert error.request_id == "req_123456"
    
    def test_full_error(self):
        """Test error with all fields."""
        error = ErrorResponse(
            error="database_error",
            message="Database connection failed",
            details={"host": "localhost", "port": 5432},
            request_id="req_789"
        )
        assert error.error == "database_error"
        assert error.message == "Database connection failed"
        assert error.details["host"] == "localhost"
        assert error.details["port"] == 5432
        assert error.request_id == "req_789"
    
    def test_missing_required_fields(self):
        """Test missing required fields."""
        with pytest.raises(ValidationError) as exc_info:
            ErrorResponse(error="test")
        
        errors = exc_info.value.errors()
        assert any(e["loc"] == ("message",) for e in errors)
    
    def test_model_dump_exclude_none(self):
        """Test serialization excludes None values."""
        error = ErrorResponse(
            error="test_error",
            message="Test message"
        )
        dumped = error.model_dump(exclude_none=True)
        assert "details" not in dumped
        assert "request_id" not in dumped
    
    def test_complex_details(self):
        """Test complex nested details."""
        error = ErrorResponse(
            error="complex_error",
            message="Complex validation failed",
            details={
                "errors": [
                    {"field": "span", "reason": "too small"},
                    {"field": "height", "reason": "too large"}
                ],
                "context": {
                    "user": "test_user",
                    "timestamp": "2024-01-15T10:30:00Z"
                }
            }
        )
        assert len(error.details["errors"]) == 2
        assert error.details["context"]["user"] == "test_user"
    
    def test_schema_example(self):
        """Test schema includes example."""
        schema = ErrorResponse.model_json_schema()
        assert "example" in schema
        example = schema["example"]
        assert example["error"] == "validation_error"
        assert example["message"] == "Invalid building input parameters"
        assert "details" in example
        assert example["request_id"] == "req_123456"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])