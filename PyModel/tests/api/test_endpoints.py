"""
Test suite for API endpoints.

Tests all carport-related endpoints and functionality.
"""

import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, <PERSON><PERSON>, Async<PERSON>ock, MagicMock
import json
import base64
import tempfile
import os
from datetime import datetime, timedelta
from pathlib import Path
import uuid

# Import after modifying path
import sys
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from src.api.main import app
from src.api.endpoints import router, generated_files, cleanup_old_files
from src.api.models import CarportRequest, CarportResponse, ResponseStatus
from src.business.building_input import BuildingInput
from src.business.engineering import EngData
from src.services.encryption import AESEncryption


@pytest.fixture
def client():
    """Create test client."""
    return TestClient(app)


@pytest.fixture
def sample_building_input():
    """Create sample building input."""
    return {
        "building_type": "CARPORT",
        "name": "Test Carport",
        "roof_type": "FLAT",
        "validate_engineering": False,
        "bays": 2,
        "span": 6000.0,
        "length": 6000.0,
        "height": 2400.0,
        "wind_speed": 32,
        "pitch": 3.0,
        "slab": True,
        "slab_thickness": 100.0,
        "soil": "M",
        "overhang": 600.0
    }


@pytest.fixture
def encrypted_request(sample_building_input):
    """Create encrypted request."""
    aes = AESEncryption("development-key-change-in-production")
    encrypted_data = aes.encrypt(json.dumps(sample_building_input))
    return CarportRequest(encrypted_data=encrypted_data)


@pytest.fixture
def mock_carport_builder():
    """Mock CarportBuilder."""
    with patch("src.api.endpoints.CarportBuilder") as mock:
        mock_carport = Mock()
        mock.create_carport.return_value = mock_carport
        yield mock


@pytest.fixture
def mock_output_service():
    """Mock OutputService."""
    with patch("src.api.endpoints.output_service") as mock:
        mock_result = Mock()
        mock_result.success = True
        mock_result.file_path = Path("/tmp/test_file.gltf")
        mock_result.errors = []
        
        mock.generate_output = AsyncMock(return_value=mock_result)
        mock.get_available_formats.return_value = ["gltf", "glb", "dxf", "ifc"]
        yield mock


class TestCreateCarportEndpoint:
    """Test /carport/create endpoint."""
    
    @pytest.mark.asyncio
    async def test_create_carport_success(self, client, encrypted_request, 
                                         mock_carport_builder, mock_output_service):
        """Test successful carport creation."""
        # Create temp file for testing
        with tempfile.NamedTemporaryFile(delete=False, suffix=".gltf") as tmp:
            tmp.write(b"test content")
            tmp_path = tmp.name
        
        mock_output_service.generate_output.return_value.file_path = Path(tmp_path)
        
        try:
            response = client.post(
                "/api/carport/create",
                json=encrypted_request.model_dump()
            )
            
            assert response.status_code == 200
            data = response.json()
            
            assert data["success"] is True
            assert data["status"] == "success"
            assert data["message"] == "Carport generated successfully"
            assert data["file_url"].startswith("/api/download/")
            assert data["error_details"] is None
            
            # Verify builder was called
            mock_carport_builder.create_carport.assert_called_once()
            
            # Verify output service was called
            mock_output_service.generate_output.assert_called_once()
            
        finally:
            # Cleanup
            if os.path.exists(tmp_path):
                os.unlink(tmp_path)
    
    @pytest.mark.asyncio
    async def test_create_carport_invalid_encryption(self, client):
        """Test creation with invalid encrypted data."""
        request = CarportRequest(encrypted_data="invalid_base64_data!!!")
        
        response = client.post(
            "/api/carport/create",
            json=request.model_dump()
        )
        
        assert response.status_code == 400
        data = response.json()
        
        assert data["error"] == "decryption_error"
        assert "Failed to decrypt" in data["message"]
        assert "details" in data
    
    @pytest.mark.asyncio
    async def test_create_carport_invalid_input(self, client):
        """Test creation with invalid building input."""
        # Encrypt invalid data
        aes = AESEncryption("development-key-change-in-production")
        invalid_data = {"invalid": "data", "no": "building_type"}
        encrypted_data = aes.encrypt(json.dumps(invalid_data))
        
        request = CarportRequest(encrypted_data=encrypted_data)
        
        response = client.post(
            "/api/carport/create",
            json=request.model_dump()
        )
        
        assert response.status_code == 400
        data = response.json()
        
        assert data["error"] == "validation_error"
        assert "Invalid building input" in data["message"]
    
    @pytest.mark.asyncio
    async def test_create_carport_with_engineering_validation(
        self, client, sample_building_input, mock_carport_builder, mock_output_service
    ):
        """Test creation with engineering validation."""
        # Enable engineering validation
        sample_building_input["validate_engineering"] = True
        
        aes = AESEncryption("development-key-change-in-production")
        encrypted_data = aes.encrypt(json.dumps(sample_building_input))
        request = CarportRequest(encrypted_data=encrypted_data)
        
        # Mock engineering service
        with patch("src.api.endpoints.EngineeringService") as mock_eng:
            mock_eng_instance = Mock()
            mock_eng.return_value = mock_eng_instance
            mock_eng_instance.__aenter__ = AsyncMock(return_value=mock_eng_instance)
            mock_eng_instance.__aexit__ = AsyncMock(return_value=None)
            
            mock_eng_data = EngData(
                ENG_RAFTER="C15024",
                ENG_PURLINSIZE="C15015",
                ENG_PURLINROW=5,
                ENG_COLUMN="SHS10010030",
                ENG_FOOTINGTYPE="bored",
                ENG_FOOTINGDIA="450",
                ENG_FOOTINGDEPTH="600"
            )
            mock_eng_instance.validate_design = AsyncMock(return_value=mock_eng_data)
            
            # Create temp file
            with tempfile.NamedTemporaryFile(delete=False) as tmp:
                mock_output_service.generate_output.return_value.file_path = Path(tmp.name)
                
                response = client.post(
                    "/api/carport/create",
                    json=request.model_dump()
                )
                
                assert response.status_code == 200
                
                # Verify engineering validation was called
                mock_eng_instance.validate_design.assert_called_once()
                
                # Verify builder received engineering data
                mock_carport_builder.create_carport.assert_called_once()
                call_args = mock_carport_builder.create_carport.call_args
                assert call_args[1] == mock_eng_data
                
                os.unlink(tmp.name)
    
    @pytest.mark.asyncio
    async def test_create_carport_engineering_validation_failure(
        self, client, sample_building_input
    ):
        """Test creation when engineering validation fails."""
        sample_building_input["validate_engineering"] = True
        
        aes = AESEncryption("development-key-change-in-production")
        encrypted_data = aes.encrypt(json.dumps(sample_building_input))
        request = CarportRequest(encrypted_data=encrypted_data)
        
        with patch("src.api.endpoints.EngineeringService") as mock_eng:
            mock_eng_instance = Mock()
            mock_eng.return_value = mock_eng_instance
            mock_eng_instance.__aenter__ = AsyncMock(return_value=mock_eng_instance)
            mock_eng_instance.__aexit__ = AsyncMock(return_value=None)
            mock_eng_instance.validate_design = AsyncMock(return_value=None)
            
            response = client.post(
                "/api/carport/create",
                json=request.model_dump()
            )
            
            assert response.status_code == 200  # Still 200 but with error response
            data = response.json()
            
            assert data["success"] is False
            assert data["status"] == "error"
            assert "Engineering validation failed" in data["message"]
    
    @pytest.mark.asyncio
    async def test_create_carport_generation_error(
        self, client, encrypted_request, mock_carport_builder
    ):
        """Test handling of generation errors."""
        mock_carport_builder.create_carport.side_effect = ValueError("Generation failed")
        
        response = client.post(
            "/api/carport/create",
            json=encrypted_request.model_dump()
        )
        
        assert response.status_code == 500
        data = response.json()
        
        assert data["error"] == "generation_error"
        assert "Failed to generate carport" in data["message"]
        assert data["details"]["reason"] == "Generation failed"
    
    @pytest.mark.asyncio
    async def test_create_carport_output_generation_failure(
        self, client, encrypted_request, mock_carport_builder, mock_output_service
    ):
        """Test handling of output generation failure."""
        mock_result = Mock()
        mock_result.success = False
        mock_result.errors = ["Failed to generate GLTF", "Invalid geometry"]
        mock_output_service.generate_output.return_value = mock_result
        
        response = client.post(
            "/api/carport/create",
            json=encrypted_request.model_dump()
        )
        
        assert response.status_code == 500
        data = response.json()
        
        assert data["error"] == "output_error"
        assert "Failed to generate output" in data["message"]
        assert data["details"]["errors"] == ["Failed to generate GLTF", "Invalid geometry"]
    
    @pytest.mark.asyncio
    async def test_create_carport_custom_output_format(
        self, client, sample_building_input, mock_carport_builder, mock_output_service
    ):
        """Test creation with custom output format."""
        sample_building_input["output_format"] = "dxf"
        
        aes = AESEncryption("development-key-change-in-production")
        encrypted_data = aes.encrypt(json.dumps(sample_building_input))
        request = CarportRequest(encrypted_data=encrypted_data)
        
        with tempfile.NamedTemporaryFile(delete=False, suffix=".dxf") as tmp:
            mock_output_service.generate_output.return_value.file_path = Path(tmp.name)
            
            response = client.post(
                "/api/carport/create",
                json=request.model_dump()
            )
            
            assert response.status_code == 200
            
            # Verify correct format was requested
            mock_output_service.generate_output.assert_called_once()
            call_args = mock_output_service.generate_output.call_args
            assert call_args[0][2] == "dxf"  # Third argument is format
            
            os.unlink(tmp.name)
    
    @pytest.mark.asyncio
    async def test_create_carport_file_tracking(
        self, client, encrypted_request, mock_carport_builder, mock_output_service
    ):
        """Test generated files are tracked correctly."""
        with tempfile.NamedTemporaryFile(delete=False) as tmp:
            mock_output_service.generate_output.return_value.file_path = Path(tmp.name)
            
            # Clear any existing files
            generated_files.clear()
            
            response = client.post(
                "/api/carport/create",
                json=encrypted_request.model_dump()
            )
            
            assert response.status_code == 200
            data = response.json()
            
            # Extract file ID from URL
            file_id = data["file_url"].split("/")[-1]
            
            # Verify file is tracked
            assert file_id in generated_files
            assert generated_files[file_id]["path"] == tmp.name
            assert "created_at" in generated_files[file_id]
            assert isinstance(generated_files[file_id]["created_at"], datetime)
            
            os.unlink(tmp.name)


class TestDownloadEndpoint:
    """Test /download/{file_id} endpoint."""
    
    def test_download_existing_file(self, client):
        """Test downloading an existing file."""
        # Create a test file
        with tempfile.NamedTemporaryFile(delete=False, suffix=".gltf") as tmp:
            tmp.write(b"test gltf content")
            tmp_path = tmp.name
        
        # Track the file
        file_id = str(uuid.uuid4())
        generated_files[file_id] = {
            "path": tmp_path,
            "created_at": datetime.utcnow(),
            "filename": "test.gltf"
        }
        
        try:
            response = client.get(f"/api/download/{file_id}")
            
            assert response.status_code == 200
            assert response.content == b"test gltf content"
            assert response.headers["content-type"] == "application/octet-stream"
            assert "filename=test.gltf" in response.headers.get("content-disposition", "")
            
        finally:
            os.unlink(tmp_path)
            del generated_files[file_id]
    
    def test_download_nonexistent_file_id(self, client):
        """Test downloading with non-existent file ID."""
        response = client.get("/api/download/nonexistent-id")
        
        assert response.status_code == 404
        data = response.json()
        
        assert data["error"] == "not_found"
        assert "File not found or has expired" in data["message"]
    
    def test_download_deleted_file(self, client):
        """Test downloading when file exists in tracking but not on disk."""
        # Track a non-existent file
        file_id = str(uuid.uuid4())
        generated_files[file_id] = {
            "path": "/nonexistent/path/file.gltf",
            "created_at": datetime.utcnow(),
            "filename": "missing.gltf"
        }
        
        response = client.get(f"/api/download/{file_id}")
        
        assert response.status_code == 404
        data = response.json()
        
        assert data["error"] == "not_found"
        assert "File not found or has been deleted" in data["message"]
        
        # Verify tracking was cleaned up
        assert file_id not in generated_files


class TestExportEndpoint:
    """Test /carport/export endpoint."""
    
    @pytest.mark.asyncio
    async def test_export_multiple_formats(
        self, client, encrypted_request, mock_carport_builder
    ):
        """Test exporting carport in multiple formats."""
        with patch("src.api.endpoints.output_manager") as mock_manager:
            # Mock zip file creation
            with tempfile.NamedTemporaryFile(suffix=".zip", delete=False) as tmp:
                mock_manager.create_download_package = AsyncMock(return_value=Path(tmp.name))
                
                response = client.post(
                    "/api/carport/export?formats=gltf,dxf,ifc",
                    json=encrypted_request.model_dump()
                )
                
                assert response.status_code == 200
                data = response.json()
                
                assert data["success"] is True
                assert "Export package created" in data["message"]
                assert data["formats"] == ["gltf", "dxf", "ifc"]
                assert data["download_url"].startswith("/api/download/")
                
                # Verify output manager was called
                mock_manager.create_download_package.assert_called_once()
                call_args = mock_manager.create_download_package.call_args
                assert call_args[0][2] == ["gltf", "dxf", "ifc"]
                
                os.unlink(tmp.name)
    
    @pytest.mark.asyncio
    async def test_export_default_formats(
        self, client, encrypted_request, mock_carport_builder
    ):
        """Test export with default formats."""
        with patch("src.api.endpoints.output_manager") as mock_manager:
            with tempfile.NamedTemporaryFile(suffix=".zip", delete=False) as tmp:
                mock_manager.create_download_package = AsyncMock(return_value=Path(tmp.name))
                
                response = client.post(
                    "/api/carport/export",
                    json=encrypted_request.model_dump()
                )
                
                assert response.status_code == 200
                data = response.json()
                
                # Default formats
                assert data["formats"] == ["gltf", "dxf", "ifc"]
                
                os.unlink(tmp.name)
    
    @pytest.mark.asyncio
    async def test_export_error_handling(
        self, client, encrypted_request, mock_carport_builder
    ):
        """Test export error handling."""
        with patch("src.api.endpoints.output_manager") as mock_manager:
            mock_manager.create_download_package = AsyncMock(
                side_effect=Exception("Export failed")
            )
            
            response = client.post(
                "/api/carport/export",
                json=encrypted_request.model_dump()
            )
            
            assert response.status_code == 500
            data = response.json()
            
            assert data["error"] == "export_error"
            assert "Failed to create export package" in data["message"]
            assert data["details"]["reason"] == "Export failed"


class TestFormatsEndpoint:
    """Test /carport/formats endpoint."""
    
    def test_get_available_formats(self, client, mock_output_service):
        """Test getting available export formats."""
        response = client.get("/api/carport/formats")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["formats"] == ["gltf", "glb", "dxf", "ifc"]
        assert data["default"] == "gltf"
        assert "description" in data
        
        # Check format descriptions
        desc = data["description"]
        assert "3D model for web" in desc["gltf"]
        assert "CAD drawings" in desc["dxf"]
        assert "BIM interoperability" in desc["ifc"]


class TestSampleRequestEndpoint:
    """Test /carport/sample-request endpoint."""
    
    def test_get_sample_request(self, client):
        """Test getting sample request."""
        response = client.get("/api/carport/sample-request")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "description" in data
        assert "decrypted_payload" in data
        assert "encrypted_request" in data
        
        # Verify decrypted payload
        payload = data["decrypted_payload"]
        assert payload["building_type"] == "CARPORT"
        assert payload["name"] == "Sample Carport"
        assert payload["span"] == 6000.0
        assert payload["length"] == 6000.0
        
        # Verify encrypted request can be decrypted
        encrypted = data["encrypted_request"]["encrypted_data"]
        aes = AESEncryption("development-key-change-in-production")
        decrypted = aes.decrypt(encrypted)
        decrypted_data = json.loads(decrypted)
        
        assert decrypted_data == payload


class TestFileCleanup:
    """Test file cleanup functionality."""
    
    def test_cleanup_old_files():
        """Test cleanup_old_files function."""
        # Create test files with different ages
        current_time = datetime.utcnow()
        old_time = current_time - timedelta(hours=25)  # Older than retention
        recent_time = current_time - timedelta(hours=1)  # Within retention
        
        # Create temp files
        with tempfile.NamedTemporaryFile(delete=False) as old_file:
            old_path = old_file.name
        with tempfile.NamedTemporaryFile(delete=False) as recent_file:
            recent_path = recent_file.name
        
        # Track files
        old_id = "old-file"
        recent_id = "recent-file"
        
        generated_files[old_id] = {
            "path": old_path,
            "created_at": old_time,
            "filename": "old.gltf"
        }
        generated_files[recent_id] = {
            "path": recent_path,
            "created_at": recent_time,
            "filename": "recent.gltf"
        }
        
        try:
            # Run cleanup
            cleanup_old_files()
            
            # Old file should be removed
            assert old_id not in generated_files
            assert not os.path.exists(old_path)
            
            # Recent file should remain
            assert recent_id in generated_files
            assert os.path.exists(recent_path)
            
        finally:
            # Cleanup
            if os.path.exists(recent_path):
                os.unlink(recent_path)
            if recent_id in generated_files:
                del generated_files[recent_id]
    
    def test_cleanup_missing_file():
        """Test cleanup handles missing files gracefully."""
        # Track a non-existent file
        file_id = "missing-file"
        generated_files[file_id] = {
            "path": "/nonexistent/file.gltf",
            "created_at": datetime.utcnow() - timedelta(hours=25),
            "filename": "missing.gltf"
        }
        
        with patch("src.api.endpoints.logger") as mock_logger:
            cleanup_old_files()
            
            # Should log error but not crash
            mock_logger.error.assert_called()
            assert file_id not in generated_files


class TestEncryptionKey:
    """Test encryption key handling."""
    
    def test_development_key_warning(self):
        """Test warning is logged for development key."""
        with patch("src.api.endpoints.logger") as mock_logger:
            with patch.dict(os.environ, {}, clear=True):
                from src.api.endpoints import get_encryption_key
                key = get_encryption_key()
                
                assert key == "development-key-change-in-production"
                mock_logger.warning.assert_called_with(
                    "Using default encryption key - change this in production!"
                )
    
    def test_custom_encryption_key(self):
        """Test custom encryption key from environment."""
        with patch.dict(os.environ, {"API_ENCRYPTION_KEY": "custom-secret-key"}):
            from src.api.endpoints import get_encryption_key
            key = get_encryption_key()
            
            assert key == "custom-secret-key"


class TestBackgroundTasks:
    """Test background task execution."""
    
    @pytest.mark.asyncio
    async def test_cleanup_scheduled_on_create(
        self, client, encrypted_request, mock_carport_builder, mock_output_service
    ):
        """Test cleanup is scheduled when creating carport."""
        with tempfile.NamedTemporaryFile(delete=False) as tmp:
            mock_output_service.generate_output.return_value.file_path = Path(tmp.name)
            
            with patch("src.api.endpoints.cleanup_old_files") as mock_cleanup:
                response = client.post(
                    "/api/carport/create",
                    json=encrypted_request.model_dump()
                )
                
                assert response.status_code == 200
                # Cleanup should be scheduled but not necessarily executed immediately
                
            os.unlink(tmp.name)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])