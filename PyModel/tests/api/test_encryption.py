"""
Test suite for encryption service.

Tests AES encryption/decryption functionality.
"""

import pytest
import base64
import json
from src.services.encryption import AESEncryption


class TestAESEncryption:
    """Test AES encryption functionality."""
    
    def test_initialization(self):
        """Test encryption service initialization."""
        aes = AESEncryption("test-secret-key")
        assert aes is not None
        assert len(aes.key) == 32  # SHA256 produces 32 bytes
    
    def test_basic_encryption_decryption(self):
        """Test basic encrypt/decrypt round trip."""
        aes = AESEncryption("test-secret-key")
        
        plaintext = "Hello, World!"
        encrypted = aes.encrypt(plaintext)
        
        # Encrypted should be base64 string
        assert isinstance(encrypted, str)
        assert len(encrypted) > 0
        
        # Should be valid base64
        try:
            base64.b64decode(encrypted)
        except Exception:
            pytest.fail("Encrypted data is not valid base64")
        
        # Decrypt back
        decrypted = aes.decrypt(encrypted)
        assert decrypted == plaintext
    
    def test_json_encryption(self):
        """Test encrypting JSON data."""
        aes = AESEncryption("test-secret-key")
        
        data = {
            "building_type": "Carport",
            "span": 6000,
            "length": 6000,
            "height": 2700,
            "bays": 2
        }
        
        # Convert to JSON string
        json_str = json.dumps(data)
        
        # Encrypt
        encrypted = aes.encrypt(json_str)
        
        # Decrypt
        decrypted = aes.decrypt(encrypted)
        
        # Parse back to dict
        decrypted_data = json.loads(decrypted)
        
        assert decrypted_data == data
    
    def test_empty_string(self):
        """Test encrypting empty string."""
        aes = AESEncryption("test-secret-key")
        
        encrypted = aes.encrypt("")
        decrypted = aes.decrypt(encrypted)
        
        assert decrypted == ""
    
    def test_unicode_text(self):
        """Test encrypting unicode text."""
        aes = AESEncryption("test-secret-key")
        
        plaintext = "Hello 世界! 🌍"
        encrypted = aes.encrypt(plaintext)
        decrypted = aes.decrypt(encrypted)
        
        assert decrypted == plaintext
    
    def test_long_text(self):
        """Test encrypting longer text."""
        aes = AESEncryption("test-secret-key")
        
        # Create text longer than one AES block (16 bytes)
        plaintext = "A" * 1000
        
        encrypted = aes.encrypt(plaintext)
        decrypted = aes.decrypt(encrypted)
        
        assert decrypted == plaintext
        assert len(decrypted) == 1000
    
    def test_different_keys(self):
        """Test that different keys produce different results."""
        aes1 = AESEncryption("key1")
        aes2 = AESEncryption("key2")
        
        plaintext = "Secret message"
        
        encrypted1 = aes1.encrypt(plaintext)
        encrypted2 = aes2.encrypt(plaintext)
        
        # Different keys should produce different ciphertext
        assert encrypted1 != encrypted2
        
        # Should not be able to decrypt with wrong key
        with pytest.raises(Exception):
            aes2.decrypt(encrypted1)
    
    def test_same_key_different_iv(self):
        """Test that same key produces different ciphertext due to random IV."""
        aes = AESEncryption("test-secret-key")
        
        plaintext = "Test message"
        
        encrypted1 = aes.encrypt(plaintext)
        encrypted2 = aes.encrypt(plaintext)
        
        # Same plaintext should produce different ciphertext (due to random IV)
        assert encrypted1 != encrypted2
        
        # But both should decrypt to same plaintext
        assert aes.decrypt(encrypted1) == plaintext
        assert aes.decrypt(encrypted2) == plaintext
    
    def test_invalid_base64(self):
        """Test decrypting invalid base64."""
        aes = AESEncryption("test-secret-key")
        
        with pytest.raises(Exception):
            aes.decrypt("not-valid-base64!")
    
    def test_corrupted_ciphertext(self):
        """Test decrypting corrupted ciphertext."""
        aes = AESEncryption("test-secret-key")
        
        # Get valid encrypted data
        encrypted = aes.encrypt("Test")
        
        # Corrupt it by changing some characters
        corrupted = encrypted[:-4] + "XXXX"
        
        with pytest.raises(Exception):
            aes.decrypt(corrupted)
    
    def test_padding(self):
        """Test that padding works correctly for various lengths."""
        aes = AESEncryption("test-secret-key")
        
        # Test various lengths around block size (16 bytes)
        for length in [1, 15, 16, 17, 31, 32, 33]:
            plaintext = "X" * length
            encrypted = aes.encrypt(plaintext)
            decrypted = aes.decrypt(encrypted)
            
            assert decrypted == plaintext
            assert len(decrypted) == length
    
    def test_special_characters(self):
        """Test encrypting special characters."""
        aes = AESEncryption("test-secret-key")
        
        plaintext = "!@#$%^&*()_+-=[]{}|;':\",./<>?"
        encrypted = aes.encrypt(plaintext)
        decrypted = aes.decrypt(encrypted)
        
        assert decrypted == plaintext
    
    def test_compatibility_with_dotnet(self):
        """Test that encryption format is compatible with .NET implementation."""
        aes = AESEncryption("test-secret-key")
        
        # The encrypted format should be:
        # Base64(IV + Ciphertext)
        # Where IV is 16 bytes and uses CBC mode with PKCS7 padding
        
        plaintext = "Test .NET compatibility"
        encrypted = aes.encrypt(plaintext)
        
        # Decode base64
        encrypted_bytes = base64.b64decode(encrypted.encode())
        
        # First 16 bytes should be IV
        assert len(encrypted_bytes) >= 16
        
        # Remaining bytes should be multiple of 16 (AES block size)
        ciphertext_len = len(encrypted_bytes) - 16
        assert ciphertext_len % 16 == 0
        
        # Should decrypt successfully
        decrypted = aes.decrypt(encrypted)
        assert decrypted == plaintext


if __name__ == "__main__":
    pytest.main([__file__, "-v"])