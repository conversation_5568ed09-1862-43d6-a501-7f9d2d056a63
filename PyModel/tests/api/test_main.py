"""
Test suite for main FastAPI application.

Tests application setup, middleware, and system endpoints.
"""

import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, Mo<PERSON>, AsyncMock
import json
import os
from datetime import datetime

# Import after modifying path
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from src.api.main import app
from src.api.models import HealthResponse, ErrorResponse


@pytest.fixture
def client():
    """Create test client."""
    return TestClient(app)


@pytest.fixture
def mock_env():
    """Mock environment variables."""
    with patch.dict(os.environ, {
        "CORS_ORIGINS": "http://localhost:3000,http://example.com",
        "PORT": "8080",
        "ENV": "production"
    }):
        yield


class TestApp:
    """Test FastAPI app configuration."""
    
    def test_app_metadata(self):
        """Test app has correct metadata."""
        assert app.title == "BIM Backend API"
        assert app.version == "1.0.0"
        assert "Building Information Modeling API" in app.description
    
    def test_api_docs_configuration(self):
        """Test API documentation URLs."""
        assert app.docs_url == "/api/docs"
        assert app.redoc_url == "/api/redoc"
        assert app.openapi_url == "/api/openapi.json"
    
    def test_middleware_configured(self):
        """Test CORS middleware is configured."""
        # Check middleware stack
        middleware_types = [m.cls.__name__ for m in app.user_middleware]
        assert "CORSMiddleware" in str(middleware_types)


class TestRootEndpoint:
    """Test root endpoint."""
    
    def test_root_endpoint(self, client):
        """Test GET / endpoint."""
        response = client.get("/")
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "BIM Backend API"
        assert data["docs"] == "/api/docs"
    
    def test_root_not_in_schema(self, client):
        """Test root endpoint is not in OpenAPI schema."""
        response = client.get("/api/openapi.json")
        schema = response.json()
        assert "/" not in schema["paths"]


class TestHealthEndpoint:
    """Test health check endpoint."""
    
    def test_health_check_success(self, client):
        """Test successful health check."""
        response = client.get("/api/health")
        assert response.status_code == 200
        
        data = response.json()
        assert data["status"] == "healthy"
        assert data["version"] == "1.0.0"
        assert "timestamp" in data
        
        # Verify timestamp format
        timestamp = datetime.fromisoformat(data["timestamp"].rstrip("Z"))
        assert isinstance(timestamp, datetime)
    
    def test_health_response_model(self, client):
        """Test health response follows model."""
        response = client.get("/api/health")
        data = response.json()
        
        # Validate against model
        health = HealthResponse(**data)
        assert health.status == "healthy"
        assert health.version == "1.0.0"
    
    def test_health_in_schema(self, client):
        """Test health endpoint is in schema."""
        response = client.get("/api/openapi.json")
        schema = response.json()
        assert "/api/health" in schema["paths"]
        assert "get" in schema["paths"]["/api/health"]
        
        # Check tags
        health_spec = schema["paths"]["/api/health"]["get"]
        assert "System" in health_spec["tags"]


class TestGlobalExceptionHandler:
    """Test global exception handling."""
    
    def test_unhandled_exception(self, client):
        """Test unhandled exception returns proper error."""
        # Mock an endpoint that raises an exception
        @app.get("/test-error")
        async def test_error():
            raise ValueError("Test error")
        
        response = client.get("/test-error")
        assert response.status_code == 500
        
        data = response.json()
        assert data["error"] == "internal_server_error"
        assert data["message"] == "An unexpected error occurred"
        assert data["details"]["type"] == "ValueError"
    
    def test_exception_with_request_id(self, client):
        """Test exception handler includes request ID if present."""
        @app.get("/test-error-with-id")
        async def test_error_with_id():
            raise RuntimeError("Test error with ID")
        
        response = client.get(
            "/test-error-with-id",
            headers={"X-Request-ID": "req_12345"}
        )
        assert response.status_code == 500
        
        data = response.json()
        assert data["request_id"] == "req_12345"
    
    def test_exception_handler_logging(self, client):
        """Test exception handler logs errors."""
        @app.get("/test-logging")
        async def test_logging():
            raise KeyError("Test logging")
        
        with patch("src.api.main.logger") as mock_logger:
            response = client.get("/test-logging")
            assert response.status_code == 500
            
            # Verify logging
            mock_logger.error.assert_called()
            args = mock_logger.error.call_args[0]
            assert "Unhandled exception" in args[0]


class TestStartupShutdown:
    """Test startup and shutdown events."""
    
    @pytest.mark.asyncio
    async def test_startup_event(self):
        """Test startup event handler."""
        with patch("src.api.main.logger") as mock_logger:
            # Manually trigger startup
            for handler in app.router.on_startup:
                await handler()
            
            # Verify logging
            mock_logger.info.assert_any_call("BIM Backend API starting up...")
            mock_logger.info.assert_any_call("BIM Backend API started successfully")
    
    @pytest.mark.asyncio
    async def test_shutdown_event(self):
        """Test shutdown event handler."""
        with patch("src.api.main.logger") as mock_logger:
            # Manually trigger shutdown
            for handler in app.router.on_shutdown:
                await handler()
            
            # Verify logging
            mock_logger.info.assert_any_call("BIM Backend API shutting down...")
            mock_logger.info.assert_any_call("BIM Backend API shutdown complete")


class TestCORSConfiguration:
    """Test CORS middleware configuration."""
    
    def test_cors_headers_default(self, client):
        """Test CORS headers with default origins."""
        response = client.options(
            "/api/health",
            headers={
                "Origin": "http://localhost:3000",
                "Access-Control-Request-Method": "GET"
            }
        )
        
        assert response.status_code == 200
        assert "access-control-allow-origin" in response.headers
    
    def test_cors_with_env_origins(self, client, mock_env):
        """Test CORS uses environment variable origins."""
        # Would need to recreate app with new env to fully test
        # This is a limitation of FastAPI's startup configuration
        pass
    
    def test_cors_allows_all_methods(self, client):
        """Test CORS allows all HTTP methods."""
        response = client.options(
            "/api/health",
            headers={
                "Origin": "http://localhost:3000",
                "Access-Control-Request-Method": "POST"
            }
        )
        
        assert response.status_code == 200
        assert "access-control-allow-methods" in response.headers


class TestAPIRouter:
    """Test API router integration."""
    
    def test_router_included(self, client):
        """Test endpoints router is included."""
        # Test that carport endpoints exist
        response = client.get("/api/openapi.json")
        schema = response.json()
        
        # Should have carport endpoints from included router
        assert any("/carport" in path for path in schema["paths"])
    
    def test_api_prefix(self, client):
        """Test all API endpoints use /api prefix."""
        response = client.get("/api/openapi.json")
        schema = response.json()
        
        # All paths except docs should start with /api
        for path in schema["paths"]:
            if path not in ["/", "/api/openapi.json"]:
                assert path.startswith("/api/")


class TestMainModule:
    """Test main module execution."""
    
    def test_main_module_execution(self):
        """Test main module can be executed."""
        with patch("uvicorn.run") as mock_run:
            with patch.dict(os.environ, {"PORT": "9000", "ENV": "production"}):
                # Import and execute main block
                import src.api.main
                if __name__ == "__main__":
                    # This would be executed if running as main
                    import uvicorn
                    uvicorn.run(
                        "api.main:app",
                        host="0.0.0.0",
                        port=9000,
                        reload=False,
                        log_level="info"
                    )
            
            # In actual execution, verify uvicorn.run would be called
            # with correct parameters


class TestOpenAPISchema:
    """Test OpenAPI schema generation."""
    
    def test_openapi_schema_accessible(self, client):
        """Test OpenAPI schema is accessible."""
        response = client.get("/api/openapi.json")
        assert response.status_code == 200
        
        schema = response.json()
        assert schema["openapi"].startswith("3.")
        assert schema["info"]["title"] == "BIM Backend API"
        assert schema["info"]["version"] == "1.0.0"
    
    def test_swagger_ui_accessible(self, client):
        """Test Swagger UI is accessible."""
        response = client.get("/api/docs")
        assert response.status_code == 200
        assert "swagger-ui" in response.text.lower()
    
    def test_redoc_accessible(self, client):
        """Test ReDoc is accessible."""
        response = client.get("/api/redoc")
        assert response.status_code == 200
        assert "redoc" in response.text.lower()


class TestLogging:
    """Test logging configuration."""
    
    def test_logging_configured(self):
        """Test logging is properly configured."""
        import logging
        import src.api.main
        
        # Get logger
        logger = logging.getLogger("src.api.main")
        
        # Should have handlers configured
        root_logger = logging.getLogger()
        assert len(root_logger.handlers) > 0
        
        # Should use INFO level
        assert root_logger.level <= logging.INFO


if __name__ == "__main__":
    pytest.main([__file__, "-v"])