"""
Comprehensive GLB/GLTF Output Test Suite
Tests both functional correctness and output accuracy for GLB/GLTF generation
Ensures feature parity with C# implementation
"""

import pytest
import asyncio
import json
import struct
import math
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
import base64

from src.bim.shed_bim import ShedBimModel
from src.bim.components import (
    Frame, Sheet, Bracket, Opening, Flashing, 
    Downpipe, BracketType, OpeningType
)
from src.bim.accessories import Stairs
from src.bim.mezzanine import Mezzanine
from src.business.building_input import BuildingInput
from src.business.structure_builder import StructureBuilder
from src.geometry import Vec3, Mat4, Quaternion, Box3
from src.materials import (
    FrameMaterial, SheetMaterial, ColorMaterial, VisualMaterial,
    MaterialType, SteelGrade, Profile
)
from src.output.gltf_generator import GltfGenerator


class TestGlbComprehensive:
    """Comprehensive GLB/GLTF output tests covering all functionality"""
    
    @pytest.fixture
    def gltf_generator(self):
        """Create GLTF generator instance"""
        return GltfGenerator()
    
    @pytest.fixture
    def simple_box_model(self):
        """Create a simple box model for testing"""
        model = ShedBimModel()
        
        # Add a simple box sheet
        model.sheets.append(Sheet(
            id="BOX1",
            boundary=[
                Vec3(0, 0, 0),
                Vec3(1000, 0, 0),
                Vec3(1000, 1000, 0),
                Vec3(0, 1000, 0)
            ],
            material=SheetMaterial(
                base_color=(255, 0, 0),  # Red
                thickness=10,
                metallic=0.0,
                roughness=0.8
            ),
            type="Floor"
        ))
        
        return model
    
    @pytest.fixture
    def complex_model(self):
        """Create a complex model with various elements"""
        building_input = BuildingInput(
            width=10000,
            length=15000,
            eave_height=4000,
            roof_pitch=10,
            bays=3,
            structural_system="Portal Frame",
            roof_type="Gable"
        )
        
        builder = StructureBuilder()
        model = builder.create_structure(building_input)
        
        # Add brackets
        model.brackets.append(Bracket(
            id="BRACKET1",
            type=BracketType.APEX_STANDARD,
            position=Vec3(5000, 0, 4000),
            rotation=Mat4.identity(),
            material=ColorMaterial(color=(150, 150, 150))
        ))
        
        # Add doors with animation
        model.openings.append(Opening(
            id="DOOR1",
            type=OpeningType.ROLLER_DOOR,
            position=Vec3(2000, 0, 0),
            width=3000,
            height=3500,
            orientation=0,
            animation_enabled=True
        ))
        
        return model
    
    # ===== FUNCTIONAL TESTS =====
    
    async def test_glb_structure(self, gltf_generator, simple_box_model):
        """Test GLB binary structure"""
        result = await gltf_generator.generate(simple_box_model, format="GLB")
        content = result.content
        
        # Check GLB header
        assert len(content) >= 12
        magic = struct.unpack('<I', content[0:4])[0]
        assert magic == 0x46546C67  # 'glTF' in little-endian
        
        version = struct.unpack('<I', content[4:8])[0]
        assert version == 2
        
        total_length = struct.unpack('<I', content[8:12])[0]
        assert total_length == len(content)
        
        # Check JSON chunk
        json_chunk_length = struct.unpack('<I', content[12:16])[0]
        json_chunk_type = struct.unpack('<I', content[16:20])[0]
        assert json_chunk_type == 0x4E4F534A  # 'JSON'
        
        # Parse JSON
        json_data = content[20:20+json_chunk_length].decode('utf-8')
        gltf = json.loads(json_data)
        
        # Validate GLTF structure
        assert "asset" in gltf
        assert gltf["asset"]["version"] == "2.0"
        assert "scenes" in gltf
        assert "nodes" in gltf
        assert "meshes" in gltf
        assert "materials" in gltf
        assert "accessors" in gltf
        assert "bufferViews" in gltf
        assert "buffers" in gltf
    
    async def test_gltf_text_format(self, gltf_generator, simple_box_model):
        """Test GLTF text format generation"""
        result = await gltf_generator.generate(simple_box_model, format="GLTF")
        content = result.content
        
        # Should be valid JSON
        gltf = json.loads(content)
        
        # Check required properties
        assert gltf["asset"]["version"] == "2.0"
        assert "generator" in gltf["asset"]
        assert "Python BIM" in gltf["asset"]["generator"]
        
        # Check buffers use data URIs
        assert len(gltf["buffers"]) > 0
        buffer_uri = gltf["buffers"][0]["uri"]
        assert buffer_uri.startswith("data:application/octet-stream;base64,")
    
    async def test_scene_hierarchy(self, gltf_generator, complex_model):
        """Test scene node hierarchy"""
        result = await gltf_generator.generate(complex_model, format="GLTF")
        gltf = json.loads(result.content)
        
        # Check scene structure
        assert len(gltf["scenes"]) == 1
        scene = gltf["scenes"][0]
        assert "nodes" in scene
        
        # Get root nodes
        root_nodes = [gltf["nodes"][i] for i in scene["nodes"]]
        
        # Should have organized hierarchy
        node_names = [node.get("name", "") for node in gltf["nodes"]]
        
        # Check for expected groups
        assert any("Frame" in name or "Structure" in name for name in node_names)
        assert any("Sheet" in name or "Cladding" in name for name in node_names)
        
        # Verify parent-child relationships
        for node in gltf["nodes"]:
            if "children" in node:
                for child_idx in node["children"]:
                    assert 0 <= child_idx < len(gltf["nodes"])
    
    async def test_coordinate_system_conversion(self, gltf_generator):
        """Test Y-up coordinate system conversion"""
        model = ShedBimModel()
        
        # Add vertical column (Z-up in source)
        model.frames.append(Frame(
            id="VERT_COL",
            start=Vec3(0, 0, 0),
            end=Vec3(0, 0, 3000),  # 3m in Z
            material=FrameMaterial(
                profile=Profile.C10015,
                grade=SteelGrade.G250,
                thickness=1.5
            ),
            type="Column"
        ))
        
        result = await gltf_generator.generate(model, format="GLTF")
        gltf = json.loads(result.content)
        
        # Find the column node
        column_node = None
        for node in gltf["nodes"]:
            if "VERT_COL" in node.get("name", ""):
                column_node = node
                break
        
        assert column_node is not None
        
        # Check transformation
        if "matrix" in column_node:
            matrix = column_node["matrix"]
            # Should have Y-up transformation applied
            # Original Z-axis should now point to Y
            assert len(matrix) == 16
    
    async def test_materials_generation(self, gltf_generator):
        """Test PBR material generation"""
        model = ShedBimModel()
        
        # Add sheets with different materials
        materials = [
            SheetMaterial(base_color=(255, 0, 0), metallic=1.0, roughness=0.1),  # Shiny red metal
            SheetMaterial(base_color=(0, 255, 0), metallic=0.0, roughness=1.0),  # Rough green
            SheetMaterial(base_color=(0, 0, 255), metallic=0.5, roughness=0.5, alpha=0.5),  # Transparent blue
        ]
        
        for i, mat in enumerate(materials):
            model.sheets.append(Sheet(
                id=f"SHEET{i}",
                boundary=[
                    Vec3(i*1000, 0, 0),
                    Vec3((i+1)*1000, 0, 0),
                    Vec3((i+1)*1000, 1000, 0),
                    Vec3(i*1000, 1000, 0)
                ],
                material=mat,
                type="Wall"
            ))
        
        result = await gltf_generator.generate(model, format="GLTF")
        gltf = json.loads(result.content)
        
        # Check materials
        assert len(gltf["materials"]) >= 3
        
        # Check PBR properties
        for i, mat in enumerate(gltf["materials"][:3]):
            assert "pbrMetallicRoughness" in mat
            pbr = mat["pbrMetallicRoughness"]
            
            # Check base color
            assert "baseColorFactor" in pbr
            color = pbr["baseColorFactor"]
            assert len(color) == 4  # RGBA
            
            # Check metallic/roughness
            assert "metallicFactor" in pbr
            assert "roughnessFactor" in pbr
            
            # Check transparency
            if i == 2:  # Third material
                assert mat.get("alphaMode") == "BLEND"
                assert color[3] == 0.5
    
    async def test_mesh_generation_from_profiles(self, gltf_generator):
        """Test mesh generation from structural profiles"""
        model = ShedBimModel()
        
        # Add C-section column
        model.frames.append(Frame(
            id="C_SECTION",
            start=Vec3(0, 0, 0),
            end=Vec3(0, 0, 3000),
            material=FrameMaterial(
                profile=Profile.C15015,  # 150mm C-section
                grade=SteelGrade.G250,
                thickness=1.5
            ),
            type="Column"
        ))
        
        result = await gltf_generator.generate(model, format="GLTF")
        gltf = json.loads(result.content)
        
        # Find mesh
        assert len(gltf["meshes"]) > 0
        mesh = gltf["meshes"][0]
        
        # Check primitives
        assert "primitives" in mesh
        primitive = mesh["primitives"][0]
        
        # Check required attributes
        assert "attributes" in primitive
        assert "POSITION" in primitive["attributes"]
        assert "NORMAL" in primitive["attributes"]
        
        # Verify accessor references
        pos_accessor_idx = primitive["attributes"]["POSITION"]
        assert 0 <= pos_accessor_idx < len(gltf["accessors"])
        
        # Check that C-section has proper geometry
        pos_accessor = gltf["accessors"][pos_accessor_idx]
        assert pos_accessor["count"] > 8  # C-section needs multiple vertices
    
    async def test_cladding_mesh_triangulation(self, gltf_generator):
        """Test cladding mesh generation with profile"""
        model = ShedBimModel()
        
        # Add corrugated sheet
        model.sheets.append(Sheet(
            id="CORRUGATED",
            boundary=[
                Vec3(0, 0, 0),
                Vec3(5000, 0, 0),
                Vec3(5000, 0, 3000),
                Vec3(0, 0, 3000)
            ],
            material=SheetMaterial(
                base_color=(100, 100, 100),
                thickness=0.4,
                profile="Corrugated",
                profile_spacing=150,  # 150mm rib spacing
                profile_height=20     # 20mm rib height
            ),
            type="Wall"
        ))
        
        result = await gltf_generator.generate(model, format="GLTF")
        gltf = json.loads(result.content)
        
        # Find cladding mesh
        cladding_mesh = None
        for i, node in enumerate(gltf["nodes"]):
            if "CORRUGATED" in node.get("name", ""):
                if "mesh" in node:
                    cladding_mesh = gltf["meshes"][node["mesh"]]
                    break
        
        assert cladding_mesh is not None
        
        # Check that mesh has many triangles for corrugation
        primitive = cladding_mesh["primitives"][0]
        pos_accessor = gltf["accessors"][primitive["attributes"]["POSITION"]]
        
        # Corrugated profile should have many vertices
        assert pos_accessor["count"] > 100
    
    async def test_bracket_mesh_generation(self, gltf_generator, complex_model):
        """Test bracket mesh generation from 3D models"""
        result = await gltf_generator.generate(complex_model, format="GLTF")
        gltf = json.loads(result.content)
        
        # Find bracket mesh
        bracket_found = False
        for node in gltf["nodes"]:
            if "BRACKET" in node.get("name", ""):
                bracket_found = True
                if "mesh" in node:
                    mesh = gltf["meshes"][node["mesh"]]
                    # Brackets should have complex geometry
                    primitive = mesh["primitives"][0]
                    pos_accessor = gltf["accessors"][primitive["attributes"]["POSITION"]]
                    assert pos_accessor["count"] > 20  # Complex shape
                break
        
        assert bracket_found
    
    async def test_animations(self, gltf_generator, complex_model):
        """Test animation generation for doors/windows"""
        result = await gltf_generator.generate(
            complex_model, 
            format="GLTF",
            include_animations=True
        )
        gltf = json.loads(result.content)
        
        # Check for animations
        assert "animations" in gltf
        assert len(gltf["animations"]) > 0
        
        animation = gltf["animations"][0]
        assert "channels" in animation
        assert "samplers" in animation
        
        # Check animation targets doors
        for channel in animation["channels"]:
            target = channel["target"]
            assert "node" in target
            assert "path" in target
            assert target["path"] in ["translation", "rotation", "scale"]
    
    async def test_unity_mode(self, gltf_generator, simple_box_model):
        """Test Unity-specific mode"""
        result = await gltf_generator.generate(
            simple_box_model, 
            format="GLB",
            unity_mode=True
        )
        
        # Extract JSON from GLB
        content = result.content
        json_length = struct.unpack('<I', content[12:16])[0]
        json_data = content[20:20+json_length].decode('utf-8')
        gltf = json.loads(json_data)
        
        # Check for Unity markers
        unity_markers = False
        for node in gltf["nodes"]:
            if "extras" in node:
                if "unity" in str(node["extras"]).lower():
                    unity_markers = True
                    break
        
        # Unity mode should adjust coordinates or add metadata
        assert unity_markers or any("Unity" in node.get("name", "") for node in gltf["nodes"])
    
    async def test_customer_mode(self, gltf_generator, complex_model):
        """Test customer-facing simplified mode"""
        # Generate normal version
        normal_result = await gltf_generator.generate(complex_model, format="GLB")
        normal_size = len(normal_result.content)
        
        # Generate customer version
        customer_result = await gltf_generator.generate(
            complex_model, 
            format="GLB",
            customer_mode=True
        )
        customer_size = len(customer_result.content)
        
        # Customer mode should be smaller
        assert customer_size < normal_size
        
        # Extract and check content
        json_length = struct.unpack('<I', customer_result.content[12:16])[0]
        json_data = customer_result.content[20:20+json_length].decode('utf-8')
        gltf = json.loads(json_data)
        
        # Should have fewer materials (consolidated)
        assert len(gltf["materials"]) < 20  # Reasonable limit
    
    # ===== ACCURACY TESTS =====
    
    async def test_geometry_precision(self, gltf_generator):
        """Test vertex position precision"""
        model = ShedBimModel()
        
        # Add precisely positioned box
        precise_positions = [
            Vec3(1234.567, 2345.678, 0),
            Vec3(2234.567, 2345.678, 0),
            Vec3(2234.567, 3345.678, 0),
            Vec3(1234.567, 3345.678, 0)
        ]
        
        model.sheets.append(Sheet(
            id="PRECISE_BOX",
            boundary=precise_positions,
            material=SheetMaterial(base_color=(255, 255, 255), thickness=10),
            type="Floor"
        ))
        
        result = await gltf_generator.generate(model, format="GLTF")
        gltf = json.loads(result.content)
        
        # Find the mesh
        mesh = gltf["meshes"][0]
        primitive = mesh["primitives"][0]
        pos_accessor_idx = primitive["attributes"]["POSITION"]
        pos_accessor = gltf["accessors"][pos_accessor_idx]
        
        # Get buffer view
        buffer_view = gltf["bufferViews"][pos_accessor["bufferView"]]
        buffer = gltf["buffers"][buffer_view["buffer"]]
        
        # Decode base64 data
        uri = buffer["uri"]
        base64_data = uri.split(",")[1]
        binary_data = base64.b64decode(base64_data)
        
        # Read positions
        offset = buffer_view.get("byteOffset", 0) + pos_accessor.get("byteOffset", 0)
        positions = []
        for i in range(pos_accessor["count"]):
            x = struct.unpack('<f', binary_data[offset:offset+4])[0]
            y = struct.unpack('<f', binary_data[offset+4:offset+8])[0]
            z = struct.unpack('<f', binary_data[offset+8:offset+12])[0]
            positions.append((x, y, z))
            offset += 12
        
        # Check precision (within float32 precision)
        tolerance = 0.001  # 1mm
        for expected in precise_positions:
            found = False
            for pos in positions:
                # Account for Y-up conversion
                if (abs(pos[0] - expected.x) < tolerance and
                    abs(pos[2] - expected.y) < tolerance):  # Y becomes Z
                    found = True
                    break
            assert found, f"Position {expected} not found within tolerance"
    
    async def test_normal_generation_accuracy(self, gltf_generator):
        """Test normal vector accuracy"""
        model = ShedBimModel()
        
        # Add a flat horizontal surface
        model.sheets.append(Sheet(
            id="FLAT_SURFACE",
            boundary=[
                Vec3(0, 0, 1000),
                Vec3(1000, 0, 1000),
                Vec3(1000, 1000, 1000),
                Vec3(0, 1000, 1000)
            ],
            material=SheetMaterial(base_color=(128, 128, 128), thickness=10),
            type="Floor"
        ))
        
        result = await gltf_generator.generate(model, format="GLTF")
        gltf = json.loads(result.content)
        
        # Get normals
        mesh = gltf["meshes"][0]
        primitive = mesh["primitives"][0]
        norm_accessor_idx = primitive["attributes"]["NORMAL"]
        norm_accessor = gltf["accessors"][norm_accessor_idx]
        
        # Decode normals
        buffer_view = gltf["bufferViews"][norm_accessor["bufferView"]]
        buffer = gltf["buffers"][buffer_view["buffer"]]
        base64_data = buffer["uri"].split(",")[1]
        binary_data = base64.b64decode(base64_data)
        
        offset = buffer_view.get("byteOffset", 0) + norm_accessor.get("byteOffset", 0)
        
        # All normals should point up (0, 1, 0) in Y-up system
        for i in range(norm_accessor["count"]):
            nx = struct.unpack('<f', binary_data[offset:offset+4])[0]
            ny = struct.unpack('<f', binary_data[offset+4:offset+8])[0]
            nz = struct.unpack('<f', binary_data[offset+8:offset+12])[0]
            offset += 12
            
            # Should be close to (0, 1, 0)
            assert abs(nx) < 0.01
            assert abs(ny - 1.0) < 0.01
            assert abs(nz) < 0.01
    
    async def test_color_accuracy(self, gltf_generator):
        """Test color conversion accuracy"""
        model = ShedBimModel()
        
        # Test specific colors
        test_colors = [
            (255, 0, 0),      # Pure red
            (0, 255, 0),      # Pure green
            (0, 0, 255),      # Pure blue
            (128, 128, 128),  # Mid gray
            (255, 128, 64),   # Orange
        ]
        
        for i, color in enumerate(test_colors):
            model.sheets.append(Sheet(
                id=f"COLOR_TEST_{i}",
                boundary=[
                    Vec3(i*100, 0, 0),
                    Vec3((i+1)*100, 0, 0),
                    Vec3((i+1)*100, 100, 0),
                    Vec3(i*100, 100, 0)
                ],
                material=SheetMaterial(base_color=color, thickness=1),
                type="Wall"
            ))
        
        result = await gltf_generator.generate(model, format="GLTF")
        gltf = json.loads(result.content)
        
        # Check each material color
        for i, (r, g, b) in enumerate(test_colors):
            if i < len(gltf["materials"]):
                mat = gltf["materials"][i]
                pbr = mat["pbrMetallicRoughness"]
                color = pbr["baseColorFactor"]
                
                # Check color conversion (0-255 to 0-1)
                assert abs(color[0] - r/255.0) < 0.01
                assert abs(color[1] - g/255.0) < 0.01
                assert abs(color[2] - b/255.0) < 0.01
                assert color[3] == 1.0  # Alpha
    
    async def test_material_deduplication(self, gltf_generator):
        """Test material caching and deduplication"""
        model = ShedBimModel()
        
        # Add multiple sheets with same material
        same_material = SheetMaterial(
            base_color=(200, 100, 50),
            metallic=0.8,
            roughness=0.2,
            thickness=0.5
        )
        
        for i in range(5):
            model.sheets.append(Sheet(
                id=f"SHEET_{i}",
                boundary=[
                    Vec3(i*1000, 0, 0),
                    Vec3((i+1)*1000, 0, 0),
                    Vec3((i+1)*1000, 1000, 0),
                    Vec3(i*1000, 1000, 0)
                ],
                material=same_material,
                type="Wall"
            ))
        
        result = await gltf_generator.generate(model, format="GLTF")
        gltf = json.loads(result.content)
        
        # Should have only one material
        unique_materials = []
        for mat in gltf["materials"]:
            pbr = mat["pbrMetallicRoughness"]
            mat_sig = (
                tuple(pbr["baseColorFactor"]),
                pbr["metallicFactor"],
                pbr["roughnessFactor"]
            )
            if mat_sig not in unique_materials:
                unique_materials.append(mat_sig)
        
        assert len(unique_materials) == 1
    
    async def test_binary_alignment(self, gltf_generator, simple_box_model):
        """Test GLB binary chunk alignment"""
        result = await gltf_generator.generate(simple_box_model, format="GLB")
        content = result.content
        
        # Check JSON chunk padding
        json_chunk_length = struct.unpack('<I', content[12:16])[0]
        json_chunk_end = 20 + json_chunk_length
        
        # Should be padded to 4-byte boundary
        assert json_chunk_end % 4 == 0
        
        # If there's a binary chunk
        if len(content) > json_chunk_end:
            # Check binary chunk alignment
            bin_chunk_length = struct.unpack('<I', content[json_chunk_end:json_chunk_end+4])[0]
            bin_chunk_type = struct.unpack('<I', content[json_chunk_end+4:json_chunk_end+8])[0]
            assert bin_chunk_type == 0x004E4942  # 'BIN\0'
            
            # Binary chunk should also be aligned
            assert (json_chunk_end + 8 + bin_chunk_length) % 4 == 0
    
    # ===== PERFORMANCE TESTS =====
    
    async def test_large_model_performance(self, gltf_generator):
        """Test performance with large model"""
        import time
        
        model = ShedBimModel()
        
        # Create large model with many elements
        for i in range(50):
            for j in range(50):
                model.sheets.append(Sheet(
                    id=f"TILE_{i}_{j}",
                    boundary=[
                        Vec3(i*100, j*100, 0),
                        Vec3((i+1)*100, j*100, 0),
                        Vec3((i+1)*100, (j+1)*100, 0),
                        Vec3(i*100, (j+1)*100, 0)
                    ],
                    material=SheetMaterial(
                        base_color=(i*5, j*5, 128),
                        thickness=1
                    ),
                    type="Floor"
                ))
        
        start_time = time.time()
        result = await gltf_generator.generate(model, format="GLB")
        end_time = time.time()
        
        assert result.success
        generation_time = end_time - start_time
        assert generation_time < 5.0  # Should complete within 5 seconds
        
        # Check file size is reasonable
        file_size = len(result.content)
        assert file_size < 50 * 1024 * 1024  # Less than 50MB
    
    async def test_mesh_optimization(self, gltf_generator):
        """Test mesh optimization and deduplication"""
        model = ShedBimModel()
        
        # Add many identical columns
        for i in range(20):
            model.frames.append(Frame(
                id=f"COL_{i}",
                start=Vec3(i*3000, 0, 0),
                end=Vec3(i*3000, 0, 3000),
                material=FrameMaterial(
                    profile=Profile.C10015,
                    grade=SteelGrade.G250,
                    thickness=1.5
                ),
                type="Column"
            ))
        
        result = await gltf_generator.generate(model, format="GLTF")
        gltf = json.loads(result.content)
        
        # Should reuse mesh for identical columns
        unique_meshes = set()
        for node in gltf["nodes"]:
            if "mesh" in node:
                unique_meshes.add(node["mesh"])
        
        # Should have fewer unique meshes than columns
        assert len(unique_meshes) < 20
    
    # ===== INTEGRATION TESTS =====
    
    async def test_complete_building_export(self, gltf_generator):
        """Test complete building export to GLB"""
        # Create full building
        building_input = BuildingInput(
            width=12000,
            length=24000,
            eave_height=5000,
            roof_pitch=12,
            bays=4,
            structural_system="Portal Frame",
            roof_type="Gable",
            wall_cladding="Colorsteel",
            roof_cladding="Colorsteel"
        )
        
        builder = StructureBuilder()
        model = builder.create_structure(building_input)
        
        # Add various elements
        model.openings.append(Opening(
            id="MAIN_DOOR",
            type=OpeningType.ROLLER_DOOR,
            position=Vec3(6000, 0, 0),
            width=4000,
            height=4000,
            orientation=0
        ))
        
        result = await gltf_generator.generate(model, format="GLB")
        
        assert result.success
        assert len(result.content) > 100000  # Substantial file
        
        # Validate GLB structure
        magic = struct.unpack('<I', result.content[0:4])[0]
        assert magic == 0x46546C67
        
        # Extract and validate JSON
        json_length = struct.unpack('<I', result.content[12:16])[0]
        json_data = result.content[20:20+json_length].decode('utf-8')
        gltf = json.loads(json_data)
        
        # Check all element types present
        node_names = [node.get("name", "") for node in gltf["nodes"]]
        assert any("Column" in name or "Frame" in name for name in node_names)
        assert any("Beam" in name or "Rafter" in name for name in node_names)
        assert any("Wall" in name or "Sheet" in name for name in node_names)
        assert any("Door" in name for name in node_names)
    
    async def test_viewer_compatibility(self, gltf_generator, complex_model):
        """Test output compatibility with standard viewers"""
        result = await gltf_generator.generate(complex_model, format="GLTF")
        gltf = json.loads(result.content)
        
        # Check for viewer compatibility requirements
        
        # 1. Must have at least one scene
        assert len(gltf["scenes"]) >= 1
        assert "scene" in gltf  # Default scene
        
        # 2. All referenced indices must be valid
        for scene in gltf["scenes"]:
            for node_idx in scene.get("nodes", []):
                assert 0 <= node_idx < len(gltf["nodes"])
        
        # 3. Materials must have valid PBR
        for material in gltf["materials"]:
            assert "pbrMetallicRoughness" in material
            pbr = material["pbrMetallicRoughness"]
            if "baseColorFactor" in pbr:
                assert len(pbr["baseColorFactor"]) == 4
                assert all(0 <= v <= 1 for v in pbr["baseColorFactor"])
        
        # 4. Accessors must have valid types
        valid_types = ["SCALAR", "VEC2", "VEC3", "VEC4", "MAT2", "MAT3", "MAT4"]
        for accessor in gltf["accessors"]:
            assert accessor["type"] in valid_types
            assert accessor["componentType"] in [5120, 5121, 5122, 5123, 5125, 5126]
        
        # 5. Buffer views must be within buffer bounds
        for buffer_view in gltf["bufferViews"]:
            buffer = gltf["buffers"][buffer_view["buffer"]]
            assert buffer_view["byteOffset"] + buffer_view["byteLength"] <= buffer["byteLength"]


# Run tests
if __name__ == "__main__":
    pytest.main([__file__, "-v"])