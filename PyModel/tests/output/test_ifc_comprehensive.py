"""
Comprehensive IFC Output Test Suite
Tests both functional correctness and output accuracy for IFC generation
Ensures feature parity with C# implementation
"""

import pytest
import asyncio
import json
import math
import re
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime
import numpy as np

from src.bim.shed_bim import ShedBimModel
from src.bim.components import (
    Frame, Sheet, Bracket, Opening, Flashing, 
    Downpipe, BracketType, OpeningType
)
from src.bim.accessories import Stairs
from src.bim.mezzanine import Mezzanine
from src.business.building_input import BuildingInput
from src.business.structure_builder import StructureBuilder
from src.business.engineering import EngineeringService
from src.geometry import Vec3, Mat4, Plane, Box3
from src.materials import (
    FrameMaterial, SheetMaterial, ColorMaterial,
    MaterialType, SteelGrade, Profile
)
from src.output.ifc_generator import IfcGenerator


class TestIfcComprehensive:
    """Comprehensive IFC output tests covering all functionality"""
    
    @pytest.fixture
    def ifc_generator(self):
        """Create IFC generator instance"""
        return IfcGenerator()
    
    @pytest.fixture
    def simple_shed(self):
        """Create a simple shed model for testing"""
        model = ShedBimModel()
        
        # Add basic frame
        model.frames.append(Frame(
            id="COL1",
            start=Vec3(0, 0, 0),
            end=Vec3(0, 0, 3000),
            material=FrameMaterial(
                profile=Profile.C10015,
                grade=SteelGrade.G250,
                thickness=1.5
            ),
            type="Column"
        ))
        
        model.frames.append(Frame(
            id="BEAM1",
            start=Vec3(0, 0, 3000),
            end=Vec3(5000, 0, 3000),
            material=FrameMaterial(
                profile=Profile.C10015,
                grade=SteelGrade.G250,
                thickness=1.5
            ),
            type="Beam"
        ))
        
        return model
    
    @pytest.fixture
    def complex_shed(self):
        """Create a complex shed model with all element types"""
        # Use structure builder to create full model
        building_input = BuildingInput(
            width=12000,
            length=20000,
            eave_height=4500,
            roof_pitch=10,
            bays=4,
            structural_system="Portal Frame",
            roof_type="Gable",
            wall_cladding="Colorsteel",
            roof_cladding="Colorsteel"
        )
        
        builder = StructureBuilder()
        model = builder.create_structure(building_input)
        
        # Add doors
        model.openings.append(Opening(
            id="DOOR1",
            type=OpeningType.PA_DOOR,
            position=Vec3(2000, 0, 0),
            width=900,
            height=2100,
            orientation=0
        ))
        
        # Add windows
        model.openings.append(Opening(
            id="WIN1",
            type=OpeningType.WINDOW_SLIDING,
            position=Vec3(5000, 0, 1200),
            width=1800,
            height=1200,
            orientation=0
        ))
        
        # Add roller door
        model.openings.append(Opening(
            id="ROLLER1",
            type=OpeningType.ROLLER_DOOR,
            position=Vec3(6000, 0, 0),
            width=3600,
            height=3600,
            orientation=0
        ))
        
        return model
    
    # ===== FUNCTIONAL TESTS =====
    
    async def test_ifc_file_structure(self, ifc_generator, simple_shed):
        """Test basic IFC file structure and headers"""
        # Test IFC4 generation
        result = await ifc_generator.generate(simple_shed, format="IFC4")
        content = result.content
        
        # Check file structure
        assert content.startswith("ISO-10303-21;")
        assert "HEADER;" in content
        assert "DATA;" in content
        assert "ENDSEC;" in content
        assert "END-ISO-10303-21;" in content
        
        # Check schema
        assert "FILE_SCHEMA(('IFC4'));" in content
        
        # Check header info
        assert "FILE_DESCRIPTION" in content
        assert "FILE_NAME" in content
        assert "ViewDefinition [CoordinationView]" in content
        
        # Test author and organization
        header_match = re.search(r"FILE_NAME\((.*?)\);", content, re.DOTALL)
        assert header_match
        assert "Python BIM Generator" in header_match.group(1)
        assert "Your Organization" in header_match.group(1)
        
        # Test timestamp format
        timestamp_match = re.search(r"'\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}'", content)
        assert timestamp_match
    
    async def test_project_hierarchy(self, ifc_generator, simple_shed):
        """Test IFC project spatial hierarchy"""
        result = await ifc_generator.generate(simple_shed, format="IFC4")
        content = result.content
        
        # Check project
        assert "IFCPROJECT(" in content
        project_match = re.search(r"IFCPROJECT\('([^']+)'", content)
        assert project_match
        project_guid = project_match.group(1)
        assert len(project_guid) == 22  # IFC GUID length
        
        # Check units
        assert "IFCUNITASSIGNMENT(" in content
        assert "IFCSIUNIT(*,.LENGTHUNIT.,.MILLI.,.METRE.)" in content
        assert "IFCSIUNIT(*,.AREAUNIT.,$,.SQUARE_METRE.)" in content
        assert "IFCSIUNIT(*,.VOLUMEUNIT.,$,.CUBIC_METRE.)" in content
        
        # Check site
        assert "IFCSITE(" in content
        site_match = re.search(r"IFCSITE\('([^']+)'", content)
        assert site_match
        
        # Check building
        assert "IFCBUILDING(" in content
        building_match = re.search(r"IFCBUILDING\('([^']+)'", content)
        assert building_match
        
        # Check building storey
        assert "IFCBUILDINGSTOREY(" in content
        storey_match = re.search(r"IFCBUILDINGSTOREY\('([^']+)'.*?'Ground Floor'", content)
        assert storey_match
    
    async def test_spatial_containment(self, ifc_generator, simple_shed):
        """Test spatial containment relationships"""
        result = await ifc_generator.generate(simple_shed, format="IFC4")
        content = result.content
        
        # Check aggregation relationships
        assert "IFCRELAGGREGATES(" in content
        
        # Project aggregates site
        project_agg = re.search(
            r"IFCRELAGGREGATES\('([^']+)'.*?'ProjectContainer'.*?\(#(\d+)\).*?\((#\d+)\)",
            content
        )
        assert project_agg
        
        # Site aggregates building
        site_agg = re.search(
            r"IFCRELAGGREGATES\('([^']+)'.*?'SiteContainer'.*?\(#(\d+)\).*?\((#\d+)\)",
            content
        )
        assert site_agg
        
        # Building aggregates storey
        building_agg = re.search(
            r"IFCRELAGGREGATES\('([^']+)'.*?'BuildingContainer'.*?\(#(\d+)\).*?\((#\d+)\)",
            content
        )
        assert building_agg
        
        # Elements contained in storey
        assert "IFCRELCONTAINEDINSPATIALSTRUCTURE(" in content
    
    async def test_columns_generation(self, ifc_generator, complex_shed):
        """Test column generation with all properties"""
        result = await ifc_generator.generate(complex_shed, format="IFC4")
        content = result.content
        
        # Count columns
        column_count = content.count("IFCCOLUMN(")
        assert column_count > 0
        
        # Check column properties
        column_match = re.search(
            r"IFCCOLUMN\('([^']+)'.*?'([^']+)'.*?'([^']+)'", 
            content
        )
        assert column_match
        column_guid = column_match.group(1)
        column_name = column_match.group(3)
        
        # Verify column has material
        assert "IFCRELASSOCIATESMATERIAL(" in content
        
        # Check property sets
        assert "IFCPROPERTYSET(" in content
        assert "Pset_ColumnCommon" in content
        
        # Check material properties
        assert "IFCMATERIAL(" in content
        material_match = re.search(r"IFCMATERIAL\('([^']+)'", content)
        assert material_match
    
    async def test_beams_and_rafters(self, ifc_generator, complex_shed):
        """Test beam and rafter generation"""
        result = await ifc_generator.generate(complex_shed, format="IFC4")
        content = result.content
        
        # Check beams
        beam_count = content.count("IFCBEAM(")
        assert beam_count > 0
        
        # Check for different beam types
        assert re.search(r"IFCBEAM.*?'Rafter'", content)
        assert re.search(r"IFCBEAM.*?'Purlin'", content)
        
        # Check beam type enum
        assert "IFCBEAMTYPEENUM" in content or ".BEAM." in content
    
    async def test_brackets_and_connections(self, ifc_generator, complex_shed):
        """Test bracket generation with mesh geometry"""
        # Add brackets to model
        complex_shed.brackets.append(Bracket(
            id="BRACKET1",
            type=BracketType.APEX_STANDARD,
            position=Vec3(6000, 0, 4500),
            rotation=Mat4.identity(),
            material=ColorMaterial(color=(200, 200, 200))
        ))
        
        result = await ifc_generator.generate(complex_shed, format="IFC4")
        content = result.content
        
        # Check for brackets (as building element proxy)
        assert "IFCBUILDINGELEMENTPROXY(" in content
        
        # Check mesh representation
        assert "IFCFACETEDBREP(" in content
        assert "IFCCLOSEDSHELL(" in content
        assert "IFCFACE(" in content
    
    async def test_wall_cladding(self, ifc_generator, complex_shed):
        """Test wall cladding generation with profiles"""
        result = await ifc_generator.generate(complex_shed, format="IFC4")
        content = result.content
        
        # Check for wall elements
        assert "IFCWALL(" in content or "IFCWALLSTANDARDCASE(" in content
        
        # Check for covering elements (cladding)
        if "IFCCOVERING(" in content:
            covering_match = re.search(
                r"IFCCOVERING\('([^']+)'.*?\.CLADDING\.",
                content
            )
            assert covering_match
    
    async def test_roof_cladding(self, ifc_generator, complex_shed):
        """Test roof cladding generation"""
        result = await ifc_generator.generate(complex_shed, format="IFC4")
        content = result.content
        
        # Check for roof elements
        assert "IFCROOF(" in content or "IFCSLAB(" in content
        
        # Check slope representation
        if "IFCROOF(" in content:
            # Verify roof has proper geometry
            assert "IFCEXTRUDEDAREASOLID(" in content or "IFCFACETEDBREP(" in content
    
    async def test_doors(self, ifc_generator, complex_shed):
        """Test door generation with different types"""
        result = await ifc_generator.generate(complex_shed, format="IFC4")
        content = result.content
        
        # Check for doors
        assert "IFCDOOR(" in content
        
        # Check door types
        door_match = re.search(r"IFCDOOR\('([^']+)'.*?'DOOR1'", content)
        assert door_match
        
        # Check door properties
        assert "Pset_DoorCommon" in content or "IFCDOOR" in content
        
        # Verify door has opening element
        assert "IFCOPENINGELEMENT(" in content
    
    async def test_windows(self, ifc_generator, complex_shed):
        """Test window generation with frames"""
        result = await ifc_generator.generate(complex_shed, format="IFC4")
        content = result.content
        
        # Check for windows
        assert "IFCWINDOW(" in content
        
        # Check window properties
        window_match = re.search(r"IFCWINDOW\('([^']+)'.*?'WIN1'", content)
        assert window_match
        
        # Check for window type
        assert "SLIDING" in content or "Sliding" in content
    
    async def test_footings(self, ifc_generator, complex_shed):
        """Test footing generation"""
        result = await ifc_generator.generate(complex_shed, format="IFC4")
        content = result.content
        
        # Check for footings
        assert "IFCFOOTING(" in content
        
        # Verify footing placement
        footing_match = re.search(
            r"IFCFOOTING\('([^']+)'.*?#(\d+).*?#(\d+)",
            content
        )
        assert footing_match
    
    async def test_material_properties(self, ifc_generator, complex_shed):
        """Test material property generation"""
        result = await ifc_generator.generate(complex_shed, format="IFC4")
        content = result.content
        
        # Check material definitions
        assert "IFCMATERIAL(" in content
        
        # Check material properties
        materials = re.findall(r"IFCMATERIAL\('([^']+)'", content)
        assert len(materials) > 0
        
        # Check for steel materials
        assert any("Steel" in content or "G250" in content or "G350" in content 
                  for mat in materials)
    
    async def test_property_sets(self, ifc_generator, complex_shed):
        """Test IFC property set generation"""
        result = await ifc_generator.generate(complex_shed, format="IFC4")
        content = result.content
        
        # Check standard property sets
        assert "IFCPROPERTYSET(" in content
        
        # Check common psets
        psets = [
            "Pset_BeamCommon",
            "Pset_ColumnCommon",
            "Material_BOM",
            "Location_Info"
        ]
        
        for pset in psets:
            if any(elem in content for elem in ["IFCBEAM", "IFCCOLUMN"]):
                # At least some psets should be present
                pass
        
        # Check property types
        assert "IFCPROPERTYSINGLEVALUE(" in content
    
    # ===== ACCURACY TESTS =====
    
    async def test_coordinate_accuracy(self, ifc_generator):
        """Test coordinate transformation accuracy"""
        model = ShedBimModel()
        
        # Add precisely positioned elements
        test_positions = [
            Vec3(0, 0, 0),
            Vec3(1000, 0, 0),
            Vec3(0, 2000, 0),
            Vec3(0, 0, 3000),
            Vec3(1234.567, 2345.678, 3456.789)
        ]
        
        for i, pos in enumerate(test_positions):
            model.frames.append(Frame(
                id=f"COL{i}",
                start=pos,
                end=pos + Vec3(0, 0, 1000),
                material=FrameMaterial(
                    profile=Profile.C10015,
                    grade=SteelGrade.G250,
                    thickness=1.5
                ),
                type="Column"
            ))
        
        result = await ifc_generator.generate(model, format="IFC4")
        content = result.content
        
        # Extract Cartesian points
        points = re.findall(
            r"IFCCARTESIANPOINT\(\(([\d\.\-]+),([\d\.\-]+),([\d\.\-]+)\)\)",
            content
        )
        
        # Convert to floats
        extracted_points = [(float(x), float(y), float(z)) for x, y, z in points]
        
        # Check accuracy (within 0.1mm)
        tolerance = 0.1
        for test_pos in test_positions:
            found = False
            for point in extracted_points:
                if (abs(point[0] - test_pos.x) < tolerance and
                    abs(point[1] - test_pos.y) < tolerance and
                    abs(point[2] - test_pos.z) < tolerance):
                    found = True
                    break
            assert found, f"Position {test_pos} not found within tolerance"
    
    async def test_dimension_accuracy(self, ifc_generator):
        """Test dimension accuracy for profiles"""
        model = ShedBimModel()
        
        # Add frame with specific dimensions
        model.frames.append(Frame(
            id="BEAM1",
            start=Vec3(0, 0, 3000),
            end=Vec3(5000, 0, 3000),
            material=FrameMaterial(
                profile=Profile.C15015,  # 150mm depth
                grade=SteelGrade.G250,
                thickness=1.5
            ),
            type="Beam"
        ))
        
        result = await ifc_generator.generate(model, format="IFC4")
        content = result.content
        
        # Check for profile dimensions
        # Should contain profile definition with 150mm depth
        assert "150" in content or "0.15" in content
    
    async def test_angle_accuracy(self, ifc_generator):
        """Test angle accuracy for roof slopes"""
        model = ShedBimModel()
        
        # Add rafter at specific angle (10 degrees)
        angle_rad = math.radians(10)
        length = 5000
        rise = length * math.tan(angle_rad)
        
        model.frames.append(Frame(
            id="RAFTER1",
            start=Vec3(0, 0, 3000),
            end=Vec3(length, 0, 3000 + rise),
            material=FrameMaterial(
                profile=Profile.C10015,
                grade=SteelGrade.G250,
                thickness=1.5
            ),
            type="Rafter"
        ))
        
        result = await ifc_generator.generate(model, format="IFC4")
        content = result.content
        
        # Verify rafter is created
        assert "IFCBEAM" in content
        assert "Rafter" in content
    
    async def test_material_mapping(self, ifc_generator):
        """Test material color and property mapping"""
        model = ShedBimModel()
        
        # Add sheet with specific color
        model.sheets.append(Sheet(
            id="WALL1",
            boundary=[
                Vec3(0, 0, 0),
                Vec3(5000, 0, 0),
                Vec3(5000, 0, 3000),
                Vec3(0, 0, 3000)
            ],
            material=SheetMaterial(
                base_color=(200, 50, 50),  # Red color
                thickness=0.4,
                profile="Corrugated"
            ),
            type="Wall"
        ))
        
        result = await ifc_generator.generate(model, format="IFC4")
        content = result.content
        
        # Check for surface style
        assert "IFCSURFACESTYLE(" in content
        
        # Check for color definition
        # Colors are normalized to 0-1 range in IFC
        color_match = re.search(
            r"IFCCOLOURRGB\(\$,([\d\.]+),([\d\.]+),([\d\.]+)\)",
            content
        )
        if color_match:
            r = float(color_match.group(1))
            g = float(color_match.group(2))
            b = float(color_match.group(3))
            
            # Check color mapping (200,50,50) -> (0.784, 0.196, 0.196)
            assert abs(r - 200/255) < 0.01
            assert abs(g - 50/255) < 0.01
            assert abs(b - 50/255) < 0.01
    
    async def test_ifc_version_support(self, ifc_generator, simple_shed):
        """Test different IFC version support"""
        versions = ["IFC2X3", "IFC4", "IFC4X3"]
        
        for version in versions:
            if version in ["IFC2X3", "IFC4"]:  # Only test supported versions
                result = await ifc_generator.generate(simple_shed, format=version)
                content = result.content
                
                # Check schema declaration
                assert f"FILE_SCHEMA(('{version}'));" in content
                
                # Check version-specific elements
                if version == "IFC4":
                    # IFC4 specific checks
                    assert "IFC4" in content
                elif version == "IFC2X3":
                    # IFC2X3 specific checks
                    assert "IFC2X3" in content
    
    async def test_complex_geometry_accuracy(self, ifc_generator):
        """Test complex geometry with holes and cutouts"""
        model = ShedBimModel()
        
        # Add wall with opening
        wall_boundary = [
            Vec3(0, 0, 0),
            Vec3(10000, 0, 0),
            Vec3(10000, 0, 3000),
            Vec3(0, 0, 3000)
        ]
        
        model.sheets.append(Sheet(
            id="WALL_WITH_OPENING",
            boundary=wall_boundary,
            holes=[
                # Window opening
                [
                    Vec3(2000, 0, 1000),
                    Vec3(3500, 0, 1000),
                    Vec3(3500, 0, 2200),
                    Vec3(2000, 0, 2200)
                ]
            ],
            material=SheetMaterial(
                base_color=(200, 200, 200),
                thickness=0.5
            ),
            type="Wall"
        ))
        
        result = await ifc_generator.generate(model, format="IFC4")
        content = result.content
        
        # Check for opening element
        assert "IFCOPENINGELEMENT(" in content
        
        # Verify opening dimensions
        opening_points = re.findall(
            r"IFCCARTESIANPOINT\(\(([\d\.\-]+),([\d\.\-]+),([\d\.\-]+)\)\)",
            content
        )
        
        # Should have points for the opening boundaries
        assert len(opening_points) > 4
    
    # ===== INTEGRATION TESTS =====
    
    async def test_complete_building_export(self, ifc_generator):
        """Test complete building export with all elements"""
        # Create full building using business logic
        building_input = BuildingInput(
            width=15000,
            length=30000,
            eave_height=6000,
            roof_pitch=15,
            bays=6,
            structural_system="Portal Frame",
            roof_type="Gable",
            wall_cladding="Colorsteel",
            roof_cladding="Colorsteel",
            add_mezzanine=True,
            mezzanine_area=100
        )
        
        builder = StructureBuilder()
        model = builder.create_structure(building_input)
        
        # Add engineering data
        eng_service = EngineeringService()
        eng_data = eng_service.calculate_engineering(building_input)
        
        # Export to IFC
        result = await ifc_generator.generate(
            model, 
            format="IFC4",
            include_engineering=True,
            engineering_data=eng_data
        )
        
        assert result.success
        assert result.content
        assert len(result.content) > 10000  # Substantial file
        
        content = result.content
        
        # Verify all major elements present
        assert "IFCPROJECT(" in content
        assert "IFCSITE(" in content
        assert "IFCBUILDING(" in content
        assert "IFCBUILDINGSTOREY(" in content
        assert "IFCCOLUMN(" in content
        assert "IFCBEAM(" in content
        assert "IFCFOOTING(" in content
        
        # Check element counts
        column_count = content.count("IFCCOLUMN(")
        beam_count = content.count("IFCBEAM(")
        assert column_count >= 14  # At least 2 columns per frame * 7 frames
        assert beam_count >= 20  # Rafters, purlins, etc.
    
    async def test_performance_large_model(self, ifc_generator):
        """Test performance with large model"""
        import time
        
        # Create large model
        model = ShedBimModel()
        
        # Add many elements
        for i in range(100):
            model.frames.append(Frame(
                id=f"COL{i}",
                start=Vec3(i * 1000, 0, 0),
                end=Vec3(i * 1000, 0, 3000),
                material=FrameMaterial(
                    profile=Profile.C10015,
                    grade=SteelGrade.G250,
                    thickness=1.5
                ),
                type="Column"
            ))
        
        # Time the generation
        start_time = time.time()
        result = await ifc_generator.generate(model, format="IFC4")
        end_time = time.time()
        
        assert result.success
        generation_time = end_time - start_time
        assert generation_time < 5.0  # Should complete within 5 seconds
        
        # Check file isn't corrupted
        content = result.content
        assert content.startswith("ISO-10303-21;")
        assert content.endswith("END-ISO-10303-21;")
    
    async def test_error_handling(self, ifc_generator):
        """Test error handling for invalid inputs"""
        # Test empty model
        empty_model = ShedBimModel()
        result = await ifc_generator.generate(empty_model, format="IFC4")
        assert not result.success
        assert "empty" in result.error.lower()
        
        # Test invalid format
        model = ShedBimModel()
        model.frames.append(Frame(
            id="F1",
            start=Vec3(0, 0, 0),
            end=Vec3(0, 0, 1000),
            material=FrameMaterial(
                profile=Profile.C10015,
                grade=SteelGrade.G250,
                thickness=1.5
            ),
            type="Column"
        ))
        
        result = await ifc_generator.generate(model, format="INVALID")
        assert not result.success
        assert "format" in result.error.lower()
    
    async def test_guid_uniqueness(self, ifc_generator, complex_shed):
        """Test that all GUIDs are unique"""
        result = await ifc_generator.generate(complex_shed, format="IFC4")
        content = result.content
        
        # Extract all GUIDs
        guids = re.findall(r"'([0-9A-Za-z_$]{22})'", content)
        
        # Check uniqueness
        assert len(guids) == len(set(guids)), "Duplicate GUIDs found"
        
        # Check GUID format (22 chars, base64-like)
        for guid in guids:
            assert len(guid) == 22
            assert all(c in "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz_$" 
                      for c in guid)


# Run tests
if __name__ == "__main__":
    pytest.main([__file__, "-v"])