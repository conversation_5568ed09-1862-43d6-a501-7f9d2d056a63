"""
Tests for DXF output generator.

This module tests the DXF CAD drawing generation functionality.
"""

import pytest
from pathlib import Path
import tempfile

from src.output.dxf import DXFGenerator
from src.output import OutputFormat
from src.bim.shed_bim import ShedBim, ShedBimPartMain
from src.bim.wall_roof import Shed<PERSON><PERSON><PERSON><PERSON>, ShedBimRoof
from src.bim.components import Shed<PERSON>imColumn, ShedBimRafter
from src.geometry.primitives import Vec3


class TestDXFGenerator:
    """Test DXF generation functionality."""
    
    @pytest.fixture
    def generator(self):
        """Create DXF generator with temp directory."""
        with tempfile.TemporaryDirectory() as tmpdir:
            yield DXFGenerator(Path(tmpdir))
            
    @pytest.fixture
    def carport_bim(self):
        """Create a carport BIM structure for testing."""
        # Create main structure
        main = ShedBimPartMain(
            roof_type="Flat",
            roof_left=ShedBimRoof(
                cladding_material="Colorbond",
                cladding_reversed=False
            ),
            side_left=ShedBimSide(),
            side_right=ShedBimSide()
        )
        
        # Add columns in a grid
        for x in [0, 3000, 6000]:
            for y in [0, 6000]:
                main.side_left.columns.append(
                    ShedBimColumn(
                        base=Vec3(x, y, 0),
                        top=Vec3(x, y, 2400),
                        frame_material="C15024"
                    )
                )
                
        # Add rafters
        main.roof_left.rafters = [
            ShedBimRafter(
                start=Vec3(0, 0, 2400),
                end=Vec3(0, 6000, 2400),
                frame_material="C15024"
            ),
            ShedBimRafter(
                start=Vec3(3000, 0, 2400),
                end=Vec3(3000, 6000, 2400),
                frame_material="C15024"
            ),
            ShedBimRafter(
                start=Vec3(6000, 0, 2400),
                end=Vec3(6000, 6000, 2400),
                frame_material="C15024"
            )
        ]
        
        return ShedBim(main=main)
        
    def test_get_supported_formats(self, generator):
        """Test supported format listing."""
        formats = generator.get_supported_formats()
        assert OutputFormat.DXF in formats
        
    def test_generate_dxf(self, generator, carport_bim):
        """Test basic DXF generation."""
        result = generator.generate(carport_bim, "test_drawing")
        
        assert result.success
        assert result.format == OutputFormat.DXF
        assert result.file_path.exists()
        assert result.file_path.suffix == ".dxf"
        assert result.file_size > 0
        
        # Verify DXF content
        content = result.file_path.read_text()
        assert "SECTION" in content
        assert "HEADER" in content
        assert "TABLES" in content
        assert "ENTITIES" in content
        assert "EOF" in content
        
    def test_multiple_views(self, generator, carport_bim):
        """Test generation with multiple views."""
        result = generator.generate(
            carport_bim, 
            "test_views",
            views=['top', 'front', 'side']
        )
        
        assert result.success
        
        content = result.file_path.read_text()
        # Check for view labels
        assert "TOP VIEW" in content
        assert "FRONT VIEW" in content
        assert "SIDE VIEW" in content
        
    def test_3d_view(self, generator, carport_bim):
        """Test 3D isometric view generation."""
        result = generator.generate(
            carport_bim,
            "test_3d",
            views=['3d']
        )
        
        assert result.success
        
        content = result.file_path.read_text()
        assert "3D VIEW" in content
        
    def test_units_configuration(self, generator, carport_bim):
        """Test different unit settings."""
        # Test millimeters
        result_mm = generator.generate(
            carport_bim,
            "test_mm",
            units='mm'
        )
        assert result_mm.success
        
        # Test meters
        result_m = generator.generate(
            carport_bim,
            "test_m",
            units='m'
        )
        assert result_m.success
        
    def test_layer_creation(self, generator, carport_bim):
        """Test DXF layer creation."""
        result = generator.generate(carport_bim, "test_layers")
        
        assert result.success
        
        content = result.file_path.read_text()
        # Check for layer definitions
        assert "LAYER" in content
        assert "COLUMNS" in content
        assert "RAFTERS" in content
        assert "ROOF" in content
        
    def test_empty_bim(self, generator):
        """Test handling of empty BIM."""
        empty_bim = ShedBim(main=None)
        result = generator.generate(empty_bim, "empty")
        
        assert not result.success
        assert len(result.errors) > 0
        
    def test_scale_factor(self, generator, carport_bim):
        """Test drawing scale factor."""
        result = generator.generate(
            carport_bim,
            "test_scale",
            scale=50  # 1:50 scale
        )
        
        assert result.success
        # Scale affects coordinate values in the output
        
    def test_color_codes(self, generator):
        """Test DXF color code constants."""
        assert DXFGenerator.COLOR_RED == 1
        assert DXFGenerator.COLOR_WHITE == 7
        assert DXFGenerator.COLOR_BYLAYER == 256


class TestDXFEntityGeneration:
    """Test DXF entity generation methods."""
    
    def test_line_generation(self):
        """Test line entity format."""
        dxf = []
        gen = DXFGenerator()
        gen._add_line(dxf, 0, 0, 100, 100, "TEST")
        
        assert "0" in dxf
        assert "LINE" in dxf
        assert "8" in dxf
        assert "TEST" in dxf
        
    def test_circle_generation(self):
        """Test circle entity format."""
        dxf = []
        gen = DXFGenerator()
        gen._add_circle(dxf, 50, 50, 25, "TEST")
        
        assert "CIRCLE" in dxf
        assert "40" in dxf  # Radius group code
        
    def test_text_generation(self):
        """Test text entity format."""
        dxf = []
        gen = DXFGenerator()
        gen._add_text(dxf, "Sample Text", 0, 0, 10, "TEXT")
        
        assert "TEXT" in dxf
        assert "Sample Text" in dxf