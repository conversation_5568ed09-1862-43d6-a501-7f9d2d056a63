"""
Test suite for base output classes.

Tests the abstract interfaces and common functionality for output generators.
"""

import pytest
from pathlib import Path
import tempfile
import shutil
from unittest.mock import Mock, patch
from typing import List

# Import after modifying path
import sys
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from src.output.base.output_base import (
    OutputFormat, OutputResult, OutputGenerator
)
from src.bim.shed_bim import ShedBim, ShedBimPartMain, ShedBimSide
from src.bim.components import ShedBimColumn


class TestOutputFormat:
    """Test OutputFormat enum."""
    
    def test_all_formats_defined(self):
        """Test all expected formats are defined."""
        expected_formats = ["gltf", "glb", "dxf", "ifc", "ifc4", "stl", "obj"]
        actual_formats = [f.value for f in OutputFormat]
        
        for fmt in expected_formats:
            assert fmt in actual_formats
    
    def test_format_values(self):
        """Test format enum values."""
        assert OutputFormat.GLTF.value == "gltf"
        assert OutputFormat.GLB.value == "glb"
        assert OutputFormat.DXF.value == "dxf"
        assert OutputFormat.IFC.value == "ifc"
        assert OutputFormat.IFC4.value == "ifc4"
        assert OutputFormat.STL.value == "stl"
        assert OutputFormat.OBJ.value == "obj"
    
    def test_format_from_string(self):
        """Test creating format from string value."""
        assert OutputFormat("gltf") == OutputFormat.GLTF
        assert OutputFormat("dxf") == OutputFormat.DXF
        assert OutputFormat("ifc") == OutputFormat.IFC
    
    def test_invalid_format_string(self):
        """Test invalid format string raises error."""
        with pytest.raises(ValueError):
            OutputFormat("invalid_format")


class TestOutputResult:
    """Test OutputResult dataclass."""
    
    def test_creation_minimal(self):
        """Test creating result with minimal data."""
        result = OutputResult(
            success=True,
            format=OutputFormat.GLTF
        )
        
        assert result.success is True
        assert result.format == OutputFormat.GLTF
        assert result.file_path is None
        assert result.file_size is None
        assert result.errors == []
        assert result.warnings == []
        assert result.metadata == {}
    
    def test_creation_full(self):
        """Test creating result with all data."""
        result = OutputResult(
            success=True,
            format=OutputFormat.DXF,
            file_path=Path("/tmp/test.dxf"),
            file_size=1024,
            errors=["Error 1"],
            warnings=["Warning 1"],
            metadata={"vertices": 100}
        )
        
        assert result.success is True
        assert result.format == OutputFormat.DXF
        assert result.file_path == Path("/tmp/test.dxf")
        assert result.file_size == 1024
        assert result.errors == ["Error 1"]
        assert result.warnings == ["Warning 1"]
        assert result.metadata == {"vertices": 100}
    
    def test_post_init_empty_lists(self):
        """Test post_init creates empty lists."""
        result = OutputResult(
            success=False,
            format=OutputFormat.IFC,
            errors=None,
            warnings=None,
            metadata=None
        )
        
        assert isinstance(result.errors, list)
        assert isinstance(result.warnings, list)
        assert isinstance(result.metadata, dict)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
        assert len(result.metadata) == 0
    
    def test_result_failure(self):
        """Test failure result."""
        result = OutputResult(
            success=False,
            format=OutputFormat.GLTF,
            errors=["Failed to generate", "Invalid geometry"]
        )
        
        assert result.success is False
        assert len(result.errors) == 2
        assert result.file_path is None
    
    def test_result_with_warnings(self):
        """Test result with warnings but success."""
        result = OutputResult(
            success=True,
            format=OutputFormat.STL,
            file_path=Path("model.stl"),
            warnings=["Material information lost in STL format"]
        )
        
        assert result.success is True
        assert len(result.warnings) == 1
        assert len(result.errors) == 0


class ConcreteOutputGenerator(OutputGenerator):
    """Concrete implementation for testing."""
    
    def generate(self, bim: ShedBim, filename: str, **options) -> OutputResult:
        """Test implementation of generate."""
        return OutputResult(
            success=True,
            format=OutputFormat.GLTF,
            file_path=self.get_output_path(filename, OutputFormat.GLTF)
        )
    
    def get_supported_formats(self) -> List[OutputFormat]:
        """Test implementation of supported formats."""
        return [OutputFormat.GLTF, OutputFormat.GLB]


class TestOutputGenerator:
    """Test OutputGenerator abstract base class."""
    
    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory."""
        temp_dir = Path(tempfile.mkdtemp())
        yield temp_dir
        shutil.rmtree(temp_dir, ignore_errors=True)
    
    @pytest.fixture
    def generator(self, temp_dir):
        """Create concrete generator instance."""
        return ConcreteOutputGenerator(output_dir=temp_dir)
    
    @pytest.fixture
    def mock_bim(self):
        """Create mock BIM model."""
        bim = Mock(spec=ShedBim)
        bim.main = Mock(spec=ShedBimPartMain)
        bim.main.roof_type = "GABLE"
        bim.main.get_sides.return_value = []
        bim.main.get_roofs.return_value = []
        return bim
    
    def test_initialization_with_dir(self, temp_dir):
        """Test initialization with output directory."""
        gen = ConcreteOutputGenerator(output_dir=temp_dir)
        
        assert gen.output_dir == temp_dir
        assert temp_dir.exists()
        assert gen.logger is not None
    
    def test_initialization_default_dir(self):
        """Test initialization with default directory."""
        with patch('pathlib.Path.cwd') as mock_cwd:
            mock_cwd.return_value = Path("/current/dir")
            gen = ConcreteOutputGenerator()
            
            assert gen.output_dir == Path("/current/dir")
    
    def test_abstract_methods_required(self):
        """Test abstract methods must be implemented."""
        with pytest.raises(TypeError):
            # Cannot instantiate abstract class
            OutputGenerator()
    
    def test_validate_bim_none(self, generator):
        """Test validation with None BIM."""
        errors = generator.validate_bim(None)
        
        assert len(errors) == 1
        assert "BIM model is None" in errors[0]
    
    def test_validate_bim_no_main(self, generator):
        """Test validation with BIM missing main."""
        bim = Mock(spec=ShedBim)
        bim.main = None
        
        errors = generator.validate_bim(bim)
        
        assert len(errors) == 1
        assert "no main structure" in errors[0]
    
    def test_validate_bim_missing_roof_type(self, generator):
        """Test validation with missing roof type."""
        bim = Mock(spec=ShedBim)
        bim.main = Mock()
        del bim.main.roof_type  # Remove attribute
        bim.main.get_sides.return_value = []
        
        errors = generator.validate_bim(bim)
        
        assert any("roof_type" in error for error in errors)
    
    def test_validate_bim_no_sides(self, generator, mock_bim):
        """Test validation with no sides."""
        mock_bim.main.get_sides.return_value = []
        
        errors = generator.validate_bim(mock_bim)
        
        assert any("No sides" in error for error in errors)
    
    def test_validate_bim_column_no_material(self, generator):
        """Test validation with column missing material."""
        bim = Mock(spec=ShedBim)
        bim.main = Mock(spec=ShedBimPartMain)
        bim.main.roof_type = "FLAT"
        
        # Create side with column missing material
        side = Mock(spec=ShedBimSide)
        column = Mock(spec=ShedBimColumn)
        column.frame_material = None
        side.columns = [column]
        
        bim.main.get_sides.return_value = [side]
        
        errors = generator.validate_bim(bim)
        
        assert any("Column missing frame material" in error for error in errors)
    
    def test_validate_bim_valid(self, generator, mock_bim):
        """Test validation with valid BIM."""
        side = Mock(spec=ShedBimSide)
        side.columns = []
        mock_bim.main.get_sides.return_value = [side]
        
        errors = generator.validate_bim(mock_bim)
        
        assert len(errors) == 0
    
    def test_get_output_path(self, generator, temp_dir):
        """Test output path generation."""
        path = generator.get_output_path("model", OutputFormat.GLTF)
        
        assert path == temp_dir / "model.gltf"
        assert path.parent == temp_dir
        assert path.suffix == ".gltf"
    
    def test_get_output_path_formats(self, generator, temp_dir):
        """Test output path for different formats."""
        formats = [
            (OutputFormat.GLTF, ".gltf"),
            (OutputFormat.GLB, ".glb"),
            (OutputFormat.DXF, ".dxf"),
            (OutputFormat.IFC, ".ifc"),
            (OutputFormat.IFC4, ".ifc4"),
            (OutputFormat.STL, ".stl"),
            (OutputFormat.OBJ, ".obj")
        ]
        
        for format, expected_ext in formats:
            path = generator.get_output_path("test", format)
            assert path.suffix == expected_ext
    
    def test_create_metadata_basic(self, generator, mock_bim):
        """Test basic metadata creation."""
        metadata = generator.create_metadata(mock_bim)
        
        assert metadata["generator"] == "BIM Backend Python"
        assert metadata["version"] == "1.0.0"
        assert metadata["building_type"] == "UNKNOWN"  # No attribute on mock
        assert metadata["roof_type"] == "GABLE"
    
    def test_create_metadata_with_components(self, generator):
        """Test metadata with component counts."""
        # Create BIM with components
        bim = Mock(spec=ShedBim)
        bim.main = Mock(spec=ShedBimPartMain)
        bim.main.roof_type = "FLAT"
        
        # Add sides with columns
        side1 = Mock()
        side1.columns = [Mock(), Mock(), Mock()]  # 3 columns
        side2 = Mock()
        side2.columns = [Mock(), Mock()]  # 2 columns
        bim.main.get_sides.return_value = [side1, side2]
        
        # Add roofs with rafters
        roof1 = Mock()
        roof1.rafters = [Mock(), Mock()]  # 2 rafters
        bim.main.get_roofs.return_value = [roof1]
        
        metadata = generator.create_metadata(bim)
        
        assert metadata["column_count"] == 5  # 3 + 2
        assert metadata["rafter_count"] == 2
    
    def test_create_metadata_no_components(self, generator, mock_bim):
        """Test metadata when components missing attributes."""
        side = Mock()
        # No columns attribute
        mock_bim.main.get_sides.return_value = [side]
        
        metadata = generator.create_metadata(mock_bim)
        
        assert metadata["column_count"] == 0
        assert metadata["rafter_count"] == 0
    
    def test_directory_creation(self, temp_dir):
        """Test output directory is created if missing."""
        sub_dir = temp_dir / "sub" / "dir"
        gen = ConcreteOutputGenerator(output_dir=sub_dir)
        
        assert sub_dir.exists()
        assert sub_dir.is_dir()
    
    def test_get_supported_formats_implementation(self, generator):
        """Test get_supported_formats implementation."""
        formats = generator.get_supported_formats()
        
        assert OutputFormat.GLTF in formats
        assert OutputFormat.GLB in formats
        assert len(formats) == 2
    
    def test_generate_implementation(self, generator, mock_bim, temp_dir):
        """Test generate implementation."""
        result = generator.generate(mock_bim, "test_model")
        
        assert result.success is True
        assert result.format == OutputFormat.GLTF
        assert result.file_path == temp_dir / "test_model.gltf"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])