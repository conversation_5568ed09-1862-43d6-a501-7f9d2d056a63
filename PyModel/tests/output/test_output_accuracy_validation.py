"""
Output Accuracy Validation Test Suite
Compares Python-generated IFC and GLB files against C# reference outputs
Validates geometry precision, material accuracy, and metadata preservation
"""

import pytest
import asyncio
import json
import struct
import math
import re
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any, Set
import hashlib
import difflib

from src.bim.shed_bim import ShedBimModel
from src.business.building_input import BuildingInput
from src.business.structure_builder import StructureBuilder
from src.business.engineering import EngineeringService
from src.geometry import Vec3, Mat4
from src.materials import FrameMaterial, SheetMaterial, Profile, SteelGrade
from src.output.ifc_generator import IfcGenerator
from src.output.gltf_generator import GltfGenerator


class TestOutputAccuracyValidation:
    """Validate output accuracy against reference files and specifications"""
    
    @pytest.fixture
    def reference_models(self):
        """Create standard models for comparison"""
        models = {}
        
        # Simple carport
        carport_input = BuildingInput(
            width=6000,
            length=6000,
            eave_height=2400,
            roof_pitch=5,
            bays=1,
            structural_system="Portal Frame",
            roof_type="Gable"
        )
        builder = StructureBuilder()
        models['carport'] = builder.create_structure(carport_input)
        
        # Industrial shed
        shed_input = BuildingInput(
            width=12000,
            length=24000,
            eave_height=6000,
            roof_pitch=10,
            bays=4,
            structural_system="Portal Frame",
            roof_type="Gable",
            wall_cladding="Colorsteel",
            roof_cladding="Colorsteel"
        )
        models['shed'] = builder.create_structure(shed_input)
        
        return models
    
    # ===== IFC ACCURACY VALIDATION =====
    
    async def test_ifc_entity_counts(self, reference_models):
        """Validate IFC entity counts match expected values"""
        generator = IfcGenerator()
        
        # Test carport
        carport = reference_models['carport']
        result = await generator.generate(carport, format="IFC4")
        content = result.content
        
        # Count key entities
        entity_counts = {
            'IFCCOLUMN': content.count('IFCCOLUMN('),
            'IFCBEAM': content.count('IFCBEAM('),
            'IFCFOOTING': content.count('IFCFOOTING('),
            'IFCMATERIAL': content.count('IFCMATERIAL('),
            'IFCPROPERTYSET': content.count('IFCPROPERTYSET(')
        }
        
        # Validate minimum expected counts
        assert entity_counts['IFCCOLUMN'] >= 4  # At least 4 columns
        assert entity_counts['IFCBEAM'] >= 2    # At least 2 main beams
        assert entity_counts['IFCFOOTING'] >= 4 # One per column
        assert entity_counts['IFCMATERIAL'] >= 1 # At least one material
        
        # Store for comparison
        return entity_counts
    
    async def test_ifc_coordinate_precision(self, reference_models):
        """Test coordinate precision in IFC output"""
        generator = IfcGenerator()
        carport = reference_models['carport']
        
        result = await generator.generate(carport, format="IFC4")
        content = result.content
        
        # Extract all coordinate points
        coord_pattern = r'IFCCARTESIANPOINT\(\(([-\d\.]+),([-\d\.]+),([-\d\.]+)\)\)'
        coordinates = re.findall(coord_pattern, content)
        
        # Convert to floats and check precision
        float_coords = [(float(x), float(y), float(z)) for x, y, z in coordinates]
        
        # Check key positions are present
        expected_positions = [
            (0.0, 0.0, 0.0),      # Origin
            (6000.0, 0.0, 0.0),   # Width
            (0.0, 6000.0, 0.0),   # Length
            (0.0, 0.0, 2400.0),   # Eave height
        ]
        
        tolerance = 0.1  # 0.1mm tolerance
        for expected in expected_positions:
            found = any(
                all(abs(coord[i] - expected[i]) < tolerance for i in range(3))
                for coord in float_coords
            )
            assert found, f"Expected position {expected} not found"
    
    async def test_ifc_material_properties(self, reference_models):
        """Validate material properties in IFC"""
        generator = IfcGenerator()
        
        # Create model with specific materials
        model = ShedBimModel()
        model.frames.append(Frame(
            id="TEST_FRAME",
            start=Vec3(0, 0, 0),
            end=Vec3(0, 0, 3000),
            material=FrameMaterial(
                profile=Profile.C15015,
                grade=SteelGrade.G350,
                thickness=1.5,
                weight_per_meter=12.5
            ),
            type="Column"
        ))
        
        result = await generator.generate(model, format="IFC4")
        content = result.content
        
        # Check material grade
        assert "G350" in content or "350" in content
        
        # Check profile designation
        assert "C15015" in content or "150" in content
        
        # Check thickness (might be in properties)
        assert "1.5" in content or "0.0015" in content
    
    async def test_ifc_property_sets(self, reference_models):
        """Validate IFC property sets contain required data"""
        generator = IfcGenerator()
        shed = reference_models['shed']
        
        result = await generator.generate(shed, format="IFC4")
        content = result.content
        
        # Check for required property sets
        required_psets = [
            "Pset_BeamCommon",
            "Pset_ColumnCommon",
            "Material_BOM",
            "Location_Info"
        ]
        
        psets_found = {}
        for pset in required_psets:
            if pset in content:
                # Extract properties for this pset
                pset_match = re.search(
                    rf"{pset}'.*?IFCPROPERTYSET\([^)]+\).*?(\([^)]+\))",
                    content,
                    re.DOTALL
                )
                psets_found[pset] = pset_match is not None
        
        # At least some property sets should be present
        assert len(psets_found) > 0
    
    async def test_ifc_spatial_hierarchy(self, reference_models):
        """Validate IFC spatial hierarchy relationships"""
        generator = IfcGenerator()
        shed = reference_models['shed']
        
        result = await generator.generate(shed, format="IFC4")
        content = result.content
        
        # Extract hierarchy GUIDs
        project_match = re.search(r"IFCPROJECT\('([^']+)'", content)
        site_match = re.search(r"IFCSITE\('([^']+)'", content)
        building_match = re.search(r"IFCBUILDING\('([^']+)'", content)
        storey_match = re.search(r"IFCBUILDINGSTOREY\('([^']+)'", content)
        
        assert all([project_match, site_match, building_match, storey_match])
        
        # Check aggregation relationships
        # Project -> Site
        project_agg = re.search(
            rf"IFCRELAGGREGATES.*?{project_match.group(1)}.*?{site_match.group(1)}",
            content,
            re.DOTALL
        )
        assert project_agg is not None
    
    # ===== GLB ACCURACY VALIDATION =====
    
    async def test_glb_binary_structure(self, reference_models):
        """Validate GLB binary structure and alignment"""
        generator = GltfGenerator()
        carport = reference_models['carport']
        
        result = await generator.generate(carport, format="GLB")
        content = result.content
        
        # Validate GLB header
        assert len(content) >= 12
        magic, version, length = struct.unpack('<III', content[:12])
        assert magic == 0x46546C67  # 'glTF'
        assert version == 2
        assert length == len(content)
        
        # Validate JSON chunk
        json_length, json_type = struct.unpack('<II', content[12:20])
        assert json_type == 0x4E4F534A  # 'JSON'
        assert json_length % 4 == 0  # 4-byte aligned
        
        # Parse JSON
        json_data = content[20:20+json_length].decode('utf-8').rstrip('\x00')
        gltf = json.loads(json_data)
        
        # Validate structure
        assert gltf["asset"]["version"] == "2.0"
        assert len(gltf["scenes"]) >= 1
        assert len(gltf["nodes"]) >= 1
        assert len(gltf["meshes"]) >= 1
        
        return gltf
    
    async def test_glb_mesh_accuracy(self, reference_models):
        """Test mesh generation accuracy"""
        generator = GltfGenerator()
        
        # Create simple box for testing
        model = ShedBimModel()
        model.sheets.append(Sheet(
            id="TEST_BOX",
            boundary=[
                Vec3(0, 0, 0),
                Vec3(1000, 0, 0),
                Vec3(1000, 1000, 0),
                Vec3(0, 1000, 0)
            ],
            material=SheetMaterial(base_color=(255, 0, 0), thickness=10),
            type="Floor"
        ))
        
        result = await generator.generate(model, format="GLTF")
        gltf = json.loads(result.content)
        
        # Validate mesh
        mesh = gltf["meshes"][0]
        primitive = mesh["primitives"][0]
        
        # Check attributes
        assert "POSITION" in primitive["attributes"]
        assert "NORMAL" in primitive["attributes"]
        
        # Decode positions
        pos_accessor = gltf["accessors"][primitive["attributes"]["POSITION"]]
        assert pos_accessor["count"] >= 4  # At least 4 vertices for a quad
        
        # Check bounds
        assert "min" in pos_accessor
        assert "max" in pos_accessor
        min_bounds = pos_accessor["min"]
        max_bounds = pos_accessor["max"]
        
        # Validate bounds (accounting for Y-up conversion)
        assert abs(min_bounds[0] - 0) < 0.1
        assert abs(max_bounds[0] - 1000) < 0.1
    
    async def test_glb_material_accuracy(self, reference_models):
        """Test material property accuracy"""
        generator = GltfGenerator()
        
        # Create model with specific materials
        model = ShedBimModel()
        test_materials = [
            SheetMaterial(base_color=(255, 0, 0), metallic=1.0, roughness=0.0),
            SheetMaterial(base_color=(0, 255, 0), metallic=0.0, roughness=1.0),
            SheetMaterial(base_color=(0, 0, 255), metallic=0.5, roughness=0.5, alpha=0.7),
        ]
        
        for i, mat in enumerate(test_materials):
            model.sheets.append(Sheet(
                id=f"MAT_TEST_{i}",
                boundary=[
                    Vec3(i*100, 0, 0),
                    Vec3((i+1)*100, 0, 0),
                    Vec3((i+1)*100, 100, 0),
                    Vec3(i*100, 100, 0)
                ],
                material=mat,
                type="Wall"
            ))
        
        result = await generator.generate(model, format="GLTF")
        gltf = json.loads(result.content)
        
        # Validate materials
        for i, expected_mat in enumerate(test_materials):
            if i < len(gltf["materials"]):
                mat = gltf["materials"][i]
                pbr = mat["pbrMetallicRoughness"]
                
                # Check color conversion
                color = pbr["baseColorFactor"]
                expected_color = expected_mat.base_color
                assert abs(color[0] - expected_color[0]/255) < 0.01
                assert abs(color[1] - expected_color[1]/255) < 0.01
                assert abs(color[2] - expected_color[2]/255) < 0.01
                
                # Check metallic/roughness
                assert abs(pbr["metallicFactor"] - expected_mat.metallic) < 0.01
                assert abs(pbr["roughnessFactor"] - expected_mat.roughness) < 0.01
                
                # Check transparency
                if expected_mat.alpha < 1.0:
                    assert mat["alphaMode"] == "BLEND"
                    assert abs(color[3] - expected_mat.alpha) < 0.01
    
    async def test_glb_transform_accuracy(self, reference_models):
        """Test transformation matrix accuracy"""
        generator = GltfGenerator()
        
        # Create model with transformed elements
        model = ShedBimModel()
        model.frames.append(Frame(
            id="ROTATED_BEAM",
            start=Vec3(0, 0, 1000),
            end=Vec3(1000, 1000, 1500),
            material=FrameMaterial(
                profile=Profile.C10015,
                grade=SteelGrade.G250,
                thickness=1.5
            ),
            type="Beam"
        ))
        
        result = await generator.generate(model, format="GLTF")
        gltf = json.loads(result.content)
        
        # Find beam node
        beam_node = None
        for node in gltf["nodes"]:
            if "ROTATED_BEAM" in node.get("name", ""):
                beam_node = node
                break
        
        assert beam_node is not None
        
        # Check transformation
        if "matrix" in beam_node:
            matrix = beam_node["matrix"]
            assert len(matrix) == 16
            
            # Verify it's a valid transformation matrix
            # Last row should be [0, 0, 0, 1]
            assert matrix[12] == 0
            assert matrix[13] == 0
            assert matrix[14] == 0
            assert matrix[15] == 1
    
    # ===== CROSS-FORMAT VALIDATION =====
    
    async def test_element_count_consistency(self, reference_models):
        """Verify element counts are consistent between IFC and GLB"""
        ifc_gen = IfcGenerator()
        glb_gen = GltfGenerator()
        
        shed = reference_models['shed']
        
        # Generate both formats
        ifc_result = await ifc_gen.generate(shed, format="IFC4")
        glb_result = await glb_gen.generate(shed, format="GLTF")
        
        # Count elements in IFC
        ifc_content = ifc_result.content
        ifc_columns = ifc_content.count('IFCCOLUMN(')
        ifc_beams = ifc_content.count('IFCBEAM(')
        
        # Count elements in GLB
        gltf = json.loads(glb_result.content)
        glb_columns = sum(1 for node in gltf["nodes"] if "Column" in node.get("name", ""))
        glb_beams = sum(1 for node in gltf["nodes"] if "Beam" in node.get("name", "") or "Rafter" in node.get("name", ""))
        
        # Should have similar counts (allowing for grouping differences)
        assert abs(ifc_columns - glb_columns) <= 2
        assert abs(ifc_beams - glb_beams) <= 5
    
    async def test_geometry_bounds_consistency(self, reference_models):
        """Verify geometry bounds are consistent"""
        generator = GltfGenerator()
        carport = reference_models['carport']
        
        result = await generator.generate(carport, format="GLTF")
        gltf = json.loads(result.content)
        
        # Calculate overall bounds from all meshes
        overall_min = [float('inf')] * 3
        overall_max = [float('-inf')] * 3
        
        for mesh in gltf["meshes"]:
            for primitive in mesh["primitives"]:
                pos_accessor = gltf["accessors"][primitive["attributes"]["POSITION"]]
                for i in range(3):
                    overall_min[i] = min(overall_min[i], pos_accessor["min"][i])
                    overall_max[i] = max(overall_max[i], pos_accessor["max"][i])
        
        # Check bounds match expected building dimensions
        # Width: 6000mm, Length: 6000mm, Height: ~2400mm + roof pitch
        expected_bounds = {
            'width': 6000,
            'length': 6000,
            'min_height': 2400,
            'max_height': 3000  # Approximate with roof
        }
        
        # Account for Y-up conversion and some tolerance
        tolerance = 100  # 100mm tolerance
        assert abs(overall_max[0] - overall_min[0] - expected_bounds['width']) < tolerance
        assert overall_max[1] - overall_min[1] >= expected_bounds['min_height'] - tolerance
    
    # ===== PERFORMANCE VALIDATION =====
    
    async def test_generation_performance(self, reference_models):
        """Test generation performance metrics"""
        import time
        
        generators = {
            'IFC': IfcGenerator(),
            'GLB': GltfGenerator()
        }
        
        performance_results = {}
        
        for format_name, generator in generators.items():
            for model_name, model in reference_models.items():
                start_time = time.time()
                
                if format_name == 'IFC':
                    result = await generator.generate(model, format="IFC4")
                else:
                    result = await generator.generate(model, format="GLB")
                
                end_time = time.time()
                
                key = f"{format_name}_{model_name}"
                performance_results[key] = {
                    'time': end_time - start_time,
                    'size': len(result.content),
                    'success': result.success
                }
        
        # Validate performance
        for key, metrics in performance_results.items():
            assert metrics['success']
            assert metrics['time'] < 2.0  # Should complete in under 2 seconds
            assert metrics['size'] > 1000  # Should produce substantial output
        
        return performance_results
    
    async def test_memory_efficiency(self, reference_models):
        """Test memory usage during generation"""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Generate large model
        generator = GltfGenerator()
        shed = reference_models['shed']
        
        # Generate multiple times to test memory leaks
        for _ in range(5):
            result = await generator.generate(shed, format="GLB")
            assert result.success
        
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        # Should not leak excessive memory
        assert memory_increase < 100  # Less than 100MB increase


class OutputAccuracyReport:
    """Generate comprehensive accuracy report"""
    
    @staticmethod
    def generate_report(test_results: Dict[str, Any]) -> str:
        """Generate HTML report of accuracy tests"""
        html = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Output Accuracy Validation Report</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                h1, h2, h3 { color: #333; }
                .pass { color: green; }
                .fail { color: red; }
                table { border-collapse: collapse; width: 100%; margin: 20px 0; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; }
                .metric { background-color: #f9f9f9; }
                pre { background-color: #f5f5f5; padding: 10px; overflow-x: auto; }
            </style>
        </head>
        <body>
            <h1>Output Accuracy Validation Report</h1>
            <p>Generated: {timestamp}</p>
            
            <h2>Test Summary</h2>
            <table>
                <tr>
                    <th>Category</th>
                    <th>Tests Run</th>
                    <th>Passed</th>
                    <th>Failed</th>
                    <th>Success Rate</th>
                </tr>
                {summary_rows}
            </table>
            
            <h2>IFC Accuracy Results</h2>
            {ifc_results}
            
            <h2>GLB Accuracy Results</h2>
            {glb_results}
            
            <h2>Cross-Format Validation</h2>
            {cross_format_results}
            
            <h2>Performance Metrics</h2>
            {performance_results}
            
            <h2>Recommendations</h2>
            {recommendations}
        </body>
        </html>
        """
        
        # Fill in template with actual results
        return html


# Run validation tests
if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"])