"""
Comprehensive Output Test Runner
Executes all output tests and generates detailed coverage and accuracy reports
"""

import asyncio
import sys
import json
import time
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Tuple
import subprocess
import pytest
import coverage


class ComprehensiveOutputTestRunner:
    """Run all output tests and generate comprehensive reports"""
    
    def __init__(self):
        self.results = {
            'timestamp': datetime.now().isoformat(),
            'tests': {},
            'coverage': {},
            'performance': {},
            'accuracy': {},
            'summary': {}
        }
        self.test_files = [
            'test_ifc_generator.py',
            'test_gltf_generator.py', 
            'test_output_service.py',
            'test_ifc_comprehensive.py',
            'test_glb_comprehensive.py',
            'test_output_accuracy_validation.py'
        ]
    
    async def run_all_tests(self):
        """Run all output tests"""
        print("=" * 80)
        print("COMPREHENSIVE OUTPUT TEST SUITE")
        print("=" * 80)
        print(f"Started at: {self.results['timestamp']}")
        print()
        
        # Initialize coverage
        cov = coverage.Coverage(source=['src/output'])
        cov.start()
        
        # Run each test file
        for test_file in self.test_files:
            print(f"\n{'='*60}")
            print(f"Running {test_file}...")
            print(f"{'='*60}")
            
            start_time = time.time()
            
            # Run pytest programmatically
            test_path = Path(__file__).parent / test_file
            if test_path.exists():
                result = pytest.main([
                    str(test_path),
                    '-v',
                    '--tb=short',
                    '--json-report',
                    f'--json-report-file=test_results_{test_file}.json'
                ])
                
                # Record results
                self.results['tests'][test_file] = {
                    'exit_code': result,
                    'duration': time.time() - start_time,
                    'status': 'PASSED' if result == 0 else 'FAILED'
                }
                
                # Load detailed results if available
                report_file = f'test_results_{test_file}.json'
                if Path(report_file).exists():
                    with open(report_file) as f:
                        detailed = json.load(f)
                        self.results['tests'][test_file]['details'] = detailed
            else:
                print(f"WARNING: Test file {test_file} not found!")
                self.results['tests'][test_file] = {
                    'status': 'NOT_FOUND',
                    'duration': 0
                }
        
        # Stop coverage and get results
        cov.stop()
        cov.save()
        
        # Get coverage data
        total_coverage = cov.report()
        self.results['coverage']['total'] = total_coverage
        
        # Generate coverage by file
        coverage_data = {}
        for filename in cov.get_data().measured_files():
            if 'src/output' in filename:
                analysis = cov.analysis2(filename)
                coverage_data[filename] = {
                    'statements': len(analysis[1]),
                    'missing': len(analysis[3]),
                    'coverage': ((len(analysis[1]) - len(analysis[3])) / len(analysis[1]) * 100) if analysis[1] else 0
                }
        self.results['coverage']['files'] = coverage_data
        
        # Run specific accuracy tests
        await self._run_accuracy_validation()
        
        # Generate summary
        self._generate_summary()
        
        # Generate reports
        self._generate_html_report()
        self._generate_json_report()
        
        print("\n" + "="*80)
        print("TEST EXECUTION COMPLETE")
        print("="*80)
        self._print_summary()
    
    async def _run_accuracy_validation(self):
        """Run specific accuracy validation tests"""
        print("\n" + "="*60)
        print("Running Accuracy Validation...")
        print("="*60)
        
        # Import and run accuracy tests
        try:
            from test_output_accuracy_validation import TestOutputAccuracyValidation
            
            validator = TestOutputAccuracyValidation()
            
            # Create test models
            from src.business.building_input import BuildingInput
            from src.business.structure_builder import StructureBuilder
            
            # Simple test case
            simple_input = BuildingInput(
                width=6000, length=6000, eave_height=2400,
                roof_pitch=5, bays=1, structural_system="Portal Frame"
            )
            builder = StructureBuilder()
            simple_model = builder.create_structure(simple_input)
            
            # Test IFC accuracy
            from src.output.ifc_generator import IfcGenerator
            ifc_gen = IfcGenerator()
            ifc_result = await ifc_gen.generate(simple_model, format="IFC4")
            
            # Test GLB accuracy
            from src.output.gltf_generator import GltfGenerator
            glb_gen = GltfGenerator()
            glb_result = await glb_gen.generate(simple_model, format="GLB")
            
            # Validate results
            self.results['accuracy'] = {
                'ifc': {
                    'generated': ifc_result.success,
                    'size': len(ifc_result.content) if ifc_result.success else 0,
                    'entities': self._count_ifc_entities(ifc_result.content) if ifc_result.success else {}
                },
                'glb': {
                    'generated': glb_result.success,
                    'size': len(glb_result.content) if glb_result.success else 0,
                    'valid_structure': self._validate_glb_structure(glb_result.content) if glb_result.success else False
                }
            }
            
        except Exception as e:
            print(f"Accuracy validation error: {e}")
            self.results['accuracy'] = {'error': str(e)}
    
    def _count_ifc_entities(self, content: str) -> Dict[str, int]:
        """Count IFC entities in content"""
        entities = [
            'IFCPROJECT', 'IFCSITE', 'IFCBUILDING', 'IFCBUILDINGSTOREY',
            'IFCCOLUMN', 'IFCBEAM', 'IFCWALL', 'IFCROOF', 'IFCFOOTING',
            'IFCMATERIAL', 'IFCPROPERTYSET'
        ]
        
        counts = {}
        for entity in entities:
            counts[entity] = content.count(f'{entity}(')
        
        return counts
    
    def _validate_glb_structure(self, content: bytes) -> bool:
        """Validate GLB binary structure"""
        import struct
        
        if len(content) < 12:
            return False
        
        magic = struct.unpack('<I', content[0:4])[0]
        version = struct.unpack('<I', content[4:8])[0]
        length = struct.unpack('<I', content[8:12])[0]
        
        return magic == 0x46546C67 and version == 2 and length == len(content)
    
    def _generate_summary(self):
        """Generate test summary"""
        total_tests = len(self.results['tests'])
        passed_tests = sum(1 for t in self.results['tests'].values() if t.get('status') == 'PASSED')
        failed_tests = sum(1 for t in self.results['tests'].values() if t.get('status') == 'FAILED')
        
        total_time = sum(t.get('duration', 0) for t in self.results['tests'].values())
        
        self.results['summary'] = {
            'total_tests': total_tests,
            'passed': passed_tests,
            'failed': failed_tests,
            'not_found': total_tests - passed_tests - failed_tests,
            'success_rate': (passed_tests / total_tests * 100) if total_tests > 0 else 0,
            'total_duration': total_time,
            'coverage': self.results['coverage'].get('total', 0)
        }
    
    def _generate_html_report(self):
        """Generate comprehensive HTML report"""
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Output Test Results - {self.results['timestamp']}</title>
            <style>
                body {{ font-family: 'Segoe UI', Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }}
                .container {{ max-width: 1200px; margin: 0 auto; background: white; padding: 30px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
                h1, h2, h3 {{ color: #333; margin-top: 30px; }}
                h1 {{ border-bottom: 3px solid #007acc; padding-bottom: 10px; }}
                h2 {{ border-bottom: 1px solid #ddd; padding-bottom: 8px; }}
                .summary {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0; }}
                .metric {{ background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; border: 1px solid #e9ecef; }}
                .metric h3 {{ margin: 0 0 10px 0; color: #495057; font-size: 14px; }}
                .metric .value {{ font-size: 36px; font-weight: bold; color: #007acc; }}
                .metric .unit {{ color: #6c757d; font-size: 14px; }}
                .pass {{ color: #28a745; }}
                .fail {{ color: #dc3545; }}
                .warning {{ color: #ffc107; }}
                table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
                th, td {{ padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }}
                th {{ background-color: #f8f9fa; font-weight: 600; color: #495057; }}
                tr:hover {{ background-color: #f8f9fa; }}
                .test-file {{ font-family: monospace; background: #e9ecef; padding: 2px 6px; border-radius: 3px; }}
                .duration {{ color: #6c757d; font-size: 14px; }}
                .coverage-bar {{ width: 100%; height: 20px; background: #e9ecef; border-radius: 4px; overflow: hidden; }}
                .coverage-fill {{ height: 100%; background: #28a745; transition: width 0.3s; }}
                .code {{ background: #f8f9fa; padding: 15px; border-radius: 4px; font-family: monospace; overflow-x: auto; }}
                .badge {{ display: inline-block; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 600; }}
                .badge-success {{ background: #d4edda; color: #155724; }}
                .badge-danger {{ background: #f8d7da; color: #721c24; }}
                .badge-warning {{ background: #fff3cd; color: #856404; }}
                .accuracy-grid {{ display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }}
                .accuracy-box {{ background: #f8f9fa; padding: 20px; border-radius: 8px; }}
                .entity-count {{ display: flex; justify-content: space-between; padding: 5px 0; }}
                .recommendations {{ background: #e7f3ff; padding: 20px; border-radius: 8px; border-left: 4px solid #007acc; }}
                .recommendations ul {{ margin: 10px 0; padding-left: 20px; }}
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🔍 Comprehensive Output Test Report</h1>
                <p><strong>Generated:</strong> {self.results['timestamp']}</p>
                
                <h2>📊 Test Summary</h2>
                <div class="summary">
                    <div class="metric">
                        <h3>Total Tests</h3>
                        <div class="value">{self.results['summary']['total_tests']}</div>
                    </div>
                    <div class="metric">
                        <h3>Passed</h3>
                        <div class="value pass">{self.results['summary']['passed']}</div>
                    </div>
                    <div class="metric">
                        <h3>Failed</h3>
                        <div class="value fail">{self.results['summary']['failed']}</div>
                    </div>
                    <div class="metric">
                        <h3>Success Rate</h3>
                        <div class="value">{self.results['summary']['success_rate']:.1f}<span class="unit">%</span></div>
                    </div>
                    <div class="metric">
                        <h3>Code Coverage</h3>
                        <div class="value">{self.results['coverage'].get('total', 0):.1f}<span class="unit">%</span></div>
                    </div>
                    <div class="metric">
                        <h3>Total Duration</h3>
                        <div class="value">{self.results['summary']['total_duration']:.2f}<span class="unit">s</span></div>
                    </div>
                </div>
                
                <h2>📋 Test Results by File</h2>
                <table>
                    <thead>
                        <tr>
                            <th>Test File</th>
                            <th>Status</th>
                            <th>Duration</th>
                            <th>Details</th>
                        </tr>
                    </thead>
                    <tbody>
                        {self._generate_test_rows()}
                    </tbody>
                </table>
                
                <h2>📈 Code Coverage</h2>
                {self._generate_coverage_section()}
                
                <h2>🎯 Accuracy Validation</h2>
                {self._generate_accuracy_section()}
                
                <h2>💡 Recommendations</h2>
                {self._generate_recommendations()}
                
                <h2>🔧 Next Steps</h2>
                <div class="recommendations">
                    <ul>
                        <li>Review failed tests and fix implementation issues</li>
                        <li>Improve code coverage in areas below 80%</li>
                        <li>Add more edge case tests for complex geometries</li>
                        <li>Validate output against industry standard tools</li>
                        <li>Performance optimize generation for large models</li>
                    </ul>
                </div>
            </div>
        </body>
        </html>
        """
        
        # Save report
        report_path = Path(__file__).parent / 'output_test_report.html'
        with open(report_path, 'w') as f:
            f.write(html)
        
        print(f"\nHTML report saved to: {report_path}")
    
    def _generate_test_rows(self) -> str:
        """Generate HTML rows for test results"""
        rows = []
        for test_file, result in self.results['tests'].items():
            status = result.get('status', 'UNKNOWN')
            status_class = 'pass' if status == 'PASSED' else 'fail' if status == 'FAILED' else 'warning'
            status_badge = f'<span class="badge badge-{"success" if status == "PASSED" else "danger" if status == "FAILED" else "warning"}">{status}</span>'
            
            duration = f"{result.get('duration', 0):.2f}s" if 'duration' in result else 'N/A'
            
            details = ''
            if 'details' in result and 'summary' in result['details']:
                summary = result['details']['summary']
                details = f"Tests: {summary.get('total', 0)}, Passed: {summary.get('passed', 0)}, Failed: {summary.get('failed', 0)}"
            
            rows.append(f"""
                <tr>
                    <td><span class="test-file">{test_file}</span></td>
                    <td>{status_badge}</td>
                    <td><span class="duration">{duration}</span></td>
                    <td>{details}</td>
                </tr>
            """)
        
        return '\n'.join(rows)
    
    def _generate_coverage_section(self) -> str:
        """Generate coverage section HTML"""
        if not self.results['coverage'].get('files'):
            return '<p>No coverage data available</p>'
        
        rows = []
        for file, data in self.results['coverage']['files'].items():
            filename = Path(file).name
            coverage_pct = data['coverage']
            coverage_class = 'pass' if coverage_pct >= 80 else 'warning' if coverage_pct >= 60 else 'fail'
            
            rows.append(f"""
                <tr>
                    <td><code>{filename}</code></td>
                    <td>{data['statements']}</td>
                    <td>{data['missing']}</td>
                    <td>
                        <div style="display: flex; align-items: center; gap: 10px;">
                            <div class="coverage-bar" style="flex: 1;">
                                <div class="coverage-fill" style="width: {coverage_pct}%;"></div>
                            </div>
                            <span class="{coverage_class}" style="min-width: 50px; text-align: right;">{coverage_pct:.1f}%</span>
                        </div>
                    </td>
                </tr>
            """)
        
        return f"""
            <table>
                <thead>
                    <tr>
                        <th>File</th>
                        <th>Statements</th>
                        <th>Missing</th>
                        <th>Coverage</th>
                    </tr>
                </thead>
                <tbody>
                    {''.join(rows)}
                </tbody>
            </table>
        """
    
    def _generate_accuracy_section(self) -> str:
        """Generate accuracy validation section"""
        if not self.results.get('accuracy'):
            return '<p>No accuracy data available</p>'
        
        acc = self.results['accuracy']
        
        ifc_section = ''
        if 'ifc' in acc:
            ifc_data = acc['ifc']
            entity_rows = []
            for entity, count in ifc_data.get('entities', {}).items():
                entity_rows.append(f'<div class="entity-count"><span>{entity}</span><span>{count}</span></div>')
            
            ifc_section = f"""
                <div class="accuracy-box">
                    <h3>IFC Generation</h3>
                    <p>Status: <span class="{'pass' if ifc_data.get('generated') else 'fail'}">{'✓ Generated' if ifc_data.get('generated') else '✗ Failed'}</span></p>
                    <p>File Size: {ifc_data.get('size', 0):,} bytes</p>
                    <h4>Entity Counts:</h4>
                    {''.join(entity_rows)}
                </div>
            """
        
        glb_section = ''
        if 'glb' in acc:
            glb_data = acc['glb']
            glb_section = f"""
                <div class="accuracy-box">
                    <h3>GLB Generation</h3>
                    <p>Status: <span class="{'pass' if glb_data.get('generated') else 'fail'}">{'✓ Generated' if glb_data.get('generated') else '✗ Failed'}</span></p>
                    <p>File Size: {glb_data.get('size', 0):,} bytes</p>
                    <p>Valid Structure: <span class="{'pass' if glb_data.get('valid_structure') else 'fail'}">{'✓ Yes' if glb_data.get('valid_structure') else '✗ No'}</span></p>
                </div>
            """
        
        return f'<div class="accuracy-grid">{ifc_section}{glb_section}</div>'
    
    def _generate_recommendations(self) -> str:
        """Generate recommendations based on results"""
        recommendations = []
        
        # Check test failures
        if self.results['summary']['failed'] > 0:
            recommendations.append("🔴 <strong>Critical:</strong> Fix failing tests before deployment")
        
        # Check coverage
        coverage = self.results['coverage'].get('total', 0)
        if coverage < 80:
            recommendations.append(f"⚠️ <strong>Coverage:</strong> Current coverage is {coverage:.1f}%. Target is 80% minimum")
        
        # Check specific file coverage
        low_coverage_files = []
        for file, data in self.results['coverage'].get('files', {}).items():
            if data['coverage'] < 70:
                low_coverage_files.append(Path(file).name)
        
        if low_coverage_files:
            recommendations.append(f"📝 <strong>Low Coverage Files:</strong> {', '.join(low_coverage_files)}")
        
        # Check accuracy
        if self.results.get('accuracy'):
            if not self.results['accuracy'].get('ifc', {}).get('generated'):
                recommendations.append("🔧 <strong>IFC:</strong> Generation failed - check implementation")
            if not self.results['accuracy'].get('glb', {}).get('generated'):
                recommendations.append("🔧 <strong>GLB:</strong> Generation failed - check implementation")
        
        if not recommendations:
            recommendations.append("✅ All tests passing with good coverage!")
        
        return '<div class="recommendations"><ul>' + ''.join(f'<li>{r}</li>' for r in recommendations) + '</ul></div>'
    
    def _generate_json_report(self):
        """Save detailed JSON report"""
        report_path = Path(__file__).parent / 'output_test_results.json'
        with open(report_path, 'w') as f:
            json.dump(self.results, f, indent=2)
        
        print(f"JSON report saved to: {report_path}")
    
    def _print_summary(self):
        """Print summary to console"""
        summary = self.results['summary']
        
        print(f"\nTest Summary:")
        print(f"  Total Tests: {summary['total_tests']}")
        print(f"  Passed: {summary['passed']} ✓")
        print(f"  Failed: {summary['failed']} ✗")
        print(f"  Success Rate: {summary['success_rate']:.1f}%")
        print(f"  Code Coverage: {self.results['coverage'].get('total', 0):.1f}%")
        print(f"  Total Duration: {summary['total_duration']:.2f}s")
        
        if summary['failed'] > 0:
            print("\n⚠️  Some tests failed. Check the HTML report for details.")
        else:
            print("\n✅ All tests passed!")


async def main():
    """Main entry point"""
    runner = ComprehensiveOutputTestRunner()
    await runner.run_all_tests()


if __name__ == "__main__":
    # Run with asyncio
    asyncio.run(main())