"""
Test suite for simple IFC generator.

Tests the simplified IFC generation functionality.
"""

import pytest
from pathlib import Path
import tempfile
import shutil
from datetime import datetime
from unittest.mock import Mock, patch, MagicMock
import uuid

# Import after modifying path
import sys
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from src.output.ifc.ifc_generator_simple import IFCGenerator
from src.output.base.output_base import OutputFormat, OutputResult
from src.bim.shed_bim import ShedBim, ShedBimPartMain, ShedBimSide
from src.bim.components import ShedBimColumn, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from src.geometry.primitives import Vec3
from src.geometry.matrix import Mat4


class TestIFCGeneratorSimple:
    """Test simplified IFC generator."""
    
    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory."""
        temp_dir = Path(tempfile.mkdtemp())
        yield temp_dir
        shutil.rmtree(temp_dir, ignore_errors=True)
    
    @pytest.fixture
    def generator(self, temp_dir):
        """Create IFC generator instance."""
        return IFCGenerator(output_dir=temp_dir)
    
    @pytest.fixture
    def mock_bim(self):
        """Create mock BIM model."""
        bim = Mock(spec=ShedBim)
        bim.main = Mock(spec=ShedBimPartMain)
        bim.main.roof_type = "GABLE"
        bim.main.get_sides.return_value = []
        bim.main.get_roofs.return_value = []
        return bim
    
    def test_initialization(self, generator):
        """Test generator initialization."""
        assert generator._entity_counter == 0
        assert generator._entity_map == {}
        assert generator.material_library is not None
    
    def test_supported_formats(self, generator):
        """Test supported format list."""
        formats = generator.get_supported_formats()
        
        assert OutputFormat.IFC in formats
        assert OutputFormat.IFC4 in formats
        assert len(formats) == 2
    
    def test_generate_with_invalid_bim(self, generator):
        """Test generation with invalid BIM."""
        result = generator.generate(None, "test")
        
        assert result.success is False
        assert result.format == OutputFormat.IFC
        assert len(result.errors) > 0
        assert "BIM model is None" in result.errors[0]
    
    def test_generate_basic_ifc(self, generator, mock_bim, temp_dir):
        """Test basic IFC generation."""
        # Mock the internal methods
        generator._write_header = Mock()
        generator._write_data = Mock()
        generator._write_footer = Mock()
        
        result = generator.generate(mock_bim, "test_model")
        
        assert result.success is True
        assert result.format == OutputFormat.IFC4  # Default version
        assert result.file_path == temp_dir / "test_model.ifc4"
        
        # Verify methods were called
        generator._write_header.assert_called_once()
        generator._write_data.assert_called_once()
        generator._write_footer.assert_called_once()
    
    def test_generate_ifc2x3_version(self, generator, mock_bim, temp_dir):
        """Test generation with IFC2X3 version."""
        generator._write_header = Mock()
        generator._write_data = Mock()
        generator._write_footer = Mock()
        
        result = generator.generate(mock_bim, "test", version="IFC2X3")
        
        assert result.success is True
        assert result.format == OutputFormat.IFC  # Not IFC4
        assert result.file_path == temp_dir / "test.ifc"
    
    def test_generate_with_options(self, generator, mock_bim):
        """Test generation with custom options."""
        generator._write_header = Mock()
        generator._write_data = Mock()
        generator._write_footer = Mock()
        
        result = generator.generate(
            mock_bim, 
            "test",
            version="IFC4",
            author="Test Author",
            organization="Test Org"
        )
        
        assert result.success is True
        
        # Check header was called with options
        call_args = generator._write_header.call_args[0]
        assert "Test Author" in str(call_args)
        assert "Test Org" in str(call_args)
    
    def test_entity_counter_reset(self, generator, mock_bim):
        """Test entity counter is reset between generations."""
        generator._entity_counter = 100
        generator._entity_map = {"test": 1}
        
        generator._write_header = Mock()
        generator._write_data = Mock()
        generator._write_footer = Mock()
        
        generator.generate(mock_bim, "test")
        
        # Check counter was reset
        assert generator._entity_counter == 0
        assert generator._entity_map == {}
    
    def test_write_header(self, generator):
        """Test IFC header writing."""
        lines = []
        generator._write_header(lines, "test.ifc", "Author", "Organization")
        
        # Check header structure
        assert any("ISO-10303-21" in line for line in lines)
        assert any("HEADER" in line for line in lines)
        assert any("FILE_DESCRIPTION" in line for line in lines)
        assert any("FILE_NAME" in line for line in lines)
        assert any("test.ifc" in line for line in lines)
        assert any("Author" in line for line in lines)
        assert any("Organization" in line for line in lines)
        assert any("ENDSEC" in line for line in lines)
    
    def test_write_footer(self, generator):
        """Test IFC footer writing."""
        lines = []
        generator._write_footer(lines)
        
        assert "ENDSEC;" in lines
        assert "END-ISO-10303-21;" in lines
    
    def test_write_data_section_start(self, generator, mock_bim):
        """Test data section initialization."""
        lines = []
        
        # Mock internal methods to test just section start
        generator._write_project = Mock()
        generator._write_site = Mock()
        generator._write_building = Mock()
        generator._write_building_storey = Mock()
        generator._write_components = Mock()
        
        generator._write_data(lines, mock_bim, "IFC4")
        
        assert "DATA;" in lines
        
        # Verify structure methods called
        generator._write_project.assert_called_once()
        generator._write_site.assert_called_once()
    
    @patch('uuid.uuid4')
    def test_create_entity(self, mock_uuid, generator):
        """Test entity creation with ID management."""
        mock_uuid.return_value.hex = "abc123"
        
        generator._entity_counter = 0
        
        entity = generator._create_entity("IFCWALL", {
            "Name": "'Test Wall'",
            "Description": "'A test wall'"
        })
        
        assert entity.startswith("#1=IFCWALL(")
        assert "'Test Wall'" in entity
        assert "'A test wall'" in entity
        assert "'abc123'" in entity  # GlobalId
        assert generator._entity_counter == 1
    
    def test_create_entity_counter_increment(self, generator):
        """Test entity counter increments correctly."""
        generator._entity_counter = 0
        
        entity1 = generator._create_entity("IFCWALL", {})
        entity2 = generator._create_entity("IFCCOLUMN", {})
        entity3 = generator._create_entity("IFCBEAM", {})
        
        assert entity1.startswith("#1=")
        assert entity2.startswith("#2=")
        assert entity3.startswith("#3=")
        assert generator._entity_counter == 3
    
    def test_format_point_3d(self, generator):
        """Test 3D point formatting."""
        point = Vec3(1.5, 2.75, 3.0)
        
        result = generator._format_point(point)
        
        assert result == "IFCCARTESIANPOINT((1.5,2.75,3.0))"
    
    def test_format_point_2d(self, generator):
        """Test 2D point formatting."""
        from src.geometry.primitives import Vec2
        point = Vec2(4.5, 6.25)
        
        result = generator._format_point(point)
        
        assert result == "IFCCARTESIANPOINT((4.5,6.25))"
    
    def test_format_direction(self, generator):
        """Test direction vector formatting."""
        direction = Vec3(0, 0, 1)
        
        result = generator._format_direction(direction)
        
        assert result == "IFCDIRECTION((0.0,0.0,1.0))"
    
    def test_format_direction_normalized(self, generator):
        """Test direction normalization."""
        direction = Vec3(2, 0, 0)  # Not normalized
        
        result = generator._format_direction(direction)
        
        assert result == "IFCDIRECTION((1.0,0.0,0.0))"  # Should be normalized
    
    def test_write_column_component(self, generator):
        """Test writing column to IFC."""
        lines = []
        
        # Create mock column
        column = Mock(spec=ShedBimColumn)
        column.start_pos = Vec3(0, 0, 0)
        column.end_pos = Vec3(0, 0, 3000)
        column.material = Mock()
        column.material.name = "SHS100x100x4"
        column.tag = "COL_1"
        
        # Mock entity creation
        generator._entity_counter = 10
        generator._create_placement = Mock(return_value="#11")
        generator._create_profile = Mock(return_value="#12")
        generator._create_direction_and_axis = Mock(return_value=("#13", "#14"))
        
        generator._write_column(lines, column)
        
        # Check column entity was created
        assert any("IFCCOLUMN" in line for line in lines)
        assert any("'COL_1'" in line for line in lines)
        
        # Verify helper methods called
        generator._create_placement.assert_called_once()
        generator._create_profile.assert_called_once()
    
    def test_write_beam_component(self, generator):
        """Test writing beam/rafter to IFC."""
        lines = []
        
        # Create mock rafter
        rafter = Mock(spec=ShedBimRafter)
        rafter.start_pos = Vec3(0, 0, 3000)
        rafter.end_pos = Vec3(6000, 0, 3500)
        rafter.material = Mock()
        rafter.material.name = "C20030"
        rafter.tag = "RAFTER_1"
        
        generator._entity_counter = 20
        generator._create_placement = Mock(return_value="#21")
        generator._create_profile = Mock(return_value="#22")
        generator._create_direction_and_axis = Mock(return_value=("#23", "#24"))
        
        generator._write_beam(lines, rafter, "RAFTER")
        
        assert any("IFCBEAM" in line for line in lines)
        assert any("'RAFTER'" in line for line in lines)
        assert any("'RAFTER_1'" in line for line in lines)
    
    def test_create_rectangular_profile(self, generator):
        """Test rectangular profile creation."""
        # Mock frame material
        material = Mock()
        material.material_type = "SHS"
        material.width = 100.0  # mm
        material.height = 150.0  # mm
        
        generator._entity_counter = 30
        profile_ref = generator._create_profile(material)
        
        assert profile_ref.startswith("#")
        # Would need to parse entity to verify dimensions
    
    def test_create_placement(self, generator):
        """Test local placement creation."""
        position = Vec3(1000, 2000, 3000)
        
        generator._entity_counter = 40
        placement_ref = generator._create_placement(position)
        
        assert placement_ref.startswith("#")
        assert generator._entity_counter > 40  # Should have created multiple entities
    
    def test_file_output(self, generator, mock_bim, temp_dir):
        """Test actual file output."""
        # Simple mock setup
        generator._write_header = Mock()
        generator._write_data = Mock()
        generator._write_footer = Mock()
        
        # Make mocks add some content
        def add_header(lines, *args):
            lines.extend(["HEADER;", "ENDSEC;"])
        
        def add_data(lines, *args):
            lines.extend(["DATA;", "#1=IFCPROJECT();", "ENDSEC;"])
        
        def add_footer(lines):
            lines.extend(["END-ISO-10303-21;"])
        
        generator._write_header.side_effect = add_header
        generator._write_data.side_effect = add_data
        generator._write_footer.side_effect = add_footer
        
        result = generator.generate(mock_bim, "output_test")
        
        assert result.success is True
        assert result.file_path.exists()
        
        # Read and verify content
        content = result.file_path.read_text()
        assert "HEADER;" in content
        assert "DATA;" in content
        assert "END-ISO-10303-21;" in content
    
    def test_metadata_creation(self, generator, mock_bim):
        """Test metadata is included in result."""
        generator._write_header = Mock()
        generator._write_data = Mock()
        generator._write_footer = Mock()
        
        result = generator.generate(mock_bim, "test")
        
        assert result.metadata is not None
        assert "generator" in result.metadata
        assert "version" in result.metadata
        assert result.metadata["generator"] == "BIM Backend Python"
    
    def test_complex_bim_structure(self, generator, temp_dir):
        """Test with more complex BIM structure."""
        # Create more detailed BIM
        bim = Mock(spec=ShedBim)
        bim.main = Mock(spec=ShedBimPartMain)
        bim.main.roof_type = "GABLE"
        
        # Add sides with columns
        left_side = Mock(spec=ShedBimSide)
        left_side.columns = []
        for i in range(3):
            col = Mock(spec=ShedBimColumn)
            col.start_pos = Vec3(0, i * 3000, 0)
            col.end_pos = Vec3(0, i * 3000, 3000)
            col.material = Mock()
            col.material.name = f"SHS100x100x4"
            col.tag = f"COL_L_{i+1}"
            col.frame_material = col.material  # For validation
            left_side.columns.append(col)
        
        bim.main.get_sides.return_value = [left_side]
        bim.main.get_roofs.return_value = []
        
        # Generate with detailed structure
        generator._write_header = Mock()
        generator._write_footer = Mock()
        
        # Capture data section
        data_lines = []
        def capture_data(lines, *args):
            lines.append("DATA;")
            data_lines.extend(lines)
        
        generator._write_data = Mock(side_effect=capture_data)
        
        result = generator.generate(bim, "complex_test")
        
        assert result.success is True
        assert result.metadata["column_count"] == 3
    
    def test_error_handling_in_generation(self, generator, mock_bim):
        """Test error handling during generation."""
        # Make write_data raise exception
        generator._write_header = Mock()
        generator._write_data = Mock(side_effect=Exception("Test error"))
        generator._write_footer = Mock()
        
        result = generator.generate(mock_bim, "error_test")
        
        assert result.success is False
        assert len(result.errors) > 0
        assert "Test error" in str(result.errors)
    
    def test_version_differences(self, generator, mock_bim):
        """Test differences between IFC versions."""
        generator._write_header = Mock()
        generator._write_footer = Mock()
        
        # Capture version passed to write_data
        captured_version = None
        def capture_version(lines, bim, version):
            nonlocal captured_version
            captured_version = version
            lines.append("DATA;")
        
        generator._write_data = Mock(side_effect=capture_version)
        
        # Test IFC4
        generator.generate(mock_bim, "test1", version="IFC4")
        assert captured_version == "IFC4"
        
        # Test IFC2X3
        generator.generate(mock_bim, "test2", version="IFC2X3")
        assert captured_version == "IFC2X3"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])