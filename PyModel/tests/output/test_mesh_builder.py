"""
Test suite for mesh building utilities.

Tests mesh data structures and mesh generation functions.
"""

import pytest
import numpy as np
from unittest.mock import Mock, patch
from pathlib import Path

# Import after modifying path
import sys
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from src.output.base.mesh_builder import <PERSON><PERSON><PERSON><PERSON>, MeshBuilder
from src.geometry.primitives import Vec3, Vec2
from src.geometry.matrix import Mat4
from src.materials.base import FrameMaterial, FrameMaterialType


class TestMeshData:
    """Test MeshData class."""
    
    def test_initialization(self):
        """Test MeshData initialization."""
        mesh = MeshData()
        
        assert len(mesh.vertices) == 0
        assert len(mesh.normals) == 0
        assert len(mesh.uvs) == 0
        assert len(mesh.triangles) == 0
        assert mesh.material_name is None
    
    def test_initialization_with_data(self):
        """Test MeshData initialization with data."""
        vertices = [Vec3(0, 0, 0), Vec3(1, 0, 0)]
        normals = [Vec3(0, 0, 1), Vec3(0, 0, 1)]
        
        mesh = MeshData(
            vertices=vertices,
            normals=normals,
            material_name="TestMaterial"
        )
        
        assert mesh.vertices == vertices
        assert mesh.normals == normals
        assert mesh.material_name == "TestMaterial"
    
    def test_add_vertex_basic(self):
        """Test adding vertex with position only."""
        mesh = MeshData()
        
        index = mesh.add_vertex(Vec3(1, 2, 3))
        
        assert index == 0
        assert len(mesh.vertices) == 1
        assert mesh.vertices[0] == Vec3(1, 2, 3)
        assert len(mesh.normals) == 1
        assert mesh.normals[0] == Vec3(0, 0, 1)  # Default normal
        assert len(mesh.uvs) == 1
        assert mesh.uvs[0] == Vec2(0, 0)  # Default UV
    
    def test_add_vertex_with_normal_and_uv(self):
        """Test adding vertex with all data."""
        mesh = MeshData()
        
        position = Vec3(1, 2, 3)
        normal = Vec3(0, 1, 0)
        uv = Vec2(0.5, 0.75)
        
        index = mesh.add_vertex(position, normal, uv)
        
        assert index == 0
        assert mesh.vertices[0] == position
        assert mesh.normals[0] == normal
        assert mesh.uvs[0] == uv
    
    def test_add_multiple_vertices(self):
        """Test adding multiple vertices returns correct indices."""
        mesh = MeshData()
        
        idx0 = mesh.add_vertex(Vec3(0, 0, 0))
        idx1 = mesh.add_vertex(Vec3(1, 0, 0))
        idx2 = mesh.add_vertex(Vec3(0, 1, 0))
        
        assert idx0 == 0
        assert idx1 == 1
        assert idx2 == 2
        assert len(mesh.vertices) == 3
    
    def test_add_triangle(self):
        """Test adding triangle."""
        mesh = MeshData()
        
        # Add vertices first
        mesh.add_vertex(Vec3(0, 0, 0))
        mesh.add_vertex(Vec3(1, 0, 0))
        mesh.add_vertex(Vec3(0, 1, 0))
        
        # Add triangle
        mesh.add_triangle(0, 1, 2)
        
        assert len(mesh.triangles) == 1
        assert mesh.triangles[0] == (0, 1, 2)
    
    def test_add_quad(self):
        """Test adding quad creates two triangles."""
        mesh = MeshData()
        
        # Add 4 vertices
        for i in range(4):
            mesh.add_vertex(Vec3(i, 0, 0))
        
        # Add quad
        mesh.add_quad(0, 1, 2, 3)
        
        assert len(mesh.triangles) == 2
        assert mesh.triangles[0] == (0, 1, 2)
        assert mesh.triangles[1] == (0, 2, 3)
    
    def test_transform_vertices(self):
        """Test transforming vertices by matrix."""
        mesh = MeshData()
        
        # Add vertices
        mesh.add_vertex(Vec3(1, 0, 0))
        mesh.add_vertex(Vec3(0, 1, 0))
        mesh.add_vertex(Vec3(0, 0, 1))
        
        # Create translation matrix
        matrix = Mat4.translation(2, 3, 4)
        
        # Transform
        mesh.transform(matrix)
        
        assert mesh.vertices[0] == Vec3(3, 3, 4)  # 1+2, 0+3, 0+4
        assert mesh.vertices[1] == Vec3(2, 4, 4)  # 0+2, 1+3, 0+4
        assert mesh.vertices[2] == Vec3(2, 3, 5)  # 0+2, 0+3, 1+4
    
    def test_transform_normals(self):
        """Test transforming normals by matrix."""
        mesh = MeshData()
        
        # Add vertex with normal
        mesh.add_vertex(Vec3(0, 0, 0), Vec3(1, 0, 0))
        
        # Create 90 degree rotation around Z
        matrix = Mat4.rotation_z(np.pi / 2)
        
        # Transform
        mesh.transform(matrix)
        
        # Normal should be rotated but still normalized
        expected_normal = Vec3(0, 1, 0)
        assert mesh.normals[0].x == pytest.approx(expected_normal.x, abs=1e-6)
        assert mesh.normals[0].y == pytest.approx(expected_normal.y, abs=1e-6)
        assert mesh.normals[0].z == pytest.approx(expected_normal.z, abs=1e-6)
        
        # Check it's normalized
        assert mesh.normals[0].length() == pytest.approx(1.0)
    
    def test_merge_empty_meshes(self):
        """Test merging empty meshes."""
        mesh1 = MeshData()
        mesh2 = MeshData()
        
        mesh1.merge(mesh2)
        
        assert len(mesh1.vertices) == 0
        assert len(mesh1.triangles) == 0
    
    def test_merge_meshes(self):
        """Test merging two meshes."""
        mesh1 = MeshData()
        mesh2 = MeshData()
        
        # First mesh: triangle
        mesh1.add_vertex(Vec3(0, 0, 0))
        mesh1.add_vertex(Vec3(1, 0, 0))
        mesh1.add_vertex(Vec3(0, 1, 0))
        mesh1.add_triangle(0, 1, 2)
        
        # Second mesh: another triangle
        mesh2.add_vertex(Vec3(2, 0, 0))
        mesh2.add_vertex(Vec3(3, 0, 0))
        mesh2.add_vertex(Vec3(2, 1, 0))
        mesh2.add_triangle(0, 1, 2)
        
        # Merge
        mesh1.merge(mesh2)
        
        assert len(mesh1.vertices) == 6
        assert len(mesh1.triangles) == 2
        
        # Check second triangle has offset indices
        assert mesh1.triangles[1] == (3, 4, 5)
    
    def test_merge_preserves_normals_and_uvs(self):
        """Test merge preserves all vertex data."""
        mesh1 = MeshData()
        mesh2 = MeshData()
        
        # Add vertices with custom normals and UVs
        mesh1.add_vertex(Vec3(0, 0, 0), Vec3(0, 0, 1), Vec2(0, 0))
        mesh2.add_vertex(Vec3(1, 0, 0), Vec3(0, 1, 0), Vec2(1, 1))
        
        mesh1.merge(mesh2)
        
        assert len(mesh1.normals) == 2
        assert len(mesh1.uvs) == 2
        assert mesh1.normals[1] == Vec3(0, 1, 0)
        assert mesh1.uvs[1] == Vec2(1, 1)


class TestMeshBuilder:
    """Test MeshBuilder static methods."""
    
    def test_create_box_basic(self):
        """Test creating basic box mesh."""
        min_pt = Vec3(0, 0, 0)
        max_pt = Vec3(1, 1, 1)
        
        mesh = MeshBuilder.create_box(min_pt, max_pt)
        
        assert len(mesh.vertices) == 8  # Box has 8 vertices
        assert len(mesh.triangles) == 12  # 6 faces * 2 triangles each
        
        # Check corners exist
        assert min_pt in mesh.vertices
        assert max_pt in mesh.vertices
    
    def test_create_box_dimensions(self):
        """Test box has correct dimensions."""
        min_pt = Vec3(-1, -2, -3)
        max_pt = Vec3(4, 5, 6)
        
        mesh = MeshBuilder.create_box(min_pt, max_pt)
        
        # Check all vertices are within bounds
        for vertex in mesh.vertices:
            assert min_pt.x <= vertex.x <= max_pt.x
            assert min_pt.y <= vertex.y <= max_pt.y
            assert min_pt.z <= vertex.z <= max_pt.z
    
    def test_create_box_winding_order(self):
        """Test box faces have correct winding order."""
        mesh = MeshBuilder.create_box(Vec3(0, 0, 0), Vec3(1, 1, 1))
        
        # Should have 12 triangles (6 faces * 2)
        assert len(mesh.triangles) == 12
        
        # Each triangle should have 3 unique indices
        for tri in mesh.triangles:
            assert len(set(tri)) == 3
            assert all(0 <= idx < 8 for idx in tri)
    
    def test_create_cylinder_basic(self):
        """Test creating basic cylinder."""
        base = Vec3(0, 0, 0)
        top = Vec3(0, 0, 1)
        radius = 0.5
        
        mesh = MeshBuilder.create_cylinder(base, top, radius, segments=8)
        
        # 8 segments * 2 (top/bottom) + 2 center vertices
        assert len(mesh.vertices) == 18
        
        # 8 side quads (16 triangles) + 8 cap triangles * 2
        assert len(mesh.triangles) == 32
    
    def test_create_cylinder_radius(self):
        """Test cylinder has correct radius."""
        base = Vec3(0, 0, 0)
        top = Vec3(0, 0, 2)
        radius = 1.5
        
        mesh = MeshBuilder.create_cylinder(base, top, radius, segments=16)
        
        # Check vertices on bottom circle (excluding center vertices)
        for i in range(16):
            vertex = mesh.vertices[i * 2]  # Bottom vertices
            distance = np.sqrt(vertex.x**2 + vertex.y**2)
            assert distance == pytest.approx(radius, rel=1e-6)
            assert vertex.z == pytest.approx(0)
    
    def test_create_cylinder_arbitrary_axis(self):
        """Test cylinder along arbitrary axis."""
        base = Vec3(1, 2, 3)
        top = Vec3(4, 5, 6)
        radius = 0.5
        
        mesh = MeshBuilder.create_cylinder(base, top, radius, segments=4)
        
        # Check that cylinder axis is correct
        axis = (top - base).normalized()
        
        # Center vertices should be at base and top
        assert base in mesh.vertices
        assert top in mesh.vertices
    
    def test_create_frame_profile_simple(self):
        """Test creating frame profile mesh."""
        # Create mock frame material
        frame = Mock(spec=FrameMaterial)
        frame.width = 100.0  # mm
        frame.height = 50.0  # mm
        frame.name = "C10050"
        frame.get_profile_points.return_value = None  # Use fallback
        
        length = 2.0  # meters
        
        mesh = MeshBuilder.create_frame_profile(frame, length)
        
        assert mesh.material_name == "C10050"
        assert len(mesh.vertices) == 8  # Box fallback
        assert len(mesh.triangles) == 12
        
        # Check dimensions (converted from mm to m)
        min_x = min(v.x for v in mesh.vertices)
        max_x = max(v.x for v in mesh.vertices)
        assert max_x - min_x == pytest.approx(0.1)  # 100mm = 0.1m
    
    def test_create_frame_profile_with_points(self):
        """Test frame profile with actual profile points."""
        frame = Mock(spec=FrameMaterial)
        frame.name = "TestProfile"
        
        # C-section profile points (in mm)
        frame.get_profile_points.return_value = [
            Vec2(0, 0),
            Vec2(100, 0),
            Vec2(100, 20),
            Vec2(90, 20),
            Vec2(90, 10),
            Vec2(10, 10),
            Vec2(10, 20),
            Vec2(0, 20)
        ]
        
        length = 1.0
        mesh = MeshBuilder.create_frame_profile(frame, length)
        
        # 8 profile points * 2 (top/bottom)
        assert len(mesh.vertices) == 16
        
        # Check extrusion length
        bottom_z = [v.z for v in mesh.vertices[:8]]
        top_z = [v.z for v in mesh.vertices[8:]]
        assert all(z == 0 for z in bottom_z)
        assert all(z == length for z in top_z)
    
    def test_create_frame_profile_with_transform(self):
        """Test frame profile with transformation."""
        frame = Mock(spec=FrameMaterial)
        frame.width = 100.0
        frame.height = 50.0
        frame.name = "Transformed"
        frame.get_profile_points.return_value = None
        
        # Create rotation matrix
        transform = Mat4.rotation_y(np.pi / 2)
        
        mesh = MeshBuilder.create_frame_profile(frame, 1.0, transform)
        
        # Mesh should be rotated
        # Original would have vertices at z=0 and z=1
        # After Y rotation, should be at x=0 and x=-1
        x_values = [v.x for v in mesh.vertices]
        assert any(x == pytest.approx(0, abs=1e-6) for x in x_values)
        assert any(x == pytest.approx(-1, abs=1e-6) for x in x_values)
    
    def test_create_sheet_basic(self):
        """Test creating sheet mesh."""
        corners = [
            Vec3(0, 0, 0),
            Vec3(1, 0, 0),
            Vec3(1, 1, 0),
            Vec3(0, 1, 0)
        ]
        
        mesh = MeshBuilder.create_sheet(corners, thickness=0.01)
        
        assert len(mesh.vertices) == 8  # 4 corners * 2 sides
        assert len(mesh.triangles) == 12  # 2 faces + 4 sides * 2 triangles
    
    def test_create_sheet_invalid_corners(self):
        """Test sheet creation with wrong number of corners."""
        corners = [Vec3(0, 0, 0), Vec3(1, 0, 0), Vec3(0, 1, 0)]
        
        with pytest.raises(ValueError, match="4 corners"):
            MeshBuilder.create_sheet(corners)
    
    def test_create_sheet_normal_calculation(self):
        """Test sheet normal is calculated correctly."""
        # XY plane sheet
        corners = [
            Vec3(0, 0, 0),
            Vec3(2, 0, 0),
            Vec3(2, 1, 0),
            Vec3(0, 1, 0)
        ]
        
        mesh = MeshBuilder.create_sheet(corners, thickness=0.1)
        
        # Front face vertices should have positive Z normal
        assert mesh.normals[0].z > 0
        assert mesh.normals[1].z > 0
        assert mesh.normals[2].z > 0
        assert mesh.normals[3].z > 0
        
        # Back face vertices should have negative Z normal
        assert mesh.normals[4].z < 0
        assert mesh.normals[5].z < 0
        assert mesh.normals[6].z < 0
        assert mesh.normals[7].z < 0
    
    def test_create_sheet_thickness(self):
        """Test sheet has correct thickness."""
        corners = [
            Vec3(0, 0, 0),
            Vec3(1, 0, 0),
            Vec3(1, 1, 0),
            Vec3(0, 1, 0)
        ]
        thickness = 0.05
        
        mesh = MeshBuilder.create_sheet(corners, thickness=thickness)
        
        # Back face should be offset by thickness
        front_z = [v.z for v in mesh.vertices[:4]]
        back_z = [v.z for v in mesh.vertices[4:]]
        
        assert all(z == pytest.approx(0) for z in front_z)
        assert all(z == pytest.approx(-thickness) for z in back_z)
    
    def test_calculate_normals_empty_mesh(self):
        """Test calculating normals on empty mesh."""
        mesh = MeshData()
        
        # Should not crash
        MeshBuilder.calculate_normals(mesh)
        
        assert len(mesh.normals) == 0
    
    def test_calculate_normals_triangle(self):
        """Test calculating normals for a triangle."""
        mesh = MeshData()
        
        # Add triangle in XY plane
        mesh.add_vertex(Vec3(0, 0, 0))
        mesh.add_vertex(Vec3(1, 0, 0))
        mesh.add_vertex(Vec3(0, 1, 0))
        mesh.add_triangle(0, 1, 2)
        
        # Calculate normals
        MeshBuilder.calculate_normals(mesh)
        
        # All vertices should have Z-up normal
        for normal in mesh.normals:
            assert normal.x == pytest.approx(0)
            assert normal.y == pytest.approx(0)
            assert normal.z == pytest.approx(1)
            assert normal.length() == pytest.approx(1)
    
    def test_calculate_normals_shared_vertex(self):
        """Test normal calculation with shared vertices."""
        mesh = MeshData()
        
        # Create two triangles sharing a vertex
        # Triangle 1: XY plane
        mesh.add_vertex(Vec3(0, 0, 0))  # Shared vertex
        mesh.add_vertex(Vec3(1, 0, 0))
        mesh.add_vertex(Vec3(0, 1, 0))
        
        # Triangle 2: XZ plane
        mesh.add_vertex(Vec3(0, 0, 1))
        
        mesh.add_triangle(0, 1, 2)  # XY plane triangle
        mesh.add_triangle(0, 3, 1)  # XZ plane triangle
        
        MeshBuilder.calculate_normals(mesh)
        
        # Shared vertex should have averaged normal
        shared_normal = mesh.normals[0]
        assert shared_normal.length() == pytest.approx(1)
        
        # Should be between the two face normals
        assert shared_normal.y > 0  # Some Y component from XZ triangle
        assert shared_normal.z > 0  # Some Z component from XY triangle


if __name__ == "__main__":
    pytest.main([__file__, "-v"])