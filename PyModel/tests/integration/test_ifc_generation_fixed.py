#!/usr/bin/env python3
"""
Test script for IFC generation from PyModel directory.
"""

import sys
from pathlib import Path
from datetime import datetime

# Add paths for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent))  # PyModel

from src.ifc_generation import CarportIFCGenerator
from src.business.building_input import BuildingInput, CarportRoofType
from src.business.structure_builder import CarportBuilder


def test_simple_carport():
    """Test a simple carport generation."""
    print("\n" + "="*60)
    print("TESTING SIMPLE CARPORT GENERATION")
    print("="*60)
    
    # Create output directory
    output_dir = Path(__file__).parent.parent.parent / "test_output" / "ifc_test"
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Initialize generator
    generator = CarportIFCGenerator(output_dir)
    
    # Generate a simple gable carport
    print("\nGenerating standard gable carport...")
    path = generator.generate_carport(
        span=6000,
        length=9000,
        height=2700,
        roof_type=CarportRoofType.GABLE,
        pitch=15,
        bays=3,
        slab=True
    )
    
    if path.exists():
        print(f"✅ Success! Generated: {path}")
        print(f"   File size: {path.stat().st_size:,} bytes")
    else:
        print(f"❌ Failed to generate file")
        return False
    
    # Generate a flat roof carport
    print("\nGenerating flat roof carport...")
    path2 = generator.generate_carport(
        span=6000,
        length=9000,
        height=2700,
        roof_type=CarportRoofType.FLAT,
        pitch=5,
        bays=3,
        slab=True
    )
    
    if path2.exists():
        print(f"✅ Success! Generated: {path2}")
        print(f"   File size: {path2.stat().st_size:,} bytes")
    else:
        print(f"❌ Failed to generate file")
        return False
    
    return True


def test_multiple_carports():
    """Test generating multiple carport configurations."""
    print("\n" + "="*60)
    print("TESTING MULTIPLE CARPORT CONFIGURATIONS")
    print("="*60)
    
    # Create output directory
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_dir = Path(f"test_output/carports_{timestamp}")
    
    # Initialize generator
    generator = CarportIFCGenerator(output_dir)
    
    print(f"\nOutput directory: {output_dir}")
    
    # Generate standard set
    print("\nGenerating standard carport set...")
    standard_files = generator.generate_standard_carports()
    
    print(f"\n✅ Generated {len(standard_files)} standard carports")
    
    # Generate report
    report = generator.generate_report(standard_files)
    report_path = output_dir / "test_report.txt"
    
    with open(report_path, 'w') as f:
        f.write(report)
    
    print(f"📄 Report saved to: {report_path}")
    
    return len(standard_files) > 0


def test_direct_creation():
    """Test direct carport creation without generator."""
    print("\n" + "="*60)
    print("TESTING DIRECT CARPORT CREATION")
    print("="*60)
    
    # Create carport structure
    building_input = BuildingInput(
        span=6000,
        length=9000,
        height=2700,
        roof_type=CarportRoofType.GABLE,
        pitch=15,
        bays=3,
        slab=True
    )
    
    print("Creating carport structure...")
    carport = CarportBuilder.create_carport(building_input)
    
    # Check structure
    print("\nStructure analysis:")
    print(f"  Main: {'✓' if carport.main else '✗'}")
    
    if carport.main:
        print(f"  Slab: {'✓' if carport.main.slab else '✗'}")
        print(f"  Left columns: {len(carport.main.side_left.columns) if carport.main.side_left else 0}")
        print(f"  Right columns: {len(carport.main.side_right.columns) if carport.main.side_right else 0}")
        
        if carport.main.roof_left:
            print(f"  Left rafters: {len(carport.main.roof_left.rafters)}")
            print(f"  Left purlin bays: {len(carport.main.roof_left.roof_purlin_bays)}")
        
        if carport.main.roof_right:
            print(f"  Right rafters: {len(carport.main.roof_right.rafters)}")
            print(f"  Right purlin bays: {len(carport.main.roof_right.roof_purlin_bays)}")
    
    return True


def main():
    """Run all tests."""
    print("=" * 80)
    print("IFC GENERATION TEST SUITE")
    print("Testing from PyModel directory")
    print("=" * 80)
    
    try:
        # Test 1: Direct creation
        if not test_direct_creation():
            print("\n❌ Direct creation test failed")
            return
        
        # Test 2: Simple carport
        if not test_simple_carport():
            print("\n❌ Simple carport test failed")
            return
        
        # Test 3: Multiple carports
        if not test_multiple_carports():
            print("\n❌ Multiple carports test failed")
            return
        
        print("\n" + "=" * 80)
        print("✅ ALL TESTS PASSED!")
        print("=" * 80)
        
    except Exception as e:
        print(f"\n❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()