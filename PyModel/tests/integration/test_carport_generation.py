"""
Integration test for complete carport generation workflow.

Tests the full pipeline from input to output generation.
"""

import pytest
import json
import tempfile
from pathlib import Path

from src.business.building_input import BuildingInput, BuildingType, CarportRoofType
from src.business.structure_builder import CarportBuilder
from src.services.encryption import AESEncryption
from src.output.gltf.gltf_generator import G<PERSON><PERSON><PERSON>enerator
from src.output.dxf.dxf_generator import DXFGenerator


class TestCarportGenerationIntegration:
    """Test complete carport generation workflow."""
    
    @pytest.mark.integration
    def test_simple_flat_carport(self):
        """Test generating a simple flat roof carport."""
        # Create input
        input_data = BuildingInput(
            building_type=BuildingType.CARPORT,
            name="Simple Flat Carport",
            roof_type=CarportRoofType.FLAT,
            span=6000,
            length=6000,
            height=2400,
            bays=2,
            wind_speed=32,
            slab=True,
            slab_thickness=100
        )
        
        # Validate input
        assert input_data.validate() == True
        
        # Generate carport structure
        carport = CarportBuilder.create_carport(input_data)
        
        # Verify basic structure
        assert carport is not None
        assert carport.main is not None
        
        # Check sides exist
        assert carport.main.side_left is not None
        assert carport.main.side_right is not None
        
        # Check columns were created
        assert len(carport.main.side_left.columns) > 0
        assert len(carport.main.side_right.columns) > 0
        
        # Verify column count (2 bays = 3 columns per side)
        assert len(carport.main.side_left.columns) == 3
        assert len(carport.main.side_right.columns) == 3
    
    @pytest.mark.integration
    def test_gable_carport_with_pitch(self):
        """Test generating a gable roof carport."""
        # Create input
        input_data = BuildingInput(
            building_type=BuildingType.CARPORT,
            name="Gable Carport",
            roof_type=CarportRoofType.GABLE,
            span=6000,
            length=9000,
            height=2700,
            bays=3,
            wind_speed=36,
            pitch=15.0,
            slab=False
        )
        
        # Validate input
        assert input_data.validate() == True
        
        # Generate carport
        carport = CarportBuilder.create_carport(input_data)
        
        # Verify gable-specific features
        assert carport.main.roof_type == "Gable"
        assert carport.main.roof_left is not None
        assert carport.main.roof_right is not None
        
        # Check rafters exist
        assert hasattr(carport.main.roof_left, 'rafters')
        assert hasattr(carport.main.roof_right, 'rafters')
    
    @pytest.mark.integration
    def test_encrypted_workflow(self):
        """Test the encrypted API workflow."""
        # Create input data
        input_dict = {
            "building_type": "Carport",
            "name": "Encrypted Test",
            "roof_type": "Flat",
            "span": 5000,
            "length": 5000,
            "height": 2500,
            "bays": 1,
            "wind_speed": 28
        }
        
        # Encrypt the input
        aes = AESEncryption("test-secret-key")
        encrypted_data = aes.encrypt(json.dumps(input_dict))
        
        # Decrypt and parse
        decrypted_json = aes.decrypt(encrypted_data)
        decrypted_dict = json.loads(decrypted_json)
        
        # Create BuildingInput from decrypted data
        input_data = BuildingInput(**decrypted_dict)
        
        # Generate carport
        carport = CarportBuilder.create_carport(input_data)
        
        assert carport is not None
        assert carport.main is not None
    
    @pytest.mark.integration
    @pytest.mark.slow
    def test_gltf_generation(self, temp_output_dir):
        """Test GLTF output generation."""
        # Create a simple carport
        input_data = BuildingInput(
            span=4000,
            length=4000,
            height=2400,
            bays=1
        )
        
        carport = CarportBuilder.create_carport(input_data)
        
        # Generate GLTF
        generator = GLTFGenerator()
        gltf_data = generator.generate(carport)
        
        # Verify GLTF structure
        assert isinstance(gltf_data, dict)
        assert "asset" in gltf_data
        assert gltf_data["asset"]["version"] == "2.0"
        
        # Check for required arrays
        assert "scenes" in gltf_data
        assert "nodes" in gltf_data
        assert "meshes" in gltf_data
        assert "buffers" in gltf_data
        
        # Verify we have content
        assert len(gltf_data["nodes"]) > 0
        assert len(gltf_data["meshes"]) > 0
    
    @pytest.mark.integration
    @pytest.mark.slow
    def test_dxf_generation(self, temp_output_dir):
        """Test DXF output generation."""
        # Create a simple carport
        input_data = BuildingInput(
            span=4000,
            length=4000,
            height=2400,
            bays=1
        )
        
        carport = CarportBuilder.create_carport(input_data)
        
        # Generate DXF
        generator = DXFGenerator()
        dxf_content = generator.generate(carport)
        
        # Verify DXF content
        assert isinstance(dxf_content, (str, bytes))
        assert len(dxf_content) > 0
        
        # Check for DXF markers
        content_str = dxf_content if isinstance(dxf_content, str) else dxf_content.decode()
        assert "SECTION" in content_str
        assert "ENTITIES" in content_str
    
    @pytest.mark.integration
    def test_multi_bay_generation(self):
        """Test generating carport with multiple bays."""
        for bay_count in [1, 2, 3, 5, 10]:
            input_data = BuildingInput(
                span=6000,
                length=3000 * bay_count,
                height=2700,
                bays=bay_count
            )
            
            carport = CarportBuilder.create_carport(input_data)
            
            # Verify column count (n bays = n+1 columns per side)
            expected_columns = bay_count + 1
            assert len(carport.main.side_left.columns) == expected_columns
            assert len(carport.main.side_right.columns) == expected_columns
    
    @pytest.mark.integration
    def test_edge_cases(self):
        """Test edge cases in carport generation."""
        # Minimum size carport
        min_input = BuildingInput(
            span=3000,  # Minimum
            length=3000,  # Minimum
            height=2100,  # Minimum
            bays=1
        )
        
        min_carport = CarportBuilder.create_carport(min_input)
        assert min_carport is not None
        
        # Maximum size carport
        max_input = BuildingInput(
            span=12000,  # Maximum
            length=30000,  # Large
            height=6000,  # High
            bays=10
        )
        
        max_carport = CarportBuilder.create_carport(max_input)
        assert max_carport is not None
    
    @pytest.mark.integration
    def test_material_consistency(self):
        """Test that materials are consistently applied."""
        input_data = BuildingInput(
            span=6000,
            length=6000,
            height=2700,
            bays=2
        )
        
        carport = CarportBuilder.create_carport(input_data)
        
        # Check all columns use same material
        left_materials = [col.material.name for col in carport.main.side_left.columns]
        right_materials = [col.material.name for col in carport.main.side_right.columns]
        
        # All columns should use same material
        assert len(set(left_materials)) == 1
        assert len(set(right_materials)) == 1
        assert left_materials[0] == right_materials[0]
    
    @pytest.mark.integration
    def test_coordinate_system(self):
        """Test that coordinate system is consistent."""
        input_data = BuildingInput(
            span=6000,
            length=6000,
            height=2700,
            bays=2
        )
        
        carport = CarportBuilder.create_carport(input_data)
        
        # Check column positions
        for col in carport.main.side_left.columns:
            # Left side columns should have negative X
            assert col.bottom.x < 0
            # Height should match input
            assert abs(col.top.z - col.bottom.z) == pytest.approx(2700, abs=1)
        
        for col in carport.main.side_right.columns:
            # Right side columns should have positive X
            assert col.bottom.x > 0


if __name__ == "__main__":
    pytest.main([__file__, "-v"])