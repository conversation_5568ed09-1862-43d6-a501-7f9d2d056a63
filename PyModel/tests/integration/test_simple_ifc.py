#!/usr/bin/env python3
"""
Simplest possible IFC generation test.
"""

import sys
from pathlib import Path

# Add paths
sys.path.insert(0, str(Path(__file__).parent.parent.parent))  # PyModel

from src.business.building_input import BuildingInput, CarportRoofType
from src.business.structure_builder import CarportBuilder
from src.ifc_generation.ifc_brep_generator import IFCBREPGenerator


def main():
    print("="*60)
    print("SIMPLE IFC GENERATION TEST")
    print("="*60)
    
    # Create the simplest carport - no overhang, basic gable
    print("\nCreating building input...")
    building_input = BuildingInput(
        span=6000,
        length=9000,
        height=2700,
        roof_type=CarportRoofType.GABLE,
        pitch=15,
        bays=3,
        slab=True
    )
    
    print(f"  Span: {building_input.span}mm")
    print(f"  Length: {building_input.length}mm")
    print(f"  Height: {building_input.height}mm")
    print(f"  Roof: {building_input.roof_type.value}")
    print(f"  Pitch: {building_input.pitch}°")
    print(f"  Bays: {building_input.bays}")
    print(f"  Slab: {building_input.slab}")
    
    print("\nCreating carport structure...")
    carport = CarportBuilder.create_carport(building_input)
    
    print("\nStructure created:")
    print(f"  Has main: {carport.main is not None}")
    if carport.main:
        print(f"  Has slab: {carport.main.slab is not None}")
        print(f"  Left columns: {len(carport.main.side_left.columns) if carport.main.side_left else 0}")
        print(f"  Right columns: {len(carport.main.side_right.columns) if carport.main.side_right else 0}")
    
    print("\nGenerating IFC...")
    generator = IFCBREPGenerator()
    output_path = Path(__file__).parent.parent.parent / "test_output" / "simple_gable.ifc"
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    try:
        ifc_content = generator.generate_ifc_content(carport, output_path)
        
        with open(output_path, 'w') as f:
            f.write(ifc_content)
        
        print(f"\n✅ Success! Generated: {output_path}")
        print(f"   File size: {len(ifc_content):,} bytes")
        print(f"   Entities: {ifc_content.count('#')}")
        
    except Exception as e:
        print(f"\n❌ Error generating IFC: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()