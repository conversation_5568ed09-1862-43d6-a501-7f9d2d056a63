"""Comprehensive integration test for Tasks 1, 2, and 3.

This test ensures that the geometry, materials, and BIM modules work together
correctly to create a complete building model.
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent))

import math
from typing import List, Tuple

# Task 1: Geometry imports
from src.geometry import (
    Vec2, Vec3, Line1, Line2, Line3, Box2, Box3,
    Mat4, <PERSON><PERSON>, <PERSON>sis3, Plane3, TriIndex
)

# Task 2: Materials imports  
from src.materials import (
    FrameMaterial, FrameMaterialType,
    CladdingMaterial, ColorMaterial,
    FootingMaterial, FootingMaterialType,
    FastenerMaterial, FastenerMaterialType,
    FlashingMaterial, BracketMaterial,
    DownpipeMaterial, StrapMaterial,
    LiningMaterial, Punching, PunchingWhere
)
from src.materials.helpers import (
    FrameMaterialHelper, CladdingMaterialHelper,
    FootingMaterialHelper, FastenerMaterialHelper
)

# Task 3: BIM imports
from src.bim import (
    <PERSON><PERSON><PERSON><PERSON>, <PERSON>d<PERSON>imPartMain, ShedBimPartLeanto,
    ShedBimSide, ShedBimRoof, ShedBimEnd, ShedBimWall,
    ShedBimSection, ShedBimColumn, ShedBimFooting,
    ShedBimBracket, ShedBimFastener, ShedBimPair, ShedBimSectionCut,
    ShedBimWallGirtBay, ShedBimRoofPurlinBay, ShedBimRoofPurlin,
    ShedBimEaveBeam, ShedBimCladding, ShedBimCladdingSegment,
    ShedBimFlashing, ShedBimDownpipe, ShedBimStrapBrace,
    ShedBimOpening, OpeningInfo, OpeningInfoDesign,
    ShedBimSlab, ShedBimSlabPiece, ShedBimComponentTag
)


class IntegrationTester:
    """Comprehensive integration tester for all three tasks."""
    
    def __init__(self):
        self.errors = []
        self.warnings = []
        self.test_count = 0
        self.pass_count = 0
    
    def log_error(self, message: str):
        self.errors.append(f"ERROR: {message}")
        print(f"❌ {message}")
    
    def log_warning(self, message: str):
        self.warnings.append(f"WARNING: {message}")
        print(f"⚠️  {message}")
    
    def assert_equal(self, actual, expected, message: str, tolerance: float = 1e-10):
        self.test_count += 1
        if isinstance(actual, (int, float)) and isinstance(expected, (int, float)):
            if abs(actual - expected) > tolerance:
                self.log_error(f"{message}: Expected {expected}, got {actual}")
            else:
                self.pass_count += 1
        else:
            if actual != expected:
                self.log_error(f"{message}: Expected {expected}, got {actual}")
            else:
                self.pass_count += 1
    
    def assert_not_none(self, obj, message: str):
        self.test_count += 1
        if obj is None:
            self.log_error(f"{message}: Object is None")
        else:
            self.pass_count += 1
    
    def assert_true(self, condition: bool, message: str):
        self.test_count += 1
        if not condition:
            self.log_error(f"{message}: Condition is False")
        else:
            self.pass_count += 1
    
    def run_all_tests(self):
        """Run all integration tests."""
        print("=" * 80)
        print("COMPREHENSIVE INTEGRATION TEST - TASKS 1, 2, 3")
        print("=" * 80)
        
        print("\n1. Testing Geometry-Materials Integration...")
        self.test_geometry_materials_integration()
        
        print("\n2. Testing Materials-BIM Integration...")
        self.test_materials_bim_integration()
        
        print("\n3. Testing Geometry-BIM Integration...")
        self.test_geometry_bim_integration()
        
        print("\n4. Testing Complete Building Creation...")
        self.test_complete_building_creation()
        
        print("\n5. Testing Real-World Carport Scenario...")
        self.test_real_world_carport()
        
        print("\n6. Testing Data Flow and Transformations...")
        self.test_data_flow_transformations()
        
        print("\n7. Testing Edge Cases and Error Handling...")
        self.test_edge_cases()
        
        print("\n8. Testing C# Alignment...")
        self.test_csharp_alignment()
        
        # Print summary
        print("\n" + "=" * 80)
        print("INTEGRATION TEST SUMMARY")
        print("=" * 80)
        print(f"Total Tests: {self.test_count}")
        print(f"Passed: {self.pass_count}")
        print(f"Failed: {self.test_count - self.pass_count}")
        print(f"Errors: {len(self.errors)}")
        print(f"Warnings: {len(self.warnings)}")
        
        if self.errors:
            print("\nERRORS:")
            for error in self.errors:
                print(f"  {error}")
        
        if self.warnings:
            print("\nWARNINGS:")
            for warning in self.warnings:
                print(f"  {warning}")
        
        success_rate = (self.pass_count / self.test_count * 100) if self.test_count > 0 else 0
        print(f"\nSuccess Rate: {success_rate:.1f}%")
        
        if success_rate == 100:
            print("\n✅ ALL INTEGRATION TESTS PASSED!")
        else:
            print("\n❌ SOME INTEGRATION TESTS FAILED!")
    
    def test_geometry_materials_integration(self):
        """Test that geometry types work correctly with materials."""
        # Test material profile uses Vec2
        profile = CladdingMaterialHelper.CORRUGATED.profile
        self.assert_not_none(profile, "CORRUGATED should have profile")
        self.assert_true(len(profile) > 0, "Profile should have points")
        self.assert_true(isinstance(profile[0], Vec2), "Profile points should be Vec2")
        
        # Test material mesh profile generation
        c_material = FrameMaterialHelper.get_frame_material("C15024")
        self.assert_not_none(c_material, "Should get C15024 material")
        
        # Test footing with Vec3 position
        footing_mat = FootingMaterialHelper.create_block(450, 450, 300)
        self.assert_equal(footing_mat.width, 450, "Footing width")
        self.assert_equal(footing_mat.depth, 300, "Footing depth")
        
        # Test material transformations
        vec = Vec3(100, 200, 300)
        transform = Mat4.translation(vec.x, vec.y, vec.z)
        origin = Vec3(0, 0, 0)
        transformed = transform.transform_point(origin)
        self.assert_equal(transformed.x, 100, "Transformed X")
        self.assert_equal(transformed.y, 200, "Transformed Y")
        self.assert_equal(transformed.z, 300, "Transformed Z")
    
    def test_materials_bim_integration(self):
        """Test that materials integrate correctly with BIM components."""
        # Create column with material
        column_mat = FrameMaterialHelper.get_frame_material("C20024")
        section = ShedBimSection(
            start_pos=Vec3(0, 0, 0),
            end_pos=Vec3(0, 3000, 0),
            material=column_mat,
            rotation=0,
            tag="COL-1"
        )
        self.assert_not_none(section.material, "Section should have material")
        self.assert_equal(section.material.name, "C20024", "Material name")
        self.assert_equal(section.material.web, 203, "Material web height")
        
        # Create footing with material
        footing_mat = FootingMaterialHelper.create_bored(450, 900)
        footing = ShedBimFooting(
            pos=Vec3(0, 0, 0),
            footing=footing_mat,
            tag="F1"
        )
        self.assert_equal(footing.footing.footing_type, FootingMaterialType.BORED, "Footing type")
        
        # Create cladding with material
        cladding = ShedBimCladding(
            cladding=CladdingMaterialHelper.CORRUGATED,
            points=[Vec3(0,0,0), Vec3(3000,0,0), Vec3(3000,3000,0), Vec3(0,3000,0)],
            normal=Vec3(0,0,1)
        )
        self.assert_equal(cladding.cladding.name, "Corrugated", "Cladding material name")
        
        # Test material in complex structures
        wall = ShedBimWall()
        girt_bay = ShedBimWallGirtBay()
        for i in range(3):
            girt = ShedBimSection(
                start_pos=Vec3(0, 600 + i*800, 0),
                end_pos=Vec3(3000, 600 + i*800, 0),
                material=FrameMaterialHelper.get_frame_material("C10019"),
                tag=f"GIRT-{i+1}"
            )
            girt_bay.girts.append(girt)
        wall.girt_bays.append(girt_bay)
        
        self.assert_equal(len(wall.girt_bays[0].girts), 3, "Should have 3 girts")
        self.assert_equal(wall.girt_bays[0].girts[0].material.name, "C10019", "Girt material")
    
    def test_geometry_bim_integration(self):
        """Test that geometry types work correctly with BIM components."""
        # Test Vec3 in BIM structures
        shed = ShedBim()
        shed.outline_frames = [
            [Vec3(0,0,0), Vec3(6000,0,0), Vec3(6000,0,9000), Vec3(0,0,9000)]
        ]
        self.assert_equal(len(shed.outline_frames), 1, "Should have one outline frame")
        self.assert_equal(len(shed.outline_frames[0]), 4, "Frame should have 4 points")
        
        # Test Line1 in BIM parts
        main = ShedBimPartMain()
        main.wall_span_extents = Line1(0, 6000)
        main.wall_length_extents = Line1(0, 9000)
        self.assert_equal(main.wall_span_extents.length(), 6000, "Wall span extent length")
        
        # Test transformations in sections
        section = ShedBimSection(
            start_pos=Vec3(0, 0, 0),
            end_pos=Vec3(0, 3000, 0),
            rotation=math.pi/2  # 90 degrees
        )
        self.assert_equal(section.rotation, math.pi/2, "Section rotation")
        
        # Test Box3 calculations
        points = [Vec3(0,0,0), Vec3(100,200,300), Vec3(-50,150,250)]
        box = Box3.from_list(points)
        self.assert_equal(box.min.x, -50, "Box min X")
        self.assert_equal(box.max.x, 100, "Box max X")
        self.assert_equal(box.min.y, 0, "Box min Y")
        self.assert_equal(box.max.y, 200, "Box max Y")
        
        # Test Basis3 in brackets
        bracket = ShedBimBracket()
        bracket.basis = Basis3.from_xy(Vec3(1,0,0), Vec3(0,1,0))
        self.assert_equal(bracket.basis.x.x, 1, "Bracket basis X.x")
        self.assert_equal(bracket.basis.y.y, 1, "Bracket basis Y.y")
        self.assert_equal(bracket.basis.z.z, 1, "Bracket basis Z.z")
    
    def test_complete_building_creation(self):
        """Test creating a complete building using all three modules."""
        # Create the main structure
        shed = ShedBim()
        shed.main = ShedBimPartMain()
        shed.main.roof_type = "GABLE"
        
        # Set extents using geometry
        shed.main.wall_span_extents = Line1(0, 6000)  # 6m span
        shed.main.wall_length_extents = Line1(0, 9000)  # 9m length
        shed.main.roof_span_extents = Line1(-300, 6300)  # With overhang
        
        # Create left side with columns
        left_side = ShedBimSide(wall_height=2700)
        column_positions = [0, 3000, 6000, 9000]  # 4 columns
        
        for i, x_pos in enumerate(column_positions):
            # Get material from catalog
            col_mat = FrameMaterialHelper.get_frame_material("C15024")
            
            # Create section using geometry
            section = ShedBimSection(
                start_pos=Vec3(x_pos, 0, 0),
                end_pos=Vec3(x_pos, 2700, 0),
                material=col_mat,
                rotation=0,
                tag=f"COL-L{i+1}"
            )
            
            # Add punchings
            section.punchings = [
                Punching(position=600, where=PunchingWhere.WEB),
                Punching(position=1800, where=PunchingWhere.WEB)
            ]
            
            # Create footing
            footing_mat = FootingMaterialHelper.create_block(450, 450, 300)
            footing = ShedBimFooting(
                pos=Vec3(x_pos, 0, 0),
                footing=footing_mat,
                tag=f"F-L{i+1}"
            )
            
            # Create column
            column = ShedBimColumn(
                column=section,
                footing=footing
            )
            
            left_side.columns.append(column)
        
        shed.main.side_left = left_side
        
        # Create right side (similar)
        right_side = ShedBimSide(wall_height=2700)
        for i, x_pos in enumerate(column_positions):
            col_mat = FrameMaterialHelper.get_frame_material("C15024")
            section = ShedBimSection(
                start_pos=Vec3(x_pos, 0, 6000),
                end_pos=Vec3(x_pos, 2700, 6000),
                material=col_mat,
                rotation=0,
                tag=f"COL-R{i+1}"
            )
            
            footing_mat = FootingMaterialHelper.create_block(450, 450, 300)
            footing = ShedBimFooting(
                pos=Vec3(x_pos, 0, 6000),
                footing=footing_mat,
                tag=f"F-R{i+1}"
            )
            
            column = ShedBimColumn(column=section, footing=footing)
            right_side.columns.append(column)
        
        shed.main.side_right = right_side
        
        # Create roof structure
        left_roof = ShedBimRoof()
        right_roof = ShedBimRoof()
        
        # Add rafters
        for i in range(4):
            x_pos = i * 3000
            # Left rafter
            left_rafter = ShedBimSection(
                start_pos=Vec3(x_pos, 2700, 0),
                end_pos=Vec3(x_pos, 3300, 3000),  # Peak at 3.3m
                material=FrameMaterialHelper.get_frame_material("C15024"),
                tag=f"RAFT-L{i+1}"
            )
            left_roof.rafters.append(left_rafter)
            
            # Right rafter
            right_rafter = ShedBimSection(
                start_pos=Vec3(x_pos, 2700, 6000),
                end_pos=Vec3(x_pos, 3300, 3000),
                material=FrameMaterialHelper.get_frame_material("C15024"),
                tag=f"RAFT-R{i+1}"
            )
            right_roof.rafters.append(right_rafter)
        
        shed.main.roof_left = left_roof
        shed.main.roof_right = right_roof
        
        # Add wall cladding
        left_wall = ShedBimWall()
        left_cladding = ShedBimCladding(
            cladding=CladdingMaterialHelper.CORRUGATED,
            color=ColorMaterial(name="COLORBOND_Classic_Cream", r=233, g=224, b=199),
            points=[
                Vec3(0, 0, 0),
                Vec3(9000, 0, 0),
                Vec3(9000, 2700, 0),
                Vec3(0, 2700, 0)
            ],
            normal=Vec3(0, 0, -1),
            right=Vec3(1, 0, 0)
        )
        left_wall.claddings.append(left_cladding)
        left_side.wall = left_wall
        
        # Add component tags
        shed.component_tags.append(
            ShedBimComponentTag(
                tag="FRAME",
                member="COLUMN",
                component="C15024",
                spacing="3000"
            )
        )
        
        # Validate the complete structure
        self.assert_not_none(shed.main, "Should have main part")
        self.assert_equal(len(shed.main.side_left.columns), 4, "Left side columns")
        self.assert_equal(len(shed.main.side_right.columns), 4, "Right side columns")
        self.assert_equal(len(shed.main.roof_left.rafters), 4, "Left rafters")
        self.assert_equal(len(shed.main.roof_right.rafters), 4, "Right rafters")
        self.assert_not_none(shed.main.side_left.wall, "Left wall")
        self.assert_equal(len(shed.main.side_left.wall.claddings), 1, "Left wall cladding")
        
        # Test methods work correctly
        sides = shed.main.get_sides()
        self.assert_equal(len(sides), 2, "Should have 2 sides")
        
        roofs = shed.main.get_roofs()
        self.assert_equal(len(roofs), 2, "Should have 2 roofs")
        
        # Verify geometry calculations
        span_vec = shed.main.roof_right.rafters[0].start_pos - shed.main.roof_left.rafters[0].start_pos
        self.assert_equal(span_vec.length(), 6000, "Span distance", tolerance=0.01)
        
        # Verify material properties
        col_material = shed.main.side_left.columns[0].column.material
        self.assert_equal(col_material.material_type, FrameMaterialType.C, "Column material type")
        self.assert_equal(col_material.web, 152, "Column web height")
        self.assert_equal(col_material.flange, 64, "Column flange width")
    
    def test_real_world_carport(self):
        """Test creating a real-world carport scenario."""
        # Carport specifications
        # - 6m x 9m flat roof carport
        # - 2.4m height
        # - 3 bays (3m each)
        # - 300mm overhang all around
        # - Posts at corners and mid-points
        
        carport = ShedBim()
        carport.main = ShedBimPartMain()
        carport.main.roof_type = "FLAT"
        
        # Define dimensions
        span = 6000
        length = 9000
        height = 2400
        overhang = 300
        bay_size = 3000
        
        # Set extents
        carport.main.wall_span_extents = Line1(0, span)
        carport.main.wall_length_extents = Line1(0, length)
        carport.main.roof_span_extents = Line1(-overhang, span + overhang)
        carport.main.roof_length_extents = Line1(-overhang, length + overhang)
        
        # Create frame outline
        carport.outline_frames = [[
            Vec3(-overhang, height, -overhang),
            Vec3(length + overhang, height, -overhang),
            Vec3(length + overhang, height, span + overhang),
            Vec3(-overhang, height, span + overhang)
        ]]
        
        # Post positions (2x4 grid)
        post_positions = [
            (0, 0), (bay_size, 0), (2*bay_size, 0), (3*bay_size, 0),  # Front
            (0, span), (bay_size, span), (2*bay_size, span), (3*bay_size, span)  # Back
        ]
        
        # Create sides
        front_side = ShedBimSide(wall_height=height)
        back_side = ShedBimSide(wall_height=height)
        
        # Add posts
        for i, (x, z) in enumerate(post_positions):
            # Select appropriate material based on position
            if x == 0 or x == length:  # Corner posts - heavier
                mat = FrameMaterialHelper.get_frame_material("C15024")
            else:  # Intermediate posts
                mat = FrameMaterialHelper.get_frame_material("C10019")
            
            # Create post
            section = ShedBimSection(
                start_pos=Vec3(x, 0, z),
                end_pos=Vec3(x, height, z),
                material=mat,
                rotation=0,
                tag=f"POST-{i+1}"
            )
            
            # Create footing
            footing = ShedBimFooting(
                pos=Vec3(x, 0, z),
                footing=FootingMaterialHelper.create_block(450, 450, 300),
                tag=f"FTG-{i+1}"
            )
            
            column = ShedBimColumn(column=section, footing=footing)
            
            # Add to appropriate side
            if z == 0:
                front_side.columns.append(column)
            else:
                back_side.columns.append(column)
        
        carport.main.side_left = front_side
        carport.main.side_right = back_side
        
        # Create roof with rafters
        roof = ShedBimRoof()
        
        # Rafters span from front to back
        rafter_positions = [0, 1500, 3000, 4500, 6000, 7500, 9000]
        for i, x in enumerate(rafter_positions):
            rafter = ShedBimSection(
                start_pos=Vec3(x, height, -overhang),
                end_pos=Vec3(x, height, span + overhang),
                material=FrameMaterialHelper.get_frame_material("C15024"),
                rotation=0,
                tag=f"RAFTER-{i+1}"
            )
            roof.rafters.append(rafter)
        
        # Add purlins
        purlin_bay = ShedBimRoofPurlinBay()
        purlin_positions = [1000, 2000, 3000, 4000, 5000]
        
        for i, z in enumerate(purlin_positions):
            purlin_section = ShedBimSection(
                start_pos=Vec3(-overhang, height, z),
                end_pos=Vec3(length + overhang, height, z),
                material=FrameMaterialHelper.get_frame_material("C10015"),
                rotation=0,
                tag=f"PURLIN-{i+1}"
            )
            
            purlin = ShedBimRoofPurlin(roof_purlin=purlin_section)
            purlin_bay.roof_purlins.append(purlin)
        
        roof.roof_purlin_bays.append(purlin_bay)
        
        # Add roof cladding
        roof_cladding = ShedBimCladding(
            cladding=CladdingMaterialHelper.MONOCLAD,
            color=ColorMaterial(name="COLORBOND_Monument", r=73, g=74, b=76),
            points=[
                Vec3(-overhang, height, -overhang),
                Vec3(length + overhang, height, -overhang),
                Vec3(length + overhang, height, span + overhang),
                Vec3(-overhang, height, span + overhang)
            ],
            normal=Vec3(0, 1, 0),  # Pointing up
            right=Vec3(1, 0, 0)
        )
        roof.cladding = roof_cladding
        
        carport.main.roof_left = roof  # For flat roof, only use roof_left
        
        # Add gutters and downpipes
        gutter = ShedBimFlashing(
            material=FlashingMaterial(name="GUTTER_QUAD_HI_FRONT"),
            position=Line3(
                Vec3(-overhang, height, -overhang),
                Vec3(length + overhang, height, -overhang)
            ),
            color=ColorMaterial(name="COLORBOND_Monument", r=73, g=74, b=76)
        )
        front_side.eave_flashings.append(gutter)
        
        # Add downpipes at corners
        for i, (x, z) in enumerate([(0, 0), (length, 0)]):
            downpipe = ShedBimDownpipe(
                material=DownpipeMaterial(name="DOWNPIPE_100x75"),
                position=Vec3(x, height, z),
                points=[Vec3(x, height, z), Vec3(x, 300, z)],  # To ground level
                color=ColorMaterial(name="COLORBOND_Monument", r=73, g=74, b=76),
                tag=f"DP-{i+1}"
            )
            front_side.downpipes.append(downpipe)
        
        # Validate carport structure
        self.assert_not_none(carport.main, "Carport should have main part")
        self.assert_equal(carport.main.roof_type, "FLAT", "Roof type")
        self.assert_equal(len(front_side.columns), 4, "Front posts")
        self.assert_equal(len(back_side.columns), 4, "Back posts")
        self.assert_equal(len(roof.rafters), 7, "Rafters")
        self.assert_equal(len(roof.roof_purlin_bays[0].roof_purlins), 5, "Purlins")
        self.assert_not_none(roof.cladding, "Roof cladding")
        self.assert_equal(len(front_side.eave_flashings), 1, "Gutter")
        self.assert_equal(len(front_side.downpipes), 2, "Downpipes")
        
        # Verify dimensions
        roof_area = (length + 2*overhang) * (span + 2*overhang)
        expected_area = 6600 * 9600  # mm²
        self.assert_equal(roof_area, expected_area, "Roof area")
        
        # Verify material usage
        post_materials = set()
        for col in front_side.columns + back_side.columns:
            post_materials.add(col.column.material.name)
        self.assert_equal(len(post_materials), 2, "Should use 2 different post materials")
        self.assert_true("C15024" in post_materials, "Should have C15024 for corners")
        self.assert_true("C10019" in post_materials, "Should have C10019 for intermediate")
    
    def test_data_flow_transformations(self):
        """Test data flow and transformations between modules."""
        # Test 1: Geometry → Materials → BIM
        # Create a transformed section
        base_pos = Vec3(1000, 0, 2000)
        rotation_angle = math.pi / 4  # 45 degrees
        
        # Create transformation matrix
        transform = Mat4.translation(base_pos.x, base_pos.y, base_pos.z)
        rotation = Mat4.rotation_y(rotation_angle)
        combined = transform * rotation
        
        # Transform endpoints
        local_start = Vec3(0, 0, 0)
        local_end = Vec3(0, 3000, 0)
        
        world_start = combined.transform_point(local_start)
        world_end = combined.transform_point(local_end)
        
        # Create section with transformed positions
        section = ShedBimSection(
            start_pos=world_start,
            end_pos=world_end,
            material=FrameMaterialHelper.get_frame_material("C15024"),
            rotation=rotation_angle,
            tag="ROTATED-COL"
        )
        
        # Verify transformation
        length = Vec3.distance(section.start_pos, section.end_pos)
        self.assert_equal(length, 3000, "Section length after transformation", tolerance=0.01)
        
        # Test 2: Complex material profile → Geometry operations
        cladding_profile = CladdingMaterialHelper.CORRUGATED.profile
        
        # Calculate profile bounds
        min_x = min(p.x for p in cladding_profile)
        max_x = max(p.x for p in cladding_profile)
        min_y = min(p.y for p in cladding_profile)
        max_y = max(p.y for p in cladding_profile)
        
        profile_width = max_x - min_x
        profile_height = max_y - min_y
        
        self.assert_true(profile_width > 0, "Profile should have width")
        self.assert_true(profile_height > 0, "Profile should have height")
        
        # Test 3: BIM hierarchy → Geometry calculations
        # Create a lean-to attached to main building
        shed = ShedBim()
        shed.main = ShedBimPartMain()
        shed.main.side_left = ShedBimSide(wall_height=3000)
        shed.main.side_right = ShedBimSide(wall_height=3000)
        
        # Add lean-to
        shed.leanto_left = ShedBimPartLeanto()
        shed.leanto_left.side = ShedBimSide(wall_height=2400)
        
        # Calculate connection points
        main_height = shed.main.side_left.wall_height
        leanto_height = shed.leanto_left.side.wall_height
        height_diff = main_height - leanto_height
        
        self.assert_equal(height_diff, 600, "Height difference for lean-to")
        
        # Test 4: Material properties → BIM validation
        # Verify material can span required distance
        purlin = FrameMaterialHelper.get_frame_material("C15024")
        max_span = 4500  # Typical max span for C15024
        
        # Create purlin section
        purlin_section = ShedBimSection(
            start_pos=Vec3(0, 2400, 0),
            end_pos=Vec3(4500, 2400, 0),
            material=purlin,
            tag="PURLIN-MAX"
        )
        
        actual_span = Vec3.distance(purlin_section.start_pos, purlin_section.end_pos)
        self.assert_true(actual_span <= max_span, f"Purlin span {actual_span} should not exceed {max_span}")
    
    def test_edge_cases(self):
        """Test edge cases and error conditions."""
        # Test 1: Zero-length vectors
        zero_vec = Vec3(0, 0, 0)
        self.assert_equal(zero_vec.length(), 0, "Zero vector length")
        
        # Normalize zero vector should return zero vector
        normalized = Vec3.normal(zero_vec)
        self.assert_equal(normalized.length(), 0, "Normalized zero vector")
        
        # Test 2: Parallel lines intersection
        line1 = Line3(Vec3(0, 0, 0), Vec3(10, 0, 0))
        line2 = Line3(Vec3(0, 1, 0), Vec3(10, 1, 0))
        
        # These lines are parallel and should not intersect
        # Note: Need to check if Line3 has intersection method
        
        # Test 3: Degenerate geometry
        # Triangle with colinear points
        p1 = Vec3(0, 0, 0)
        p2 = Vec3(5, 0, 0)
        p3 = Vec3(10, 0, 0)
        
        # Cross product should be zero
        v1 = p2 - p1
        v2 = p3 - p1
        cross = Vec3.cross(v1, v2)
        self.assert_equal(cross.length(), 0, "Colinear points cross product", tolerance=1e-10)
        
        # Test 4: Empty BIM structures
        empty_shed = ShedBim()
        self.assert_true(empty_shed.main is None, "Empty shed should have no main")
        self.assert_equal(len(empty_shed.outline_frames), 0, "Empty shed outline frames")
        
        # Test 5: Material edge cases
        # Back-to-back section
        b2b_material = FrameMaterialHelper.get_frame_material("2C15024")
        self.assert_true(b2b_material.is_b2b, "Should be back-to-back")
        self.assert_equal(b2b_material.material_type, FrameMaterialType.C, "B2B material type")
        
        # Test 6: Extreme rotations
        for angle in [0, math.pi/2, math.pi, 3*math.pi/2, 2*math.pi]:
            mat = Mat4.rotation_z(angle)
            point = Vec3(1, 0, 0)
            rotated = mat.transform_point(point)
            # Verify rotation maintains distance
            self.assert_equal(rotated.length(), 1, f"Rotation {angle} preserves length", tolerance=1e-10)
        
        # Test 7: Large coordinate values
        large_coord = 1000000.0
        large_vec = Vec3(large_coord, large_coord, large_coord)
        length = large_vec.length()
        expected = math.sqrt(3) * large_coord
        self.assert_equal(length, expected, "Large coordinate length", tolerance=1.0)
        
        # Test 8: Slab edge cases
        slab = ShedBimSlab()
        # No pieces defined
        pieces = slab.get_pieces()
        self.assert_equal(len(pieces), 0, "Empty slab pieces")
        
        # Only middle piece
        slab.middle = ShedBimSlabPiece(thickness=100)
        front = slab.get_front()
        back = slab.get_back()
        self.assert_true(front is slab.middle, "Front defaults to middle")
        self.assert_true(back is slab.middle, "Back defaults to middle")
    
    def test_csharp_alignment(self):
        """Test specific C# alignment requirements."""
        # Test 1: Property naming conventions
        material = FrameMaterialHelper.get_frame_material("C15024")
        # C# uses Width/Height, but also has Web/Flange properties
        self.assert_equal(material.web, material.height, "Web equals height")
        self.assert_equal(material.flange, material.width, "Flange equals width")
        
        # Test 2: Enum values match C#
        self.assert_equal(FrameMaterialType.C.value, 1, "C material type value")
        self.assert_equal(FootingMaterialType.BLOCK.value, 0, "Block footing type value")
        self.assert_equal(OpeningInfoDesign.ROLLER_DOOR.value, 1, "Roller door design value")
        
        # Test 3: Method signatures
        # Vec3 static methods
        v1 = Vec3(1, 2, 3)
        v2 = Vec3(4, 5, 6)
        dot_product = Vec3.dot(v1, v2)
        expected_dot = 1*4 + 2*5 + 3*6  # 32
        self.assert_equal(dot_product, expected_dot, "Vec3 dot product")
        
        # Test 4: Material factory methods
        c_section = FrameMaterial.create_c(
            name="TEST_C",
            section=150,
            is_b2b=False,
            web=152,
            flange=64,
            lip=18.5,
            thickness=2.4,
            web_hole_centers=60
        )
        self.assert_equal(c_section.material_type, FrameMaterialType.C, "Factory creates C type")
        self.assert_equal(c_section.section, 150, "Section size preserved")
        
        # Test 5: Collection behaviors
        # ShedBimPair behavior
        pair = ShedBimPair[ShedBimBracket]()
        bracket1 = ShedBimBracket()
        bracket2 = ShedBimBracket()
        
        pair.add(bracket1)
        self.assert_true(pair.item1 is bracket1, "First item added to item1")
        self.assert_true(pair.item2 is None, "Item2 still none")
        
        pair.add(bracket2)
        self.assert_true(pair.item2 is bracket2, "Second item added to item2")
        
        # Test 6: Nullable/Optional alignment
        section = ShedBimSection()
        self.assert_true(section.start_pos is None, "Start pos defaults to None")
        self.assert_true(section.material is None, "Material defaults to None")
        self.assert_equal(section.rotation, 0.0, "Rotation defaults to 0")
        self.assert_equal(section.tag, "", "Tag defaults to empty string")
        
        # Test 7: String representations
        cut = ShedBimSectionCut(transform=Mat4.identity(), box=Box3(Vec3(0,0,0), Vec3(0,0,0)))
        string_rep = str(cut)
        self.assert_true("Transform:" in string_rep, "String contains Transform")
        self.assert_true("Box:" in string_rep, "String contains Box")
        
        # Test 8: Specific C# patterns
        # Pattern: Get methods that return filtered lists
        c_materials = FrameMaterialHelper.get_all_materials_by_type(FrameMaterialType.C)
        z_materials = FrameMaterialHelper.get_all_materials_by_type(FrameMaterialType.Z)
        self.assert_true(len(c_materials) > 0, "Should have C materials")
        self.assert_true(len(z_materials) > 0, "Should have Z materials")
        self.assert_true(len(c_materials) > len(z_materials), "More C materials than Z")
        
        # Pattern: Computed properties
        slab_piece = ShedBimSlabPiece(thickness=0)
        self.assert_true(slab_piece.is_ground, "Zero thickness is ground")
        self.assert_true(not slab_piece.is_slab, "Zero thickness is not slab")
        
        slab_piece.thickness = 100
        self.assert_true(slab_piece.is_slab, "Positive thickness is slab")
        self.assert_true(not slab_piece.is_ground, "Positive thickness is not ground")


# Run the integration tests
if __name__ == "__main__":
    tester = IntegrationTester()
    tester.run_all_tests()