ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('ViewDefinition [CoordinationView]'),'2;1');
FILE_NAME('carport.ifc','2025-06-30T18:01:30',('PyModel'),('BREP IFC Generator'),'IFC2X3','PyModel','');
FILE_SCHEMA(('IFC2X3'));
ENDSEC;
DATA;
#1= IFCPERSON($,$,'User',$,$,$,$,$);
#2= IFCORGANIZATION($,'PyModel',$,$,$);
#3= IFCAPPLICATION(#2,'1.0','BREP IFC Generator','PyModel');
#4= IFCOWNERHISTORY(#1,#3,.READWRITE.,$,$,$,$,1234567890);
#5= IFCSIUNIT(*,.LENGTHUNIT.,.MILLI.,.METRE.);
#6= IFCSIUNIT(*,.PLANEANGLEUNIT.,$,.RADIAN.);
#7= IFCUNITASSIGNMENT((#5,#6));
#8= IFCCARTESIANPOINT((0,0,0));
#9= IFCDIRECTION((1.,0.,0.));
#10= IFCDIRECTION((0.,0.,1.));
#11= IFCAXIS2PLACEMENT3D(#8,#10,#9);
#12= IFCGEOMETRICREPRESENTATIONCONTEXT($,'Model',3,1e-05,#11,$);
#13= IFCPROJECT('cec68297-a70d-40cb-adf0-9c5d5ebbc4d5',#4,'Carport Project',$,$,$,$,(#12),#7);
#14= IFCSITE('97fd73ee-a715-4c86-82a5-a100f5aadf00',#4,'Site',$,$,$,$,$,.ELEMENT.,$,$,$,$,$);
#15= IFCBUILDING('9155766a-b980-462b-a0d8-690589c9fcb8',#4,'Carport',$,$,$,$,$,.ELEMENT.,$,$,$);
#16= IFCBUILDINGSTOREY('c5efb46c-6c6d-4205-b474-d44db6f9044b',#4,'Ground Floor',$,$,$,$,$,.ELEMENT.,0.0);
#17= IFCRELAGGREGATES('b0f0c428-08c7-44d1-b551-8eb03546f3a8',#4,$,$,#13,(#14));
#18= IFCRELAGGREGATES('707cc9c1-aa0e-4d3c-92ef-c99ece01215d',#4,$,$,#14,(#15));
#19= IFCRELAGGREGATES('8dc8c999-751f-43bf-8043-d5fadee9e1c7',#4,$,$,#15,(#16));
#20= IFCMATERIAL('Steel');
#21= IFCCARTESIANPOINT((-37.5,-37.5,0));
#22= IFCCARTESIANPOINT((37.5,-37.5,0));
#23= IFCCARTESIANPOINT((37.5,37.5,0));
#24= IFCCARTESIANPOINT((-37.5,37.5,0));
#25= IFCCARTESIANPOINT((-37.5,-37.5,3023.294094993037));
#26= IFCCARTESIANPOINT((37.5,-37.5,3023.294094993037));
#27= IFCCARTESIANPOINT((37.5,37.5,3023.294094993037));
#28= IFCCARTESIANPOINT((-37.5,37.5,3023.294094993037));
#29= IFCPOLYLOOP((#21,#22,#23,#24));
#30= IFCFACEOUTERBOUND(#29,.T.);
#31= IFCCARTESIANPOINT((-35.0,-35.0,0));
#32= IFCCARTESIANPOINT((-35.0,35.0,0));
#33= IFCCARTESIANPOINT((35.0,35.0,0));
#34= IFCCARTESIANPOINT((35.0,-35.0,0));
#35= IFCPOLYLOOP((#31,#32,#33,#34));
#36= IFCFACEBOUND(#35,.F.);
#37= IFCFACE((#30,#36));
#38= IFCCLOSEDSHELL((#37));
#39= IFCFACETEDBREP(#38);
#40= IFCCARTESIANPOINT((101.5,37.5,0));
#41= IFCAXIS2PLACEMENT3D(#40,$,$);
#42= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#39));
#43= IFCPRODUCTDEFINITIONSHAPE($,$,(#42));
#44= IFCCOLUMN('f12e1946-1ddb-4315-bbf1-f1e1a40de759',#4,'Column_L1',$,$,#41,#43,$,$);
#45= IFCCARTESIANPOINT((-37.5,-37.5,0));
#46= IFCCARTESIANPOINT((37.5,-37.5,0));
#47= IFCCARTESIANPOINT((37.5,37.5,0));
#48= IFCCARTESIANPOINT((-37.5,37.5,0));
#49= IFCCARTESIANPOINT((-37.5,-37.5,3023.294094993037));
#50= IFCCARTESIANPOINT((37.5,-37.5,3023.294094993037));
#51= IFCCARTESIANPOINT((37.5,37.5,3023.294094993037));
#52= IFCCARTESIANPOINT((-37.5,37.5,3023.294094993037));
#53= IFCPOLYLOOP((#45,#46,#47,#48));
#54= IFCFACEOUTERBOUND(#53,.T.);
#55= IFCCARTESIANPOINT((-35.0,-35.0,0));
#56= IFCCARTESIANPOINT((-35.0,35.0,0));
#57= IFCCARTESIANPOINT((35.0,35.0,0));
#58= IFCCARTESIANPOINT((35.0,-35.0,0));
#59= IFCPOLYLOOP((#55,#56,#57,#58));
#60= IFCFACEBOUND(#59,.F.);
#61= IFCFACE((#54,#60));
#62= IFCCLOSEDSHELL((#61));
#63= IFCFACETEDBREP(#62);
#64= IFCCARTESIANPOINT((101.5,2520.0,0));
#65= IFCAXIS2PLACEMENT3D(#64,$,$);
#66= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#63));
#67= IFCPRODUCTDEFINITIONSHAPE($,$,(#66));
#68= IFCCOLUMN('fee7e2e0-1693-405f-bd59-c7b7aa734e70',#4,'Column_L2',$,$,#65,#67,$,$);
#69= IFCCARTESIANPOINT((-37.5,-37.5,0));
#70= IFCCARTESIANPOINT((37.5,-37.5,0));
#71= IFCCARTESIANPOINT((37.5,37.5,0));
#72= IFCCARTESIANPOINT((-37.5,37.5,0));
#73= IFCCARTESIANPOINT((-37.5,-37.5,3023.294094993037));
#74= IFCCARTESIANPOINT((37.5,-37.5,3023.294094993037));
#75= IFCCARTESIANPOINT((37.5,37.5,3023.294094993037));
#76= IFCCARTESIANPOINT((-37.5,37.5,3023.294094993037));
#77= IFCPOLYLOOP((#69,#70,#71,#72));
#78= IFCFACEOUTERBOUND(#77,.T.);
#79= IFCCARTESIANPOINT((-35.0,-35.0,0));
#80= IFCCARTESIANPOINT((-35.0,35.0,0));
#81= IFCCARTESIANPOINT((35.0,35.0,0));
#82= IFCCARTESIANPOINT((35.0,-35.0,0));
#83= IFCPOLYLOOP((#79,#80,#81,#82));
#84= IFCFACEBOUND(#83,.F.);
#85= IFCFACE((#78,#84));
#86= IFCCLOSEDSHELL((#85));
#87= IFCFACETEDBREP(#86);
#88= IFCCARTESIANPOINT((101.5,5040.0,0));
#89= IFCAXIS2PLACEMENT3D(#88,$,$);
#90= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#87));
#91= IFCPRODUCTDEFINITIONSHAPE($,$,(#90));
#92= IFCCOLUMN('b573df71-7f56-4a20-90b1-4cf443a0645e',#4,'Column_L3',$,$,#89,#91,$,$);
#93= IFCCARTESIANPOINT((-37.5,-37.5,0));
#94= IFCCARTESIANPOINT((37.5,-37.5,0));
#95= IFCCARTESIANPOINT((37.5,37.5,0));
#96= IFCCARTESIANPOINT((-37.5,37.5,0));
#97= IFCCARTESIANPOINT((-37.5,-37.5,3023.294094993037));
#98= IFCCARTESIANPOINT((37.5,-37.5,3023.294094993037));
#99= IFCCARTESIANPOINT((37.5,37.5,3023.294094993037));
#100= IFCCARTESIANPOINT((-37.5,37.5,3023.294094993037));
#101= IFCPOLYLOOP((#93,#94,#95,#96));
#102= IFCFACEOUTERBOUND(#101,.T.);
#103= IFCCARTESIANPOINT((-35.0,-35.0,0));
#104= IFCCARTESIANPOINT((-35.0,35.0,0));
#105= IFCCARTESIANPOINT((35.0,35.0,0));
#106= IFCCARTESIANPOINT((35.0,-35.0,0));
#107= IFCPOLYLOOP((#103,#104,#105,#106));
#108= IFCFACEBOUND(#107,.F.);
#109= IFCFACE((#102,#108));
#110= IFCCLOSEDSHELL((#109));
#111= IFCFACETEDBREP(#110);
#112= IFCCARTESIANPOINT((101.5,7560.0,0));
#113= IFCAXIS2PLACEMENT3D(#112,$,$);
#114= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#111));
#115= IFCPRODUCTDEFINITIONSHAPE($,$,(#114));
#116= IFCCOLUMN('ae7c1365-6efb-454f-95e6-0d755ac483fa',#4,'Column_L4',$,$,#113,#115,$,$);
#117= IFCCARTESIANPOINT((-37.5,-37.5,0));
#118= IFCCARTESIANPOINT((37.5,-37.5,0));
#119= IFCCARTESIANPOINT((37.5,37.5,0));
#120= IFCCARTESIANPOINT((-37.5,37.5,0));
#121= IFCCARTESIANPOINT((-37.5,-37.5,3023.294094993037));
#122= IFCCARTESIANPOINT((37.5,-37.5,3023.294094993037));
#123= IFCCARTESIANPOINT((37.5,37.5,3023.294094993037));
#124= IFCCARTESIANPOINT((-37.5,37.5,3023.294094993037));
#125= IFCPOLYLOOP((#117,#118,#119,#120));
#126= IFCFACEOUTERBOUND(#125,.T.);
#127= IFCCARTESIANPOINT((-35.0,-35.0,0));
#128= IFCCARTESIANPOINT((-35.0,35.0,0));
#129= IFCCARTESIANPOINT((35.0,35.0,0));
#130= IFCCARTESIANPOINT((35.0,-35.0,0));
#131= IFCPOLYLOOP((#127,#128,#129,#130));
#132= IFCFACEBOUND(#131,.F.);
#133= IFCFACE((#126,#132));
#134= IFCCLOSEDSHELL((#133));
#135= IFCFACETEDBREP(#134);
#136= IFCCARTESIANPOINT((101.5,10080.0,0));
#137= IFCAXIS2PLACEMENT3D(#136,$,$);
#138= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#135));
#139= IFCPRODUCTDEFINITIONSHAPE($,$,(#138));
#140= IFCCOLUMN('90d2ff03-7c10-4767-a9b9-8fa32851808f',#4,'Column_L5',$,$,#137,#139,$,$);
#141= IFCCARTESIANPOINT((-37.5,-37.5,0));
#142= IFCCARTESIANPOINT((37.5,-37.5,0));
#143= IFCCARTESIANPOINT((37.5,37.5,0));
#144= IFCCARTESIANPOINT((-37.5,37.5,0));
#145= IFCCARTESIANPOINT((-37.5,-37.5,3023.294094993037));
#146= IFCCARTESIANPOINT((37.5,-37.5,3023.294094993037));
#147= IFCCARTESIANPOINT((37.5,37.5,3023.294094993037));
#148= IFCCARTESIANPOINT((-37.5,37.5,3023.294094993037));
#149= IFCPOLYLOOP((#141,#142,#143,#144));
#150= IFCFACEOUTERBOUND(#149,.T.);
#151= IFCCARTESIANPOINT((-35.0,-35.0,0));
#152= IFCCARTESIANPOINT((-35.0,35.0,0));
#153= IFCCARTESIANPOINT((35.0,35.0,0));
#154= IFCCARTESIANPOINT((35.0,-35.0,0));
#155= IFCPOLYLOOP((#151,#152,#153,#154));
#156= IFCFACEBOUND(#155,.F.);
#157= IFCFACE((#150,#156));
#158= IFCCLOSEDSHELL((#157));
#159= IFCFACETEDBREP(#158);
#160= IFCCARTESIANPOINT((101.5,12562.5,0));
#161= IFCAXIS2PLACEMENT3D(#160,$,$);
#162= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#159));
#163= IFCPRODUCTDEFINITIONSHAPE($,$,(#162));
#164= IFCCOLUMN('480fbc1c-49b4-49e4-9ea1-ca52071ba608',#4,'Column_L6',$,$,#161,#163,$,$);
#165= IFCCARTESIANPOINT((-37.5,-37.5,0));
#166= IFCCARTESIANPOINT((37.5,-37.5,0));
#167= IFCCARTESIANPOINT((37.5,37.5,0));
#168= IFCCARTESIANPOINT((-37.5,37.5,0));
#169= IFCCARTESIANPOINT((-37.5,-37.5,3023.294094993037));
#170= IFCCARTESIANPOINT((37.5,-37.5,3023.294094993037));
#171= IFCCARTESIANPOINT((37.5,37.5,3023.294094993037));
#172= IFCCARTESIANPOINT((-37.5,37.5,3023.294094993037));
#173= IFCPOLYLOOP((#165,#166,#167,#168));
#174= IFCFACEOUTERBOUND(#173,.T.);
#175= IFCCARTESIANPOINT((-35.0,-35.0,0));
#176= IFCCARTESIANPOINT((-35.0,35.0,0));
#177= IFCCARTESIANPOINT((35.0,35.0,0));
#178= IFCCARTESIANPOINT((35.0,-35.0,0));
#179= IFCPOLYLOOP((#175,#176,#177,#178));
#180= IFCFACEBOUND(#179,.F.);
#181= IFCFACE((#174,#180));
#182= IFCCLOSEDSHELL((#181));
#183= IFCFACETEDBREP(#182);
#184= IFCCARTESIANPOINT((8362.5,37.5,0));
#185= IFCAXIS2PLACEMENT3D(#184,$,$);
#186= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#183));
#187= IFCPRODUCTDEFINITIONSHAPE($,$,(#186));
#188= IFCCOLUMN('4790cdcc-59c4-4100-b6d5-1afdb7622d12',#4,'Column_R1',$,$,#185,#187,$,$);
#189= IFCCARTESIANPOINT((-37.5,-37.5,0));
#190= IFCCARTESIANPOINT((37.5,-37.5,0));
#191= IFCCARTESIANPOINT((37.5,37.5,0));
#192= IFCCARTESIANPOINT((-37.5,37.5,0));
#193= IFCCARTESIANPOINT((-37.5,-37.5,3023.294094993037));
#194= IFCCARTESIANPOINT((37.5,-37.5,3023.294094993037));
#195= IFCCARTESIANPOINT((37.5,37.5,3023.294094993037));
#196= IFCCARTESIANPOINT((-37.5,37.5,3023.294094993037));
#197= IFCPOLYLOOP((#189,#190,#191,#192));
#198= IFCFACEOUTERBOUND(#197,.T.);
#199= IFCCARTESIANPOINT((-35.0,-35.0,0));
#200= IFCCARTESIANPOINT((-35.0,35.0,0));
#201= IFCCARTESIANPOINT((35.0,35.0,0));
#202= IFCCARTESIANPOINT((35.0,-35.0,0));
#203= IFCPOLYLOOP((#199,#200,#201,#202));
#204= IFCFACEBOUND(#203,.F.);
#205= IFCFACE((#198,#204));
#206= IFCCLOSEDSHELL((#205));
#207= IFCFACETEDBREP(#206);
#208= IFCCARTESIANPOINT((8362.5,2520.0,0));
#209= IFCAXIS2PLACEMENT3D(#208,$,$);
#210= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#207));
#211= IFCPRODUCTDEFINITIONSHAPE($,$,(#210));
#212= IFCCOLUMN('2fb532d8-1312-4390-84a8-f8b6cf9bd193',#4,'Column_R2',$,$,#209,#211,$,$);
#213= IFCCARTESIANPOINT((-37.5,-37.5,0));
#214= IFCCARTESIANPOINT((37.5,-37.5,0));
#215= IFCCARTESIANPOINT((37.5,37.5,0));
#216= IFCCARTESIANPOINT((-37.5,37.5,0));
#217= IFCCARTESIANPOINT((-37.5,-37.5,3023.294094993037));
#218= IFCCARTESIANPOINT((37.5,-37.5,3023.294094993037));
#219= IFCCARTESIANPOINT((37.5,37.5,3023.294094993037));
#220= IFCCARTESIANPOINT((-37.5,37.5,3023.294094993037));
#221= IFCPOLYLOOP((#213,#214,#215,#216));
#222= IFCFACEOUTERBOUND(#221,.T.);
#223= IFCCARTESIANPOINT((-35.0,-35.0,0));
#224= IFCCARTESIANPOINT((-35.0,35.0,0));
#225= IFCCARTESIANPOINT((35.0,35.0,0));
#226= IFCCARTESIANPOINT((35.0,-35.0,0));
#227= IFCPOLYLOOP((#223,#224,#225,#226));
#228= IFCFACEBOUND(#227,.F.);
#229= IFCFACE((#222,#228));
#230= IFCCLOSEDSHELL((#229));
#231= IFCFACETEDBREP(#230);
#232= IFCCARTESIANPOINT((8362.5,5040.0,0));
#233= IFCAXIS2PLACEMENT3D(#232,$,$);
#234= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#231));
#235= IFCPRODUCTDEFINITIONSHAPE($,$,(#234));
#236= IFCCOLUMN('6aa2dc6b-1833-4425-8a4c-3f644328ccdf',#4,'Column_R3',$,$,#233,#235,$,$);
#237= IFCCARTESIANPOINT((-37.5,-37.5,0));
#238= IFCCARTESIANPOINT((37.5,-37.5,0));
#239= IFCCARTESIANPOINT((37.5,37.5,0));
#240= IFCCARTESIANPOINT((-37.5,37.5,0));
#241= IFCCARTESIANPOINT((-37.5,-37.5,3023.294094993037));
#242= IFCCARTESIANPOINT((37.5,-37.5,3023.294094993037));
#243= IFCCARTESIANPOINT((37.5,37.5,3023.294094993037));
#244= IFCCARTESIANPOINT((-37.5,37.5,3023.294094993037));
#245= IFCPOLYLOOP((#237,#238,#239,#240));
#246= IFCFACEOUTERBOUND(#245,.T.);
#247= IFCCARTESIANPOINT((-35.0,-35.0,0));
#248= IFCCARTESIANPOINT((-35.0,35.0,0));
#249= IFCCARTESIANPOINT((35.0,35.0,0));
#250= IFCCARTESIANPOINT((35.0,-35.0,0));
#251= IFCPOLYLOOP((#247,#248,#249,#250));
#252= IFCFACEBOUND(#251,.F.);
#253= IFCFACE((#246,#252));
#254= IFCCLOSEDSHELL((#253));
#255= IFCFACETEDBREP(#254);
#256= IFCCARTESIANPOINT((8362.5,7560.0,0));
#257= IFCAXIS2PLACEMENT3D(#256,$,$);
#258= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#255));
#259= IFCPRODUCTDEFINITIONSHAPE($,$,(#258));
#260= IFCCOLUMN('669ec110-34e7-4e1b-9385-c1976b368f78',#4,'Column_R4',$,$,#257,#259,$,$);
#261= IFCCARTESIANPOINT((-37.5,-37.5,0));
#262= IFCCARTESIANPOINT((37.5,-37.5,0));
#263= IFCCARTESIANPOINT((37.5,37.5,0));
#264= IFCCARTESIANPOINT((-37.5,37.5,0));
#265= IFCCARTESIANPOINT((-37.5,-37.5,3023.294094993037));
#266= IFCCARTESIANPOINT((37.5,-37.5,3023.294094993037));
#267= IFCCARTESIANPOINT((37.5,37.5,3023.294094993037));
#268= IFCCARTESIANPOINT((-37.5,37.5,3023.294094993037));
#269= IFCPOLYLOOP((#261,#262,#263,#264));
#270= IFCFACEOUTERBOUND(#269,.T.);
#271= IFCCARTESIANPOINT((-35.0,-35.0,0));
#272= IFCCARTESIANPOINT((-35.0,35.0,0));
#273= IFCCARTESIANPOINT((35.0,35.0,0));
#274= IFCCARTESIANPOINT((35.0,-35.0,0));
#275= IFCPOLYLOOP((#271,#272,#273,#274));
#276= IFCFACEBOUND(#275,.F.);
#277= IFCFACE((#270,#276));
#278= IFCCLOSEDSHELL((#277));
#279= IFCFACETEDBREP(#278);
#280= IFCCARTESIANPOINT((8362.5,10080.0,0));
#281= IFCAXIS2PLACEMENT3D(#280,$,$);
#282= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#279));
#283= IFCPRODUCTDEFINITIONSHAPE($,$,(#282));
#284= IFCCOLUMN('1f72985d-9f83-4416-a4dd-2ad6d89b699a',#4,'Column_R5',$,$,#281,#283,$,$);
#285= IFCCARTESIANPOINT((-37.5,-37.5,0));
#286= IFCCARTESIANPOINT((37.5,-37.5,0));
#287= IFCCARTESIANPOINT((37.5,37.5,0));
#288= IFCCARTESIANPOINT((-37.5,37.5,0));
#289= IFCCARTESIANPOINT((-37.5,-37.5,3023.294094993037));
#290= IFCCARTESIANPOINT((37.5,-37.5,3023.294094993037));
#291= IFCCARTESIANPOINT((37.5,37.5,3023.294094993037));
#292= IFCCARTESIANPOINT((-37.5,37.5,3023.294094993037));
#293= IFCPOLYLOOP((#285,#286,#287,#288));
#294= IFCFACEOUTERBOUND(#293,.T.);
#295= IFCCARTESIANPOINT((-35.0,-35.0,0));
#296= IFCCARTESIANPOINT((-35.0,35.0,0));
#297= IFCCARTESIANPOINT((35.0,35.0,0));
#298= IFCCARTESIANPOINT((35.0,-35.0,0));
#299= IFCPOLYLOOP((#295,#296,#297,#298));
#300= IFCFACEBOUND(#299,.F.);
#301= IFCFACE((#294,#300));
#302= IFCCLOSEDSHELL((#301));
#303= IFCFACETEDBREP(#302);
#304= IFCCARTESIANPOINT((8362.5,12562.5,0));
#305= IFCAXIS2PLACEMENT3D(#304,$,$);
#306= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#303));
#307= IFCPRODUCTDEFINITIONSHAPE($,$,(#306));
#308= IFCCOLUMN('6cf400b5-b01e-4174-a83b-229e383a18fd',#4,'Column_R6',$,$,#305,#307,$,$);
#309= IFCCARTESIANPOINT((0,0,0));
#310= IFCCARTESIANPOINT((64,0,0));
#311= IFCCARTESIANPOINT((64,152,0));
#312= IFCCARTESIANPOINT((0,152,0));
#313= IFCCARTESIANPOINT((0,0,4366.219631528018));
#314= IFCCARTESIANPOINT((64,0,4366.219631528018));
#315= IFCCARTESIANPOINT((64,152,4366.219631528018));
#316= IFCCARTESIANPOINT((0,152,4366.219631528018));
#317= IFCPOLYLOOP((#309,#310,#311,#312));
#318= IFCFACEOUTERBOUND(#317,.T.);
#319= IFCFACE((#318));
#320= IFCPOLYLOOP((#313,#316,#315,#314));
#321= IFCFACEOUTERBOUND(#320,.T.);
#322= IFCFACE((#321));
#323= IFCPOLYLOOP((#309,#313,#314,#310));
#324= IFCFACEOUTERBOUND(#323,.T.);
#325= IFCFACE((#324));
#326= IFCPOLYLOOP((#311,#315,#316,#312));
#327= IFCFACEOUTERBOUND(#326,.T.);
#328= IFCFACE((#327));
#329= IFCPOLYLOOP((#309,#312,#316,#313));
#330= IFCFACEOUTERBOUND(#329,.T.);
#331= IFCFACE((#330));
#332= IFCPOLYLOOP((#310,#314,#315,#311));
#333= IFCFACEOUTERBOUND(#332,.T.);
#334= IFCFACE((#333));
#335= IFCCLOSEDSHELL((#319,#322,#325,#328,#331,#334));
#336= IFCFACETEDBREP(#335);
#337= IFCCARTESIANPOINT((101.5,37.5,3023.294094993037));
#338= IFCDIRECTION((0.938683883514507,0.0,0.3447790115858612));
#339= IFCAXIS2PLACEMENT3D(#337,#338,$);
#340= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#336));
#341= IFCPRODUCTDEFINITIONSHAPE($,$,(#340));
#342= IFCMEMBER('814221ee-fa75-4a21-a9d9-e27fac16f796',#4,'Rafter_L1',$,$,#339,#341,$,$);
#343= IFCCARTESIANPOINT((0,0,0));
#344= IFCCARTESIANPOINT((64,0,0));
#345= IFCCARTESIANPOINT((64,152,0));
#346= IFCCARTESIANPOINT((0,152,0));
#347= IFCCARTESIANPOINT((0,0,4366.219631528018));
#348= IFCCARTESIANPOINT((64,0,4366.219631528018));
#349= IFCCARTESIANPOINT((64,152,4366.219631528018));
#350= IFCCARTESIANPOINT((0,152,4366.219631528018));
#351= IFCPOLYLOOP((#343,#344,#345,#346));
#352= IFCFACEOUTERBOUND(#351,.T.);
#353= IFCFACE((#352));
#354= IFCPOLYLOOP((#347,#350,#349,#348));
#355= IFCFACEOUTERBOUND(#354,.T.);
#356= IFCFACE((#355));
#357= IFCPOLYLOOP((#343,#347,#348,#344));
#358= IFCFACEOUTERBOUND(#357,.T.);
#359= IFCFACE((#358));
#360= IFCPOLYLOOP((#345,#349,#350,#346));
#361= IFCFACEOUTERBOUND(#360,.T.);
#362= IFCFACE((#361));
#363= IFCPOLYLOOP((#343,#346,#350,#347));
#364= IFCFACEOUTERBOUND(#363,.T.);
#365= IFCFACE((#364));
#366= IFCPOLYLOOP((#344,#348,#349,#345));
#367= IFCFACEOUTERBOUND(#366,.T.);
#368= IFCFACE((#367));
#369= IFCCLOSEDSHELL((#353,#356,#359,#362,#365,#368));
#370= IFCFACETEDBREP(#369);
#371= IFCCARTESIANPOINT((101.5,2520.0,3023.294094993037));
#372= IFCDIRECTION((0.938683883514507,0.0,0.3447790115858612));
#373= IFCAXIS2PLACEMENT3D(#371,#372,$);
#374= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#370));
#375= IFCPRODUCTDEFINITIONSHAPE($,$,(#374));
#376= IFCMEMBER('ecfb955e-8767-4ec6-b0ef-ebb331df8d4b',#4,'Rafter_L2',$,$,#373,#375,$,$);
#377= IFCCARTESIANPOINT((0,0,0));
#378= IFCCARTESIANPOINT((64,0,0));
#379= IFCCARTESIANPOINT((64,152,0));
#380= IFCCARTESIANPOINT((0,152,0));
#381= IFCCARTESIANPOINT((0,0,4366.219631528018));
#382= IFCCARTESIANPOINT((64,0,4366.219631528018));
#383= IFCCARTESIANPOINT((64,152,4366.219631528018));
#384= IFCCARTESIANPOINT((0,152,4366.219631528018));
#385= IFCPOLYLOOP((#377,#378,#379,#380));
#386= IFCFACEOUTERBOUND(#385,.T.);
#387= IFCFACE((#386));
#388= IFCPOLYLOOP((#381,#384,#383,#382));
#389= IFCFACEOUTERBOUND(#388,.T.);
#390= IFCFACE((#389));
#391= IFCPOLYLOOP((#377,#381,#382,#378));
#392= IFCFACEOUTERBOUND(#391,.T.);
#393= IFCFACE((#392));
#394= IFCPOLYLOOP((#379,#383,#384,#380));
#395= IFCFACEOUTERBOUND(#394,.T.);
#396= IFCFACE((#395));
#397= IFCPOLYLOOP((#377,#380,#384,#381));
#398= IFCFACEOUTERBOUND(#397,.T.);
#399= IFCFACE((#398));
#400= IFCPOLYLOOP((#378,#382,#383,#379));
#401= IFCFACEOUTERBOUND(#400,.T.);
#402= IFCFACE((#401));
#403= IFCCLOSEDSHELL((#387,#390,#393,#396,#399,#402));
#404= IFCFACETEDBREP(#403);
#405= IFCCARTESIANPOINT((101.5,5040.0,3023.294094993037));
#406= IFCDIRECTION((0.938683883514507,0.0,0.3447790115858612));
#407= IFCAXIS2PLACEMENT3D(#405,#406,$);
#408= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#404));
#409= IFCPRODUCTDEFINITIONSHAPE($,$,(#408));
#410= IFCMEMBER('2c82d806-41b7-4e18-8f3d-2bece18fc64a',#4,'Rafter_L3',$,$,#407,#409,$,$);
#411= IFCCARTESIANPOINT((0,0,0));
#412= IFCCARTESIANPOINT((64,0,0));
#413= IFCCARTESIANPOINT((64,152,0));
#414= IFCCARTESIANPOINT((0,152,0));
#415= IFCCARTESIANPOINT((0,0,4366.219631528018));
#416= IFCCARTESIANPOINT((64,0,4366.219631528018));
#417= IFCCARTESIANPOINT((64,152,4366.219631528018));
#418= IFCCARTESIANPOINT((0,152,4366.219631528018));
#419= IFCPOLYLOOP((#411,#412,#413,#414));
#420= IFCFACEOUTERBOUND(#419,.T.);
#421= IFCFACE((#420));
#422= IFCPOLYLOOP((#415,#418,#417,#416));
#423= IFCFACEOUTERBOUND(#422,.T.);
#424= IFCFACE((#423));
#425= IFCPOLYLOOP((#411,#415,#416,#412));
#426= IFCFACEOUTERBOUND(#425,.T.);
#427= IFCFACE((#426));
#428= IFCPOLYLOOP((#413,#417,#418,#414));
#429= IFCFACEOUTERBOUND(#428,.T.);
#430= IFCFACE((#429));
#431= IFCPOLYLOOP((#411,#414,#418,#415));
#432= IFCFACEOUTERBOUND(#431,.T.);
#433= IFCFACE((#432));
#434= IFCPOLYLOOP((#412,#416,#417,#413));
#435= IFCFACEOUTERBOUND(#434,.T.);
#436= IFCFACE((#435));
#437= IFCCLOSEDSHELL((#421,#424,#427,#430,#433,#436));
#438= IFCFACETEDBREP(#437);
#439= IFCCARTESIANPOINT((101.5,7560.0,3023.294094993037));
#440= IFCDIRECTION((0.938683883514507,0.0,0.3447790115858612));
#441= IFCAXIS2PLACEMENT3D(#439,#440,$);
#442= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#438));
#443= IFCPRODUCTDEFINITIONSHAPE($,$,(#442));
#444= IFCMEMBER('763a008c-0f1b-46eb-96ca-82a181080588',#4,'Rafter_L4',$,$,#441,#443,$,$);
#445= IFCCARTESIANPOINT((0,0,0));
#446= IFCCARTESIANPOINT((64,0,0));
#447= IFCCARTESIANPOINT((64,152,0));
#448= IFCCARTESIANPOINT((0,152,0));
#449= IFCCARTESIANPOINT((0,0,4366.219631528018));
#450= IFCCARTESIANPOINT((64,0,4366.219631528018));
#451= IFCCARTESIANPOINT((64,152,4366.219631528018));
#452= IFCCARTESIANPOINT((0,152,4366.219631528018));
#453= IFCPOLYLOOP((#445,#446,#447,#448));
#454= IFCFACEOUTERBOUND(#453,.T.);
#455= IFCFACE((#454));
#456= IFCPOLYLOOP((#449,#452,#451,#450));
#457= IFCFACEOUTERBOUND(#456,.T.);
#458= IFCFACE((#457));
#459= IFCPOLYLOOP((#445,#449,#450,#446));
#460= IFCFACEOUTERBOUND(#459,.T.);
#461= IFCFACE((#460));
#462= IFCPOLYLOOP((#447,#451,#452,#448));
#463= IFCFACEOUTERBOUND(#462,.T.);
#464= IFCFACE((#463));
#465= IFCPOLYLOOP((#445,#448,#452,#449));
#466= IFCFACEOUTERBOUND(#465,.T.);
#467= IFCFACE((#466));
#468= IFCPOLYLOOP((#446,#450,#451,#447));
#469= IFCFACEOUTERBOUND(#468,.T.);
#470= IFCFACE((#469));
#471= IFCCLOSEDSHELL((#455,#458,#461,#464,#467,#470));
#472= IFCFACETEDBREP(#471);
#473= IFCCARTESIANPOINT((101.5,10080.0,3023.294094993037));
#474= IFCDIRECTION((0.938683883514507,0.0,0.3447790115858612));
#475= IFCAXIS2PLACEMENT3D(#473,#474,$);
#476= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#472));
#477= IFCPRODUCTDEFINITIONSHAPE($,$,(#476));
#478= IFCMEMBER('bedacf98-c2c1-4888-bada-235f499367be',#4,'Rafter_L5',$,$,#475,#477,$,$);
#479= IFCCARTESIANPOINT((0,0,0));
#480= IFCCARTESIANPOINT((64,0,0));
#481= IFCCARTESIANPOINT((64,152,0));
#482= IFCCARTESIANPOINT((0,152,0));
#483= IFCCARTESIANPOINT((0,0,4366.219631528018));
#484= IFCCARTESIANPOINT((64,0,4366.219631528018));
#485= IFCCARTESIANPOINT((64,152,4366.219631528018));
#486= IFCCARTESIANPOINT((0,152,4366.219631528018));
#487= IFCPOLYLOOP((#479,#480,#481,#482));
#488= IFCFACEOUTERBOUND(#487,.T.);
#489= IFCFACE((#488));
#490= IFCPOLYLOOP((#483,#486,#485,#484));
#491= IFCFACEOUTERBOUND(#490,.T.);
#492= IFCFACE((#491));
#493= IFCPOLYLOOP((#479,#483,#484,#480));
#494= IFCFACEOUTERBOUND(#493,.T.);
#495= IFCFACE((#494));
#496= IFCPOLYLOOP((#481,#485,#486,#482));
#497= IFCFACEOUTERBOUND(#496,.T.);
#498= IFCFACE((#497));
#499= IFCPOLYLOOP((#479,#482,#486,#483));
#500= IFCFACEOUTERBOUND(#499,.T.);
#501= IFCFACE((#500));
#502= IFCPOLYLOOP((#480,#484,#485,#481));
#503= IFCFACEOUTERBOUND(#502,.T.);
#504= IFCFACE((#503));
#505= IFCCLOSEDSHELL((#489,#492,#495,#498,#501,#504));
#506= IFCFACETEDBREP(#505);
#507= IFCCARTESIANPOINT((101.5,12562.5,3023.294094993037));
#508= IFCDIRECTION((0.938683883514507,0.0,0.3447790115858612));
#509= IFCAXIS2PLACEMENT3D(#507,#508,$);
#510= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#506));
#511= IFCPRODUCTDEFINITIONSHAPE($,$,(#510));
#512= IFCMEMBER('e0de870b-9cd9-4137-a7e4-8ce30d79d0d0',#4,'Rafter_L6',$,$,#509,#511,$,$);
#513= IFCCARTESIANPOINT((0,0,0));
#514= IFCCARTESIANPOINT((300,0,0));
#515= IFCCARTESIANPOINT((300,300,0));
#516= IFCCARTESIANPOINT((0,300,0));
#517= IFCCARTESIANPOINT((0,0,300));
#518= IFCCARTESIANPOINT((300,0,300));
#519= IFCCARTESIANPOINT((300,300,300));
#520= IFCCARTESIANPOINT((0,300,300));
#521= IFCPOLYLOOP((#513,#514,#515,#516));
#522= IFCFACEOUTERBOUND(#521,.T.);
#523= IFCFACE((#522));
#524= IFCPOLYLOOP((#517,#520,#519,#518));
#525= IFCFACEOUTERBOUND(#524,.T.);
#526= IFCFACE((#525));
#527= IFCPOLYLOOP((#513,#517,#518,#514));
#528= IFCFACEOUTERBOUND(#527,.T.);
#529= IFCFACE((#528));
#530= IFCPOLYLOOP((#515,#519,#520,#516));
#531= IFCFACEOUTERBOUND(#530,.T.);
#532= IFCFACE((#531));
#533= IFCPOLYLOOP((#513,#516,#520,#517));
#534= IFCFACEOUTERBOUND(#533,.T.);
#535= IFCFACE((#534));
#536= IFCPOLYLOOP((#514,#518,#519,#515));
#537= IFCFACEOUTERBOUND(#536,.T.);
#538= IFCFACE((#537));
#539= IFCCLOSEDSHELL((#523,#526,#529,#532,#535,#538));
#540= IFCFACETEDBREP(#539);
#541= IFCCARTESIANPOINT((-48.5,-112.5,-300));
#542= IFCAXIS2PLACEMENT3D(#541,$,$);
#543= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#540));
#544= IFCPRODUCTDEFINITIONSHAPE($,$,(#543));
#545= IFCFOOTING('505d95d3-8fb2-4d1b-848e-5f7e61b95b62',#4,'Footing_L1',$,$,#542,#544,$,.FOOTING_BEAM.);
#546= IFCCARTESIANPOINT((0,0,0));
#547= IFCCARTESIANPOINT((300,0,0));
#548= IFCCARTESIANPOINT((300,300,0));
#549= IFCCARTESIANPOINT((0,300,0));
#550= IFCCARTESIANPOINT((0,0,300));
#551= IFCCARTESIANPOINT((300,0,300));
#552= IFCCARTESIANPOINT((300,300,300));
#553= IFCCARTESIANPOINT((0,300,300));
#554= IFCPOLYLOOP((#546,#547,#548,#549));
#555= IFCFACEOUTERBOUND(#554,.T.);
#556= IFCFACE((#555));
#557= IFCPOLYLOOP((#550,#553,#552,#551));
#558= IFCFACEOUTERBOUND(#557,.T.);
#559= IFCFACE((#558));
#560= IFCPOLYLOOP((#546,#550,#551,#547));
#561= IFCFACEOUTERBOUND(#560,.T.);
#562= IFCFACE((#561));
#563= IFCPOLYLOOP((#548,#552,#553,#549));
#564= IFCFACEOUTERBOUND(#563,.T.);
#565= IFCFACE((#564));
#566= IFCPOLYLOOP((#546,#549,#553,#550));
#567= IFCFACEOUTERBOUND(#566,.T.);
#568= IFCFACE((#567));
#569= IFCPOLYLOOP((#547,#551,#552,#548));
#570= IFCFACEOUTERBOUND(#569,.T.);
#571= IFCFACE((#570));
#572= IFCCLOSEDSHELL((#556,#559,#562,#565,#568,#571));
#573= IFCFACETEDBREP(#572);
#574= IFCCARTESIANPOINT((-48.5,2370.0,-300));
#575= IFCAXIS2PLACEMENT3D(#574,$,$);
#576= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#573));
#577= IFCPRODUCTDEFINITIONSHAPE($,$,(#576));
#578= IFCFOOTING('81defb77-1d4c-4064-971f-46ec87c20fe1',#4,'Footing_L2',$,$,#575,#577,$,.FOOTING_BEAM.);
#579= IFCCARTESIANPOINT((0,0,0));
#580= IFCCARTESIANPOINT((300,0,0));
#581= IFCCARTESIANPOINT((300,300,0));
#582= IFCCARTESIANPOINT((0,300,0));
#583= IFCCARTESIANPOINT((0,0,300));
#584= IFCCARTESIANPOINT((300,0,300));
#585= IFCCARTESIANPOINT((300,300,300));
#586= IFCCARTESIANPOINT((0,300,300));
#587= IFCPOLYLOOP((#579,#580,#581,#582));
#588= IFCFACEOUTERBOUND(#587,.T.);
#589= IFCFACE((#588));
#590= IFCPOLYLOOP((#583,#586,#585,#584));
#591= IFCFACEOUTERBOUND(#590,.T.);
#592= IFCFACE((#591));
#593= IFCPOLYLOOP((#579,#583,#584,#580));
#594= IFCFACEOUTERBOUND(#593,.T.);
#595= IFCFACE((#594));
#596= IFCPOLYLOOP((#581,#585,#586,#582));
#597= IFCFACEOUTERBOUND(#596,.T.);
#598= IFCFACE((#597));
#599= IFCPOLYLOOP((#579,#582,#586,#583));
#600= IFCFACEOUTERBOUND(#599,.T.);
#601= IFCFACE((#600));
#602= IFCPOLYLOOP((#580,#584,#585,#581));
#603= IFCFACEOUTERBOUND(#602,.T.);
#604= IFCFACE((#603));
#605= IFCCLOSEDSHELL((#589,#592,#595,#598,#601,#604));
#606= IFCFACETEDBREP(#605);
#607= IFCCARTESIANPOINT((-48.5,4890.0,-300));
#608= IFCAXIS2PLACEMENT3D(#607,$,$);
#609= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#606));
#610= IFCPRODUCTDEFINITIONSHAPE($,$,(#609));
#611= IFCFOOTING('ac362888-fd3f-475b-8aa4-d4a51191cd22',#4,'Footing_L3',$,$,#608,#610,$,.FOOTING_BEAM.);
#612= IFCCARTESIANPOINT((0,0,0));
#613= IFCCARTESIANPOINT((300,0,0));
#614= IFCCARTESIANPOINT((300,300,0));
#615= IFCCARTESIANPOINT((0,300,0));
#616= IFCCARTESIANPOINT((0,0,300));
#617= IFCCARTESIANPOINT((300,0,300));
#618= IFCCARTESIANPOINT((300,300,300));
#619= IFCCARTESIANPOINT((0,300,300));
#620= IFCPOLYLOOP((#612,#613,#614,#615));
#621= IFCFACEOUTERBOUND(#620,.T.);
#622= IFCFACE((#621));
#623= IFCPOLYLOOP((#616,#619,#618,#617));
#624= IFCFACEOUTERBOUND(#623,.T.);
#625= IFCFACE((#624));
#626= IFCPOLYLOOP((#612,#616,#617,#613));
#627= IFCFACEOUTERBOUND(#626,.T.);
#628= IFCFACE((#627));
#629= IFCPOLYLOOP((#614,#618,#619,#615));
#630= IFCFACEOUTERBOUND(#629,.T.);
#631= IFCFACE((#630));
#632= IFCPOLYLOOP((#612,#615,#619,#616));
#633= IFCFACEOUTERBOUND(#632,.T.);
#634= IFCFACE((#633));
#635= IFCPOLYLOOP((#613,#617,#618,#614));
#636= IFCFACEOUTERBOUND(#635,.T.);
#637= IFCFACE((#636));
#638= IFCCLOSEDSHELL((#622,#625,#628,#631,#634,#637));
#639= IFCFACETEDBREP(#638);
#640= IFCCARTESIANPOINT((-48.5,7410.0,-300));
#641= IFCAXIS2PLACEMENT3D(#640,$,$);
#642= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#639));
#643= IFCPRODUCTDEFINITIONSHAPE($,$,(#642));
#644= IFCFOOTING('63718b89-062b-4964-b7c9-a68efc5c1299',#4,'Footing_L4',$,$,#641,#643,$,.FOOTING_BEAM.);
#645= IFCCARTESIANPOINT((0,0,0));
#646= IFCCARTESIANPOINT((300,0,0));
#647= IFCCARTESIANPOINT((300,300,0));
#648= IFCCARTESIANPOINT((0,300,0));
#649= IFCCARTESIANPOINT((0,0,300));
#650= IFCCARTESIANPOINT((300,0,300));
#651= IFCCARTESIANPOINT((300,300,300));
#652= IFCCARTESIANPOINT((0,300,300));
#653= IFCPOLYLOOP((#645,#646,#647,#648));
#654= IFCFACEOUTERBOUND(#653,.T.);
#655= IFCFACE((#654));
#656= IFCPOLYLOOP((#649,#652,#651,#650));
#657= IFCFACEOUTERBOUND(#656,.T.);
#658= IFCFACE((#657));
#659= IFCPOLYLOOP((#645,#649,#650,#646));
#660= IFCFACEOUTERBOUND(#659,.T.);
#661= IFCFACE((#660));
#662= IFCPOLYLOOP((#647,#651,#652,#648));
#663= IFCFACEOUTERBOUND(#662,.T.);
#664= IFCFACE((#663));
#665= IFCPOLYLOOP((#645,#648,#652,#649));
#666= IFCFACEOUTERBOUND(#665,.T.);
#667= IFCFACE((#666));
#668= IFCPOLYLOOP((#646,#650,#651,#647));
#669= IFCFACEOUTERBOUND(#668,.T.);
#670= IFCFACE((#669));
#671= IFCCLOSEDSHELL((#655,#658,#661,#664,#667,#670));
#672= IFCFACETEDBREP(#671);
#673= IFCCARTESIANPOINT((-48.5,9930.0,-300));
#674= IFCAXIS2PLACEMENT3D(#673,$,$);
#675= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#672));
#676= IFCPRODUCTDEFINITIONSHAPE($,$,(#675));
#677= IFCFOOTING('83509670-2b65-48b3-bde0-5acf99f5987e',#4,'Footing_L5',$,$,#674,#676,$,.FOOTING_BEAM.);
#678= IFCCARTESIANPOINT((0,0,0));
#679= IFCCARTESIANPOINT((300,0,0));
#680= IFCCARTESIANPOINT((300,300,0));
#681= IFCCARTESIANPOINT((0,300,0));
#682= IFCCARTESIANPOINT((0,0,300));
#683= IFCCARTESIANPOINT((300,0,300));
#684= IFCCARTESIANPOINT((300,300,300));
#685= IFCCARTESIANPOINT((0,300,300));
#686= IFCPOLYLOOP((#678,#679,#680,#681));
#687= IFCFACEOUTERBOUND(#686,.T.);
#688= IFCFACE((#687));
#689= IFCPOLYLOOP((#682,#685,#684,#683));
#690= IFCFACEOUTERBOUND(#689,.T.);
#691= IFCFACE((#690));
#692= IFCPOLYLOOP((#678,#682,#683,#679));
#693= IFCFACEOUTERBOUND(#692,.T.);
#694= IFCFACE((#693));
#695= IFCPOLYLOOP((#680,#684,#685,#681));
#696= IFCFACEOUTERBOUND(#695,.T.);
#697= IFCFACE((#696));
#698= IFCPOLYLOOP((#678,#681,#685,#682));
#699= IFCFACEOUTERBOUND(#698,.T.);
#700= IFCFACE((#699));
#701= IFCPOLYLOOP((#679,#683,#684,#680));
#702= IFCFACEOUTERBOUND(#701,.T.);
#703= IFCFACE((#702));
#704= IFCCLOSEDSHELL((#688,#691,#694,#697,#700,#703));
#705= IFCFACETEDBREP(#704);
#706= IFCCARTESIANPOINT((-48.5,12412.5,-300));
#707= IFCAXIS2PLACEMENT3D(#706,$,$);
#708= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#705));
#709= IFCPRODUCTDEFINITIONSHAPE($,$,(#708));
#710= IFCFOOTING('b2d5826f-4129-40be-9ae4-b328ab055d78',#4,'Footing_L6',$,$,#707,#709,$,.FOOTING_BEAM.);
#711= IFCRELCONTAINEDINSPATIALSTRUCTURE('8da318b5-0e97-4f0d-b04a-a0488cb97abb',#4,$,$,(#44,#68,#92,#116,#140,#164,#188,#212,#236,#260,#284,#308,#342,#376,#410,#444,#478,#512,#545,#578,#611,#644,#677,#710),#16);
#712= IFCRELASSOCIATESMATERIAL('6c7d2ac5-1009-4e06-855a-5aac4db16000',#4,$,$,(#44,#68,#92,#116,#140,#164,#188,#212,#236,#260,#284,#308),#20);
#713= IFCRELASSOCIATESMATERIAL('4a4e412a-f58b-41cd-b9f8-ddf0753eef28',#4,$,$,(#342,#376,#410,#444,#478,#512),#20);
ENDSEC;
END-ISO-10303-21;