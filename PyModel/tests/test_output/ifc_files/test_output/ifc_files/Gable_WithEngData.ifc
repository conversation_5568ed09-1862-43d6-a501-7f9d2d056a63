ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('ViewDefinition [CoordinationView]'),'2;1');
FILE_NAME('carport.ifc','2025-06-30T18:01:30',('PyModel'),('BREP IFC Generator'),'IFC2X3','PyModel','');
FILE_SCHEMA(('IFC2X3'));
ENDSEC;
DATA;
#1= IFCPERSON($,$,'User',$,$,$,$,$);
#2= IFCORGANIZATION($,'PyModel',$,$,$);
#3= IFCAPPLICATION(#2,'1.0','BREP IFC Generator','PyModel');
#4= IFCOWNERHISTORY(#1,#3,.READWRITE.,$,$,$,$,1234567890);
#5= IFCSIUNIT(*,.LENGTHUNIT.,.MILLI.,.METRE.);
#6= IFCSIUNIT(*,.PLANEANGLEUNIT.,$,.RADIAN.);
#7= IFCUNITASSIGNMENT((#5,#6));
#8= IFCCARTESIANPOINT((0,0,0));
#9= IFCDIRECTION((1.,0.,0.));
#10= IFCDIRECTION((0.,0.,1.));
#11= IFCAXIS2PLACEMENT3D(#8,#10,#9);
#12= IFCGEOMETRICREPRESENTATIONCONTEXT($,'Model',3,1e-05,#11,$);
#13= IFCPROJECT('2e943c5c-6a07-4fe7-b5b6-ef571e337f48',#4,'Carport Project',$,$,$,$,(#12),#7);
#14= IFCSITE('5ff5520b-0281-4724-b08f-cde0b1a52a87',#4,'Site',$,$,$,$,$,.ELEMENT.,$,$,$,$,$);
#15= IFCBUILDING('3f8152b8-3582-40ee-826f-5c3ea93b7893',#4,'Carport',$,$,$,$,$,.ELEMENT.,$,$,$);
#16= IFCBUILDINGSTOREY('95763c5e-5f5f-48b0-b8b5-d800c1c22480',#4,'Ground Floor',$,$,$,$,$,.ELEMENT.,0.0);
#17= IFCRELAGGREGATES('2d1b796a-647e-4c0c-b04c-f2d86f38e351',#4,$,$,#13,(#14));
#18= IFCRELAGGREGATES('6fa02966-b87d-4d2e-8897-d6200487d6e0',#4,$,$,#14,(#15));
#19= IFCRELAGGREGATES('53a4fd1f-cfb7-4eb1-8cb3-36fdbc2e6a5c',#4,$,$,#15,(#16));
#20= IFCMATERIAL('Steel');
#21= IFCCARTESIANPOINT((-50.0,-50.0,0));
#22= IFCCARTESIANPOINT((50.0,-50.0,0));
#23= IFCCARTESIANPOINT((50.0,50.0,0));
#24= IFCCARTESIANPOINT((-50.0,50.0,0));
#25= IFCCARTESIANPOINT((-50.0,-50.0,2720.3641386247655));
#26= IFCCARTESIANPOINT((50.0,-50.0,2720.3641386247655));
#27= IFCCARTESIANPOINT((50.0,50.0,2720.3641386247655));
#28= IFCCARTESIANPOINT((-50.0,50.0,2720.3641386247655));
#29= IFCPOLYLOOP((#21,#22,#23,#24));
#30= IFCFACEOUTERBOUND(#29,.T.);
#31= IFCCARTESIANPOINT((-46.0,-46.0,0));
#32= IFCCARTESIANPOINT((-46.0,46.0,0));
#33= IFCCARTESIANPOINT((46.0,46.0,0));
#34= IFCCARTESIANPOINT((46.0,-46.0,0));
#35= IFCPOLYLOOP((#31,#32,#33,#34));
#36= IFCFACEBOUND(#35,.F.);
#37= IFCFACE((#30,#36));
#38= IFCCLOSEDSHELL((#37));
#39= IFCFACETEDBREP(#38);
#40= IFCCARTESIANPOINT((101.0,50.0,0));
#41= IFCAXIS2PLACEMENT3D(#40,$,$);
#42= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#39));
#43= IFCPRODUCTDEFINITIONSHAPE($,$,(#42));
#44= IFCCOLUMN('9f4d7bc2-4085-4019-95c5-7c74dc5ee5e7',#4,'Column_L1',$,$,#41,#43,$,$);
#45= IFCCARTESIANPOINT((-50.0,-50.0,0));
#46= IFCCARTESIANPOINT((50.0,-50.0,0));
#47= IFCCARTESIANPOINT((50.0,50.0,0));
#48= IFCCARTESIANPOINT((-50.0,50.0,0));
#49= IFCCARTESIANPOINT((-50.0,-50.0,2720.3641386247655));
#50= IFCCARTESIANPOINT((50.0,-50.0,2720.3641386247655));
#51= IFCCARTESIANPOINT((50.0,50.0,2720.3641386247655));
#52= IFCCARTESIANPOINT((-50.0,50.0,2720.3641386247655));
#53= IFCPOLYLOOP((#45,#46,#47,#48));
#54= IFCFACEOUTERBOUND(#53,.T.);
#55= IFCCARTESIANPOINT((-46.0,-46.0,0));
#56= IFCCARTESIANPOINT((-46.0,46.0,0));
#57= IFCCARTESIANPOINT((46.0,46.0,0));
#58= IFCCARTESIANPOINT((46.0,-46.0,0));
#59= IFCPOLYLOOP((#55,#56,#57,#58));
#60= IFCFACEBOUND(#59,.F.);
#61= IFCFACE((#54,#60));
#62= IFCCLOSEDSHELL((#61));
#63= IFCFACETEDBREP(#62);
#64= IFCCARTESIANPOINT((101.0,3000.0,0));
#65= IFCAXIS2PLACEMENT3D(#64,$,$);
#66= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#63));
#67= IFCPRODUCTDEFINITIONSHAPE($,$,(#66));
#68= IFCCOLUMN('2b86e172-bf31-49c6-aab3-6c2decd9f0f5',#4,'Column_L2',$,$,#65,#67,$,$);
#69= IFCCARTESIANPOINT((-50.0,-50.0,0));
#70= IFCCARTESIANPOINT((50.0,-50.0,0));
#71= IFCCARTESIANPOINT((50.0,50.0,0));
#72= IFCCARTESIANPOINT((-50.0,50.0,0));
#73= IFCCARTESIANPOINT((-50.0,-50.0,2720.3641386247655));
#74= IFCCARTESIANPOINT((50.0,-50.0,2720.3641386247655));
#75= IFCCARTESIANPOINT((50.0,50.0,2720.3641386247655));
#76= IFCCARTESIANPOINT((-50.0,50.0,2720.3641386247655));
#77= IFCPOLYLOOP((#69,#70,#71,#72));
#78= IFCFACEOUTERBOUND(#77,.T.);
#79= IFCCARTESIANPOINT((-46.0,-46.0,0));
#80= IFCCARTESIANPOINT((-46.0,46.0,0));
#81= IFCCARTESIANPOINT((46.0,46.0,0));
#82= IFCCARTESIANPOINT((46.0,-46.0,0));
#83= IFCPOLYLOOP((#79,#80,#81,#82));
#84= IFCFACEBOUND(#83,.F.);
#85= IFCFACE((#78,#84));
#86= IFCCLOSEDSHELL((#85));
#87= IFCFACETEDBREP(#86);
#88= IFCCARTESIANPOINT((101.0,6000.0,0));
#89= IFCAXIS2PLACEMENT3D(#88,$,$);
#90= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#87));
#91= IFCPRODUCTDEFINITIONSHAPE($,$,(#90));
#92= IFCCOLUMN('f8bd2d26-0ede-4941-9d6d-5f52e4c4c15d',#4,'Column_L3',$,$,#89,#91,$,$);
#93= IFCCARTESIANPOINT((-50.0,-50.0,0));
#94= IFCCARTESIANPOINT((50.0,-50.0,0));
#95= IFCCARTESIANPOINT((50.0,50.0,0));
#96= IFCCARTESIANPOINT((-50.0,50.0,0));
#97= IFCCARTESIANPOINT((-50.0,-50.0,2720.3641386247655));
#98= IFCCARTESIANPOINT((50.0,-50.0,2720.3641386247655));
#99= IFCCARTESIANPOINT((50.0,50.0,2720.3641386247655));
#100= IFCCARTESIANPOINT((-50.0,50.0,2720.3641386247655));
#101= IFCPOLYLOOP((#93,#94,#95,#96));
#102= IFCFACEOUTERBOUND(#101,.T.);
#103= IFCCARTESIANPOINT((-46.0,-46.0,0));
#104= IFCCARTESIANPOINT((-46.0,46.0,0));
#105= IFCCARTESIANPOINT((46.0,46.0,0));
#106= IFCCARTESIANPOINT((46.0,-46.0,0));
#107= IFCPOLYLOOP((#103,#104,#105,#106));
#108= IFCFACEBOUND(#107,.F.);
#109= IFCFACE((#102,#108));
#110= IFCCLOSEDSHELL((#109));
#111= IFCFACETEDBREP(#110);
#112= IFCCARTESIANPOINT((101.0,8950.0,0));
#113= IFCAXIS2PLACEMENT3D(#112,$,$);
#114= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#111));
#115= IFCPRODUCTDEFINITIONSHAPE($,$,(#114));
#116= IFCCOLUMN('7f56e04f-46ca-4b03-83db-a64fc5cef391',#4,'Column_L4',$,$,#113,#115,$,$);
#117= IFCCARTESIANPOINT((-37.5,-37.5,0));
#118= IFCCARTESIANPOINT((37.5,-37.5,0));
#119= IFCCARTESIANPOINT((37.5,37.5,0));
#120= IFCCARTESIANPOINT((-37.5,37.5,0));
#121= IFCCARTESIANPOINT((-37.5,-37.5,2720.3641386247655));
#122= IFCCARTESIANPOINT((37.5,-37.5,2720.3641386247655));
#123= IFCCARTESIANPOINT((37.5,37.5,2720.3641386247655));
#124= IFCCARTESIANPOINT((-37.5,37.5,2720.3641386247655));
#125= IFCPOLYLOOP((#117,#118,#119,#120));
#126= IFCFACEOUTERBOUND(#125,.T.);
#127= IFCCARTESIANPOINT((-35.0,-35.0,0));
#128= IFCCARTESIANPOINT((-35.0,35.0,0));
#129= IFCCARTESIANPOINT((35.0,35.0,0));
#130= IFCCARTESIANPOINT((35.0,-35.0,0));
#131= IFCPOLYLOOP((#127,#128,#129,#130));
#132= IFCFACEBOUND(#131,.F.);
#133= IFCFACE((#126,#132));
#134= IFCCLOSEDSHELL((#133));
#135= IFCFACETEDBREP(#134);
#136= IFCCARTESIANPOINT((5950.0,50.0,0));
#137= IFCAXIS2PLACEMENT3D(#136,$,$);
#138= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#135));
#139= IFCPRODUCTDEFINITIONSHAPE($,$,(#138));
#140= IFCCOLUMN('3a49e6ec-4712-4202-8a70-4bcf05ede213',#4,'Column_R1',$,$,#137,#139,$,$);
#141= IFCCARTESIANPOINT((-37.5,-37.5,0));
#142= IFCCARTESIANPOINT((37.5,-37.5,0));
#143= IFCCARTESIANPOINT((37.5,37.5,0));
#144= IFCCARTESIANPOINT((-37.5,37.5,0));
#145= IFCCARTESIANPOINT((-37.5,-37.5,2720.3641386247655));
#146= IFCCARTESIANPOINT((37.5,-37.5,2720.3641386247655));
#147= IFCCARTESIANPOINT((37.5,37.5,2720.3641386247655));
#148= IFCCARTESIANPOINT((-37.5,37.5,2720.3641386247655));
#149= IFCPOLYLOOP((#141,#142,#143,#144));
#150= IFCFACEOUTERBOUND(#149,.T.);
#151= IFCCARTESIANPOINT((-35.0,-35.0,0));
#152= IFCCARTESIANPOINT((-35.0,35.0,0));
#153= IFCCARTESIANPOINT((35.0,35.0,0));
#154= IFCCARTESIANPOINT((35.0,-35.0,0));
#155= IFCPOLYLOOP((#151,#152,#153,#154));
#156= IFCFACEBOUND(#155,.F.);
#157= IFCFACE((#150,#156));
#158= IFCCLOSEDSHELL((#157));
#159= IFCFACETEDBREP(#158);
#160= IFCCARTESIANPOINT((5950.0,3000.0,0));
#161= IFCAXIS2PLACEMENT3D(#160,$,$);
#162= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#159));
#163= IFCPRODUCTDEFINITIONSHAPE($,$,(#162));
#164= IFCCOLUMN('a6152074-64dd-4bab-81cb-1508608400df',#4,'Column_R2',$,$,#161,#163,$,$);
#165= IFCCARTESIANPOINT((-37.5,-37.5,0));
#166= IFCCARTESIANPOINT((37.5,-37.5,0));
#167= IFCCARTESIANPOINT((37.5,37.5,0));
#168= IFCCARTESIANPOINT((-37.5,37.5,0));
#169= IFCCARTESIANPOINT((-37.5,-37.5,2720.3641386247655));
#170= IFCCARTESIANPOINT((37.5,-37.5,2720.3641386247655));
#171= IFCCARTESIANPOINT((37.5,37.5,2720.3641386247655));
#172= IFCCARTESIANPOINT((-37.5,37.5,2720.3641386247655));
#173= IFCPOLYLOOP((#165,#166,#167,#168));
#174= IFCFACEOUTERBOUND(#173,.T.);
#175= IFCCARTESIANPOINT((-35.0,-35.0,0));
#176= IFCCARTESIANPOINT((-35.0,35.0,0));
#177= IFCCARTESIANPOINT((35.0,35.0,0));
#178= IFCCARTESIANPOINT((35.0,-35.0,0));
#179= IFCPOLYLOOP((#175,#176,#177,#178));
#180= IFCFACEBOUND(#179,.F.);
#181= IFCFACE((#174,#180));
#182= IFCCLOSEDSHELL((#181));
#183= IFCFACETEDBREP(#182);
#184= IFCCARTESIANPOINT((5950.0,6000.0,0));
#185= IFCAXIS2PLACEMENT3D(#184,$,$);
#186= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#183));
#187= IFCPRODUCTDEFINITIONSHAPE($,$,(#186));
#188= IFCCOLUMN('1ac5a794-7558-45a5-9c8a-4e94d3b5ade6',#4,'Column_R3',$,$,#185,#187,$,$);
#189= IFCCARTESIANPOINT((-37.5,-37.5,0));
#190= IFCCARTESIANPOINT((37.5,-37.5,0));
#191= IFCCARTESIANPOINT((37.5,37.5,0));
#192= IFCCARTESIANPOINT((-37.5,37.5,0));
#193= IFCCARTESIANPOINT((-37.5,-37.5,2720.3641386247655));
#194= IFCCARTESIANPOINT((37.5,-37.5,2720.3641386247655));
#195= IFCCARTESIANPOINT((37.5,37.5,2720.3641386247655));
#196= IFCCARTESIANPOINT((-37.5,37.5,2720.3641386247655));
#197= IFCPOLYLOOP((#189,#190,#191,#192));
#198= IFCFACEOUTERBOUND(#197,.T.);
#199= IFCCARTESIANPOINT((-35.0,-35.0,0));
#200= IFCCARTESIANPOINT((-35.0,35.0,0));
#201= IFCCARTESIANPOINT((35.0,35.0,0));
#202= IFCCARTESIANPOINT((35.0,-35.0,0));
#203= IFCPOLYLOOP((#199,#200,#201,#202));
#204= IFCFACEBOUND(#203,.F.);
#205= IFCFACE((#198,#204));
#206= IFCCLOSEDSHELL((#205));
#207= IFCFACETEDBREP(#206);
#208= IFCCARTESIANPOINT((5950.0,8950.0,0));
#209= IFCAXIS2PLACEMENT3D(#208,$,$);
#210= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#207));
#211= IFCPRODUCTDEFINITIONSHAPE($,$,(#210));
#212= IFCCOLUMN('b8f64964-63e9-4e5b-bf54-42a0917b3706',#4,'Column_R4',$,$,#209,#211,$,$);
#213= IFCCARTESIANPOINT((0,0,0));
#214= IFCCARTESIANPOINT((64,0,0));
#215= IFCCARTESIANPOINT((64,152,0));
#216= IFCCARTESIANPOINT((0,152,0));
#217= IFCCARTESIANPOINT((0,0,3003.0063767278248));
#218= IFCCARTESIANPOINT((64,0,3003.0063767278248));
#219= IFCCARTESIANPOINT((64,152,3003.0063767278248));
#220= IFCCARTESIANPOINT((0,152,3003.0063767278248));
#221= IFCPOLYLOOP((#213,#214,#215,#216));
#222= IFCFACEOUTERBOUND(#221,.T.);
#223= IFCFACE((#222));
#224= IFCPOLYLOOP((#217,#220,#219,#218));
#225= IFCFACEOUTERBOUND(#224,.T.);
#226= IFCFACE((#225));
#227= IFCPOLYLOOP((#213,#217,#218,#214));
#228= IFCFACEOUTERBOUND(#227,.T.);
#229= IFCFACE((#228));
#230= IFCPOLYLOOP((#215,#219,#220,#216));
#231= IFCFACEOUTERBOUND(#230,.T.);
#232= IFCFACE((#231));
#233= IFCPOLYLOOP((#213,#216,#220,#217));
#234= IFCFACEOUTERBOUND(#233,.T.);
#235= IFCFACE((#234));
#236= IFCPOLYLOOP((#214,#218,#219,#215));
#237= IFCFACEOUTERBOUND(#236,.T.);
#238= IFCFACE((#237));
#239= IFCCLOSEDSHELL((#223,#226,#229,#232,#235,#238));
#240= IFCFACETEDBREP(#239);
#241= IFCCARTESIANPOINT((101.0,50.0,2720.3641386247655));
#242= IFCDIRECTION((0.9653659154593093,0.0,0.2608996919687903));
#243= IFCAXIS2PLACEMENT3D(#241,#242,$);
#244= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#240));
#245= IFCPRODUCTDEFINITIONSHAPE($,$,(#244));
#246= IFCMEMBER('c791d6b2-be06-4caf-a3e3-b1ed0f801450',#4,'Rafter_L1',$,$,#243,#245,$,$);
#247= IFCCARTESIANPOINT((0,0,0));
#248= IFCCARTESIANPOINT((64,0,0));
#249= IFCCARTESIANPOINT((64,152,0));
#250= IFCCARTESIANPOINT((0,152,0));
#251= IFCCARTESIANPOINT((0,0,3003.0063767278248));
#252= IFCCARTESIANPOINT((64,0,3003.0063767278248));
#253= IFCCARTESIANPOINT((64,152,3003.0063767278248));
#254= IFCCARTESIANPOINT((0,152,3003.0063767278248));
#255= IFCPOLYLOOP((#247,#248,#249,#250));
#256= IFCFACEOUTERBOUND(#255,.T.);
#257= IFCFACE((#256));
#258= IFCPOLYLOOP((#251,#254,#253,#252));
#259= IFCFACEOUTERBOUND(#258,.T.);
#260= IFCFACE((#259));
#261= IFCPOLYLOOP((#247,#251,#252,#248));
#262= IFCFACEOUTERBOUND(#261,.T.);
#263= IFCFACE((#262));
#264= IFCPOLYLOOP((#249,#253,#254,#250));
#265= IFCFACEOUTERBOUND(#264,.T.);
#266= IFCFACE((#265));
#267= IFCPOLYLOOP((#247,#250,#254,#251));
#268= IFCFACEOUTERBOUND(#267,.T.);
#269= IFCFACE((#268));
#270= IFCPOLYLOOP((#248,#252,#253,#249));
#271= IFCFACEOUTERBOUND(#270,.T.);
#272= IFCFACE((#271));
#273= IFCCLOSEDSHELL((#257,#260,#263,#266,#269,#272));
#274= IFCFACETEDBREP(#273);
#275= IFCCARTESIANPOINT((101.0,3000.0,2720.3641386247655));
#276= IFCDIRECTION((0.9653659154593093,0.0,0.2608996919687903));
#277= IFCAXIS2PLACEMENT3D(#275,#276,$);
#278= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#274));
#279= IFCPRODUCTDEFINITIONSHAPE($,$,(#278));
#280= IFCMEMBER('bdd837d0-fadc-48a8-bc8f-23a788e8aa82',#4,'Rafter_L2',$,$,#277,#279,$,$);
#281= IFCCARTESIANPOINT((0,0,0));
#282= IFCCARTESIANPOINT((64,0,0));
#283= IFCCARTESIANPOINT((64,152,0));
#284= IFCCARTESIANPOINT((0,152,0));
#285= IFCCARTESIANPOINT((0,0,3003.0063767278248));
#286= IFCCARTESIANPOINT((64,0,3003.0063767278248));
#287= IFCCARTESIANPOINT((64,152,3003.0063767278248));
#288= IFCCARTESIANPOINT((0,152,3003.0063767278248));
#289= IFCPOLYLOOP((#281,#282,#283,#284));
#290= IFCFACEOUTERBOUND(#289,.T.);
#291= IFCFACE((#290));
#292= IFCPOLYLOOP((#285,#288,#287,#286));
#293= IFCFACEOUTERBOUND(#292,.T.);
#294= IFCFACE((#293));
#295= IFCPOLYLOOP((#281,#285,#286,#282));
#296= IFCFACEOUTERBOUND(#295,.T.);
#297= IFCFACE((#296));
#298= IFCPOLYLOOP((#283,#287,#288,#284));
#299= IFCFACEOUTERBOUND(#298,.T.);
#300= IFCFACE((#299));
#301= IFCPOLYLOOP((#281,#284,#288,#285));
#302= IFCFACEOUTERBOUND(#301,.T.);
#303= IFCFACE((#302));
#304= IFCPOLYLOOP((#282,#286,#287,#283));
#305= IFCFACEOUTERBOUND(#304,.T.);
#306= IFCFACE((#305));
#307= IFCCLOSEDSHELL((#291,#294,#297,#300,#303,#306));
#308= IFCFACETEDBREP(#307);
#309= IFCCARTESIANPOINT((101.0,6000.0,2720.3641386247655));
#310= IFCDIRECTION((0.9653659154593093,0.0,0.2608996919687903));
#311= IFCAXIS2PLACEMENT3D(#309,#310,$);
#312= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#308));
#313= IFCPRODUCTDEFINITIONSHAPE($,$,(#312));
#314= IFCMEMBER('8dc3492c-a494-4003-afa8-68c1159d12bc',#4,'Rafter_L3',$,$,#311,#313,$,$);
#315= IFCCARTESIANPOINT((0,0,0));
#316= IFCCARTESIANPOINT((64,0,0));
#317= IFCCARTESIANPOINT((64,152,0));
#318= IFCCARTESIANPOINT((0,152,0));
#319= IFCCARTESIANPOINT((0,0,3003.0063767278248));
#320= IFCCARTESIANPOINT((64,0,3003.0063767278248));
#321= IFCCARTESIANPOINT((64,152,3003.0063767278248));
#322= IFCCARTESIANPOINT((0,152,3003.0063767278248));
#323= IFCPOLYLOOP((#315,#316,#317,#318));
#324= IFCFACEOUTERBOUND(#323,.T.);
#325= IFCFACE((#324));
#326= IFCPOLYLOOP((#319,#322,#321,#320));
#327= IFCFACEOUTERBOUND(#326,.T.);
#328= IFCFACE((#327));
#329= IFCPOLYLOOP((#315,#319,#320,#316));
#330= IFCFACEOUTERBOUND(#329,.T.);
#331= IFCFACE((#330));
#332= IFCPOLYLOOP((#317,#321,#322,#318));
#333= IFCFACEOUTERBOUND(#332,.T.);
#334= IFCFACE((#333));
#335= IFCPOLYLOOP((#315,#318,#322,#319));
#336= IFCFACEOUTERBOUND(#335,.T.);
#337= IFCFACE((#336));
#338= IFCPOLYLOOP((#316,#320,#321,#317));
#339= IFCFACEOUTERBOUND(#338,.T.);
#340= IFCFACE((#339));
#341= IFCCLOSEDSHELL((#325,#328,#331,#334,#337,#340));
#342= IFCFACETEDBREP(#341);
#343= IFCCARTESIANPOINT((101.0,8950.0,2720.3641386247655));
#344= IFCDIRECTION((0.9653659154593093,0.0,0.2608996919687903));
#345= IFCAXIS2PLACEMENT3D(#343,#344,$);
#346= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#342));
#347= IFCPRODUCTDEFINITIONSHAPE($,$,(#346));
#348= IFCMEMBER('d6838a23-d066-44ae-8510-9c64799c9392',#4,'Rafter_L4',$,$,#345,#347,$,$);
#349= IFCCARTESIANPOINT((0,0,0));
#350= IFCCARTESIANPOINT((300,0,0));
#351= IFCCARTESIANPOINT((300,300,0));
#352= IFCCARTESIANPOINT((0,300,0));
#353= IFCCARTESIANPOINT((0,0,300));
#354= IFCCARTESIANPOINT((300,0,300));
#355= IFCCARTESIANPOINT((300,300,300));
#356= IFCCARTESIANPOINT((0,300,300));
#357= IFCPOLYLOOP((#349,#350,#351,#352));
#358= IFCFACEOUTERBOUND(#357,.T.);
#359= IFCFACE((#358));
#360= IFCPOLYLOOP((#353,#356,#355,#354));
#361= IFCFACEOUTERBOUND(#360,.T.);
#362= IFCFACE((#361));
#363= IFCPOLYLOOP((#349,#353,#354,#350));
#364= IFCFACEOUTERBOUND(#363,.T.);
#365= IFCFACE((#364));
#366= IFCPOLYLOOP((#351,#355,#356,#352));
#367= IFCFACEOUTERBOUND(#366,.T.);
#368= IFCFACE((#367));
#369= IFCPOLYLOOP((#349,#352,#356,#353));
#370= IFCFACEOUTERBOUND(#369,.T.);
#371= IFCFACE((#370));
#372= IFCPOLYLOOP((#350,#354,#355,#351));
#373= IFCFACEOUTERBOUND(#372,.T.);
#374= IFCFACE((#373));
#375= IFCCLOSEDSHELL((#359,#362,#365,#368,#371,#374));
#376= IFCFACETEDBREP(#375);
#377= IFCCARTESIANPOINT((-49.0,-100.0,-300));
#378= IFCAXIS2PLACEMENT3D(#377,$,$);
#379= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#376));
#380= IFCPRODUCTDEFINITIONSHAPE($,$,(#379));
#381= IFCFOOTING('b11f742e-1e9b-4ccc-be86-30bcbc1da3be',#4,'Footing_L1',$,$,#378,#380,$,.FOOTING_BEAM.);
#382= IFCCARTESIANPOINT((0,0,0));
#383= IFCCARTESIANPOINT((300,0,0));
#384= IFCCARTESIANPOINT((300,300,0));
#385= IFCCARTESIANPOINT((0,300,0));
#386= IFCCARTESIANPOINT((0,0,300));
#387= IFCCARTESIANPOINT((300,0,300));
#388= IFCCARTESIANPOINT((300,300,300));
#389= IFCCARTESIANPOINT((0,300,300));
#390= IFCPOLYLOOP((#382,#383,#384,#385));
#391= IFCFACEOUTERBOUND(#390,.T.);
#392= IFCFACE((#391));
#393= IFCPOLYLOOP((#386,#389,#388,#387));
#394= IFCFACEOUTERBOUND(#393,.T.);
#395= IFCFACE((#394));
#396= IFCPOLYLOOP((#382,#386,#387,#383));
#397= IFCFACEOUTERBOUND(#396,.T.);
#398= IFCFACE((#397));
#399= IFCPOLYLOOP((#384,#388,#389,#385));
#400= IFCFACEOUTERBOUND(#399,.T.);
#401= IFCFACE((#400));
#402= IFCPOLYLOOP((#382,#385,#389,#386));
#403= IFCFACEOUTERBOUND(#402,.T.);
#404= IFCFACE((#403));
#405= IFCPOLYLOOP((#383,#387,#388,#384));
#406= IFCFACEOUTERBOUND(#405,.T.);
#407= IFCFACE((#406));
#408= IFCCLOSEDSHELL((#392,#395,#398,#401,#404,#407));
#409= IFCFACETEDBREP(#408);
#410= IFCCARTESIANPOINT((-49.0,2850.0,-300));
#411= IFCAXIS2PLACEMENT3D(#410,$,$);
#412= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#409));
#413= IFCPRODUCTDEFINITIONSHAPE($,$,(#412));
#414= IFCFOOTING('bcd3dcee-a0fa-49a7-aed0-bb9a9935b630',#4,'Footing_L2',$,$,#411,#413,$,.FOOTING_BEAM.);
#415= IFCCARTESIANPOINT((0,0,0));
#416= IFCCARTESIANPOINT((300,0,0));
#417= IFCCARTESIANPOINT((300,300,0));
#418= IFCCARTESIANPOINT((0,300,0));
#419= IFCCARTESIANPOINT((0,0,300));
#420= IFCCARTESIANPOINT((300,0,300));
#421= IFCCARTESIANPOINT((300,300,300));
#422= IFCCARTESIANPOINT((0,300,300));
#423= IFCPOLYLOOP((#415,#416,#417,#418));
#424= IFCFACEOUTERBOUND(#423,.T.);
#425= IFCFACE((#424));
#426= IFCPOLYLOOP((#419,#422,#421,#420));
#427= IFCFACEOUTERBOUND(#426,.T.);
#428= IFCFACE((#427));
#429= IFCPOLYLOOP((#415,#419,#420,#416));
#430= IFCFACEOUTERBOUND(#429,.T.);
#431= IFCFACE((#430));
#432= IFCPOLYLOOP((#417,#421,#422,#418));
#433= IFCFACEOUTERBOUND(#432,.T.);
#434= IFCFACE((#433));
#435= IFCPOLYLOOP((#415,#418,#422,#419));
#436= IFCFACEOUTERBOUND(#435,.T.);
#437= IFCFACE((#436));
#438= IFCPOLYLOOP((#416,#420,#421,#417));
#439= IFCFACEOUTERBOUND(#438,.T.);
#440= IFCFACE((#439));
#441= IFCCLOSEDSHELL((#425,#428,#431,#434,#437,#440));
#442= IFCFACETEDBREP(#441);
#443= IFCCARTESIANPOINT((-49.0,5850.0,-300));
#444= IFCAXIS2PLACEMENT3D(#443,$,$);
#445= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#442));
#446= IFCPRODUCTDEFINITIONSHAPE($,$,(#445));
#447= IFCFOOTING('197958dd-a6d4-4461-a429-554d8ec2fd72',#4,'Footing_L3',$,$,#444,#446,$,.FOOTING_BEAM.);
#448= IFCCARTESIANPOINT((0,0,0));
#449= IFCCARTESIANPOINT((300,0,0));
#450= IFCCARTESIANPOINT((300,300,0));
#451= IFCCARTESIANPOINT((0,300,0));
#452= IFCCARTESIANPOINT((0,0,300));
#453= IFCCARTESIANPOINT((300,0,300));
#454= IFCCARTESIANPOINT((300,300,300));
#455= IFCCARTESIANPOINT((0,300,300));
#456= IFCPOLYLOOP((#448,#449,#450,#451));
#457= IFCFACEOUTERBOUND(#456,.T.);
#458= IFCFACE((#457));
#459= IFCPOLYLOOP((#452,#455,#454,#453));
#460= IFCFACEOUTERBOUND(#459,.T.);
#461= IFCFACE((#460));
#462= IFCPOLYLOOP((#448,#452,#453,#449));
#463= IFCFACEOUTERBOUND(#462,.T.);
#464= IFCFACE((#463));
#465= IFCPOLYLOOP((#450,#454,#455,#451));
#466= IFCFACEOUTERBOUND(#465,.T.);
#467= IFCFACE((#466));
#468= IFCPOLYLOOP((#448,#451,#455,#452));
#469= IFCFACEOUTERBOUND(#468,.T.);
#470= IFCFACE((#469));
#471= IFCPOLYLOOP((#449,#453,#454,#450));
#472= IFCFACEOUTERBOUND(#471,.T.);
#473= IFCFACE((#472));
#474= IFCCLOSEDSHELL((#458,#461,#464,#467,#470,#473));
#475= IFCFACETEDBREP(#474);
#476= IFCCARTESIANPOINT((-49.0,8800.0,-300));
#477= IFCAXIS2PLACEMENT3D(#476,$,$);
#478= IFCSHAPEREPRESENTATION(#12,'Body','Brep',(#475));
#479= IFCPRODUCTDEFINITIONSHAPE($,$,(#478));
#480= IFCFOOTING('6ee05942-21cc-4b88-85b3-78f2161cf9f2',#4,'Footing_L4',$,$,#477,#479,$,.FOOTING_BEAM.);
#481= IFCRELCONTAINEDINSPATIALSTRUCTURE('0edc8235-e884-42f1-a529-adf716b39498',#4,$,$,(#44,#68,#92,#116,#140,#164,#188,#212,#246,#280,#314,#348,#381,#414,#447,#480),#16);
#482= IFCRELASSOCIATESMATERIAL('b932f689-015f-439c-8865-3ce7bcdca3d3',#4,$,$,(#44,#68,#92,#116,#140,#164,#188,#212),#20);
#483= IFCRELASSOCIATESMATERIAL('3a7f2574-f520-43aa-8114-6ef40fc6c444',#4,$,$,(#246,#280,#314,#348),#20);
ENDSEC;
END-ISO-10303-21;