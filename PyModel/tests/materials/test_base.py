"""
Test suite for material base classes.

Tests material definitions, properties, and profile generation.
"""

import pytest
from src.materials.base import (
    FrameMaterial, FrameMaterialType,
    BracketMaterial, CladdingMaterial, ColorMaterial,
    FastenerMaterialType, FootingMaterialType,
    FootingMaterial, StrapMaterial
)
from src.geometry.primitives import Vec2


class TestFrameMaterial:
    """Test FrameMaterial functionality."""
    
    def test_creation(self):
        """Test frame material creation with basic properties."""
        material = FrameMaterial(
            name="C10015",
            material_type=FrameMaterialType.C,
            width=102.0,
            height=51.0,
            thickness=1.5,
            lip=13.5
        )
        
        assert material.name == "C10015"
        assert material.material_type == FrameMaterialType.C
        assert material.width == 102.0
        assert material.height == 51.0
        assert material.thickness == 1.5
        assert material.lip == 13.5
    
    def test_properties(self):
        """Test computed properties."""
        material = FrameMaterial(
            name="C10015",
            material_type=FrameMaterialType.C,
            width=102.0,
            height=51.0,
            thickness=1.5
        )
        
        # Test web and flange properties
        assert material.web == 51.0  # height
        assert material.flange == 102.0  # width
        
        # Test ID generation
        assert material.id == "C10015"
        
        # Test flipped ID
        material.flipped = True
        assert material.id == "C10015 (flipped)"
    
    def test_c_channel_profile(self):
        """Test C-channel profile generation."""
        material = FrameMaterial(
            name="C10015",
            material_type=FrameMaterialType.C,
            width=100.0,
            height=50.0,
            thickness=2.0,
            lip=10.0,
            section=0  # Center the profile
        )
        
        profile = material.get_profile_points()
        
        # C-channel should have multiple points for the shape
        assert len(profile) > 8  # At least 8 points for basic C shape
        
        # All points should be Vec2
        assert all(isinstance(p, Vec2) for p in profile)
        
        # Check that profile is centered (section=0)
        # The profile should have points on both sides of origin
        x_coords = [p.x for p in profile]
        y_coords = [p.y for p in profile]
        
        assert min(x_coords) < 0
        assert max(x_coords) > 0
        assert min(y_coords) < 0
        assert max(y_coords) > 0
    
    def test_shs_profile(self):
        """Test square hollow section profile generation."""
        material = FrameMaterial(
            name="SHS100",
            material_type=FrameMaterialType.SHS,
            width=100.0,
            height=100.0,
            thickness=3.0,
            section=0
        )
        
        profile = material.get_profile_points()
        
        # SHS should have points for outer and inner rectangles
        assert len(profile) >= 8  # At least 4 outer + 4 inner points
        
        # Check symmetry for square section
        x_coords = [p.x for p in profile]
        y_coords = [p.y for p in profile]
        
        # Should be centered
        assert abs(min(x_coords) + max(x_coords)) < 1e-10
        assert abs(min(y_coords) + max(y_coords)) < 1e-10
    
    def test_top_hat_profile(self):
        """Test top hat profile generation."""
        material = FrameMaterial(
            name="TH100",
            material_type=FrameMaterialType.TH,
            width=100.0,
            height=50.0,
            thickness=2.0,
            lip=15.0,
            section=0
        )
        
        profile = material.get_profile_points()
        
        # Top hat should have points defining the shape
        assert len(profile) >= 6
        
        # All points should be Vec2
        assert all(isinstance(p, Vec2) for p in profile)
    
    def test_z_section_profile(self):
        """Test Z-section profile generation."""
        material = FrameMaterial(
            name="Z200",
            material_type=FrameMaterialType.Z,
            width=100.0,
            height=200.0,
            thickness=2.5,
            section=0
        )
        
        profile = material.get_profile_points()
        
        # Z-section should have points for the Z shape
        assert len(profile) >= 8
        
        # Check that we have the characteristic Z shape
        # (flanges on opposite sides)
        assert all(isinstance(p, Vec2) for p in profile)
    
    def test_unknown_profile_type(self):
        """Test default profile for unknown types."""
        material = FrameMaterial(
            name="Unknown",
            material_type=FrameMaterialType.UNKNOWN,
            width=100.0,
            height=50.0,
            thickness=2.0
        )
        
        profile = material.get_profile_points()
        
        # Should return a simple rectangle
        assert len(profile) >= 4
        assert all(isinstance(p, Vec2) for p in profile)
    
    def test_from_string(self):
        """Test creating material from string specification."""
        # Test C-channel parsing
        material = FrameMaterial.from_string("C10015")
        assert material.name == "C10015"
        assert material.material_type == FrameMaterialType.C
        assert material.width == 102.0
        assert material.height == 51.0
        assert material.thickness == 1.5
        
        # Test with flipped modifier
        material_flipped = FrameMaterial.from_string("C10015 (flipped)")
        assert material_flipped.name == "C10015"
        assert material_flipped.flipped == True


class TestBracketMaterial:
    """Test BracketMaterial functionality."""
    
    def test_creation(self):
        """Test bracket material creation."""
        bracket = BracketMaterial(
            name="StandardBracket",
            mesh_name="bracket_mesh_01"
        )
        
        assert bracket.name == "StandardBracket"
        assert bracket.mesh_name == "bracket_mesh_01"
    
    def test_equality(self):
        """Test bracket material equality."""
        b1 = BracketMaterial("Bracket1", "mesh1")
        b2 = BracketMaterial("Bracket1", "mesh1")
        b3 = BracketMaterial("Bracket2", "mesh1")
        
        assert b1 == b2
        assert b1 != b3


class TestCladdingMaterial:
    """Test CladdingMaterial functionality."""
    
    def test_creation(self):
        """Test cladding material creation."""
        cladding = CladdingMaterial(
            name="Corrugated",
            coverage=762.0,
            pitch=76.2,
            depth=18.0
        )
        
        assert cladding.name == "Corrugated"
        assert cladding.coverage == 762.0
        assert cladding.pitch == 76.2
        assert cladding.depth == 18.0
    
    def test_sheets_calculation(self):
        """Test calculating number of sheets needed."""
        cladding = CladdingMaterial(
            name="Corrugated",
            coverage=762.0,  # 762mm coverage per sheet
            pitch=76.2,
            depth=18.0
        )
        
        # Test sheet calculation
        sheets = cladding.get_sheets(3000.0)  # 3000mm span
        assert sheets == 4  # ceil(3000 / 762) = 4


class TestColorMaterial:
    """Test ColorMaterial functionality."""
    
    def test_creation(self):
        """Test color material creation."""
        color = ColorMaterial(
            name="Monument",
            r=77,
            g=77,
            b=77,
            a=255
        )
        
        assert color.name == "Monument"
        assert color.r == 77
        assert color.g == 77
        assert color.b == 77
        assert color.a == 255
    
    def test_default_alpha(self):
        """Test default alpha value."""
        color = ColorMaterial(
            name="Red",
            r=255,
            g=0,
            b=0
        )
        
        assert color.a == 255  # Default alpha should be opaque


class TestFootingMaterial:
    """Test FootingMaterial functionality."""
    
    def test_block_footing(self):
        """Test block footing creation."""
        footing = FootingMaterial(
            footing_type=FootingMaterialType.BLOCK,
            width=400.0,
            height=400.0,
            depth=600.0
        )
        
        assert footing.footing_type == FootingMaterialType.BLOCK
        assert footing.width == 400.0
        assert footing.height == 400.0
        assert footing.depth == 600.0
    
    def test_bored_footing(self):
        """Test bored footing creation."""
        footing = FootingMaterial(
            footing_type=FootingMaterialType.BORED,
            diameter=450.0,
            depth=1200.0
        )
        
        assert footing.footing_type == FootingMaterialType.BORED
        assert footing.diameter == 450.0
        assert footing.depth == 1200.0


class TestStrapMaterial:
    """Test StrapMaterial functionality."""
    
    def test_creation(self):
        """Test strap material creation."""
        strap = StrapMaterial(
            name="StrapBrace",
            width=50.0,
            thickness=3.0
        )
        
        assert strap.name == "StrapBrace"
        assert strap.width == 50.0
        assert strap.thickness == 3.0


if __name__ == "__main__":
    pytest.main([__file__, "-v"])