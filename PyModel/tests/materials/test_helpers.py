"""
Test suite for material helper utilities.

Tests all material helper functions and catalogs.
"""

import pytest
from src.materials.helpers import (
    FrameMaterialHelper,
    CladdingProfileHelper,
    CladdingMaterialHelper,
    FastenerMaterialHelper,
    FootingMaterialHelper,
    FlashingMaterialHelper,
    BracketMaterialHelper
)
from src.materials.base import (
    FrameMaterial, FrameMaterialType,
    CladdingMaterial, FastenerMaterial,
    FootingMaterial, FootingMaterialType,
    FlashingMaterial, BracketMaterial
)
from src.geometry.primitives import Vec2


class TestFrameMaterialHelper:
    """Test FrameMaterialHelper functionality."""
    
    def test_web_hole_centers(self):
        """Test web hole center constants."""
        assert FrameMaterialHelper.WEB_HOLE_CENTERS_100 == 40
        assert FrameMaterialHelper.WEB_HOLE_CENTERS_150 == 60
        assert FrameMaterialHelper.WEB_HOLE_CENTERS_200 == 110
        assert FrameMaterialHelper.WEB_HOLE_CENTERS_250 == 160
        assert FrameMaterialHelper.WEB_HOLE_CENTERS_300 == 210
        assert FrameMaterialHelper.WEB_HOLE_CENTERS_350 == 210
        assert FrameMaterialHelper.WEB_HOLE_CENTERS_400 == 310
    
    def test_get_c_section(self):
        """Test retrieving C-sections."""
        # Single C-section
        c100 = FrameMaterialHelper.get_frame_material("C10010")
        assert c100.name == "C10010"
        assert c100.material_type == FrameMaterialType.C
        assert c100.web == 102
        assert c100.flange == 51
        assert c100.lip == 12.5
        assert c100.thickness == 1.0
        assert c100.is_b2b == False
        
        # Back-to-back C-section
        c2_150 = FrameMaterialHelper.get_frame_material("2C15024")
        assert c2_150.name == "2C15024"
        assert c2_150.material_type == FrameMaterialType.C
        assert c2_150.is_b2b == True
        assert c2_150.web == 152
        assert c2_150.flange == 128
    
    def test_get_z_section(self):
        """Test retrieving Z-sections."""
        z200 = FrameMaterialHelper.get_frame_material("Z20019")
        assert z200.name == "Z20019"
        assert z200.material_type == FrameMaterialType.Z
        assert z200.web == 203
        assert z200.flange == 79
        assert z200.lip == 19.5
        assert z200.thickness == 1.9
    
    def test_get_th_section(self):
        """Test retrieving TopHat sections."""
        th096 = FrameMaterialHelper.get_frame_material("TH096100")
        assert th096.name == "TH096100"
        assert th096.material_type == FrameMaterialType.TH
        assert th096.web == 109
        assert th096.flange == 96
        assert th096.thickness == 1.00
        
        # Test RB variants
        rb22 = FrameMaterialHelper.get_frame_material("RB22")
        assert rb22.name == "RB22"
        assert rb22.material_type == FrameMaterialType.TH
    
    def test_get_shs_section(self):
        """Test retrieving SHS sections."""
        shs75 = FrameMaterialHelper.get_frame_material("SHS07507525")
        assert shs75.name == "SHS07507525"
        assert shs75.material_type == FrameMaterialType.SHS
        assert shs75.web == 75
        assert shs75.flange == 75
        assert shs75.thickness == 2.5
    
    def test_get_pad_section(self):
        """Test retrieving PAD sections."""
        pad = FrameMaterialHelper.get_frame_material("PADJ10027")
        assert pad.name == "PADJ10027"
        assert pad.material_type == FrameMaterialType.PAD
        assert pad.web == 104
        assert pad.flange == 57
    
    def test_get_floor_section(self):
        """Test retrieving floor joist sections."""
        j182 = FrameMaterialHelper.get_frame_material("J18210")
        assert j182.name == "J18210"
        assert j182.material_type == FrameMaterialType.C
        assert j182.web == 182
        assert j182.flange == 51
        
        # Test bearer sections
        b235 = FrameMaterialHelper.get_frame_material("B23519")
        assert b235.name == "B23519"
        assert b235.material_type == FrameMaterialType.C
        assert b235.web == 236
        assert b235.flange == 72
    
    def test_get_srdj_section(self):
        """Test retrieving SRDJ sections."""
        srdj = FrameMaterialHelper.get_frame_material("SRDTS64")
        assert srdj.name == "SRDTS64"
        assert srdj.material_type == FrameMaterialType.SRDJ
        assert srdj.web == 69.8
        assert srdj.flange == 102
    
    def test_aliases(self):
        """Test material name aliases."""
        # C-section aliases
        c100 = FrameMaterialHelper.get_frame_material("C100")
        assert c100.name == "C10010"
        
        c150 = FrameMaterialHelper.get_frame_material("C150")
        assert c150.name == "C15024"
        
        # Z-section aliases
        z100 = FrameMaterialHelper.get_frame_material("Z100")
        assert z100.name == "Z10010"
        
        # TopHat aliases
        th96 = FrameMaterialHelper.get_frame_material("TH96")
        assert th96.name == "TH096100"
        
        # SHS/RHS aliases
        shs75 = FrameMaterialHelper.get_frame_material("SHS75")
        assert shs75.name == "SHS07507525"
        
        rhs75 = FrameMaterialHelper.get_frame_material("RHS7525")
        assert rhs75.name == "SHS07507525"
    
    def test_invalid_material_name(self):
        """Test error for invalid material name."""
        with pytest.raises(ValueError, match="Frame material not found"):
            FrameMaterialHelper.get_frame_material("INVALID")
    
    def test_material_caching(self):
        """Test material caching."""
        # First retrieval
        mat1 = FrameMaterialHelper.get_frame_material("C10010")
        
        # Second retrieval should return cached instance
        mat2 = FrameMaterialHelper.get_frame_material("C10010")
        
        # Should be the same object
        assert mat1 is mat2
    
    def test_get_frame_material_single(self):
        """Test getting single version of B2B material."""
        # Get B2B material
        b2b = FrameMaterialHelper.get_frame_material("2C15024")
        assert b2b.is_b2b == True
        
        # Get single version
        single = FrameMaterialHelper.get_frame_material_single("2C15024")
        assert single.name == "C15024"
        assert single.is_b2b == False
        
        # Already single material should return itself
        already_single = FrameMaterialHelper.get_frame_material_single("C15024")
        assert already_single.name == "C15024"
        assert already_single.is_b2b == False
    
    def test_get_all_materials_by_type(self):
        """Test getting all materials of a specific type."""
        # Get all C-sections
        c_sections = FrameMaterialHelper.get_all_materials_by_type(FrameMaterialType.C)
        assert len(c_sections) > 0
        assert all(mat.material_type == FrameMaterialType.C for mat in c_sections)
        
        # Get all Z-sections
        z_sections = FrameMaterialHelper.get_all_materials_by_type(FrameMaterialType.Z)
        assert len(z_sections) > 0
        assert all(mat.material_type == FrameMaterialType.Z for mat in z_sections)
        
        # Get all TopHat sections
        th_sections = FrameMaterialHelper.get_all_materials_by_type(FrameMaterialType.TH)
        assert len(th_sections) > 0
        assert all(mat.material_type == FrameMaterialType.TH for mat in th_sections)


class TestCladdingProfileHelper:
    """Test CladdingProfileHelper functionality."""
    
    def test_create_sample(self):
        """Test sample profile creation."""
        profile = CladdingProfileHelper.create_sample()
        assert len(profile) == 5
        assert profile[0] == Vec2(0, 0)
        assert profile[1] == Vec2(150, 0)
        assert profile[2] == Vec2(160, 30)
        assert profile[3] == Vec2(190, 30)
        assert profile[4] == Vec2(200, 0)
    
    def test_create_corrugated(self):
        """Test corrugated profile creation."""
        # Default parameters
        profile = CladdingProfileHelper.create_corrugated()
        assert len(profile) == 9  # num_segments + 1
        assert profile[0].x == 0
        
        # Custom parameters
        profile2 = CladdingProfileHelper.create_corrugated(profile_height=25, num_segments=10)
        assert len(profile2) == 11
        
        # Verify it creates a wave pattern
        # Y values should oscillate between 0 and profile_height
        y_values = [p.y for p in profile]
        assert min(y_values) == pytest.approx(0, abs=1e-10)
        assert max(y_values) == pytest.approx(19)  # Default profile_height
    
    def test_create_monoclad(self):
        """Test Monoclad profile creation."""
        profile = CladdingProfileHelper.create_monoclad()
        assert len(profile) == 11
        assert profile[0] == Vec2(0, 0)
        assert profile[2] == Vec2(19.7, 29)  # Peak
        assert profile[-1] == Vec2(187.9, 0)
    
    def test_create_monopanel(self):
        """Test Monopanel profile creation."""
        profile = CladdingProfileHelper.create_monopanel()
        assert len(profile) == 5
        assert profile[0] == Vec2(0, 0)
        assert profile[2] == Vec2(28, 12)  # Peak
        assert profile[-1] == Vec2(121, 0)
    
    def test_create_k_panel(self):
        """Test K-Panel profile creation."""
        profile = CladdingProfileHelper.create_k_panel()
        assert len(profile) == 13
        assert profile[0] == Vec2(0, 0)
        assert profile[10] == Vec2(200, 12)  # Peak
        assert profile[-1] == Vec2(220, 0)
    
    def test_create_sharpline(self):
        """Test Sharpline profile creation."""
        profile = CladdingProfileHelper.create_sharpline()
        assert len(profile) == 5
        assert profile[0] == Vec2(0, 0)
        assert profile[2] == Vec2(272, 25)  # Peak
        assert profile[-1] == Vec2(282, 0)
    
    def test_create_t_rib(self):
        """Test T-Rib profile creation."""
        profile = CladdingProfileHelper.create_t_rib()
        assert len(profile) == 9
        assert profile[0] == Vec2(0, 0)
        assert profile[6] == Vec2(147, 29)  # Peak
        assert profile[-1] == Vec2(190, 0)
    
    def test_create_metrib(self):
        """Test Metrib profile creation."""
        profile = CladdingProfileHelper.create_metrib()
        assert len(profile) == 13
        assert profile[0] == Vec2(0, 0)
        assert profile[10] == Vec2(119, 22)  # Peak
        assert profile[-1] == Vec2(150, 0)
    
    def test_create_metclad(self):
        """Test Metclad profile creation."""
        profile = CladdingProfileHelper.create_metclad()
        assert len(profile) == 13
        assert profile[0] == Vec2(0, 0)
        assert profile[10] == Vec2(151, 12)  # Peak
        assert profile[-1] == Vec2(170, 0)


class TestCladdingMaterialHelper:
    """Test CladdingMaterialHelper functionality."""
    
    def test_corrugated_material(self):
        """Test corrugated material properties."""
        mat = CladdingMaterialHelper.CORRUGATED
        assert mat.name == "Corrugated"
        assert mat.design == "Corrugated"
        assert mat.cover_width == 762
        assert mat.rib_height == 16
        assert mat.overlap == 93
        assert mat.bmt == 0.42
        assert mat.tct == 0.48
        assert mat.is_profile_rotated == False
        assert len(mat.profile) > 0
    
    def test_monoclad_material(self):
        """Test Monoclad material properties."""
        mat = CladdingMaterialHelper.MONOCLAD
        assert mat.name == "Monoclad"
        assert mat.design == "Monoclad"
        assert mat.cover_width == 762
        assert mat.rib_height == 29
        assert mat.overlap == 68
        assert mat.bmt == 0.42
        assert mat.tct == 0.48
        assert mat.is_profile_rotated == False
    
    def test_monopanel_material(self):
        """Test M-Panel material properties."""
        mat = CladdingMaterialHelper.MONOPANEL
        assert mat.name == "M-Panel"
        assert mat.design == "M-Panel"
        assert mat.cover_width == 762
        assert mat.rib_height == 12
        assert mat.overlap == 93
        assert mat.bmt == 0.48
        assert mat.tct == 0.53
        assert mat.is_profile_rotated == True
    
    def test_get_cladding_material(self):
        """Test retrieving cladding materials by name."""
        # Exact match
        mat = CladdingMaterialHelper.get_cladding_material("Corrugated")
        assert mat.name == "Corrugated"
        
        # Case insensitive
        mat2 = CladdingMaterialHelper.get_cladding_material("corrugated")
        assert mat2.name == "Corrugated"
        
        # M-Panel
        mat3 = CladdingMaterialHelper.get_cladding_material("M-Panel")
        assert mat3.name == "M-Panel"
    
    def test_invalid_cladding_material(self):
        """Test error for invalid cladding material."""
        with pytest.raises(ValueError, match="Cladding material not found"):
            CladdingMaterialHelper.get_cladding_material("INVALID")
    
    def test_all_cladding_materials(self):
        """Test all cladding materials in catalog."""
        catalog = CladdingMaterialHelper.CLADDING_MATERIALS
        assert len(catalog) == 6
        assert "CORRUGATED" in catalog
        assert "MONOCLAD" in catalog
        assert "M-PANEL" in catalog
        assert "K-PANEL" in catalog
        assert "HORIZONTAL CORRUGATED" in catalog
        assert "SHARPLINE" in catalog


class TestFastenerMaterialHelper:
    """Test FastenerMaterialHelper functionality."""
    
    def test_get_bolt(self):
        """Test retrieving bolt materials."""
        # M12x30
        bolt1 = FastenerMaterialHelper.get_fastener_material("M12x30")
        assert bolt1.name == "M12x30"
        assert bolt1.material_type == "Bolt"
        assert bolt1.length == 50
        assert bolt1.pitch == 25
        assert bolt1.diameter == 12
        
        # M16x33
        bolt2 = FastenerMaterialHelper.get_fastener_material("M16x33")
        assert bolt2.name == "M16x33"
        assert bolt2.material_type == "Bolt"
        assert bolt2.length == 50
        assert bolt2.pitch == 29
        assert bolt2.diameter == 16
        
        # M16x45
        bolt3 = FastenerMaterialHelper.get_fastener_material("M16x45")
        assert bolt3.name == "M16x45"
        assert bolt3.material_type == "Bolt"
        assert bolt3.length == 50
        assert bolt3.pitch == 29
        assert bolt3.diameter == 16
    
    def test_case_insensitive(self):
        """Test case insensitive retrieval."""
        bolt1 = FastenerMaterialHelper.get_fastener_material("m12x30")
        bolt2 = FastenerMaterialHelper.get_fastener_material("M12X30")
        assert bolt1.name == "M12x30"
        assert bolt2.name == "M12x30"
    
    def test_invalid_fastener(self):
        """Test error for invalid fastener."""
        with pytest.raises(ValueError, match="Fastener material not found"):
            FastenerMaterialHelper.get_fastener_material("INVALID")


class TestFootingMaterialHelper:
    """Test FootingMaterialHelper functionality."""
    
    def test_create_footing(self):
        """Test creating generic footing."""
        footing = FootingMaterialHelper.create_footing(
            FootingMaterialType.BLOCK,
            width=600,
            length=600,
            depth=450
        )
        assert footing.footing_type == FootingMaterialType.BLOCK
        assert footing.width == 600
        assert footing.length == 600
        assert footing.depth == 450
    
    def test_create_block(self):
        """Test creating block footing."""
        block = FootingMaterialHelper.create_block(
            width=500,
            length=500,
            depth=400
        )
        assert block.footing_type == FootingMaterialType.BLOCK
        assert block.width == 500
        assert block.length == 500
        assert block.depth == 400
    
    def test_create_bored(self):
        """Test creating bored footing."""
        bored = FootingMaterialHelper.create_bored(
            diameter=450,
            depth=600
        )
        assert bored.footing_type == FootingMaterialType.BORED
        assert bored.width == 450  # Diameter stored as width
        assert bored.length == 450  # Diameter stored as length
        assert bored.depth == 600


class TestFlashingMaterialHelper:
    """Test FlashingMaterialHelper functionality."""
    
    def test_create_flashing(self):
        """Test creating flashing material."""
        # Default thickness
        flash1 = FlashingMaterialHelper.create_flashing(
            name="Ridge Capping",
            description="Standard ridge capping"
        )
        assert flash1.name == "Ridge Capping"
        assert flash1.description == "Standard ridge capping"
        assert flash1.thickness == 0.55
        assert flash1.profile == []
        assert flash1.front_faces == []
        assert flash1.back_faces == []
        
        # Custom thickness
        flash2 = FlashingMaterialHelper.create_flashing(
            name="Barge Capping",
            description="Heavy duty barge capping",
            thickness=0.75
        )
        assert flash2.name == "Barge Capping"
        assert flash2.thickness == 0.75


class TestBracketMaterialHelper:
    """Test BracketMaterialHelper functionality."""
    
    def test_get_bracket_material(self):
        """Test getting bracket material."""
        bracket = BracketMaterialHelper.get_bracket_material(
            mesh_name="Apex_Brackets.APB11100-UF",
            name="APB11100-UF"
        )
        assert bracket.name == "APB11100-UF"
        assert bracket.mesh_name == "Apex_Brackets.APB11100-UF"
    
    def test_apex_brackets(self):
        """Test apex bracket definitions."""
        apb1 = BracketMaterialHelper.ApexBrackets.APB11100_UF()
        assert apb1.name == "APB11100-UF"
        assert apb1.mesh_name == "Apex_Brackets.APB11100-UF"
        
        apb2 = BracketMaterialHelper.ApexBrackets.APB15100_UF()
        assert apb2.name == "APB15100-UF"
        assert apb2.mesh_name == "Apex_Brackets.APB15100-UF"
    
    def test_base_cleat_brackets(self):
        """Test base cleat bracket definitions."""
        db1 = BracketMaterialHelper.BaseCleatBrackets.DB501H2()
        assert db1.name == "DB501H2"
        assert db1.mesh_name == "Base_Cleat_Brackets.DB501H2"
        
        db2 = BracketMaterialHelper.BaseCleatBrackets.DB1002H()
        assert db2.name == "DB1002H"
        assert db2.mesh_name == "Base_Cleat_Brackets.DB1002H"
    
    def test_eave_purlin_brackets(self):
        """Test eave purlin bracket definitions."""
        epb1 = BracketMaterialHelper.EavePurlinBrackets.EPB64S51_PH()
        assert epb1.name == "EPB64S51-PH"
        assert epb1.mesh_name == "Eave_Purlin_Brackets.EPB64S51-PH"
        
        epb2 = BracketMaterialHelper.EavePurlinBrackets.EPB100S51_PH()
        assert epb2.name == "EPB100S51-PH"
        assert epb2.mesh_name == "Eave_Purlin_Brackets.EPB100S51-PH"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])