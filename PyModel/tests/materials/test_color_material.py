"""Tests for ColorMaterial class.

Tests align with C# ColorMaterialHelper.cs functionality.
"""

import pytest
from materials import ColorMaterial


class TestColorMaterial:
    """Test ColorMaterial creation and manipulation."""
    
    def test_from_hex(self):
        """Test creating color from hex string.
        
        C# Ref: ColorMaterialHelper.cs lines 25-56
        """
        # Test valid hex color
        color = ColorMaterial.from_hex("Basalt", "CB", "6D6C6E")
        assert color.name == "Basalt"
        assert color.finish == "CB"
        assert color.r == 0x6D  # 109
        assert color.g == 0x6C  # 108
        assert color.b == 0x6E  # 110
        assert color.a == 255
        
        # Test white color
        white = ColorMaterial.from_hex("Dover White", "CB", "F9FBF1")
        assert white.r == 0xF9  # 249
        assert white.g == 0xFB  # 251
        assert white.b == 0xF1  # 241
        
        # Test black color
        black = ColorMaterial.from_hex("Night Sky", "CB", "000000")
        assert black.r == 0
        assert black.g == 0
        assert black.b == 0
    
    def test_from_hex_invalid(self):
        """Test invalid hex string handling."""
        with pytest.raises(ValueError, match="Expected hex format"):
            ColorMaterial.from_hex("Bad", "CB", "FF")  # Too short
        
        with pytest.raises(ValueError, match="Expected hex format"):
            ColorMaterial.from_hex("Bad", "CB", "FFFFFFF")  # Too long
    
    def test_from_rgb(self):
        """Test creating color from RGB values.
        
        C# Ref: ColorMaterialHelper.cs lines 152-176
        """
        # Test Azure color from Colorsteel
        azure = ColorMaterial.from_rgb("Azure", "CB", 77, 109, 125)
        assert azure.name == "Azure"
        assert azure.finish == "CB"
        assert azure.r == 77
        assert azure.g == 109
        assert azure.b == 125
        assert azure.a == 255
        
        # Test with custom alpha
        transparent = ColorMaterial.from_rgb("Clear", "", 255, 255, 255, 128)
        assert transparent.a == 128
    
    def test_from_cmyk(self):
        """Test creating color from CMYK values."""
        # Test pure cyan
        cyan = ColorMaterial.from_cmyk("Cyan", "CB", 1.0, 0.0, 0.0, 0.0)
        assert cyan.r == 0
        assert cyan.g == 255
        assert cyan.b == 255
        
        # Test pure magenta
        magenta = ColorMaterial.from_cmyk("Magenta", "CB", 0.0, 1.0, 0.0, 0.0)
        assert magenta.r == 255
        assert magenta.g == 0
        assert magenta.b == 255
        
        # Test pure yellow
        yellow = ColorMaterial.from_cmyk("Yellow", "CB", 0.0, 0.0, 1.0, 0.0)
        assert yellow.r == 255
        assert yellow.g == 255
        assert yellow.b == 0
        
        # Test black
        black = ColorMaterial.from_cmyk("Black", "CB", 0.0, 0.0, 0.0, 1.0)
        assert black.r == 0
        assert black.g == 0
        assert black.b == 0
        
        # Test 50% gray (K=0.5)
        gray = ColorMaterial.from_cmyk("Gray", "CB", 0.0, 0.0, 0.0, 0.5)
        assert gray.r == 127
        assert gray.g == 127
        assert gray.b == 127
    
    def test_default_values(self):
        """Test default values for ColorMaterial."""
        color = ColorMaterial()
        assert color.name == ""
        assert color.finish == ""
        assert color.r == 0
        assert color.g == 0
        assert color.b == 0
        assert color.a == 255  # Default alpha is opaque
    
    def test_equality(self):
        """Test color equality."""
        color1 = ColorMaterial.from_rgb("Test", "CB", 100, 150, 200)
        color2 = ColorMaterial.from_rgb("Test", "CB", 100, 150, 200)
        color3 = ColorMaterial.from_rgb("Test", "CB", 100, 150, 201)  # Different blue
        
        assert color1 == color2
        assert color1 != color3


class TestColorbondColors:
    """Test predefined Colorbond colors.
    
    C# Ref: ColorMaterialHelper.cs lines 21-143
    """
    
    def test_zincalume(self):
        """Test Zincalume color."""
        from materials.visual import ColorLibrary
        
        zinc = ColorLibrary.COLORBOND_COLORS["ZINCALUME"]
        assert zinc.name == "Zincalume"
        assert zinc.finish == "ZA"
        assert zinc.r == 0x99  # 153
        assert zinc.g == 0x99  # 153
        assert zinc.b == 0x99  # 153
    
    def test_monument(self):
        """Test Monument color (common default)."""
        from materials.visual import ColorLibrary
        
        monument = ColorLibrary.COLORBOND_COLORS["MONUMENT"]
        assert monument.name == "Monument"
        assert monument.finish == "CB"
        assert monument.r == 0x32  # 50
        assert monument.g == 0x32  # 50
        assert monument.b == 0x33  # 51
    
    def test_color_lookup(self):
        """Test color lookup functionality."""
        from materials.visual import ColorLibrary
        
        # Test normal lookup
        basalt = ColorLibrary.get_color("Basalt")
        assert basalt is not None
        assert basalt.name == "Basalt"
        
        # Test with spaces
        classic_cream = ColorLibrary.get_color("Classic Cream")
        assert classic_cream is not None
        assert classic_cream.name == "Classic Cream"
        
        # Test case insensitive
        monument_lower = ColorLibrary.get_color("monument")
        assert monument_lower is not None
        assert monument_lower.name == "Monument"
        
        # Test not found
        not_found = ColorLibrary.get_color("NotAColor")
        assert not_found is None


class TestColorsteelColors:
    """Test predefined Colorsteel colors for NZ.
    
    C# Ref: ColorMaterialHelper.cs lines 145-176
    """
    
    def test_colorsteel_colors(self):
        """Test some Colorsteel colors."""
        from materials.visual import ColorLibrary
        
        # Test Azure
        azure = ColorLibrary.get_color("Azure", "COLORSTEEL")
        assert azure is not None
        assert azure.name == "Azure"
        assert azure.r == 77
        assert azure.g == 109
        assert azure.b == 125
        
        # Test Bone White
        bone_white = ColorLibrary.get_color("Bone White", "COLORSTEEL")
        assert bone_white is not None
        assert bone_white.r == 182
        assert bone_white.g == 182
        assert bone_white.b == 168


class TestSkylightColors:
    """Test skylight colors.
    
    C# Ref: ColorMaterialHelper.cs lines 178-182
    """
    
    def test_skylight_colors(self):
        """Test skylight material colors."""
        from materials.visual import ColorLibrary
        
        # Test clear skylight
        clear = ColorLibrary.get_color("Clear", "SKYLIGHT")
        assert clear is not None
        assert clear.name == "Clear Skylight"
        assert clear.r == 124
        assert clear.g == 221
        assert clear.b == 255
        assert clear.a == 200  # Translucent
        
        # Test opal skylight
        opal = ColorLibrary.get_color("Opal", "SKYLIGHT")
        assert opal is not None
        assert opal.name == "Opal Skylight"
        assert opal.r == 64
        assert opal.g == 64
        assert opal.b == 64
        assert opal.a == 200  # Translucent


class TestGetOrCreateColor:
    """Test get_or_create_color functionality.
    
    C# Ref: ColorMaterialHelper.cs lines 57-110
    """
    
    def test_rgb_string_parsing(self):
        """Test parsing RGB string format."""
        from materials.visual import ColorLibrary
        
        # Test RGB with 0-1 range values
        color1 = ColorLibrary.get_or_create_color("rgb(0.5,0.75,1.0)")
        assert color1.name == "Custom Color"
        assert color1.r == 127  # 0.5 * 255
        assert color1.g == 191  # 0.75 * 255
        assert color1.b == 255  # 1.0 * 255
        assert color1.a == 255
        
        # Test RGB with 0-255 range values
        color2 = ColorLibrary.get_or_create_color("rgb(100,150,200)")
        assert color2.r == 100
        assert color2.g == 150
        assert color2.b == 200
        
        # Test RGBA
        color3 = ColorLibrary.get_or_create_color("rgb(1.0,1.0,1.0,128)")
        assert color3.r == 255
        assert color3.g == 255
        assert color3.b == 255
        assert color3.a == 128
    
    def test_color_name_lookup(self):
        """Test looking up colors by name."""
        from materials.visual import ColorLibrary
        
        # Test existing color
        monument = ColorLibrary.get_or_create_color("Monument")
        assert monument.name == "Monument"
        assert monument.finish == "CB"
        
        # Test with underscores instead of spaces
        classic_cream = ColorLibrary.get_or_create_color("Classic_Cream")
        assert classic_cream.name == "Classic Cream"
        
        # Test default fallback
        default = ColorLibrary.get_or_create_color("")
        assert default.name == "Monument"  # Default
    
    def test_invalid_rgb_fallback(self):
        """Test fallback for invalid RGB strings."""
        from materials.visual import ColorLibrary
        
        # Invalid RGB format should fall back to default
        color = ColorLibrary.get_or_create_color("rgb(invalid)")
        assert color.name == "Monument"  # Default fallback