"""
Test suite for visual properties and texture mapping.

Tests visual properties, color libraries, and material appearance functionality.
"""

import pytest
import math
from src.materials.visual import (
    MaterialFinish, TextureMapping, MaterialAppearance,
    ColorLibrary, MaterialProfile, MaterialVisualizer
)
from src.materials.base import ColorMaterial


class TestMaterialFinish:
    """Test MaterialFinish enum."""
    
    def test_finish_values(self):
        """Test material finish enum values."""
        assert MaterialFinish.COLORBOND.value == "CB"
        assert MaterialFinish.ZINCALUME.value == "ZA"
        assert MaterialFinish.POWDER_COAT.value == "PC"
        assert MaterialFinish.ANODIZED.value == "AN"
        assert MaterialFinish.MILL.value == "ML"
        assert MaterialFinish.CUSTOM.value == ""


class TestTextureMapping:
    """Test TextureMapping functionality."""
    
    def test_creation_defaults(self):
        """Test texture mapping with default values."""
        tm = TextureMapping()
        assert tm.u_scale == 1.0
        assert tm.v_scale == 1.0
        assert tm.u_offset == 0.0
        assert tm.v_offset == 0.0
        assert tm.rotation == 0.0
    
    def test_creation_custom(self):
        """Test texture mapping with custom values."""
        tm = TextureMapping(
            u_scale=2.0,
            v_scale=0.5,
            u_offset=0.25,
            v_offset=0.75,
            rotation=math.pi / 4
        )
        
        assert tm.u_scale == 2.0
        assert tm.v_scale == 0.5
        assert tm.u_offset == 0.25
        assert tm.v_offset == 0.75
        assert tm.rotation == pytest.approx(math.pi / 4)
    
    def test_apply_to_uv_identity(self):
        """Test UV transformation with identity mapping."""
        tm = TextureMapping()
        u, v = tm.apply_to_uv(0.5, 0.5)
        assert u == 0.5
        assert v == 0.5
    
    def test_apply_to_uv_scale(self):
        """Test UV transformation with scaling."""
        tm = TextureMapping(u_scale=2.0, v_scale=0.5)
        u, v = tm.apply_to_uv(0.5, 0.8)
        assert u == 1.0  # 0.5 * 2.0
        assert v == 0.4  # 0.8 * 0.5
    
    def test_apply_to_uv_offset(self):
        """Test UV transformation with offset."""
        tm = TextureMapping(u_offset=0.25, v_offset=-0.1)
        u, v = tm.apply_to_uv(0.5, 0.5)
        assert u == 0.75  # 0.5 + 0.25
        assert v == 0.4   # 0.5 - 0.1
    
    def test_apply_to_uv_rotation(self):
        """Test UV transformation with 90-degree rotation."""
        tm = TextureMapping(rotation=math.pi / 2)  # 90 degrees
        u, v = tm.apply_to_uv(1.0, 0.0)
        assert u == pytest.approx(0.0, abs=1e-10)  # Rotated to Y axis
        assert v == pytest.approx(1.0)
    
    def test_apply_to_uv_combined(self):
        """Test UV transformation with all transformations."""
        tm = TextureMapping(
            u_scale=2.0,
            v_scale=2.0,
            u_offset=0.5,
            v_offset=0.5,
            rotation=0  # No rotation for simpler test
        )
        u, v = tm.apply_to_uv(0.25, 0.25)
        assert u == 1.0  # 0.25 * 2.0 + 0.5
        assert v == 1.0  # 0.25 * 2.0 + 0.5


class TestMaterialAppearance:
    """Test MaterialAppearance functionality."""
    
    def test_creation_defaults(self):
        """Test appearance with default values."""
        app = MaterialAppearance()
        assert app.base_color is None
        assert app.metallic == 0.0
        assert app.roughness == 0.5
        assert app.opacity == 1.0
        assert app.emissive_color is None
        assert app.emissive_intensity == 0.0
        assert app.texture_mapping is None
    
    def test_creation_custom(self):
        """Test appearance with custom values."""
        color = ColorMaterial(name="Red", finish="CB", r=255, g=0, b=0)
        tm = TextureMapping(u_scale=2.0)
        
        app = MaterialAppearance(
            base_color=color,
            metallic=0.8,
            roughness=0.2,
            opacity=0.5,
            texture_mapping=tm
        )
        
        assert app.base_color == color
        assert app.metallic == 0.8
        assert app.roughness == 0.2
        assert app.opacity == 0.5
        assert app.texture_mapping == tm
    
    def test_is_transparent(self):
        """Test transparency detection."""
        app1 = MaterialAppearance(opacity=1.0)
        assert app1.is_transparent() == False
        
        app2 = MaterialAppearance(opacity=0.99)
        assert app2.is_transparent() == True
        
        app3 = MaterialAppearance(opacity=0.0)
        assert app3.is_transparent() == True
    
    def test_is_emissive(self):
        """Test emissive detection."""
        # No emission
        app1 = MaterialAppearance()
        assert app1.is_emissive() == False
        
        # Has color but no intensity
        app2 = MaterialAppearance(
            emissive_color=ColorMaterial(name="Glow", finish="", r=255, g=255, b=255),
            emissive_intensity=0.0
        )
        assert app2.is_emissive() == False
        
        # Has intensity but no color
        app3 = MaterialAppearance(emissive_intensity=1.0)
        assert app3.is_emissive() == False
        
        # Has both
        app4 = MaterialAppearance(
            emissive_color=ColorMaterial(name="Glow", finish="", r=255, g=255, b=255),
            emissive_intensity=0.5
        )
        assert app4.is_emissive() == True


class TestColorLibrary:
    """Test ColorLibrary functionality."""
    
    def test_colorbond_colors(self):
        """Test Colorbond color definitions."""
        colors = ColorLibrary.COLORBOND_COLORS
        
        # Check some key colors
        assert "MONUMENT" in colors
        assert "SURFMIST" in colors
        assert "ZINCALUME" in colors
        
        # Check Monument color values
        monument = colors["MONUMENT"]
        assert monument.name == "Monument"
        assert monument.finish == "CB"
        assert monument.r == 50  # Hex 323233
        assert monument.g == 50
        assert monument.b == 51
    
    def test_colorsteel_colors(self):
        """Test Colorsteel color definitions."""
        colors = ColorLibrary.COLORSTEEL_COLORS
        
        # Check some key colors
        assert "IRONSAND" in colors
        assert "KARAKA" in colors
        
        # Check Ironsand color
        ironsand = colors["IRONSAND"]
        assert ironsand.name == "Ironsand"
        assert ironsand.finish == "CB"
        assert ironsand.r == 65
        assert ironsand.g == 63
        assert ironsand.b == 61
    
    def test_skylight_colors(self):
        """Test skylight color definitions."""
        colors = ColorLibrary.SKYLIGHT_COLORS
        
        assert "CLEAR" in colors
        assert "OPAL" in colors
        
        # Check transparency
        clear = colors["CLEAR"]
        assert clear.a == 200  # Semi-transparent
    
    def test_get_color_colorbond(self):
        """Test getting color from Colorbond library."""
        # Exact match
        color = ColorLibrary.get_color("MONUMENT", "COLORBOND")
        assert color is not None
        assert color.name == "Monument"
        
        # Case insensitive
        color2 = ColorLibrary.get_color("monument", "COLORBOND")
        assert color2 is not None
        assert color2.name == "Monument"
        
        # With spaces
        color3 = ColorLibrary.get_color("Classic Cream", "COLORBOND")
        assert color3 is not None
        assert color3.name == "Classic Cream"
    
    def test_get_color_not_found(self):
        """Test getting non-existent color."""
        color = ColorLibrary.get_color("INVALID_COLOR", "COLORBOND")
        assert color is None
        
        # Invalid library
        color2 = ColorLibrary.get_color("MONUMENT", "INVALID_LIBRARY")
        assert color2 is None
    
    def test_get_or_create_color_existing(self):
        """Test get_or_create with existing color."""
        color = ColorLibrary.get_or_create_color("Monument")
        assert color.name == "Monument"
        assert color.finish == "CB"
    
    def test_get_or_create_color_rgb_format(self):
        """Test get_or_create with RGB format."""
        # RGB with 0-255 values
        color1 = ColorLibrary.get_or_create_color("rgb(128,64,32)")
        assert color1.name == "Custom Color"
        assert color1.r == 128
        assert color1.g == 64
        assert color1.b == 32
        assert color1.a == 255
        
        # RGB with 0-1 values
        color2 = ColorLibrary.get_or_create_color("rgb(0.5,0.25,0.125)")
        assert color2.r == 127  # 0.5 * 255
        assert color2.g == 63   # 0.25 * 255
        assert color2.b == 31   # 0.125 * 255
        
        # RGBA format
        color3 = ColorLibrary.get_or_create_color("rgb(255,0,0,128)")
        assert color3.r == 255
        assert color3.g == 0
        assert color3.b == 0
        assert color3.a == 128
    
    def test_get_or_create_color_default(self):
        """Test get_or_create with invalid input uses default."""
        # Empty string
        color1 = ColorLibrary.get_or_create_color("")
        assert color1.name == "Monument"  # Default
        
        # Invalid RGB format
        color2 = ColorLibrary.get_or_create_color("rgb(invalid)")
        assert color2.name == "Monument"  # Default
        
        # Non-existent color name
        color3 = ColorLibrary.get_or_create_color("INVALID_COLOR")
        assert color3.name == "Monument"  # Default
    
    def test_get_or_create_color_custom_default(self):
        """Test get_or_create with custom default."""
        color = ColorLibrary.get_or_create_color(
            "INVALID",
            default_name="Surfmist",
            default_library="COLORBOND"
        )
        assert color.name == "Surfmist"


class TestMaterialProfile:
    """Test MaterialProfile functionality."""
    
    def test_creation_empty(self):
        """Test empty profile creation."""
        profile = MaterialProfile()
        assert profile.points == []
        assert profile.is_closed == True
        assert profile.thickness == 0.0
    
    def test_creation_with_points(self):
        """Test profile with points."""
        points = [(0, 0), (10, 0), (10, 5), (0, 5)]
        profile = MaterialProfile(
            points=points,
            is_closed=True,
            thickness=0.5
        )
        
        assert len(profile.points) == 4
        assert profile.is_closed == True
        assert profile.thickness == 0.5
    
    def test_get_bounding_box(self):
        """Test bounding box calculation."""
        points = [(1, 2), (5, 3), (3, 7), (2, 4)]
        profile = MaterialProfile(points=points)
        
        min_x, min_y, max_x, max_y = profile.get_bounding_box()
        assert min_x == 1
        assert min_y == 2
        assert max_x == 5
        assert max_y == 7
    
    def test_get_bounding_box_empty(self):
        """Test bounding box for empty profile."""
        profile = MaterialProfile()
        bbox = profile.get_bounding_box()
        assert bbox == (0, 0, 0, 0)
    
    def test_get_width(self):
        """Test profile width calculation."""
        points = [(10, 0), (30, 0), (30, 20), (10, 20)]
        profile = MaterialProfile(points=points)
        assert profile.get_width() == 20  # 30 - 10
    
    def test_get_height(self):
        """Test profile height calculation."""
        points = [(0, 5), (10, 5), (10, 15), (0, 15)]
        profile = MaterialProfile(points=points)
        assert profile.get_height() == 10  # 15 - 5


class TestMaterialVisualizer:
    """Test MaterialVisualizer functionality."""
    
    def test_get_material_appearance_metal(self):
        """Test metal appearance."""
        app = MaterialVisualizer.get_material_appearance("metal")
        assert app.metallic == 0.8
        assert app.roughness == 0.3
        assert app.base_color.name == "Monument"  # Default
    
    def test_get_material_appearance_steel(self):
        """Test steel appearance."""
        app = MaterialVisualizer.get_material_appearance("steel")
        assert app.metallic == 0.9
        assert app.roughness == 0.2
        assert app.base_color.name == "Zincalume"
    
    def test_get_material_appearance_glass(self):
        """Test glass appearance."""
        app = MaterialVisualizer.get_material_appearance("glass")
        assert app.metallic == 0.0
        assert app.roughness == 0.1
        assert app.opacity == 0.8
        assert app.is_transparent() == True
    
    def test_get_material_appearance_concrete(self):
        """Test concrete appearance."""
        app = MaterialVisualizer.get_material_appearance("concrete")
        assert app.metallic == 0.0
        assert app.roughness == 0.8
        assert app.base_color.name == "Concrete"
    
    def test_get_material_appearance_custom_color(self):
        """Test appearance with custom color override."""
        custom_color = ColorMaterial(name="Custom", finish="CB", r=200, g=100, b=50)
        app = MaterialVisualizer.get_material_appearance("metal", custom_color)
        assert app.base_color == custom_color
        assert app.metallic == 0.8  # Still uses metal properties
    
    def test_get_material_appearance_unknown(self):
        """Test appearance for unknown material type."""
        app = MaterialVisualizer.get_material_appearance("unknown_material")
        assert app.base_color is None
        assert app.metallic == 0.0
        assert app.roughness == 0.5  # Default values
    
    def test_interpolate_color(self):
        """Test color interpolation."""
        color1 = ColorMaterial(name="Red", finish="CB", r=255, g=0, b=0, a=255)
        color2 = ColorMaterial(name="Blue", finish="CB", r=0, g=0, b=255, a=255)
        
        # Halfway interpolation
        mid = MaterialVisualizer.interpolate_color(color1, color2, 0.5)
        assert mid.r == 127  # (255 + 0) / 2
        assert mid.g == 0
        assert mid.b == 127  # (0 + 255) / 2
        assert mid.a == 255
        
        # At start
        start = MaterialVisualizer.interpolate_color(color1, color2, 0.0)
        assert start.r == 255
        assert start.b == 0
        
        # At end
        end = MaterialVisualizer.interpolate_color(color1, color2, 1.0)
        assert end.r == 0
        assert end.b == 255
    
    def test_interpolate_color_clamping(self):
        """Test color interpolation with clamping."""
        color1 = ColorMaterial(name="C1", finish="", r=100, g=100, b=100)
        color2 = ColorMaterial(name="C2", finish="", r=200, g=200, b=200)
        
        # Out of range t values should be clamped
        result1 = MaterialVisualizer.interpolate_color(color1, color2, -0.5)
        assert result1.r == 100  # Clamped to color1
        
        result2 = MaterialVisualizer.interpolate_color(color1, color2, 1.5)
        assert result2.r == 200  # Clamped to color2


if __name__ == "__main__":
    pytest.main([__file__, "-v"])