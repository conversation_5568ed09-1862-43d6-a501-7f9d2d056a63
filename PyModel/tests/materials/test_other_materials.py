"""Tests for other material classes.

Tests for BracketMaterial, CladdingMaterial, FootingMaterial, Punching, etc.
"""

import pytest
from geometry import Vec3, Vec2, Box3, Basis3
from materials import (
    BracketMaterial, BracketMesh, BracketAttachment,
    CladdingMaterial, FlashingMaterial, DownpipeMaterial,
    FastenerMaterial, FastenerMaterialType,
    FootingMaterial, FootingMaterialType,
    Punching, PunchingWhere,
    StrapMaterial, LiningMaterial
)


class TestBracketMaterial:
    """Test BracketMaterial and related classes.
    
    C# Ref: Materials.cs lines 10-66
    """
    
    def test_bracket_attachment(self):
        """Test BracketAttachment creation.
        
        C# Ref: Materials.cs lines 52-66
        """
        # Test with explicit basis
        pos = Vec3(10, 20, 30)
        basis = Basis3(Vec3(1, 0, 0), Vec3(0, 1, 0), Vec3(0, 0, 1))
        attachment = BracketAttachment(pos, basis)
        
        assert attachment.position == pos
        assert attachment.basis == basis
        
        # Test from_position with default basis
        attachment2 = BracketAttachment.from_position(pos)
        assert attachment2.position == pos
        assert attachment2.basis == Basis3.unit_xyz()
    
    def test_bracket_mesh(self):
        """Test BracketMesh creation.
        
        C# Ref: Materials.cs lines 45-50
        """
        mesh = BracketMesh(id="TestMesh")
        
        assert mesh.id == "TestMesh"
        assert mesh.mesh is None
        assert len(mesh.attachments) == 0
        
        # Add attachments
        mesh.attachments["top"] = BracketAttachment.from_position(Vec3(0, 10, 0))
        mesh.attachments["bottom"] = BracketAttachment.from_position(Vec3(0, -10, 0))
        
        assert len(mesh.attachments) == 2
        assert "top" in mesh.attachments
        assert "bottom" in mesh.attachments
    
    def test_bracket_material_basic(self):
        """Test BracketMaterial basic functionality.
        
        C# Ref: Materials.cs lines 10-43
        """
        material = BracketMaterial(name="TestBracket", mesh_name="TestMesh")
        
        assert material.name == "TestBracket"
        assert material.mesh_name == "TestMesh"
        
        # Test internal_set_mesh
        mesh = BracketMesh(id="NewMesh")
        material.internal_set_mesh(mesh)
        
        assert material.mesh_name == "NewMesh"
        assert material._mesh == mesh
    
    def test_bracket_material_mesh_not_created(self):
        """Test BracketMaterial when mesh not created."""
        material = BracketMaterial(name="TestBracket", mesh_name="TestMesh")
        
        # Should raise NotImplementedError since BracketMaterialHelper not implemented
        with pytest.raises(NotImplementedError):
            material.get_bracket_mesh()


class TestCladdingMaterial:
    """Test CladdingMaterial class.
    
    C# Ref: Materials.cs lines 68-95
    """
    
    def test_cladding_material_creation(self):
        """Test creating cladding material."""
        profile = [Vec2(0, 0), Vec2(10, 0), Vec2(10, 5), Vec2(0, 5)]
        
        cladding = CladdingMaterial(
            name="Corrugated",
            design="CGI",
            cover_width=762.0,
            rib_height=19.0,
            overlap=150.0,
            bmt=0.42,
            tct=0.48,
            profile=profile,
            is_profile_rotated=False
        )
        
        assert cladding.name == "Corrugated"
        assert cladding.design == "CGI"
        assert cladding.cover_width == 762.0
        assert cladding.rib_height == 19.0
        assert cladding.overlap == 150.0
        assert cladding.bmt == 0.42  # Base metal thickness
        assert cladding.tct == 0.48  # Total coated thickness
        assert len(cladding.profile) == 4
        assert cladding.is_profile_rotated == False
    
    def test_cladding_material_defaults(self):
        """Test default values."""
        cladding = CladdingMaterial()
        
        assert cladding.name == ""
        assert cladding.design == ""
        assert cladding.cover_width == 0.0
        assert cladding.rib_height == 0.0
        assert cladding.overlap == 0.0
        assert cladding.bmt == 0.0
        assert cladding.tct == 0.0
        assert len(cladding.profile) == 0
        assert cladding.is_profile_rotated == False


class TestFlashingAndDownpipe:
    """Test FlashingMaterial and DownpipeMaterial classes.
    
    C# Ref: Materials.cs lines 170-199
    """
    
    def test_flashing_material(self):
        """Test FlashingMaterial creation.
        
        C# Ref: Materials.cs lines 170-183
        """
        profile = [Vec2(0, 0), Vec2(50, 0), Vec2(50, 10), Vec2(0, 10)]
        cap = [Vec2(0, 0), Vec2(50, 0), Vec2(50, 5), Vec2(0, 5)]
        
        flashing = FlashingMaterial(
            name="Ridge Flashing",
            description="Standard ridge flashing",
            thickness=0.48,
            profile=profile,
            front_faces=[0, 2],  # Front facing triangles
            cap_outline=cap
        )
        
        assert flashing.name == "Ridge Flashing"
        assert flashing.description == "Standard ridge flashing"
        assert flashing.thickness == 0.48
        assert len(flashing.profile) == 4
        assert flashing.front_faces == [0, 2]
        assert len(flashing.cap_outline) == 4
    
    def test_downpipe_material(self):
        """Test DownpipeMaterial creation.
        
        C# Ref: Materials.cs lines 186-199
        """
        profile = [Vec2(0, 0), Vec2(100, 0), Vec2(100, 100), Vec2(0, 100)]
        
        downpipe = DownpipeMaterial(
            name="Square Downpipe",
            description="100x100 square downpipe",
            thickness=0.55,
            profile=profile,
            front_faces=[0, 1, 2, 3],
            cap_outline=profile  # Same as profile for square
        )
        
        assert downpipe.name == "Square Downpipe"
        assert downpipe.description == "100x100 square downpipe"
        assert downpipe.thickness == 0.55
        assert len(downpipe.profile) == 4
        assert len(downpipe.front_faces) == 4
        assert len(downpipe.cap_outline) == 4


class TestFastenerMaterial:
    """Test FastenerMaterial class.
    
    C# Ref: Materials.cs lines 201-236
    """
    
    def test_create_bolt(self):
        """Test creating bolt fastener.
        
        C# Ref: Materials.cs lines 212-223
        """
        bolt = FastenerMaterial.create_bolt(
            name="M12x50",
            length=50,
            head_diameter=18.0,
            clearance_hole_diameter=13.0
        )
        
        assert bolt.name == "M12x50"
        assert bolt.material_type == FastenerMaterialType.BOLT
        assert bolt.length == 50.0  # Converted to float
        assert bolt.head_diameter == 18.0
        assert bolt.clearance_hole_diameter == 13.0
        assert bolt.description == ""  # Default
    
    def test_fastener_material_defaults(self):
        """Test default values."""
        fastener = FastenerMaterial()
        
        assert fastener.name == ""
        assert fastener.description == ""
        assert fastener.length == 0.0
        assert fastener.head_diameter == 0.0
        assert fastener.clearance_hole_diameter == 0.0
        assert fastener.material_type == FastenerMaterialType.UNKNOWN


class TestFootingMaterial:
    """Test FootingMaterial class.
    
    C# Ref: Materials.cs lines 238-273
    """
    
    def test_footing_material_creation(self):
        """Test creating footing material."""
        footing = FootingMaterial(
            footing_type=FootingMaterialType.BLOCK,
            width=600.0,
            length=600.0,
            depth=300.0
        )
        
        assert footing.footing_type == FootingMaterialType.BLOCK
        assert footing.width == 600.0
        assert footing.length == 600.0
        assert footing.depth == 300.0
    
    def test_footing_material_equality(self):
        """Test FootingMaterial equality.
        
        C# Ref: Materials.cs lines 248-266
        """
        footing1 = FootingMaterial(
            footing_type=FootingMaterialType.BLOCK,
            width=600.0,
            length=600.0,
            depth=300.0
        )
        
        footing2 = FootingMaterial(
            footing_type=FootingMaterialType.BLOCK,
            width=600.0,
            length=600.0,
            depth=300.0
        )
        
        footing3 = FootingMaterial(
            footing_type=FootingMaterialType.BORED,  # Different type
            width=600.0,
            length=600.0,
            depth=300.0
        )
        
        assert footing1 == footing2
        assert footing1 != footing3
        assert footing1 != "not a footing"  # Different type
    
    def test_footing_material_hash(self):
        """Test FootingMaterial hash.
        
        C# Ref: Materials.cs lines 258-261
        """
        footing1 = FootingMaterial(
            footing_type=FootingMaterialType.BLOCK,
            width=600.0,
            length=600.0,
            depth=300.0
        )
        
        footing2 = FootingMaterial(
            footing_type=FootingMaterialType.BLOCK,
            width=600.0,
            length=600.0,
            depth=300.0
        )
        
        # Equal objects should have same hash
        assert hash(footing1) == hash(footing2)
        
        # Can be used in sets/dicts
        footing_set = {footing1, footing2}
        assert len(footing_set) == 1  # Same footing


class TestPunching:
    """Test Punching struct.
    
    C# Ref: Materials.cs lines 505-560
    """
    
    def test_punching_creation(self):
        """Test creating punching."""
        punching = Punching(position=100.0, where=PunchingWhere.WEB)
        
        assert punching.position == 100.0
        assert punching.where == PunchingWhere.WEB
    
    def test_punching_negate(self):
        """Test negating punching position.
        
        C# Ref: Materials.cs lines 517-520
        """
        punching = Punching(position=100.0, where=PunchingWhere.WEB)
        negated = punching.negate()
        
        assert negated.position == -100.0
        assert negated.where == PunchingWhere.WEB
    
    def test_punching_abs(self):
        """Test converting to absolute position.
        
        C# Ref: Materials.cs lines 522-529
        """
        # Positive position remains unchanged
        punching = Punching(position=100.0, where=PunchingWhere.FLANGE)
        abs_punching = punching.abs(section_length=1000.0)
        assert abs_punching.position == 100.0
        
        # Negative position is converted
        punching2 = Punching(position=-100.0, where=PunchingWhere.FLANGE)
        abs_punching2 = punching2.abs(section_length=1000.0)
        assert abs_punching2.position == 900.0  # 1000 - 100
    
    def test_punching_add(self):
        """Test adding to position.
        
        C# Ref: Materials.cs lines 531-534
        """
        punching = Punching(position=100.0, where=PunchingWhere.CENTER)
        added = punching.add(50.0)
        
        assert added.position == 150.0
        assert added.where == PunchingWhere.CENTER
    
    def test_punching_string(self):
        """Test string representation.
        
        C# Ref: Materials.cs lines 536-539
        """
        punching = Punching(position=100.0, where=PunchingWhere.WEB)
        assert str(punching) == "100.0 @ WEB"
    
    def test_punching_equality(self):
        """Test punching equality.
        
        C# Ref: Materials.cs lines 541-544
        """
        punching1 = Punching(position=100.0, where=PunchingWhere.WEB)
        punching2 = Punching(position=100.0, where=PunchingWhere.WEB)
        punching3 = Punching(position=100.0, where=PunchingWhere.FLANGE)
        
        assert punching1 == punching2
        assert punching1 != punching3
        assert punching1 != "not a punching"
    
    def test_punching_static_creators(self):
        """Test static creator methods.
        
        C# Ref: Materials.cs lines 546-559
        """
        web = Punching.create_web(100.0)
        assert web.position == 100.0
        assert web.where == PunchingWhere.WEB
        
        flange = Punching.create_flange(200.0)
        assert flange.position == 200.0
        assert flange.where == PunchingWhere.FLANGE
        
        center = Punching.create_center(300.0)
        assert center.position == 300.0
        assert center.where == PunchingWhere.CENTER


class TestStrapAndLining:
    """Test StrapMaterial and LiningMaterial classes.
    
    C# Ref: Materials.cs lines 571-587
    """
    
    def test_strap_material(self):
        """Test StrapMaterial creation.
        
        C# Ref: Materials.cs lines 571-576
        """
        strap = StrapMaterial(
            name="Strap 50x2",
            width=50.0,
            thickness=2.0
        )
        
        assert strap.name == "Strap 50x2"
        assert strap.width == 50.0
        assert strap.thickness == 2.0
    
    def test_lining_material(self):
        """Test LiningMaterial creation.
        
        C# Ref: Materials.cs lines 578-587
        """
        lining = LiningMaterial(
            name="Insulation Blanket",
            product_code="IB-100",
            thickness=100.0,
            width_per_roll=1200.0,
            length_per_roll=15000.0,
            overlap=100.0,
            area_sqm_per_roll=18.0
        )
        
        assert lining.name == "Insulation Blanket"
        assert lining.product_code == "IB-100"
        assert lining.thickness == 100.0
        assert lining.width_per_roll == 1200.0
        assert lining.length_per_roll == 15000.0
        assert lining.overlap == 100.0
        assert lining.area_sqm_per_roll == 18.0