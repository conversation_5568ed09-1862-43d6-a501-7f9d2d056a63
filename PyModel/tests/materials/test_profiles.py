"""
Test suite for mesh profile generation.

Tests profile creation for different frame material types.
"""

import pytest
import math
from src.materials.profiles import (
    MeshProfileHelper, MeshProfile, MeshProfileItem, PunchingMap
)
from src.materials.base import (
    FrameMaterial, FrameMaterialType, PunchingWhere
)
from src.geometry.primitives import Vec2


class TestMeshProfileItem:
    """Test MeshProfileItem functionality."""
    
    def test_creation(self):
        """Test profile item creation."""
        vertices = [Vec2(0, 0), Vec2(1, 0), Vec2(1, 1), Vec2(0, 1)]
        item = MeshProfileItem(vertexes=vertices)
        
        assert len(item.vertexes) == 4
        assert item.inner_vertexes is None
        assert item.is_hollow() == False
    
    def test_hollow_profile(self):
        """Test hollow profile item."""
        outer = [Vec2(0, 0), Vec2(2, 0), Vec2(2, 2), Vec2(0, 2)]
        inner = [Vec2(0.5, 0.5), Vec2(1.5, 0.5), Vec2(1.5, 1.5), Vec2(0.5, 1.5)]
        
        item = MeshProfileItem(vertexes=outer, inner_vertexes=inner)
        
        assert len(item.vertexes) == 4
        assert len(item.inner_vertexes) == 4
        assert item.is_hollow() == True


class TestPunchingMap:
    """Test PunchingMap functionality."""
    
    def test_creation(self):
        """Test punching map creation."""
        pm = PunchingMap(
            where=PunchingWhere.WEB,
            position=Vec2(10, 20),
            angle=math.pi / 4
        )
        
        assert pm.where == PunchingWhere.WEB
        assert pm.position == Vec2(10, 20)
        assert pm.angle == pytest.approx(math.pi / 4)
    
    def test_default_angle(self):
        """Test default angle is zero."""
        pm = PunchingMap(
            where=PunchingWhere.FLANGE_TOP,
            position=Vec2(0, 0)
        )
        assert pm.angle == 0.0


class TestMeshProfile:
    """Test MeshProfile functionality."""
    
    def test_creation(self):
        """Test mesh profile creation."""
        profile = MeshProfile()
        assert profile.items == []
        assert profile.punching_map == []
    
    def test_with_data(self):
        """Test profile with items and punching maps."""
        item = MeshProfileItem(vertexes=[Vec2(0, 0)])
        pm = PunchingMap(PunchingWhere.WEB, Vec2(5, 5))
        
        profile = MeshProfile(items=[item], punching_map=[pm])
        
        assert len(profile.items) == 1
        assert len(profile.punching_map) == 1


class TestMeshProfileHelper:
    """Test MeshProfileHelper functionality."""
    
    def test_constants(self):
        """Test angle constants."""
        assert MeshProfileHelper.DEG0 == 0.0
        assert MeshProfileHelper.DEG90 == pytest.approx(math.pi / 2)
        assert MeshProfileHelper.DEG180 == pytest.approx(math.pi)
        assert MeshProfileHelper.DEG270 == pytest.approx(3 * math.pi / 2)
        assert MeshProfileHelper.DEG360 == pytest.approx(2 * math.pi)
    
    def test_create_c_section_profile(self):
        """Test C-section profile creation."""
        material = FrameMaterial.create_c(
            name="C10010",
            web_nom=100,
            is_b2b=False,
            web=102,
            flange=51,
            lip=12.5,
            thickness=1.0,
            web_hole_centers=40
        )
        
        profile = MeshProfileHelper.create_mesh_profile(material)
        
        # Should have one profile item
        assert len(profile.items) == 1
        
        # Should have vertices for C-shape
        item = profile.items[0]
        assert len(item.vertexes) >= 8  # C-section has at least 8 vertices
        assert not item.is_hollow()
        
        # Should have punching maps for web holes
        assert len(profile.punching_map) == 2  # Two web holes
        
        # Check punching positions
        pm1, pm2 = profile.punching_map
        assert pm1.where == PunchingWhere.WEB
        assert pm2.where == PunchingWhere.WEB
        assert pm1.position.x == 0  # Centered on web
        assert pm2.position.x == 0
    
    def test_create_c_section_flipped(self):
        """Test flipped C-section profile."""
        material = FrameMaterial.create_c(
            name="C10010",
            web_nom=100,
            is_b2b=False,
            web=102,
            flange=51,
            lip=12.5,
            thickness=1.0,
            web_hole_centers=40,
            flipped=True
        )
        
        profile = MeshProfileHelper.create_mesh_profile(material)
        item = profile.items[0]
        
        # Flipped profile should have mirrored X coordinates
        # Get min/max X to verify mirroring
        x_coords = [v.x for v in item.vertexes]
        
        # For a flipped C-section, the lip should be on the positive X side
        assert max(x_coords) > abs(min(x_coords))
    
    def test_create_c2_section_profile(self):
        """Test back-to-back C-section profile."""
        material = FrameMaterial.create_c(
            name="2C10010",
            web_nom=100,
            is_b2b=True,
            web=102,
            flange=102,  # Double flange width
            lip=12.5,
            thickness=1.0,
            web_hole_centers=40
        )
        
        profile = MeshProfileHelper.create_mesh_profile(material)
        
        # Should have two profile items (one for each C)
        assert len(profile.items) == 2
        
        # Should have punching maps for both sections
        assert len(profile.punching_map) == 4  # 2 holes per C-section
    
    def test_create_z_section_profile(self):
        """Test Z-section profile creation."""
        material = FrameMaterial.create_z(
            name="Z10010",
            web_nom=100,
            web=102,
            flange_f=53,
            flange_e=49,
            lip=12.5,
            thickness=1.0,
            web_hole_centers=40
        )
        
        profile = MeshProfileHelper.create_mesh_profile(material)
        
        # Should have one profile item
        assert len(profile.items) == 1
        
        # Z-section has complex shape
        item = profile.items[0]
        assert len(item.vertexes) >= 12  # Z-section has many vertices
        assert not item.is_hollow()
        
        # Should have punching maps
        assert len(profile.punching_map) > 0
    
    def test_create_th_section_profile(self):
        """Test TopHat section profile creation."""
        material = FrameMaterial.create_th(
            name="TH096100",
            web_nom=96,
            web=109,
            flange=96,
            thickness=1.0
        )
        
        profile = MeshProfileHelper.create_mesh_profile(material)
        
        # Should have one hollow profile item
        assert len(profile.items) == 1
        
        item = profile.items[0]
        assert item.is_hollow()
        assert len(item.vertexes) == 4  # Outer rectangle
        assert len(item.inner_vertexes) == 4  # Inner rectangle
        
        # TopHat doesn't have punching maps
        assert len(profile.punching_map) == 0
    
    def test_create_shs_section_profile(self):
        """Test Square Hollow Section profile creation."""
        material = FrameMaterial.create_shs(
            name="SHS07507525",
            width=75,
            height=75,
            thickness=2.5
        )
        
        profile = MeshProfileHelper.create_mesh_profile(material)
        
        # Should have one hollow profile item
        assert len(profile.items) == 1
        
        item = profile.items[0]
        assert item.is_hollow()
        assert len(item.vertexes) == 4  # Outer square
        assert len(item.inner_vertexes) == 4  # Inner square
        
        # Check dimensions
        outer_width = max(v.x for v in item.vertexes) - min(v.x for v in item.vertexes)
        inner_width = max(v.x for v in item.inner_vertexes) - min(v.x for v in item.inner_vertexes)
        
        assert outer_width == pytest.approx(75)
        assert inner_width == pytest.approx(70)  # 75 - 2*2.5
    
    def test_create_pad_section_simple(self):
        """Test simple PAD stile profile."""
        material = FrameMaterial.create_pad_stile(
            name="NRDJ064",
            web_nom=64,
            web=66,
            flange=25,
            thickness=1.6,
            rebate_width=0,
            rebate_height=0,
            rebate_tail=0
        )
        
        profile = MeshProfileHelper.create_mesh_profile(material)
        
        # Simple PAD is hollow rectangle
        assert len(profile.items) == 1
        item = profile.items[0]
        assert item.is_hollow()
    
    def test_create_pad_section_with_rebate(self):
        """Test PAD stile profile with rebate."""
        material = FrameMaterial.create_pad_stile(
            name="PADJ06427",
            web_nom=64,
            web=66,
            flange=57,
            thickness=1.6,
            rebate_width=27,
            rebate_height=27,
            rebate_tail=24
        )
        
        profile = MeshProfileHelper.create_mesh_profile(material)
        
        # PAD with rebate has complex shape
        assert len(profile.items) == 1
        item = profile.items[0]
        assert not item.is_hollow()  # Not a simple hollow shape
        assert len(item.vertexes) >= 12  # Complex profile with rebate
    
    def test_create_srdj_section_profile(self):
        """Test Side Roller Door Jamb profile."""
        material = FrameMaterial.create_side_roller_door_jamb(
            name="SRDTS64",
            web_nom=64,
            web=69.8,
            flange=102,
            tail=35,
            thickness=1.9
        )
        
        profile = MeshProfileHelper.create_mesh_profile(material)
        
        # Should have one profile item
        assert len(profile.items) == 1
        
        item = profile.items[0]
        assert len(item.vertexes) == 8  # SRDJ has specific shape
        assert not item.is_hollow()
        
        # Check tail cutout is present
        y_coords = [v.y for v in item.vertexes]
        y_range = max(y_coords) - min(y_coords)
        expected_range = material.web - material.srdj_tail
        
        # The profile height should be reduced by the tail
        assert y_range < material.web
    
    def test_unsupported_material_type(self):
        """Test error for unsupported material type."""
        material = FrameMaterial(
            name="Unknown",
            material_type=FrameMaterialType.UNKNOWN,
            width=100,
            height=100,
            thickness=1.0
        )
        
        with pytest.raises(ValueError, match="Unsupported material type"):
            MeshProfileHelper.create_mesh_profile(material)
    
    def test_profile_arc_parameter(self):
        """Test draw_profile_arcs parameter."""
        material = FrameMaterial.create_c(
            name="C10010",
            web_nom=100,
            is_b2b=False,
            web=102,
            flange=51,
            lip=12.5,
            thickness=1.0
        )
        
        # With arcs
        profile1 = MeshProfileHelper.create_mesh_profile(material, draw_profile_arcs=True)
        
        # Without arcs (might have fewer vertices for curves)
        profile2 = MeshProfileHelper.create_mesh_profile(material, draw_profile_arcs=False)
        
        # Both should be valid profiles
        assert len(profile1.items) == 1
        assert len(profile2.items) == 1
    
    def test_z_section_punching_maps(self):
        """Test Z-section specific punching maps."""
        material = FrameMaterial.create_z(
            name="Z10010",
            web_nom=100,
            web=102,
            flange_f=53,
            flange_e=49,
            lip=12.5,
            thickness=1.0,
            web_hole_centers=40
        )
        
        profile = MeshProfileHelper.create_mesh_profile(material)
        
        # Z-section has left/right web holes
        assert len(profile.punching_map) == 2
        
        # Check punching locations
        for pm in profile.punching_map:
            assert pm.where in [PunchingWhere.WEB_LEFT, PunchingWhere.WEB_RIGHT]
    
    def test_profile_symmetry(self):
        """Test profile symmetry for symmetric sections."""
        material = FrameMaterial.create_shs(
            name="SHS10010040",
            width=100,
            height=100,
            thickness=4.0
        )
        
        profile = MeshProfileHelper.create_mesh_profile(material)
        item = profile.items[0]
        
        # SHS should be symmetric about both axes
        x_coords = [v.x for v in item.vertexes]
        y_coords = [v.y for v in item.vertexes]
        
        # Check X symmetry
        assert abs(max(x_coords)) == pytest.approx(abs(min(x_coords)))
        
        # Check Y symmetry
        assert abs(max(y_coords)) == pytest.approx(abs(min(y_coords)))


if __name__ == "__main__":
    pytest.main([__file__, "-v"])