"""
Test suite for business logic helper functions.

Tests helper utilities and configuration constants.
"""

import pytest
import math
from src.business.helpers import CarportConfig, CarportHelpers
from src.business.building_input import BuildingInput
from src.business.engineering import EngData
from src.geometry.primitives import Vec3
from src.materials.base import FrameMaterial, ColorMaterial
from src.materials.helpers import FrameMaterialHelper


class TestCarportConfig:
    """Test CarportConfig constants."""
    
    def test_standard_dimensions(self):
        """Test standard dimension constants."""
        assert CarportConfig.SLAB_THICKNESS == 100.0
        assert CarportConfig.MIN_BAY_SIZE == 1500.0
        assert CarportConfig.MAX_BAY_SIZE == 6000.0
    
    def test_clearances(self):
        """Test clearance constants."""
        assert CarportConfig.RAFTER_CLEARANCE == 50.0
        assert CarportConfig.PURLIN_CLEARANCE == 25.0
    
    def test_spacings(self):
        """Test spacing constants."""
        assert CarportConfig.DEFAULT_PURLIN_SPACING == 1200.0
        assert CarportConfig.MAX_PURLIN_SPACING == 1500.0
    
    def test_dimension_limits(self):
        """Test minimum and maximum dimension limits."""
        # Minimums
        assert CarportConfig.MIN_SPAN == 2400.0
        assert CarportConfig.MIN_LENGTH == 2400.0
        assert CarportConfig.MIN_HEIGHT == 2100.0
        
        # Maximums
        assert CarportConfig.MAX_SPAN == 12000.0
        assert CarportConfig.MAX_LENGTH == 30000.0
        assert CarportConfig.MAX_HEIGHT == 6000.0
    
    def test_engineering_limits(self):
        """Test engineering limit constants."""
        assert CarportConfig.MAX_WIND_SPEED == 69
        assert CarportConfig.MIN_ROOF_PITCH == 1.0
        assert CarportConfig.MAX_ROOF_PITCH == 30.0


class TestCarportHelpers:
    """Test CarportHelpers functionality."""
    
    def test_calculate_bay_positions(self):
        """Test bay position calculation."""
        # 3 bays over 9m
        positions = CarportHelpers.calculate_bay_positions(9000, 3)
        assert len(positions) == 4  # 3 bays = 4 frames
        assert positions == [0, 3000, 6000, 9000]
        
        # Single bay
        positions = CarportHelpers.calculate_bay_positions(6000, 1)
        assert len(positions) == 2
        assert positions == [0, 6000]
        
        # 5 bays over 15m
        positions = CarportHelpers.calculate_bay_positions(15000, 5)
        assert len(positions) == 6
        assert positions == [0, 3000, 6000, 9000, 12000, 15000]
        
        # Edge case: 0 bays
        positions = CarportHelpers.calculate_bay_positions(6000, 0)
        assert len(positions) == 1
        assert positions == [0]
    
    def test_calculate_purlin_positions_flat(self):
        """Test purlin positions for flat/skillion roof."""
        # 5 rows on 6m span
        positions = CarportHelpers.calculate_purlin_positions(6000, 5, "Flat")
        assert len(positions) == 5
        # Should be evenly spaced
        assert positions[0] == pytest.approx(1000)
        assert positions[1] == pytest.approx(2000)
        assert positions[2] == pytest.approx(3000)
        assert positions[3] == pytest.approx(4000)
        assert positions[4] == pytest.approx(5000)
        
        # 3 rows on 4.5m span
        positions = CarportHelpers.calculate_purlin_positions(4500, 3, "Skillion")
        assert len(positions) == 3
        assert positions[0] == pytest.approx(1125)
        assert positions[1] == pytest.approx(2250)
        assert positions[2] == pytest.approx(3375)
    
    def test_calculate_purlin_positions_gable(self):
        """Test purlin positions for gable roof."""
        # Gable roof - purlins on each side
        positions = CarportHelpers.calculate_purlin_positions(6000, 3, "Gable")
        assert len(positions) == 3
        # Half span is 3000, so spacing is 750
        assert positions[0] == pytest.approx(750)
        assert positions[1] == pytest.approx(1500)
        assert positions[2] == pytest.approx(2250)
        
        # Larger span
        positions = CarportHelpers.calculate_purlin_positions(8000, 4, "Gable")
        assert len(positions) == 4
        # Half span is 4000, spacing is 800
        assert positions[0] == pytest.approx(800)
        assert positions[1] == pytest.approx(1600)
        assert positions[2] == pytest.approx(2400)
        assert positions[3] == pytest.approx(3200)
    
    def test_calculate_roof_slope(self):
        """Test roof slope calculation."""
        # 15 degree pitch on 6m span
        rise = CarportHelpers.calculate_roof_slope(6000, 15)
        expected = 6000 * math.tan(math.radians(15))
        assert rise == pytest.approx(expected)
        assert rise == pytest.approx(1607.7, rel=0.01)
        
        # 5 degree pitch on 3m span
        rise = CarportHelpers.calculate_roof_slope(3000, 5)
        expected = 3000 * math.tan(math.radians(5))
        assert rise == pytest.approx(expected)
        assert rise == pytest.approx(262.5, rel=0.01)
        
        # 30 degree pitch on 4m span
        rise = CarportHelpers.calculate_roof_slope(4000, 30)
        expected = 4000 * math.tan(math.radians(30))
        assert rise == pytest.approx(expected)
        assert rise == pytest.approx(2309.4, rel=0.01)
        
        # 0 degree pitch (flat)
        rise = CarportHelpers.calculate_roof_slope(6000, 0)
        assert rise == 0
    
    def test_get_color_material(self):
        """Test color material lookup."""
        # Standard colors
        color = CarportHelpers.get_color_material("WOODLAND_GREY")
        assert color is not None
        assert color.name == "WOODLAND_GREY"
        assert color.r == 138
        assert color.g == 135
        assert color.b == 133
        
        color = CarportHelpers.get_color_material("MONUMENT")
        assert color.name == "MONUMENT"
        assert color.r == 79
        assert color.g == 78
        assert color.b == 78
        
        color = CarportHelpers.get_color_material("CLASSIC_CREAM")
        assert color.name == "CLASSIC_CREAM"
        assert color.r == 233
        assert color.g == 224
        assert color.b == 199
        
        # Case insensitive with spaces
        color = CarportHelpers.get_color_material("woodland grey")
        assert color.name == "woodland grey"
        assert color.r == 138
        
        # Unknown color returns grey
        color = CarportHelpers.get_color_material("UNKNOWN_COLOR")
        assert color.name == "UNKNOWN_COLOR"
        assert color.r == 128
        assert color.g == 128
        assert color.b == 128
        
        # Empty string
        color = CarportHelpers.get_color_material("")
        assert color is None
        
        # None input
        color = CarportHelpers.get_color_material(None)
        assert color is None
    
    def test_validate_frame_spacing(self):
        """Test frame spacing validation."""
        # Valid spacing (3m bays)
        positions = [0, 3000, 6000, 9000]
        assert CarportHelpers.validate_frame_spacing(positions) is True
        
        # Valid spacing (2m bays)
        positions = [0, 2000, 4000, 6000]
        assert CarportHelpers.validate_frame_spacing(positions) is True
        
        # Invalid spacing (too close)
        positions = [0, 1000, 2000, 3000]
        assert CarportHelpers.validate_frame_spacing(positions) is False
        
        # Mixed spacing - one too close
        positions = [0, 3000, 4200, 6000]  # Middle spacing only 1200
        assert CarportHelpers.validate_frame_spacing(positions) is False
        
        # Exactly minimum spacing
        positions = [0, 1500, 3000, 4500]
        assert CarportHelpers.validate_frame_spacing(positions) is True
        
        # Single frame
        positions = [0]
        assert CarportHelpers.validate_frame_spacing(positions) is True
        
        # Empty list
        positions = []
        assert CarportHelpers.validate_frame_spacing(positions) is True
        
        # Custom minimum spacing
        positions = [0, 2000, 4000]
        assert CarportHelpers.validate_frame_spacing(positions, min_spacing=2500) is False
        assert CarportHelpers.validate_frame_spacing(positions, min_spacing=2000) is True
    
    def test_calculate_bracket_positions(self):
        """Test bracket position calculation."""
        column = FrameMaterialHelper.shs_section(100, 100, 3.0)
        rafter = FrameMaterialHelper.c_section(15024)
        
        # Haunch connection
        col_pos, raf_pos = CarportHelpers.calculate_bracket_positions(
            column, rafter, "haunch"
        )
        assert col_pos.x == 0
        assert col_pos.y == column.web - 50  # 100 - 50 = 50
        assert col_pos.z == 0
        assert raf_pos.x == rafter.flange + 50
        assert raf_pos.y == 0
        assert raf_pos.z == 0
        
        # Apex connection
        col_pos, raf_pos = CarportHelpers.calculate_bracket_positions(
            column, rafter, "apex"
        )
        assert col_pos == Vec3(0, 0, 0)  # Not used for apex
        assert raf_pos.x == 0
        assert raf_pos.y == rafter.web / 2
        assert raf_pos.z == 0
        
        # Unknown connection type
        col_pos, raf_pos = CarportHelpers.calculate_bracket_positions(
            column, rafter, "unknown"
        )
        assert col_pos == Vec3(0, 0, 0)
        assert raf_pos == Vec3(0, 0, 0)
    
    def test_calculate_punching_points(self):
        """Test punching point calculation."""
        material = FrameMaterialHelper.c_section(15024)
        
        # Single connection at center
        points = CarportHelpers.calculate_punching_points(material, 3000, 1)
        assert len(points) == 1
        assert points[0] == 1500
        
        # Multiple connections evenly spaced
        points = CarportHelpers.calculate_punching_points(material, 3000, 3)
        assert len(points) == 3
        assert points[0] == 750
        assert points[1] == 1500
        assert points[2] == 2250
        
        # Two connections
        points = CarportHelpers.calculate_punching_points(material, 4000, 2)
        assert len(points) == 2
        assert points[0] == pytest.approx(1333, abs=1)
        assert points[1] == pytest.approx(2667, abs=1)
        
        # No connections
        points = CarportHelpers.calculate_punching_points(material, 3000, 0)
        assert len(points) == 0
        
        # Negative connections
        points = CarportHelpers.calculate_punching_points(material, 3000, -1)
        assert len(points) == 0
    
    def test_get_downpipe_positions(self):
        """Test downpipe position calculation."""
        # Short building - single downpipe at start
        positions = CarportHelpers.get_downpipe_positions(6000)
        assert len(positions) == 1
        assert positions[0] == 0
        
        # Medium building - still single downpipe
        positions = CarportHelpers.get_downpipe_positions(10000)
        assert len(positions) == 1
        assert positions[0] == 0
        
        # Long building - two downpipes at ends
        positions = CarportHelpers.get_downpipe_positions(15000)
        assert len(positions) == 2
        assert positions[0] == 0
        assert positions[1] == 15000
        
        # Very long building - multiple downpipes
        positions = CarportHelpers.get_downpipe_positions(30000)
        assert len(positions) == 3
        assert positions[0] == 0
        assert positions[1] == 15000
        assert positions[2] == 30000
        
        # Custom spacing
        positions = CarportHelpers.get_downpipe_positions(20000, downpipe_spacing=8000)
        assert len(positions) == 3
        assert positions[0] == 0
        assert positions[1] == 10000
        assert positions[2] == 20000
        
        # Exactly at spacing threshold
        positions = CarportHelpers.get_downpipe_positions(12000)
        assert len(positions) == 1
        
        positions = CarportHelpers.get_downpipe_positions(12001)
        assert len(positions) == 2
    
    def test_validate_engineering_override(self):
        """Test engineering override validation."""
        # No engineering data - always valid
        building_input = BuildingInput()
        assert CarportHelpers.validate_engineering_override(building_input, None) is True
        
        # No frame override - always valid
        building_input.frame_override = False
        eng_data = EngData(
            ENG_RAFTER="C15024",
            ENG_PURLINSIZE="TH064100",
            ENG_PURLINROW=5,
            ENG_COLUMN="SHS10010030",
            ENG_FOOTINGTYPE="bored",
            ENG_FOOTINGDIA="450",
            ENG_FOOTINGDEPTH="600"
        )
        assert CarportHelpers.validate_engineering_override(building_input, eng_data) is True
        
        # With frame override (currently always returns True - TODO implementation)
        building_input.frame_override = True
        building_input.rafter_override = "C20030"
        assert CarportHelpers.validate_engineering_override(building_input, eng_data) is True
    
    def test_interpolate_roof_height_flat(self):
        """Test roof height interpolation for flat roof."""
        # Flat roof with 3 degree pitch
        base_height = 2400
        span = 6000
        pitch = 3
        
        # At start
        height = CarportHelpers.interpolate_roof_height(0, span, base_height, pitch, "Flat")
        assert height == base_height
        
        # At middle
        height = CarportHelpers.interpolate_roof_height(3000, span, base_height, pitch, "Flat")
        expected = base_height + 3000 * math.tan(math.radians(pitch))
        assert height == pytest.approx(expected)
        
        # At end
        height = CarportHelpers.interpolate_roof_height(6000, span, base_height, pitch, "Flat")
        expected = base_height + 6000 * math.tan(math.radians(pitch))
        assert height == pytest.approx(expected)
    
    def test_interpolate_roof_height_gable(self):
        """Test roof height interpolation for gable roof."""
        # Gable roof with 15 degree pitch
        base_height = 2400
        span = 6000
        pitch = 15
        
        # At start (eave)
        height = CarportHelpers.interpolate_roof_height(0, span, base_height, pitch, "Gable")
        assert height == base_height
        
        # At ridge (center)
        height = CarportHelpers.interpolate_roof_height(3000, span, base_height, pitch, "Gable")
        expected = base_height + 3000 * math.tan(math.radians(pitch))
        assert height == pytest.approx(expected)
        
        # Past ridge (descending)
        height = CarportHelpers.interpolate_roof_height(4000, span, base_height, pitch, "Gable")
        expected = base_height + (6000 - 4000) * math.tan(math.radians(pitch))
        assert height == pytest.approx(expected)
        
        # At far eave
        height = CarportHelpers.interpolate_roof_height(6000, span, base_height, pitch, "Gable")
        assert height == base_height
    
    def test_interpolate_roof_height_skillion(self):
        """Test roof height interpolation for skillion roof."""
        # Skillion is same as flat
        base_height = 2700
        span = 5000
        pitch = 5
        
        height = CarportHelpers.interpolate_roof_height(2500, span, base_height, pitch, "Skillion")
        expected = base_height + 2500 * math.tan(math.radians(pitch))
        assert height == pytest.approx(expected)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])