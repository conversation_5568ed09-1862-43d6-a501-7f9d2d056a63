"""
Test suite for structure builder module.

Tests the builder pattern implementation for carport construction.
"""

import pytest
import math
from unittest.mock import Mock, patch
from src.business.structure_builder import (
    CarportProduct, StructureBuilderBase, CarportBuilder
)
from src.business.building_input import BuildingInput, BuildingType, CarportRoofType
from src.business.engineering import EngData
from src.business.helpers import CarportConfig
from src.geometry.primitives import Vec3, Line1
from src.materials.base import (
    FrameMaterial, FrameMaterialType, FootingMaterialType,
    PunchingWhere
)
from src.bim.shed_bim import ShedBim, ShedBimPartMain


class TestCarportProduct:
    """Test CarportProduct functionality."""
    
    def test_creation_defaults(self):
        """Test product creation with defaults."""
        product = CarportProduct()
        assert product.job_number is not None
        assert len(product.job_number) > 0  # UUID string
        assert product.main is None
        assert product.leanto_left is None
        assert product.leanto_right is None
        assert product.outline_frames == []
    
    def test_creation_with_data(self):
        """Test product creation with data."""
        main = ShedBimPartMain()
        product = CarportProduct(
            job_number="TEST-123",
            main=main
        )
        assert product.job_number == "TEST-123"
        assert product.main is main
    
    def test_to_shed_bim_conversion(self):
        """Test conversion to ShedBim."""
        main = ShedBimPartMain()
        main.roof_type = "FLAT"
        
        product = CarportProduct(main=main)
        product.outline_frames = [[Vec3(0, 0, 0), Vec3(6000, 0, 0)]]
        
        shed_bim = product.to_shed_bim()
        assert isinstance(shed_bim, ShedBim)
        assert shed_bim.main is main
        assert shed_bim.main.roof_type == "FLAT"
        assert len(shed_bim.outline_frames) == 1


class TestStructureBuilderBase:
    """Test StructureBuilderBase abstract class."""
    
    @pytest.fixture
    def building_input(self):
        """Create test building input."""
        return BuildingInput(
            building_type=BuildingType.CARPORT,
            roof_type=CarportRoofType.FLAT,
            span=6000,
            length=9000,
            height=2700,
            bays=3,
            pitch=3,
            cladding_type="corrugated"
        )
    
    @pytest.fixture
    def eng_data(self):
        """Create test engineering data."""
        return EngData(
            ENG_RAFTER="C20030",
            ENG_PURLINSIZE="C15024",
            ENG_PURLINROW=7,
            ENG_COLUMN="SHS15015040",
            ENG_APEXBRACE="C15024",
            ENG_FOOTINGTYPE="bored",
            ENG_FOOTINGDIA="600",
            ENG_FOOTINGDEPTH="900"
        )
    
    def test_cannot_instantiate_abstract_class(self):
        """Test that abstract class cannot be instantiated."""
        with pytest.raises(TypeError):
            StructureBuilderBase()
    
    def test_initialization_with_building_input(self, building_input):
        """Test initialization with building input."""
        # Create concrete implementation for testing
        class TestBuilder(StructureBuilderBase):
            def find_slab_structure(self, main): pass
            def find_brackets_structure(self, main): pass
            def find_columns_and_footings_structure(self, main): pass
            def find_rafter_structure(self, main): pass
            def find_roof_claddings_structure(self, main): pass
            def find_purlins_structure(self, main): pass
            def find_eave_purlin_structure(self, main): pass
            def find_attached_awning_wall_structure(self, leanto): pass
            def find_flashings_structure(self, main): pass
            def find_gutters_and_downpipes_structure(self, main): pass
            def find_braces_structure(self, main): pass
            def find_punchings_structure(self, main): pass
            def find_fasteners_structure(self, main): pass
            def find_wall_claddings_parent(self, main): pass
        
        builder = TestBuilder(building_input=building_input)
        
        assert builder.building_type == BuildingType.CARPORT
        assert builder.building_input is building_input
        assert builder.shed_input is None
        assert builder.bay_size == 3000  # 9000 / 3
        assert builder.tan_ratio == pytest.approx(math.tan(math.radians(3)))
        assert builder.cos_ratio == pytest.approx(math.cos(math.radians(3)))
        assert builder.sin_ratio == pytest.approx(math.sin(math.radians(3)))
        assert builder.is_corrugated is True
        assert builder.height == 2700
        assert builder.span == 6000
        assert builder.has_eng_data is False
    
    def test_initialization_without_input(self):
        """Test initialization fails without input."""
        class TestBuilder(StructureBuilderBase):
            pass
        
        with pytest.raises(ValueError, match="Either building_input or shed_input must be provided"):
            TestBuilder()
    
    def test_default_materials_initialization(self, building_input):
        """Test default materials are initialized."""
        class TestBuilder(StructureBuilderBase):
            def find_slab_structure(self, main): pass
            def find_brackets_structure(self, main): pass
            def find_columns_and_footings_structure(self, main): pass
            def find_rafter_structure(self, main): pass
            def find_roof_claddings_structure(self, main): pass
            def find_purlins_structure(self, main): pass
            def find_eave_purlin_structure(self, main): pass
            def find_attached_awning_wall_structure(self, leanto): pass
            def find_flashings_structure(self, main): pass
            def find_gutters_and_downpipes_structure(self, main): pass
            def find_braces_structure(self, main): pass
            def find_punchings_structure(self, main): pass
            def find_fasteners_structure(self, main): pass
            def find_wall_claddings_parent(self, main): pass
        
        builder = TestBuilder(building_input=building_input)
        
        # Frame materials
        assert builder.purlin_rows == 3
        assert builder.rafter.name == "C15015"
        assert builder.column.name == "SHS07507525"
        assert builder.purlin.name == "C15015"  # Not TopHat for flat roof
        assert builder.eave_purlin.name == "C15015"
        assert builder.apex_brace.name == "C10010"
        assert builder.knee_brace.name == "C10010"
        
        # Footing
        assert builder.footing.footing_type == FootingMaterialType.BORED
        assert builder.footing.diameter == 300
        assert builder.footing.depth == 300
        
        # Cladding
        assert builder.corrugated_roof.name == "CORRUGATED"
        assert builder.monoclad_roof.name == "MONOCLAD"
        assert builder.roof_material is builder.corrugated_roof
        
        # Downpipes
        assert builder.rect_100x50_downpipe.name == "RECT_100x50"
        assert builder.rect_100x75_downpipe.name == "RECT_100x75"
    
    def test_engineering_data_application(self, building_input, eng_data):
        """Test engineering data is applied correctly."""
        class TestBuilder(StructureBuilderBase):
            def find_slab_structure(self, main): pass
            def find_brackets_structure(self, main): pass
            def find_columns_and_footings_structure(self, main): pass
            def find_rafter_structure(self, main): pass
            def find_roof_claddings_structure(self, main): pass
            def find_purlins_structure(self, main): pass
            def find_eave_purlin_structure(self, main): pass
            def find_attached_awning_wall_structure(self, leanto): pass
            def find_flashings_structure(self, main): pass
            def find_gutters_and_downpipes_structure(self, main): pass
            def find_braces_structure(self, main): pass
            def find_punchings_structure(self, main): pass
            def find_fasteners_structure(self, main): pass
            def find_wall_claddings_parent(self, main): pass
        
        builder = TestBuilder(building_input=building_input, eng_data=eng_data)
        
        assert builder.has_eng_data is True
        assert builder.rafter.name == "C20030"
        assert builder.purlin.name == "C15024"
        assert builder.purlin_rows == 7
        assert builder.column.name == "SHS15015040"
        assert builder.apex_brace.name == "C15024"
        assert builder.knee_brace.name == "C15024"
        
        # Footing from engineering
        assert builder.footing.footing_type == FootingMaterialType.BORED
        assert builder.footing.diameter == 600
        assert builder.footing.depth == 900
    
    def test_frame_overrides(self, building_input):
        """Test frame material overrides."""
        class TestBuilder(StructureBuilderBase):
            def find_slab_structure(self, main): pass
            def find_brackets_structure(self, main): pass
            def find_columns_and_footings_structure(self, main): pass
            def find_rafter_structure(self, main): pass
            def find_roof_claddings_structure(self, main): pass
            def find_purlins_structure(self, main): pass
            def find_eave_purlin_structure(self, main): pass
            def find_attached_awning_wall_structure(self, leanto): pass
            def find_flashings_structure(self, main): pass
            def find_gutters_and_downpipes_structure(self, main): pass
            def find_braces_structure(self, main): pass
            def find_punchings_structure(self, main): pass
            def find_fasteners_structure(self, main): pass
            def find_wall_claddings_parent(self, main): pass
        
        building_input.frame_override = True
        building_input.post_override = "SHS10010030"
        building_input.rafter_override = "C25030"
        building_input.purlin_override = "C20024"
        building_input.eave_purlin_override = "C15024"
        
        builder = TestBuilder(building_input=building_input)
        
        assert builder.column.name == "SHS10010030"
        assert builder.rafter.name == "C25030"
        assert builder.purlin.name == "C20024"
        assert builder.eave_purlin.name == "C15024"
    
    def test_purlin_row_calculation_with_extra_purlins(self, building_input):
        """Test purlin row calculation with extra purlins."""
        class TestBuilder(StructureBuilderBase):
            def find_slab_structure(self, main): pass
            def find_brackets_structure(self, main): pass
            def find_columns_and_footings_structure(self, main): pass
            def find_rafter_structure(self, main): pass
            def find_roof_claddings_structure(self, main): pass
            def find_purlins_structure(self, main): pass
            def find_eave_purlin_structure(self, main): pass
            def find_attached_awning_wall_structure(self, leanto): pass
            def find_flashings_structure(self, main): pass
            def find_gutters_and_downpipes_structure(self, main): pass
            def find_braces_structure(self, main): pass
            def find_punchings_structure(self, main): pass
            def find_fasteners_structure(self, main): pass
            def find_wall_claddings_parent(self, main): pass
        
        building_input.frame_override = True
        building_input.max_purlin_space = 0
        building_input.extra_purlins = 1
        
        # For flat roof
        builder = TestBuilder(building_input=building_input)
        assert builder.purlin_rows == 6
        
        # For gable roof
        building_input.roof_type = CarportRoofType.GABLE
        builder = TestBuilder(building_input=building_input)
        assert builder.purlin_rows == 4
    
    def test_purlin_row_calculation_with_max_spacing(self, building_input):
        """Test purlin row calculation with max spacing."""
        class TestBuilder(StructureBuilderBase):
            def find_slab_structure(self, main): pass
            def find_brackets_structure(self, main): pass
            def find_columns_and_footings_structure(self, main): pass
            def find_rafter_structure(self, main): pass
            def find_roof_claddings_structure(self, main): pass
            def find_purlins_structure(self, main): pass
            def find_eave_purlin_structure(self, main): pass
            def find_attached_awning_wall_structure(self, leanto): pass
            def find_flashings_structure(self, main): pass
            def find_gutters_and_downpipes_structure(self, main): pass
            def find_braces_structure(self, main): pass
            def find_punchings_structure(self, main): pass
            def find_fasteners_structure(self, main): pass
            def find_wall_claddings_parent(self, main): pass
        
        building_input.frame_override = True
        building_input.max_purlin_space = 1200  # 6000 / 1200 = 5
        
        builder = TestBuilder(building_input=building_input)
        assert builder.purlin_rows == 5
    
    def test_cache_initialization(self, building_input):
        """Test cache dictionaries are initialized."""
        class TestBuilder(StructureBuilderBase):
            def find_slab_structure(self, main): pass
            def find_brackets_structure(self, main): pass
            def find_columns_and_footings_structure(self, main): pass
            def find_rafter_structure(self, main): pass
            def find_roof_claddings_structure(self, main): pass
            def find_purlins_structure(self, main): pass
            def find_eave_purlin_structure(self, main): pass
            def find_attached_awning_wall_structure(self, leanto): pass
            def find_flashings_structure(self, main): pass
            def find_gutters_and_downpipes_structure(self, main): pass
            def find_braces_structure(self, main): pass
            def find_punchings_structure(self, main): pass
            def find_fasteners_structure(self, main): pass
            def find_wall_claddings_parent(self, main): pass
        
        builder = TestBuilder(building_input=building_input)
        
        assert isinstance(builder.cache_apex_punching_points, dict)
        assert isinstance(builder.cache_haunch_rafter_punching_points, dict)
        assert isinstance(builder.cache_haunch_column_punching_points, dict)
        assert len(builder.cache_apex_punching_points) == 0


class TestCarportBuilder:
    """Test CarportBuilder implementation."""
    
    @pytest.fixture
    def building_input(self):
        """Create test building input."""
        return BuildingInput(
            building_type=BuildingType.CARPORT,
            name="Test Carport",
            roof_type=CarportRoofType.FLAT,
            span=6000,
            length=6000,
            height=2400,
            bays=2,
            pitch=3,
            slab=True,
            overhang=300,
            cladding_type="corrugated",
            roof_color="WOODLAND_GREY",
            post_color="MONUMENT"
        )
    
    @pytest.fixture
    def eng_data(self):
        """Create test engineering data."""
        return EngData(
            ENG_RAFTER="C15024",
            ENG_PURLINSIZE="C15015",
            ENG_PURLINROW=5,
            ENG_COLUMN="SHS10010030",
            ENG_FOOTINGTYPE="bored",
            ENG_FOOTINGDIA="450",
            ENG_FOOTINGDEPTH="600"
        )
    
    def test_initialization(self, building_input):
        """Test carport builder initialization."""
        builder = CarportBuilder(building_input)
        
        assert isinstance(builder.bim, CarportProduct)
        assert builder.building_input is building_input
        assert builder.building_type == BuildingType.CARPORT
        
        # Carport-specific defaults
        assert builder.purlin_rows == 5  # Flat roof default
        assert builder.purlin.name == "C15012"
    
    def test_initialization_gable_defaults(self, building_input):
        """Test carport builder initialization with gable roof."""
        building_input.roof_type = CarportRoofType.GABLE
        builder = CarportBuilder(building_input)
        
        assert builder.purlin_rows == 3  # Gable roof default
        assert builder.purlin.name == "TH064100"
    
    def test_static_factory_method(self, building_input):
        """Test static create_carport method."""
        product = CarportBuilder.create_carport(building_input)
        
        assert isinstance(product, CarportProduct)
        assert product.main is not None
        assert product.job_number is not None
    
    def test_bim_initialization(self, building_input):
        """Test BIM structure initialization."""
        builder = CarportBuilder(building_input)
        builder._initialize_bim()
        
        assert builder.bim.main is not None
        assert builder.bim.main.side_left is not None
        assert builder.bim.main.side_right is not None
        assert builder.bim.main.roof_type == "Flat"
        assert builder.bim.main.roof_left is not None
        assert builder.bim.main.roof_right is None  # Not gable
        
        # Check dimensions
        assert builder.bim.main.wall_span_extents.start == 0
        assert builder.bim.main.wall_span_extents.end == 6000
        assert builder.bim.main.wall_length_extents.start == 0
        assert builder.bim.main.wall_length_extents.end == 6000
        assert builder.bim.main.wall_height == 2400
    
    def test_slab_creation(self, building_input):
        """Test slab structure creation."""
        builder = CarportBuilder(building_input)
        builder._initialize_bim()
        builder.find_slab_structure(builder.bim.main)
        
        assert builder.bim.main.slab is not None
        assert builder.bim.main.slab.middle is not None
        
        slab = builder.bim.main.slab.middle
        assert slab.thickness == CarportConfig.SLAB_THICKNESS
        assert slab.bottom_left.x == 300  # overhang
        assert slab.bottom_right.x == 6300  # overhang + span
        assert slab.top_right.z == 6000  # length
    
    def test_slab_not_created_when_disabled(self, building_input):
        """Test slab is not created when disabled."""
        building_input.slab = False
        builder = CarportBuilder(building_input)
        builder._initialize_bim()
        builder.find_slab_structure(builder.bim.main)
        
        assert builder.bim.main.slab is None
    
    def test_column_creation_flat_roof(self, building_input):
        """Test column creation for flat roof."""
        builder = CarportBuilder(building_input)
        builder._initialize_bim()
        builder.find_columns_and_footings_structure(builder.bim.main)
        
        # Should have 3 frames for 2 bays
        assert len(builder.bim.main.side_left.columns) == 3
        assert len(builder.bim.main.side_right.columns) == 3
        
        # Check first left column
        left_col = builder.bim.main.side_left.columns[0]
        assert left_col.column is not None
        assert left_col.footing is not None
        assert left_col.column.tag == "COL_MAIN_L_1"
        assert left_col.footing.tag == "FOOT_MAIN_L_1"
        
        # Check column positions
        assert left_col.column.start_pos.z == 0  # Ground level
        assert left_col.column.end_pos.z > building_input.height  # Above eave
        
        # Check footing
        assert left_col.footing.footing is builder.footing
        assert left_col.footing.pos == left_col.column.start_pos
    
    def test_column_creation_gable_roof(self, building_input):
        """Test column creation for gable roof."""
        building_input.roof_type = CarportRoofType.GABLE
        builder = CarportBuilder(building_input)
        builder._initialize_bim()
        builder.find_columns_and_footings_structure(builder.bim.main)
        
        # Columns should have same height for gable
        left_col = builder.bim.main.side_left.columns[0]
        right_col = builder.bim.main.side_right.columns[0]
        assert left_col.column.end_pos.z == right_col.column.end_pos.z
    
    def test_rafter_creation_flat_roof(self, building_input):
        """Test rafter creation for flat roof."""
        builder = CarportBuilder(building_input)
        builder._initialize_bim()
        builder.find_columns_and_footings_structure(builder.bim.main)
        builder.find_rafter_structure(builder.bim.main)
        
        # Should have one rafter per frame
        assert len(builder.bim.main.rafters) == 3
        
        # Check first rafter
        rafter = builder.bim.main.rafters[0]
        assert rafter.tag == "RAFTER_1"
        assert rafter.material is builder.rafter
        
        # Rafter should span from left to right column
        left_col = builder.bim.main.side_left.columns[0]
        right_col = builder.bim.main.side_right.columns[0]
        assert rafter.start_pos == left_col.column.end_pos
        assert rafter.end_pos == right_col.column.end_pos
    
    def test_rafter_creation_gable_roof(self, building_input):
        """Test rafter creation for gable roof."""
        building_input.roof_type = CarportRoofType.GABLE
        building_input.pitch = 15
        builder = CarportBuilder(building_input)
        builder._initialize_bim()
        builder.find_columns_and_footings_structure(builder.bim.main)
        builder.find_rafter_structure(builder.bim.main)
        
        # Should have two rafters per frame
        assert len(builder.bim.main.rafters) == 6  # 3 frames x 2 rafters
        
        # Check rafters meet at ridge
        left_rafter = builder.bim.main.rafters[0]
        right_rafter = builder.bim.main.rafters[1]
        assert left_rafter.tag == "RAFTER_L_1"
        assert right_rafter.tag == "RAFTER_R_1"
        assert left_rafter.end_pos == right_rafter.end_pos  # Meet at ridge
    
    def test_roof_cladding_creation(self, building_input):
        """Test roof cladding creation."""
        builder = CarportBuilder(building_input)
        builder._initialize_bim()
        builder.find_roof_claddings_structure(builder.bim.main)
        
        assert builder.bim.main.roof_left.wall is not None
        assert builder.bim.main.roof_left.wall.cladding is not None
        
        cladding = builder.bim.main.roof_left.wall.cladding
        assert cladding.material is builder.roof_material
        assert len(cladding.segments) == 2  # One per bay
        
        # Check first segment
        segment = cladding.segments[0]
        assert segment.bottom_left.x == building_input.overhang
        assert segment.bottom_left.y == 0
        assert segment.bottom_right.y == builder.bay_size
    
    def test_purlin_creation(self, building_input):
        """Test purlin creation."""
        builder = CarportBuilder(building_input)
        builder._initialize_bim()
        builder.find_purlins_structure(builder.bim.main)
        
        assert builder.bim.main.roof_left.purlin_bays is not None
        assert len(builder.bim.main.roof_left.purlin_bays) == builder.purlin_rows
        
        # Check first purlin
        first_bay = builder.bim.main.roof_left.purlin_bays[0]
        assert len(first_bay.purlins) > 0
        first_purlin = first_bay.purlins[0].purlin
        assert first_purlin.material is builder.purlin
        assert first_purlin.tag == "PURLIN_1"
        
        # Purlin should run full length
        purlin_length = first_purlin.end_pos.y - first_purlin.start_pos.y
        assert purlin_length == building_input.length
    
    def test_eave_purlin_creation_gable_only(self, building_input):
        """Test eave purlins are created only for gable roofs."""
        # Flat roof - no eave purlins
        builder = CarportBuilder(building_input)
        builder._initialize_bim()
        builder.find_eave_purlin_structure(builder.bim.main)
        assert not hasattr(builder.bim.main, 'eave_purlins')
        
        # Gable roof - has eave purlins
        building_input.roof_type = CarportRoofType.GABLE
        builder = CarportBuilder(building_input)
        builder._initialize_bim()
        builder.find_eave_purlin_structure(builder.bim.main)
        assert hasattr(builder.bim.main, 'eave_purlins')
        assert len(builder.bim.main.eave_purlins) == 2  # Left and right
    
    def test_flashing_creation(self, building_input):
        """Test flashing creation."""
        builder = CarportBuilder(building_input)
        builder._initialize_bim()
        builder.find_flashings_structure(builder.bim.main)
        
        assert hasattr(builder.bim.main, 'flashings')
        assert len(builder.bim.main.flashings) >= 2  # Front and back
        
        # Check tags
        tags = [f.tag for f in builder.bim.main.flashings]
        assert "FLASHING_FRONT" in tags
        assert "FLASHING_BACK" in tags
    
    def test_downpipe_creation(self, building_input):
        """Test downpipe creation."""
        building_input.downpipe_size = "large"
        builder = CarportBuilder(building_input)
        builder._initialize_bim()
        builder.find_gutters_and_downpipes_structure(builder.bim.main)
        
        assert hasattr(builder.bim.main, 'downpipes')
        assert len(builder.bim.main.downpipes) >= 1
        
        # Check downpipe properties
        dp = builder.bim.main.downpipes[0]
        assert dp.material.name == "RECT_100x75"  # Large size
        assert dp.length == building_input.height
        assert dp.tag == "DOWNPIPE_1"
    
    def test_punching_creation(self, building_input):
        """Test punching hole creation."""
        builder = CarportBuilder(building_input)
        builder._initialize_bim()
        builder.find_columns_and_footings_structure(builder.bim.main)
        builder.find_punchings_structure(builder.bim.main)
        
        # Check punchings added to columns
        for column in builder.bim.main.side_left.columns:
            assert hasattr(column.column, 'punchings')
            assert len(column.column.punchings) > 0
            
            # Check punching properties
            punching = column.column.punchings[0]
            assert punching.where == PunchingWhere.WEB
            assert punching.position == column.column.end_pos.z - 100
    
    def test_engineering_data_application(self, building_input, eng_data):
        """Test builder with engineering data."""
        product = CarportBuilder.create_carport(building_input, eng_data)
        
        # Check engineering specs applied
        col = product.main.side_left.columns[0]
        assert col.column.material.name == eng_data.ENG_COLUMN
        
        # Check footing from engineering
        footing = col.footing.footing
        assert footing.footing_type == FootingMaterialType.BORED
        assert footing.diameter == 450
        assert footing.depth == 600
    
    def test_complete_carport_generation(self, building_input):
        """Test complete carport generation pipeline."""
        product = CarportBuilder.create_carport(building_input)
        
        # Verify all major components exist
        assert product.main is not None
        assert product.main.slab is not None
        assert len(product.main.side_left.columns) > 0
        assert len(product.main.side_right.columns) > 0
        assert hasattr(product.main, 'rafters') and len(product.main.rafters) > 0
        assert product.main.roof_left is not None
        assert product.main.roof_left.wall is not None
        assert product.main.roof_left.wall.cladding is not None
        assert len(product.main.roof_left.purlin_bays) > 0
        assert hasattr(product.main, 'flashings')
        assert hasattr(product.main, 'downpipes')
        
        # Verify it can convert to ShedBim
        shed_bim = product.to_shed_bim()
        assert isinstance(shed_bim, ShedBim)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])