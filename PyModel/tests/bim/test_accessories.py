"""
Test suite for BIM accessory components.

Tests flashing, downpipe, strap, and roller door components.
"""

import pytest
from src.bim.accessories import (
    ShedBimFlashing, ShedBimFlashingCut, ShedBimDownpipe,
    ShedBimStrapBrace, ShedBimStrapLine, ShedBimRollerDoorCylinder
)
from src.geometry.primitives import Vec3, Line3, Mat4, Box3
from src.materials.base import (
    FlashingMaterial, DownpipeMaterial, StrapMaterial, ColorMaterial
)


class TestShedBimFlashing:
    """Test ShedBimFlashing functionality."""
    
    def test_creation_defaults(self):
        """Test flashing creation with defaults."""
        flashing = ShedBimFlashing()
        assert flashing.material is None
        assert flashing.color is None
        assert flashing.position is None
        assert flashing.rotation == 0.0
        assert flashing.cap_start == False
        assert flashing.cap_end == False
        assert flashing.ends == []
        assert flashing.stiffeners == []
        assert flashing.plumb_cuts == []
        assert flashing.presentation_offset is None
    
    def test_creation_with_data(self):
        """Test flashing creation with data."""
        material = FlashingMaterial(
            name="Ridge Capping",
            description="Standard ridge",
            thickness=0.55
        )
        color = ColorMaterial(name="Monument", finish="CB", r=50, g=50, b=50)
        position = Line3(Vec3(0, 0, 0), Vec3(10, 0, 0))
        
        flashing = ShedBimFlashing(
            material=material,
            color=color,
            position=position,
            rotation=45.0,
            cap_start=True,
            cap_end=True
        )
        
        assert flashing.material == material
        assert flashing.color == color
        assert flashing.position == position
        assert flashing.rotation == 45.0
        assert flashing.cap_start == True
        assert flashing.cap_end == True
    
    def test_get_presentation_position_no_position(self):
        """Test presentation position when position is None."""
        flashing = ShedBimFlashing()
        assert flashing.get_presentation_position() is None
    
    def test_get_presentation_position_no_offset(self):
        """Test presentation position without offset."""
        position = Line3(Vec3(1, 2, 3), Vec3(4, 5, 6))
        flashing = ShedBimFlashing(position=position)
        
        result = flashing.get_presentation_position()
        assert result == position
    
    def test_get_presentation_position_with_offset(self):
        """Test presentation position with offset applied."""
        position = Line3(Vec3(10, 20, 30), Vec3(40, 50, 60))
        offset = Line3(Vec3(1, 2, 3), Vec3(4, 5, 6))
        
        flashing = ShedBimFlashing(
            position=position,
            presentation_offset=offset
        )
        
        result = flashing.get_presentation_position()
        assert result.start == Vec3(11, 22, 33)  # 10+1, 20+2, 30+3
        assert result.end == Vec3(44, 55, 66)    # 40+4, 50+5, 60+6
    
    def test_clone_shallow(self):
        """Test shallow cloning of flashing."""
        material = FlashingMaterial(name="Test", thickness=0.5)
        cuts = [ShedBimFlashingCut()]
        
        flashing = ShedBimFlashing(
            material=material,
            rotation=30.0,
            plumb_cuts=cuts
        )
        
        clone = flashing.clone_shallow()
        
        # Check values are copied
        assert clone.material == material
        assert clone.rotation == 30.0
        
        # Check it's a shallow copy (lists are same object)
        assert clone.plumb_cuts is flashing.plumb_cuts
        
        # Modify original list affects clone
        flashing.plumb_cuts.append(ShedBimFlashingCut())
        assert len(clone.plumb_cuts) == 2


class TestShedBimFlashingCut:
    """Test ShedBimFlashingCut functionality."""
    
    def test_creation_defaults(self):
        """Test cut creation with defaults."""
        cut = ShedBimFlashingCut()
        assert cut.transform is None
        assert cut.box is None
    
    def test_creation_with_data(self):
        """Test cut creation with data."""
        transform = Mat4.identity()
        box = Box3(Vec3(0, 0, 0), Vec3(10, 10, 10))
        
        cut = ShedBimFlashingCut(transform=transform, box=box)
        assert cut.transform == transform
        assert cut.box == box
    
    def test_string_representation(self):
        """Test string representation."""
        cut = ShedBimFlashingCut()
        assert str(cut) == "Transform:None, Box:None"
        
        transform = Mat4.identity()
        box = Box3(Vec3(0, 0, 0), Vec3(1, 1, 1))
        cut2 = ShedBimFlashingCut(transform=transform, box=box)
        
        result = str(cut2)
        assert "Transform:" in result
        assert "Box:" in result


class TestShedBimDownpipe:
    """Test ShedBimDownpipe functionality."""
    
    def test_creation_defaults(self):
        """Test downpipe creation with defaults."""
        downpipe = ShedBimDownpipe()
        assert downpipe.material is None
        assert downpipe.color is None
        assert downpipe.position is None
        assert downpipe.points == []
        assert downpipe.tag == ""
        assert downpipe.strap == []
        assert downpipe.nozzle is None
    
    def test_creation_with_data(self):
        """Test downpipe creation with data."""
        material = DownpipeMaterial(
            name="PVC Downpipe",
            size=100,
            diameter=100,
            mesh_name="downpipe_100"
        )
        color = ColorMaterial(name="White", finish="", r=255, g=255, b=255)
        position = Vec3(1000, 2000, 3000)
        points = [Vec3(0, 0, 0), Vec3(0, 0, -1000), Vec3(100, 0, -1000)]
        
        downpipe = ShedBimDownpipe(
            material=material,
            color=color,
            position=position,
            points=points,
            tag="DP-01"
        )
        
        assert downpipe.material == material
        assert downpipe.color == color
        assert downpipe.position == position
        assert len(downpipe.points) == 3
        assert downpipe.tag == "DP-01"
    
    def test_points_manipulation(self):
        """Test manipulating downpipe points."""
        downpipe = ShedBimDownpipe()
        
        # Add points
        downpipe.points.append(Vec3(0, 0, 0))
        downpipe.points.append(Vec3(0, 0, -500))
        downpipe.points.append(Vec3(200, 0, -500))
        
        assert len(downpipe.points) == 3
        assert downpipe.points[1].z == -500
        assert downpipe.points[2].x == 200


class TestShedBimStrapBrace:
    """Test ShedBimStrapBrace functionality."""
    
    def test_creation_defaults(self):
        """Test strap brace creation with defaults."""
        brace = ShedBimStrapBrace()
        assert brace.lines == []
        assert brace.material is None
        assert brace.tag == ""
    
    def test_creation_with_data(self):
        """Test strap brace creation with data."""
        material = StrapMaterial(
            name="Strap Brace",
            thickness=2.0,
            width=30.0
        )
        
        line1 = ShedBimStrapLine(
            points=[Vec3(0, 0, 0), Vec3(1000, 1000, 0)],
            normal=Vec3(0, 0, 1)
        )
        line2 = ShedBimStrapLine(
            points=[Vec3(1000, 0, 0), Vec3(0, 1000, 0)],
            normal=Vec3(0, 0, 1)
        )
        
        brace = ShedBimStrapBrace(
            lines=[line1, line2],
            material=material,
            tag="SB-01"
        )
        
        assert len(brace.lines) == 2
        assert brace.material == material
        assert brace.tag == "SB-01"
    
    def test_lines_manipulation(self):
        """Test manipulating strap lines."""
        brace = ShedBimStrapBrace()
        
        # Add lines
        line = ShedBimStrapLine()
        brace.lines.append(line)
        
        assert len(brace.lines) == 1
        assert brace.lines[0] == line


class TestShedBimStrapLine:
    """Test ShedBimStrapLine functionality."""
    
    def test_creation_defaults(self):
        """Test strap line creation with defaults."""
        line = ShedBimStrapLine()
        assert line.points == []
        assert line.normal is None
    
    def test_creation_with_data(self):
        """Test strap line creation with data."""
        points = [
            Vec3(0, 0, 0),
            Vec3(500, 500, 0),
            Vec3(1000, 0, 0)
        ]
        normal = Vec3(0, 0, 1)
        
        line = ShedBimStrapLine(points=points, normal=normal)
        
        assert len(line.points) == 3
        assert line.points[1] == Vec3(500, 500, 0)
        assert line.normal == normal
    
    def test_normal_direction(self):
        """Test normal vector properties."""
        # Create a line in XY plane
        points = [Vec3(0, 0, 0), Vec3(1, 0, 0), Vec3(1, 1, 0)]
        normal = Vec3(0, 0, 1)  # Z-up normal
        
        line = ShedBimStrapLine(points=points, normal=normal)
        
        # Normal should be unit vector
        assert line.normal.length() == pytest.approx(1.0)
        
        # Normal should be perpendicular to plane
        assert line.normal.z == 1.0


class TestShedBimRollerDoorCylinder:
    """Test ShedBimRollerDoorCylinder functionality."""
    
    def test_creation_defaults(self):
        """Test cylinder creation with defaults."""
        cylinder = ShedBimRollerDoorCylinder()
        assert cylinder.start_pos is None
        assert cylinder.end_pos is None
        assert cylinder.drum_radius == 0.0
    
    def test_creation_with_data(self):
        """Test cylinder creation with data."""
        start = Vec3(0, 3000, 2500)
        end = Vec3(3000, 3000, 2500)
        radius = 150.0
        
        cylinder = ShedBimRollerDoorCylinder(
            start_pos=start,
            end_pos=end,
            drum_radius=radius
        )
        
        assert cylinder.start_pos == start
        assert cylinder.end_pos == end
        assert cylinder.drum_radius == radius
    
    def test_cylinder_length(self):
        """Test calculating cylinder length."""
        cylinder = ShedBimRollerDoorCylinder(
            start_pos=Vec3(0, 0, 0),
            end_pos=Vec3(3000, 0, 0),
            drum_radius=100
        )
        
        # Calculate length
        if cylinder.start_pos and cylinder.end_pos:
            length = (cylinder.end_pos - cylinder.start_pos).length()
            assert length == 3000.0
    
    def test_cylinder_orientation(self):
        """Test cylinder orientation."""
        # Horizontal cylinder
        cylinder_h = ShedBimRollerDoorCylinder(
            start_pos=Vec3(0, 1000, 2000),
            end_pos=Vec3(3000, 1000, 2000),
            drum_radius=150
        )
        
        # Vertical cylinder
        cylinder_v = ShedBimRollerDoorCylinder(
            start_pos=Vec3(1000, 1000, 0),
            end_pos=Vec3(1000, 1000, 2500),
            drum_radius=150
        )
        
        # Check orientations
        if cylinder_h.start_pos and cylinder_h.end_pos:
            h_dir = cylinder_h.end_pos - cylinder_h.start_pos
            assert h_dir.x != 0  # Horizontal
            assert h_dir.z == 0
        
        if cylinder_v.start_pos and cylinder_v.end_pos:
            v_dir = cylinder_v.end_pos - cylinder_v.start_pos
            assert v_dir.x == 0
            assert v_dir.z != 0  # Vertical


if __name__ == "__main__":
    pytest.main([__file__, "-v"])