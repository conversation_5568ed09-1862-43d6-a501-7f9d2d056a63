"""
Test suite for core BIM model classes.

Tests the main building structure representations.
"""

import pytest
from src.bim.shed_bim import (
    ShedBim, ShedBimPart, ShedBimPartMain, ShedBimPartLeanto,
    ShedBimSide, ShedBimComponentTag, ShedBimSamples
)
from src.bim.wall_roof import ShedBimRoof, ShedBimEnd, ShedBimSlab, ShedBimMezz
from src.bim.components import ShedBimColumn, ShedBimSection, ShedBimPair
from src.bim.accessories import ShedBimFlashing
from src.geometry.primitives import Vec2, Vec3
from src.geometry.lines import Line1


class TestShedBim:
    """Test ShedBim root model class."""
    
    def test_creation_defaults(self):
        """Test BIM creation with defaults."""
        bim = ShedBim()
        assert bim.main is None
        assert bim.leanto_left is None
        assert bim.leanto_right is None
        assert bim.outline_frames == []
        assert bim.component_tags == []
        assert bim.samples is None
    
    def test_creation_with_main_building(self):
        """Test BIM with main building part."""
        main = ShedBimPartMain(roof_type="GABLE")
        bim = ShedBim(main=main)
        
        assert bim.main is not None
        assert bim.main.roof_type == "GABLE"
    
    def test_creation_with_lean_tos(self):
        """Test BIM with lean-to extensions."""
        main = ShedBimPartMain(roof_type="GABLE")
        left = ShedBimPartLeanto(roof_type="MONO")
        right = ShedBimPartLeanto(roof_type="MONO")
        
        bim = ShedBim(
            main=main,
            leanto_left=left,
            leanto_right=right
        )
        
        assert bim.main is not None
        assert bim.leanto_left is not None
        assert bim.leanto_right is not None
        assert bim.leanto_left.roof_type == "MONO"
    
    def test_outline_frames(self):
        """Test building outline frames."""
        bim = ShedBim()
        
        # Add frame outlines
        frame1 = [
            Vec3(0, 0, 0),
            Vec3(6000, 0, 0),
            Vec3(6000, 0, 3000),
            Vec3(3000, 0, 4500),  # Gable peak
            Vec3(0, 0, 3000)
        ]
        
        frame2 = [
            Vec3(0, 6000, 0),
            Vec3(6000, 6000, 0),
            Vec3(6000, 6000, 3000),
            Vec3(3000, 6000, 4500),  # Gable peak
            Vec3(0, 6000, 3000)
        ]
        
        bim.outline_frames.extend([frame1, frame2])
        assert len(bim.outline_frames) == 2
        assert len(bim.outline_frames[0]) == 5  # Gable shape
    
    def test_component_tags(self):
        """Test component tagging system."""
        bim = ShedBim()
        
        # Add component tags (simplified as the actual class isn't shown)
        tag1 = ShedBimComponentTag(tag="COL-01", component_type="COLUMN")
        tag2 = ShedBimComponentTag(tag="RAF-01", component_type="RAFTER")
        
        bim.component_tags.extend([tag1, tag2])
        assert len(bim.component_tags) == 2


class TestShedBimPart:
    """Test ShedBimPart abstract base class."""
    
    def test_cannot_instantiate_abstract(self):
        """Test that abstract class cannot be instantiated."""
        with pytest.raises(TypeError):
            ShedBimPart()
    
    def test_concrete_subclass(self):
        """Test concrete subclass has required properties."""
        main = ShedBimPartMain()
        
        # Should have all base class properties
        assert hasattr(main, 'endbay_sizes')
        assert hasattr(main, 'endbay_offsets')
        assert hasattr(main, 'wall_span_extents')
        assert hasattr(main, 'wall_span_inner_extents')
        assert hasattr(main, 'wall_length_extents')
        assert hasattr(main, 'roof_frame_extents')
        assert hasattr(main, 'roof_span_extents')
        assert hasattr(main, 'roof_length_extents')
        assert hasattr(main, 'ends')
        assert hasattr(main, 'mezz')
        assert hasattr(main, 'slab')
        assert hasattr(main, 'ridge_divider')
        assert hasattr(main, 'open_end_bay_corner_flashings')
        
        # Should implement abstract methods
        assert hasattr(main, 'get_sides')
        assert hasattr(main, 'get_roofs')


class TestShedBimPartMain:
    """Test ShedBimPartMain functionality."""
    
    def test_creation_defaults(self):
        """Test main part creation with defaults."""
        main = ShedBimPartMain()
        
        # Base class properties
        assert main.endbay_sizes == []
        assert main.endbay_offsets == []
        assert main.wall_span_extents is None
        assert main.ends == []
        
        # Main part specific
        assert main.roof_type == ""
        assert main.roof_left is None
        assert main.roof_right is None
        assert main.roof_mansard_left is None
        assert main.roof_mansard_right is None
        assert main.apex_braces == []
        assert main.apex_braces_left == []
        assert main.apex_braces_right == []
        assert main.ridge_flashings == []
        assert main.apex_brackets == []
        assert main.apex_brackets_left == []
        assert main.apex_brackets_right == []
        assert main.side_left is None
        assert main.side_right is None
    
    def test_gable_roof_configuration(self):
        """Test gable roof configuration."""
        main = ShedBimPartMain(roof_type="GABLE")
        
        # Create roof sections
        roof_left = ShedBimRoof(pitch=15.0)
        roof_right = ShedBimRoof(pitch=15.0)
        
        main.roof_left = roof_left
        main.roof_right = roof_right
        
        roofs = main.get_roofs()
        assert len(roofs) == 2
        assert roofs[0] == roof_left
        assert roofs[1] == roof_right
    
    def test_mono_roof_configuration(self):
        """Test mono-slope roof configuration."""
        main = ShedBimPartMain(roof_type="MONO")
        
        # Mono roof only has left side
        roof_left = ShedBimRoof(pitch=5.0)
        main.roof_left = roof_left
        
        roofs = main.get_roofs()
        assert len(roofs) == 1
        assert roofs[0] == roof_left
    
    def test_mansard_roof_configuration(self):
        """Test mansard roof configuration."""
        main = ShedBimPartMain(roof_type="MANSARD")
        
        # Mansard has 4 roof sections
        main.roof_mansard_left = ShedBimRoof(pitch=45.0)
        main.roof_left = ShedBimRoof(pitch=5.0)
        main.roof_right = ShedBimRoof(pitch=5.0)
        main.roof_mansard_right = ShedBimRoof(pitch=45.0)
        
        roofs = main.get_roofs()
        assert len(roofs) == 4
        # Check order is correct
        assert roofs[0] == main.roof_mansard_left
        assert roofs[1] == main.roof_left
        assert roofs[2] == main.roof_right
        assert roofs[3] == main.roof_mansard_right
    
    def test_get_sides(self):
        """Test getting building sides."""
        main = ShedBimPartMain()
        
        # Create sides
        side_left = ShedBimSide(wall_height=3000)
        side_right = ShedBimSide(wall_height=3000)
        
        main.side_left = side_left
        main.side_right = side_right
        
        sides = main.get_sides()
        assert len(sides) == 2
        assert sides[0] == side_left
        assert sides[1] == side_right
    
    def test_apex_braces_configuration(self):
        """Test apex brace configuration."""
        main = ShedBimPartMain()
        
        # Create apex braces
        brace1 = ShedBimSection(tag="APEX-BR-01")
        brace2 = ShedBimSection(tag="APEX-BR-02")
        
        main.apex_braces.extend([brace1, brace2])
        
        # For complex roof, might have left/right braces
        brace_left = ShedBimSection(tag="APEX-BR-L1")
        main.apex_braces_left.append(brace_left)
        
        brace_right = ShedBimSection(tag="APEX-BR-R1")
        main.apex_braces_right.append(brace_right)
        
        # Get all brace lists
        brace_lists = main.get_apex_brace_lists()
        assert len(brace_lists) == 3
        assert len(brace_lists[0]) == 1  # Left
        assert len(brace_lists[1]) == 2  # Center
        assert len(brace_lists[2]) == 1  # Right
    
    def test_building_extents(self):
        """Test building extent properties."""
        main = ShedBimPartMain()
        
        # Set wall extents
        main.wall_span_extents = Line1(0, 6000)  # 6m span
        main.wall_span_inner_extents = Line1(100, 5900)  # Inner span
        main.wall_length_extents = Line1(0, 12000)  # 12m length
        
        # Set roof extents
        main.roof_span_extents = Line1(-300, 6300)  # With overhang
        main.roof_length_extents = Line1(-300, 12300)  # With overhang
        
        # Add roof frame extents
        main.roof_frame_extents.extend([
            Vec2(0, 0),
            Vec2(0, 12000)
        ])
        
        assert main.wall_span_extents.length() == 6000
        assert main.roof_span_extents.length() == 6600  # Including overhangs
        assert len(main.roof_frame_extents) == 2


class TestShedBimPartLeanto:
    """Test ShedBimPartLeanto functionality."""
    
    def test_creation_defaults(self):
        """Test lean-to creation with defaults."""
        leanto = ShedBimPartLeanto()
        
        assert leanto.roof_type == ""
        assert leanto.roof is None
        assert leanto.side is None
        assert leanto.parapet_flashings == []
        assert leanto.connection_brackets == []
    
    def test_mono_leanto_configuration(self):
        """Test mono-slope lean-to configuration."""
        leanto = ShedBimPartLeanto(roof_type="MONO")
        
        # Create single roof
        roof = ShedBimRoof(pitch=5.0)
        leanto.roof = roof
        
        # Create single side
        side = ShedBimSide(wall_height=2400)
        leanto.side = side
        
        # Test get methods
        roofs = leanto.get_roofs()
        assert len(roofs) == 1
        assert roofs[0] == roof
        
        sides = leanto.get_sides()
        assert len(sides) == 1
        assert sides[0] == side
    
    def test_parapet_configuration(self):
        """Test lean-to with parapet wall."""
        leanto = ShedBimPartLeanto()
        
        # Add parapet flashings
        flashing1 = ShedBimFlashing(tag="PARAPET-01")
        flashing2 = ShedBimFlashing(tag="PARAPET-02")
        
        leanto.parapet_flashings.extend([flashing1, flashing2])
        assert len(leanto.parapet_flashings) == 2
    
    def test_connection_brackets(self):
        """Test lean-to connection to main building."""
        leanto = ShedBimPartLeanto()
        
        # Add connection brackets (pairs)
        pair1 = ShedBimPair()
        pair1.add(ShedBimBracket(location="TOP"))
        pair1.add(ShedBimBracket(location="BOTTOM"))
        
        leanto.connection_brackets.append(pair1)
        assert len(leanto.connection_brackets) == 1
        assert leanto.connection_brackets[0].item1.location == "TOP"
        assert leanto.connection_brackets[0].item2.location == "BOTTOM"
    
    def test_empty_leanto(self):
        """Test lean-to with no components."""
        leanto = ShedBimPartLeanto()
        
        # Should return empty lists
        assert leanto.get_roofs() == []
        assert leanto.get_sides() == []


class TestShedBimSide:
    """Test ShedBimSide functionality."""
    
    def test_creation_defaults(self):
        """Test side creation with defaults."""
        side = ShedBimSide()
        
        assert side.wall_height == 0.0
        assert side.columns == []
        assert side.column_haunch_bracket_offsets == []
        assert side.column_stiffeners == []
        assert side.column_composite_sections == []
        assert side.column_cover_flashings == []
        assert side.eave_beams == []
        assert side.eave_trimmers == []
        assert side.knee_braces == []
        assert side.longitudinal_brace_bays == []
        assert side.header_beams == []
        assert side.flybracing_purlins == []
        assert side.eave_purlin_over_wall_bracings == []
        assert side.eave_flashings == []
        assert side.downpipes == []
        assert side.wall is None
        assert side.opening is None
    
    def test_column_configuration(self):
        """Test side with columns."""
        side = ShedBimSide(wall_height=3000)
        
        # Add columns at regular intervals
        for i in range(5):  # 5 columns
            column = ShedBimColumn(
                column=ShedBimSection(
                    start_pos=Vec3(i * 3000, 0, 0),
                    end_pos=Vec3(i * 3000, 0, 3000),
                    tag=f"COL-{i+1}"
                )
            )
            side.columns.append(column)
        
        assert len(side.columns) == 5
        assert side.columns[2].column.start_pos.x == 6000  # Middle column
    
    def test_structural_elements(self):
        """Test side structural elements."""
        side = ShedBimSide()
        
        # Add knee braces
        knee_brace = ShedBimSection(tag="KNEE-BR-01")
        side.knee_braces.append(knee_brace)
        
        # Add eave beams
        from src.bim.wall_roof import ShedBimEaveBeam
        eave_beam = ShedBimEaveBeam()
        side.eave_beams.append(eave_beam)
        
        # Add downpipes
        from src.bim.accessories import ShedBimDownpipe
        downpipe = ShedBimDownpipe(tag="DP-01")
        side.downpipes.append(downpipe)
        
        assert len(side.knee_braces) == 1
        assert len(side.eave_beams) == 1
        assert len(side.downpipes) == 1


# Placeholder classes for testing (these would be in other modules)
@dataclass
class ShedBimComponentTag:
    """Component tagging for BIM elements."""
    tag: str = ""
    component_type: str = ""


@dataclass
class ShedBimSamples:
    """Sample data for BIM visualization."""
    pass


@dataclass
class ShedBimRidgeDivider:
    """Ridge divider component."""
    pass


if __name__ == "__main__":
    pytest.main([__file__, "-v"])