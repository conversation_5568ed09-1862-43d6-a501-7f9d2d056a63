"""
Test suite for BIM mezzanine components.

Tests mezzanine stairs and related structures.
"""

import pytest
from src.bim.mezzanine import ShedBimMezzStairs
from src.bim.openings import MezzanineStairsInfo
from src.bim.components import ShedBimSection, ShedBimColumn
from src.geometry.primitives import Vec3
from src.materials.base import FrameMaterial


class TestShedBimMezzStairs:
    """Test ShedBimMezzStairs functionality."""
    
    def test_creation_defaults(self):
        """Test stairs creation with defaults."""
        stairs = ShedBimMezzStairs()
        assert stairs.info is None
        assert stairs.clearance_upper is None
        assert stairs.stairs_upper is None
        assert stairs.stairs_lower is None
        assert stairs.clearance_lower is None
        assert stairs.bearer is None
        assert stairs.posts == []
    
    def test_creation_with_info(self):
        """Test stairs creation with stairs info."""
        info = MezzanineStairsInfo(
            width=1000.0,
            run=3000.0,
            rise=2500.0
        )
        
        stairs = ShedBimMezzStairs(info=info)
        assert stairs.info == info
        assert stairs.info.width == 1000.0
        assert stairs.info.run == 3000.0
        assert stairs.info.rise == 2500.0
    
    def test_creation_with_clearance_points(self):
        """Test stairs creation with clearance points."""
        clearance_upper = Vec3(1000, 2000, 3000)
        stairs_upper = Vec3(1000, 2000, 2500)
        stairs_lower = Vec3(1000, 5000, 0)
        clearance_lower = Vec3(1000, 5500, 0)
        
        stairs = ShedBimMezzStairs(
            clearance_upper=clearance_upper,
            stairs_upper=stairs_upper,
            stairs_lower=stairs_lower,
            clearance_lower=clearance_lower
        )
        
        assert stairs.clearance_upper == clearance_upper
        assert stairs.stairs_upper == stairs_upper
        assert stairs.stairs_lower == stairs_lower
        assert stairs.clearance_lower == clearance_lower
    
    def test_creation_with_structural_elements(self):
        """Test stairs creation with bearer and posts."""
        # Create bearer
        bearer_material = FrameMaterial(
            name="C20015",
            material_type="C",
            width=203,
            height=76,
            thickness=1.5
        )
        
        bearer = ShedBimSection(
            start_pos=Vec3(0, 0, 2500),
            end_pos=Vec3(1000, 0, 2500),
            material=bearer_material,
            tag="STR-BEARER-01"
        )
        
        # Create posts
        post1 = ShedBimColumn(
            column=ShedBimSection(
                start_pos=Vec3(0, 0, 0),
                end_pos=Vec3(0, 0, 2500),
                tag="STR-POST-01"
            )
        )
        
        post2 = ShedBimColumn(
            column=ShedBimSection(
                start_pos=Vec3(1000, 0, 0),
                end_pos=Vec3(1000, 0, 2500),
                tag="STR-POST-02"
            )
        )
        
        stairs = ShedBimMezzStairs(
            bearer=bearer,
            posts=[post1, post2]
        )
        
        assert stairs.bearer == bearer
        assert len(stairs.posts) == 2
        assert stairs.posts[0].column.tag == "STR-POST-01"
        assert stairs.posts[1].column.tag == "STR-POST-02"
    
    def test_complete_stairs_configuration(self):
        """Test complete stairs configuration."""
        # Create complete stairs with all components
        info = MezzanineStairsInfo(
            width=1200.0,
            run=3500.0,
            rise=2700.0,
            tread_count=15,
            tread_height=180.0,
            tread_depth=250.0
        )
        
        stairs = ShedBimMezzStairs(
            info=info,
            clearance_upper=Vec3(1500, 2000, 3200),
            stairs_upper=Vec3(1500, 2000, 2700),
            stairs_lower=Vec3(1500, 5500, 0),
            clearance_lower=Vec3(1500, 6000, 0)
        )
        
        # Calculate total run from clearance points
        if stairs.clearance_upper and stairs.clearance_lower:
            total_run = (stairs.clearance_lower - stairs.clearance_upper).length()
            # Should be approximately the hypotenuse of rise and run
            expected = (info.run**2 + info.rise**2)**0.5
            assert total_run > expected * 0.9  # Allow some tolerance
    
    def test_add_posts_dynamically(self):
        """Test adding posts dynamically."""
        stairs = ShedBimMezzStairs()
        
        # Add posts one by one
        for i in range(3):
            post = ShedBimColumn(
                column=ShedBimSection(
                    start_pos=Vec3(i * 500, 0, 0),
                    end_pos=Vec3(i * 500, 0, 2500),
                    tag=f"POST-{i+1}"
                )
            )
            stairs.posts.append(post)
        
        assert len(stairs.posts) == 3
        assert stairs.posts[1].column.start_pos.x == 500
    
    def test_stairs_geometry_validation(self):
        """Test stairs geometry relationships."""
        stairs = ShedBimMezzStairs(
            clearance_upper=Vec3(0, 0, 3000),
            stairs_upper=Vec3(0, 0, 2500),
            stairs_lower=Vec3(0, 3000, 0),
            clearance_lower=Vec3(0, 3500, 0)
        )
        
        # Clearance should be above stairs at upper level
        assert stairs.clearance_upper.z > stairs.stairs_upper.z
        
        # Stairs lower should be at ground level
        assert stairs.stairs_lower.z == 0
        
        # Clearance lower should be beyond stairs lower
        assert stairs.clearance_lower.y > stairs.stairs_lower.y
    
    def test_stairs_with_landing(self):
        """Test stairs configuration with landing platform."""
        # Create stairs with landing represented by bearer
        landing_bearer = ShedBimSection(
            start_pos=Vec3(0, 1500, 1250),  # Mid-height landing
            end_pos=Vec3(1200, 1500, 1250),
            tag="LANDING-BEARER"
        )
        
        stairs = ShedBimMezzStairs(
            bearer=landing_bearer,
            stairs_upper=Vec3(600, 0, 2500),    # Upper flight
            stairs_lower=Vec3(600, 3000, 0)     # Lower flight
        )
        
        # Landing should be at mid-height
        if stairs.bearer and stairs.bearer.start_pos:
            landing_height = stairs.bearer.start_pos.z
            total_rise = stairs.stairs_upper.z - stairs.stairs_lower.z
            assert landing_height == pytest.approx(total_rise / 2, rel=0.1)
    
    def test_empty_stairs(self):
        """Test empty stairs configuration."""
        stairs = ShedBimMezzStairs()
        
        # All attributes should be None or empty
        assert stairs.info is None
        assert stairs.clearance_upper is None
        assert stairs.bearer is None
        assert len(stairs.posts) == 0
        
        # Should be able to check without errors
        if stairs.info:
            assert False, "Should not reach here"
        
        if stairs.posts:
            assert False, "Should not reach here"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])