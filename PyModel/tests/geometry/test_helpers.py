"""
Test suite for geometry helper functions.

Tests all utility functions in helpers.py.
"""

import pytest
import math
from src.geometry.helpers import (
    # Constants
    DEG0, DEG90, DEG180, DEG270, DEG360,
    TO_DEG, TO_RAD,
    # Functions
    normalize_angle, mirror_angle,
    trig_soh, trig_sho, trig_cah, trig_cha, trig_toa, trig_tao,
    v2, v3, vx, vy, vz, v2xy, v2xz, v2yz,
    ln1, ln2, ln3, ln2xy, ln2xz, ln2yz,
    polar, rotate,
    mid, interpolate, round_vec, round_line,
    get_extents, get_offsets, get_bay_sizes,
    ln_swap, ln_extend, ln_offset, ln_up, ln_down,
    ln_low_high, ln_left_right, ln_range,
    poly_offset, ln_offset_polyline,
    box_line_inters, get_near_far
)
from src.geometry.primitives import Vec2, Vec3
from src.geometry.lines import Line1, Line2, Line3
from src.geometry.boxes import Box2, Box3


class TestConstants:
    """Test geometric constants."""
    
    def test_degree_constants(self):
        """Test degree to radian constants."""
        assert DEG0 == pytest.approx(0.0)
        assert DEG90 == pytest.approx(math.pi / 2)
        assert DEG180 == pytest.approx(math.pi)
        assert DEG270 == pytest.approx(3 * math.pi / 2)
        assert DEG360 == pytest.approx(2 * math.pi)
    
    def test_conversion_constants(self):
        """Test degree/radian conversion constants."""
        assert TO_DEG == pytest.approx(180.0 / math.pi)
        assert TO_RAD == pytest.approx(math.pi / 180.0)
        
        # Test conversions
        assert 90 * TO_RAD == pytest.approx(math.pi / 2)
        assert (math.pi / 2) * TO_DEG == pytest.approx(90.0)


class TestAngleFunctions:
    """Test angle manipulation functions."""
    
    def test_normalize_angle(self):
        """Test angle normalization to [-π, π]."""
        # Already in range
        assert normalize_angle(0.0) == pytest.approx(0.0)
        assert normalize_angle(math.pi / 2) == pytest.approx(math.pi / 2)
        assert normalize_angle(-math.pi / 2) == pytest.approx(-math.pi / 2)
        
        # Need normalization
        assert normalize_angle(3 * math.pi) == pytest.approx(-math.pi)
        assert normalize_angle(-3 * math.pi) == pytest.approx(math.pi)
        assert normalize_angle(2 * math.pi) == pytest.approx(0.0)
        
        # Multiple rotations
        assert normalize_angle(10 * math.pi) == pytest.approx(0.0)
        assert normalize_angle(7 * math.pi / 2) == pytest.approx(-math.pi / 2)
    
    def test_mirror_angle(self):
        """Test angle mirroring."""
        # Mirror around 0
        assert mirror_angle(math.pi / 4) == pytest.approx(-math.pi / 4)
        assert mirror_angle(-math.pi / 3) == pytest.approx(math.pi / 3)
        
        # Special cases
        assert mirror_angle(0.0) == pytest.approx(0.0)
        assert mirror_angle(math.pi) == pytest.approx(-math.pi)


class TestTrigonometryHelpers:
    """Test trigonometry helper functions."""
    
    def test_soh_sho(self):
        """Test sine-based calculations."""
        # SOH: sin = opposite / hypotenuse
        assert trig_soh(math.pi / 6, 2.0) == pytest.approx(1.0)  # sin(30°) * 2
        
        # SHO: hypotenuse = opposite / sin
        assert trig_sho(math.pi / 6, 1.0) == pytest.approx(2.0)  # 1 / sin(30°)
    
    def test_cah_cha(self):
        """Test cosine-based calculations."""
        # CAH: cos = adjacent / hypotenuse
        assert trig_cah(math.pi / 3, 2.0) == pytest.approx(1.0)  # cos(60°) * 2
        
        # CHA: hypotenuse = adjacent / cos
        assert trig_cha(math.pi / 3, 1.0) == pytest.approx(2.0)  # 1 / cos(60°)
    
    def test_toa_tao(self):
        """Test tangent-based calculations."""
        # TOA: tan = opposite / adjacent
        assert trig_toa(math.pi / 4, 1.0) == pytest.approx(1.0)  # tan(45°) * 1
        
        # TAO: adjacent = opposite / tan
        assert trig_tao(math.pi / 4, 1.0) == pytest.approx(1.0)  # 1 / tan(45°)


class TestVectorCreation:
    """Test vector creation shortcuts."""
    
    def test_v2_v3_creation(self):
        """Test basic vector creation."""
        vec2 = v2(3, 4)
        assert vec2 == Vec2(3, 4)
        
        vec3 = v3(1, 2, 3)
        assert vec3 == Vec3(1, 2, 3)
    
    def test_axis_vectors(self):
        """Test axis-aligned vector creation."""
        assert vx(5) == Vec3(5, 0, 0)
        assert vy(3) == Vec3(0, 3, 0)
        assert vz(7) == Vec3(0, 0, 7)
    
    def test_planar_vectors(self):
        """Test planar vector creation."""
        assert v2xy(2, 3) == Vec3(2, 3, 0)
        assert v2xz(4, 5) == Vec3(4, 0, 5)
        assert v2yz(6, 7) == Vec3(0, 6, 7)


class TestLineCreation:
    """Test line creation shortcuts."""
    
    def test_line_creation(self):
        """Test basic line creation."""
        line1 = ln1(0, 10)
        assert line1 == Line1(0, 10)
        
        line2 = ln2(v2(0, 0), v2(5, 5))
        assert isinstance(line2, Line2)
        
        line3 = ln3(v3(0, 0, 0), v3(1, 1, 1))
        assert isinstance(line3, Line3)
    
    def test_planar_lines(self):
        """Test planar line creation."""
        # XY plane
        line_xy = ln2xy(0, 0, 10, 10)
        assert line_xy.start == Vec2(0, 0)
        assert line_xy.end == Vec2(10, 10)
        
        # XZ plane
        line_xz = ln2xz(0, 0, 5, 5)
        assert isinstance(line_xz, Line3)
        assert line_xz.start == Vec3(0, 0, 0)
        assert line_xz.end == Vec3(5, 0, 5)
        
        # YZ plane
        line_yz = ln2yz(0, 0, 3, 3)
        assert isinstance(line_yz, Line3)
        assert line_yz.start == Vec3(0, 0, 0)
        assert line_yz.end == Vec3(0, 3, 3)


class TestPolarAndRotation:
    """Test polar coordinates and rotation."""
    
    def test_polar(self):
        """Test polar coordinate conversion."""
        # 0 degrees
        vec = polar(0, 5)
        assert vec.x == pytest.approx(5.0)
        assert vec.y == pytest.approx(0.0)
        
        # 90 degrees
        vec2 = polar(math.pi / 2, 3)
        assert vec2.x == pytest.approx(0.0, abs=1e-10)
        assert vec2.y == pytest.approx(3.0)
        
        # 45 degrees
        vec3 = polar(math.pi / 4, math.sqrt(2))
        assert vec3.x == pytest.approx(1.0)
        assert vec3.y == pytest.approx(1.0)
    
    def test_rotate(self):
        """Test 2D vector rotation."""
        # Rotate unit X by 90 degrees
        vec = Vec2(1, 0)
        rotated = rotate(vec, math.pi / 2)
        assert rotated.x == pytest.approx(0.0, abs=1e-10)
        assert rotated.y == pytest.approx(1.0)
        
        # Rotate by 180 degrees
        rotated2 = rotate(vec, math.pi)
        assert rotated2.x == pytest.approx(-1.0)
        assert rotated2.y == pytest.approx(0.0, abs=1e-10)


class TestInterpolation:
    """Test interpolation functions."""
    
    def test_mid(self):
        """Test midpoint calculation."""
        # Vec2
        mid2 = mid(Vec2(0, 0), Vec2(10, 10))
        assert mid2 == Vec2(5, 5)
        
        # Vec3
        mid3 = mid(Vec3(0, 0, 0), Vec3(10, 10, 10))
        assert mid3 == Vec3(5, 5, 5)
        
        # Scalar
        mid_scalar = mid(0.0, 10.0)
        assert mid_scalar == pytest.approx(5.0)
    
    def test_interpolate(self):
        """Test linear interpolation."""
        # Scalar
        assert interpolate(0.0, 10.0, 0.0) == pytest.approx(0.0)
        assert interpolate(0.0, 10.0, 0.5) == pytest.approx(5.0)
        assert interpolate(0.0, 10.0, 1.0) == pytest.approx(10.0)
        
        # Line1
        line = Line1(0, 10)
        assert interpolate(line, 0.5) == pytest.approx(5.0)
        
        # Line2
        line2 = Line2(Vec2(0, 0), Vec2(10, 10))
        mid_point = interpolate(line2, 0.5)
        assert mid_point == Vec2(5, 5)
        
        # Line3
        line3 = Line3(Vec3(0, 0, 0), Vec3(10, 10, 10))
        mid_point3 = interpolate(line3, 0.5)
        assert mid_point3 == Vec3(5, 5, 5)


class TestRounding:
    """Test rounding functions."""
    
    def test_round_vec(self):
        """Test vector rounding."""
        # Vec2
        vec2 = Vec2(1.234567, 2.345678)
        rounded2 = round_vec(vec2, 2)
        assert rounded2.x == pytest.approx(1.23)
        assert rounded2.y == pytest.approx(2.35)
        
        # Vec3
        vec3 = Vec3(1.111, 2.222, 3.333)
        rounded3 = round_vec(vec3, 1)
        assert rounded3.x == pytest.approx(1.1)
        assert rounded3.y == pytest.approx(2.2)
        assert rounded3.z == pytest.approx(3.3)
    
    def test_round_line(self):
        """Test line rounding."""
        line = Line2(Vec2(1.234, 2.345), Vec2(3.456, 4.567))
        rounded = round_line(line, 1)
        assert rounded.start.x == pytest.approx(1.2)
        assert rounded.start.y == pytest.approx(2.3)
        assert rounded.end.x == pytest.approx(3.5)
        assert rounded.end.y == pytest.approx(4.6)


class TestExtentsAndOffsets:
    """Test extent and offset calculations."""
    
    def test_get_extents(self):
        """Test getting min/max extents."""
        values = [5.0, 2.0, 8.0, 1.0, 9.0]
        min_val, max_val = get_extents(values)
        assert min_val == 1.0
        assert max_val == 9.0
    
    def test_get_offsets(self):
        """Test calculating offsets from positions."""
        # Regular spacing
        positions = [0.0, 3.0, 6.0, 9.0]
        offsets = get_offsets(positions)
        assert offsets == [0.0, 3.0, 3.0, 3.0]
        
        # Irregular spacing
        positions2 = [0.0, 2.0, 7.0, 10.0]
        offsets2 = get_offsets(positions2)
        assert offsets2 == [0.0, 2.0, 5.0, 3.0]
    
    def test_get_bay_sizes(self):
        """Test bay size calculation with rounding."""
        # Evenly divisible
        extents = get_bay_sizes(10000, 4)
        assert extents == [0, 2500, 5000, 7500, 10000]
        
        # With remainder (should round)
        extents2 = get_bay_sizes(10000, 3)
        # 10000/3 = 3333.33, rounds to 3333/3334
        assert extents2 == [0, 3333, 6667, 10000]


class TestLineOperations:
    """Test line manipulation functions."""
    
    def test_ln_swap(self):
        """Test swapping line endpoints."""
        line = Line2(Vec2(1, 2), Vec2(3, 4))
        swapped = ln_swap(line)
        assert swapped.start == Vec2(3, 4)
        assert swapped.end == Vec2(1, 2)
    
    def test_ln_extend(self):
        """Test extending line length."""
        # Horizontal line
        line = Line2(Vec2(0, 0), Vec2(10, 0))
        extended = ln_extend(line, 5)  # Extend by 5 on each end
        assert extended.start == Vec2(-5, 0)
        assert extended.end == Vec2(15, 0)
        
        # Diagonal line
        line2 = Line3(Vec3(0, 0, 0), Vec3(3, 4, 0))  # Length 5
        extended2 = ln_extend(line2, 2.5)
        # Should extend by 2.5 in each direction
        assert extended2.magnitude() == pytest.approx(10.0)
    
    def test_ln_offset(self):
        """Test offsetting line perpendicular to direction."""
        # Horizontal line, offset up
        line = Line2(Vec2(0, 0), Vec2(10, 0))
        offset = ln_offset(line, 5)
        assert offset.start == Vec2(0, 5)
        assert offset.end == Vec2(10, 5)
        
        # Vertical line, offset right
        line2 = Line2(Vec2(0, 0), Vec2(0, 10))
        offset2 = ln_offset(line2, 3)
        assert offset2.start == Vec2(3, 0)
        assert offset2.end == Vec2(3, 10)
    
    def test_ln_up_down(self):
        """Test moving line up/down (Z direction)."""
        line = Line3(Vec3(0, 0, 0), Vec3(10, 0, 0))
        
        up = ln_up(line, 5)
        assert up.start.z == 5
        assert up.end.z == 5
        
        down = ln_down(line, 3)
        assert down.start.z == -3
        assert down.end.z == -3
    
    def test_ln_low_high(self):
        """Test getting lowest/highest points of line."""
        line = Line3(Vec3(0, 0, 5), Vec3(10, 0, 2))
        
        low = ln_low_high(line, True)  # Get lowest
        assert low == Vec3(10, 0, 2)
        
        high = ln_low_high(line, False)  # Get highest
        assert high == Vec3(0, 0, 5)
    
    def test_ln_left_right(self):
        """Test getting leftmost/rightmost points."""
        line = Line2(Vec2(10, 0), Vec2(5, 5))
        
        left = ln_left_right(line, True)  # Get leftmost
        assert left == Vec2(5, 5)
        
        right = ln_left_right(line, False)  # Get rightmost
        assert right == Vec2(10, 0)
    
    def test_ln_range(self):
        """Test getting range of coordinates."""
        lines = [
            Line2(Vec2(0, 0), Vec2(5, 5)),
            Line2(Vec2(2, -2), Vec2(8, 3)),
            Line2(Vec2(-1, 1), Vec2(6, 4))
        ]
        
        x_range = ln_range(lines, 0)  # X coordinate
        assert x_range == (-1, 8)
        
        y_range = ln_range(lines, 1)  # Y coordinate
        assert y_range == (-2, 5)


class TestPolygonOperations:
    """Test polygon offset operations."""
    
    def test_poly_offset_square(self):
        """Test offsetting a square polygon."""
        # Square centered at origin
        square = [
            Vec2(-5, -5),
            Vec2(5, -5),
            Vec2(5, 5),
            Vec2(-5, 5)
        ]
        
        # Outward offset
        offset = poly_offset(square, 2)
        # Should be larger square
        assert len(offset) == 4
        assert offset[0] == Vec2(-7, -7)
        assert offset[1] == Vec2(7, -7)
        assert offset[2] == Vec2(7, 7)
        assert offset[3] == Vec2(-7, 7)
        
        # Inward offset
        inset = poly_offset(square, -2)
        assert len(inset) == 4
        assert inset[0] == Vec2(-3, -3)
    
    def test_ln_offset_polyline(self):
        """Test offsetting a polyline."""
        # L-shaped polyline
        polyline = [
            Vec2(0, 0),
            Vec2(10, 0),
            Vec2(10, 10)
        ]
        
        offset = ln_offset_polyline(polyline, 2)
        assert len(offset) == 3
        # First segment offset up
        assert offset[0].y == pytest.approx(2)
        # Corner should be handled correctly
        assert offset[1].x == pytest.approx(12)


class TestIntersections:
    """Test intersection functions."""
    
    def test_box_line_inters(self):
        """Test box-line intersection."""
        box = Box2(Vec2(0, 0), Vec2(10, 10))
        
        # Line through box
        line1 = Line2(Vec2(-5, 5), Vec2(15, 5))
        inters1 = box_line_inters(box, line1)
        assert inters1 is not None
        assert len(inters1) == 2
        assert Vec2(0, 5) in inters1
        assert Vec2(10, 5) in inters1
        
        # Line outside box
        line2 = Line2(Vec2(-5, -5), Vec2(-5, 15))
        inters2 = box_line_inters(box, line2)
        assert inters2 is None or len(inters2) == 0
        
        # Line touching corner
        line3 = Line2(Vec2(-5, -5), Vec2(5, 5))
        inters3 = box_line_inters(box, line3)
        assert inters3 is not None
        assert Vec2(0, 0) in inters3
    
    def test_get_near_far(self):
        """Test getting near/far intersection points."""
        box = Box3(Vec3(0, 0, 0), Vec3(10, 10, 10))
        
        # Ray from outside pointing through box
        origin = Vec3(-5, 5, 5)
        direction = Vec3(1, 0, 0)  # Pointing in +X
        
        near, far = get_near_far(box, origin, direction)
        assert near is not None
        assert far is not None
        assert near.x == pytest.approx(0.0)  # Enter at X=0
        assert far.x == pytest.approx(10.0)  # Exit at X=10
        
        # Ray missing box
        origin2 = Vec3(-5, -5, -5)
        direction2 = Vec3(0, 0, -1)  # Pointing away
        
        near2, far2 = get_near_far(box, origin2, direction2)
        assert near2 is None
        assert far2 is None


if __name__ == "__main__":
    pytest.main([__file__, "-v"])