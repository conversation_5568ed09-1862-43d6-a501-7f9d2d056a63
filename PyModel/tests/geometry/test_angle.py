"""
Test suite for <PERSON><PERSON> class.

Tests angle representation and conversions.
"""

import pytest
import math
from src.geometry.angle import <PERSON><PERSON>, AngleUnit


class TestAngle:
    """Test Angle functionality."""
    
    def test_creation_radians(self):
        """Test angle creation in radians."""
        angle = Angle(math.pi / 2, AngleUnit.RADIANS)
        assert angle.radians == pytest.approx(math.pi / 2)
        assert angle.degrees == pytest.approx(90.0)
    
    def test_creation_degrees(self):
        """Test angle creation in degrees."""
        angle = Angle(45.0, AngleUnit.DEGREES)
        assert angle.degrees == pytest.approx(45.0)
        assert angle.radians == pytest.approx(math.pi / 4)
    
    def test_from_radians(self):
        """Test static creation from radians."""
        angle = Angle.from_radians(math.pi)
        assert angle.radians == pytest.approx(math.pi)
        assert angle.degrees == pytest.approx(180.0)
    
    def test_from_degrees(self):
        """Test static creation from degrees."""
        angle = Angle.from_degrees(270.0)
        assert angle.degrees == pytest.approx(270.0)
        assert angle.radians == pytest.approx(3 * math.pi / 2)
    
    def test_zero_angle(self):
        """Test zero angle."""
        zero = Angle.zero()
        assert zero.radians == 0.0
        assert zero.degrees == 0.0
    
    def test_right_angle(self):
        """Test right angle (90 degrees)."""
        right = Angle.right()
        assert right.degrees == pytest.approx(90.0)
        assert right.radians == pytest.approx(math.pi / 2)
    
    def test_straight_angle(self):
        """Test straight angle (180 degrees)."""
        straight = Angle.straight()
        assert straight.degrees == pytest.approx(180.0)
        assert straight.radians == pytest.approx(math.pi)
    
    def test_full_angle(self):
        """Test full angle (360 degrees)."""
        full = Angle.full()
        assert full.degrees == pytest.approx(360.0)
        assert full.radians == pytest.approx(2 * math.pi)
    
    def test_normalization(self):
        """Test angle normalization to [0, 2π)."""
        # Positive overflow
        angle1 = Angle.from_degrees(450)  # 450 = 360 + 90
        normalized1 = angle1.normalized()
        assert normalized1.degrees == pytest.approx(90.0)
        
        # Negative angle
        angle2 = Angle.from_degrees(-90)  # -90 = 360 - 90 = 270
        normalized2 = angle2.normalized()
        assert normalized2.degrees == pytest.approx(270.0)
        
        # Multiple rotations
        angle3 = Angle.from_radians(5 * math.pi)  # 2.5 rotations
        normalized3 = angle3.normalized()
        assert normalized3.radians == pytest.approx(math.pi)
    
    def test_signed_normalization(self):
        """Test angle normalization to [-π, π]."""
        # Positive angle < π
        angle1 = Angle.from_radians(math.pi / 2)
        signed1 = angle1.normalized_signed()
        assert signed1.radians == pytest.approx(math.pi / 2)
        
        # Positive angle > π
        angle2 = Angle.from_radians(3 * math.pi / 2)
        signed2 = angle2.normalized_signed()
        assert signed2.radians == pytest.approx(-math.pi / 2)
        
        # Negative angle
        angle3 = Angle.from_radians(-3 * math.pi / 2)
        signed3 = angle3.normalized_signed()
        assert signed3.radians == pytest.approx(math.pi / 2)
    
    def test_trigonometric_functions(self):
        """Test trigonometric functions."""
        # 30 degrees
        angle30 = Angle.from_degrees(30)
        assert angle30.sin() == pytest.approx(0.5)
        assert angle30.cos() == pytest.approx(math.sqrt(3) / 2)
        assert angle30.tan() == pytest.approx(1 / math.sqrt(3))
        
        # 45 degrees
        angle45 = Angle.from_degrees(45)
        assert angle45.sin() == pytest.approx(math.sqrt(2) / 2)
        assert angle45.cos() == pytest.approx(math.sqrt(2) / 2)
        assert angle45.tan() == pytest.approx(1.0)
        
        # 90 degrees
        angle90 = Angle.from_degrees(90)
        assert angle90.sin() == pytest.approx(1.0)
        assert angle90.cos() == pytest.approx(0.0, abs=1e-10)
    
    def test_arithmetic_operations(self):
        """Test angle arithmetic."""
        angle1 = Angle.from_degrees(30)
        angle2 = Angle.from_degrees(45)
        
        # Addition
        sum_angle = angle1 + angle2
        assert sum_angle.degrees == pytest.approx(75.0)
        
        # Subtraction
        diff_angle = angle2 - angle1
        assert diff_angle.degrees == pytest.approx(15.0)
        
        # Multiplication by scalar
        double = angle1 * 2
        assert double.degrees == pytest.approx(60.0)
        
        # Division by scalar
        half = angle2 / 2
        assert half.degrees == pytest.approx(22.5)
        
        # Negation
        neg = -angle1
        assert neg.degrees == pytest.approx(-30.0)
    
    def test_comparison_operations(self):
        """Test angle comparisons."""
        angle1 = Angle.from_degrees(30)
        angle2 = Angle.from_degrees(45)
        angle3 = Angle.from_degrees(30)
        
        # Equality
        assert angle1 == angle3
        assert angle1 != angle2
        
        # Ordering
        assert angle1 < angle2
        assert angle2 > angle1
        assert angle1 <= angle3
        assert angle2 >= angle1
    
    def test_complementary_angle(self):
        """Test complementary angle (90° - angle)."""
        angle = Angle.from_degrees(30)
        comp = angle.complementary()
        assert comp.degrees == pytest.approx(60.0)
        
        # Complementary of 45° is 45°
        angle45 = Angle.from_degrees(45)
        comp45 = angle45.complementary()
        assert comp45.degrees == pytest.approx(45.0)
    
    def test_supplementary_angle(self):
        """Test supplementary angle (180° - angle)."""
        angle = Angle.from_degrees(60)
        supp = angle.supplementary()
        assert supp.degrees == pytest.approx(120.0)
        
        # Supplementary of 90° is 90°
        angle90 = Angle.from_degrees(90)
        supp90 = angle90.supplementary()
        assert supp90.degrees == pytest.approx(90.0)
    
    def test_from_vector(self):
        """Test angle creation from 2D vector."""
        # Positive X axis
        angle1 = Angle.from_vector(1, 0)
        assert angle1.degrees == pytest.approx(0.0)
        
        # Positive Y axis
        angle2 = Angle.from_vector(0, 1)
        assert angle2.degrees == pytest.approx(90.0)
        
        # 45 degrees
        angle3 = Angle.from_vector(1, 1)
        assert angle3.degrees == pytest.approx(45.0)
        
        # Negative X axis
        angle4 = Angle.from_vector(-1, 0)
        assert angle4.degrees == pytest.approx(180.0)
    
    def test_difference_between_angles(self):
        """Test finding difference between angles."""
        angle1 = Angle.from_degrees(30)
        angle2 = Angle.from_degrees(60)
        
        diff = angle1.difference_to(angle2)
        assert diff.degrees == pytest.approx(30.0)
        
        # Wrapping case
        angle3 = Angle.from_degrees(10)
        angle4 = Angle.from_degrees(350)
        
        diff2 = angle3.difference_to(angle4)
        # Should be -20 (shorter path)
        assert abs(diff2.degrees) == pytest.approx(20.0)
    
    def test_angle_bisector(self):
        """Test finding angle bisector."""
        angle1 = Angle.from_degrees(0)
        angle2 = Angle.from_degrees(90)
        
        bisector = Angle.bisect(angle1, angle2)
        assert bisector.degrees == pytest.approx(45.0)
        
        # Wrapping case
        angle3 = Angle.from_degrees(350)
        angle4 = Angle.from_degrees(10)
        
        bisector2 = Angle.bisect(angle3, angle4)
        assert bisector2.degrees == pytest.approx(0.0) or bisector2.degrees == pytest.approx(360.0)
    
    def test_lerp(self):
        """Test linear interpolation between angles."""
        angle1 = Angle.from_degrees(0)
        angle2 = Angle.from_degrees(90)
        
        # Halfway
        mid = Angle.lerp(angle1, angle2, 0.5)
        assert mid.degrees == pytest.approx(45.0)
        
        # Quarter way
        quarter = Angle.lerp(angle1, angle2, 0.25)
        assert quarter.degrees == pytest.approx(22.5)
        
        # Full interpolation
        assert Angle.lerp(angle1, angle2, 0.0) == angle1
        assert Angle.lerp(angle1, angle2, 1.0) == angle2
    
    def test_quadrant(self):
        """Test quadrant determination."""
        # First quadrant (0-90°)
        assert Angle.from_degrees(45).quadrant() == 1
        
        # Second quadrant (90-180°)
        assert Angle.from_degrees(135).quadrant() == 2
        
        # Third quadrant (180-270°)
        assert Angle.from_degrees(225).quadrant() == 3
        
        # Fourth quadrant (270-360°)
        assert Angle.from_degrees(315).quadrant() == 4
        
        # Boundary cases
        assert Angle.from_degrees(0).quadrant() == 1
        assert Angle.from_degrees(90).quadrant() == 2
        assert Angle.from_degrees(180).quadrant() == 3
        assert Angle.from_degrees(270).quadrant() == 4
    
    def test_string_representation(self):
        """Test string representations."""
        angle = Angle.from_degrees(45.5)
        
        # Default string
        s = str(angle)
        assert "45.5°" in s or "45.5 degrees" in s
        
        # Repr
        r = repr(angle)
        assert "Angle" in r
        assert "45.5" in r
    
    def test_special_angles(self):
        """Test recognition of special angles."""
        # Check if angle is acute
        assert Angle.from_degrees(45).is_acute() == True
        assert Angle.from_degrees(90).is_acute() == False
        
        # Check if angle is right
        assert Angle.from_degrees(90).is_right() == True
        assert Angle.from_degrees(89.9).is_right() == False
        
        # Check if angle is obtuse
        assert Angle.from_degrees(120).is_obtuse() == True
        assert Angle.from_degrees(90).is_obtuse() == False
        
        # Check if angle is straight
        assert Angle.from_degrees(180).is_straight() == True
        assert Angle.from_degrees(179.9).is_straight() == False
        
        # Check if angle is reflex
        assert Angle.from_degrees(270).is_reflex() == True
        assert Angle.from_degrees(180).is_reflex() == False


if __name__ == "__main__":
    pytest.main([__file__, "-v"])