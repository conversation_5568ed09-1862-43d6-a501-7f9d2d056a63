"""Comprehensive tests for Vec3 class."""

import math
import pytest
from src.geometry.primitives import Vec3


class TestVec3Creation:
    """Test Vec3 creation and basic properties."""
    
    def test_create_vec3(self):
        """Test creating a Vec3 instance."""
        v = Vec3(1.0, 2.0, 3.0)
        assert v.x == 1.0
        assert v.y == 2.0
        assert v.z == 3.0
    
    def test_origin(self):
        """Test origin vector."""
        v = Vec3.origin()
        assert v.x == 0.0
        assert v.y == 0.0
        assert v.z == 0.0
    
    def test_unit_vectors(self):
        """Test unit vectors."""
        unit_x = Vec3.unit_x()
        assert unit_x.x == 1.0
        assert unit_x.y == 0.0
        assert unit_x.z == 0.0
        
        unit_y = Vec3.unit_y()
        assert unit_y.x == 0.0
        assert unit_y.y == 1.0
        assert unit_y.z == 0.0
        
        unit_z = Vec3.unit_z()
        assert unit_z.x == 0.0
        assert unit_z.y == 0.0
        assert unit_z.z == 1.0
    
    def test_min_max_values(self):
        """Test min/max value vectors."""
        min_v = Vec3.min_value()
        assert min_v.x == -float('inf')
        assert min_v.y == -float('inf')
        assert min_v.z == -float('inf')
        
        max_v = Vec3.max_value()
        assert max_v.x == float('inf')
        assert max_v.y == float('inf')
        assert max_v.z == float('inf')


class TestVec3Operations:
    """Test Vec3 mathematical operations."""
    
    def test_length(self):
        """Test vector length calculation."""
        v = Vec3(2.0, 3.0, 6.0)
        assert v.length() == 7.0  # sqrt(4 + 9 + 36) = 7
        
        v2 = Vec3(0.0, 0.0, 0.0)
        assert v2.length() == 0.0
    
    def test_length_squared(self):
        """Test squared length calculation."""
        v = Vec3(2.0, 3.0, 6.0)
        assert v.length_squared() == 49.0
    
    def test_dot_product(self):
        """Test dot product calculation."""
        v1 = Vec3(1.0, 2.0, 3.0)
        v2 = Vec3(4.0, 5.0, 6.0)
        assert Vec3.dot(v1, v2) == 32.0  # 1*4 + 2*5 + 3*6
        
        # Perpendicular vectors
        v3 = Vec3(1.0, 0.0, 0.0)
        v4 = Vec3(0.0, 1.0, 0.0)
        assert Vec3.dot(v3, v4) == 0.0
    
    def test_cross_product(self):
        """Test cross product calculation."""
        # Standard basis vectors
        x = Vec3(1.0, 0.0, 0.0)
        y = Vec3(0.0, 1.0, 0.0)
        z = Vec3(0.0, 0.0, 1.0)
        
        # x × y = z
        result = Vec3.cross(x, y)
        assert result.x == 0.0
        assert result.y == 0.0
        assert result.z == 1.0
        
        # y × z = x
        result = Vec3.cross(y, z)
        assert result.x == 1.0
        assert result.y == 0.0
        assert result.z == 0.0
        
        # z × x = y
        result = Vec3.cross(z, x)
        assert result.x == 0.0
        assert result.y == 1.0
        assert result.z == 0.0
        
        # General case
        v1 = Vec3(2.0, 3.0, 4.0)
        v2 = Vec3(5.0, 6.0, 7.0)
        result = Vec3.cross(v1, v2)
        assert result.x == -3.0  # 3*7 - 4*6
        assert result.y == 6.0   # 4*5 - 2*7
        assert result.z == -3.0  # 2*6 - 3*5
    
    def test_normalization(self):
        """Test vector normalization."""
        v = Vec3(3.0, 0.0, 4.0)
        normalized = Vec3.normal(v)
        assert math.isclose(normalized.x, 0.6)
        assert math.isclose(normalized.y, 0.0)
        assert math.isclose(normalized.z, 0.8)
        assert math.isclose(normalized.length(), 1.0)
        
        # Zero vector
        zero = Vec3(0.0, 0.0, 0.0)
        normalized_zero = Vec3.normal(zero)
        assert normalized_zero.x == 0.0
        assert normalized_zero.y == 0.0
        assert normalized_zero.z == 0.0
    
    def test_distance(self):
        """Test distance calculation between vectors."""
        v1 = Vec3(1.0, 2.0, 3.0)
        v2 = Vec3(4.0, 6.0, 3.0)
        distance = Vec3.distance(v1, v2)
        assert distance == 5.0  # sqrt(9 + 16 + 0)
    
    def test_distance_squared(self):
        """Test squared distance calculation."""
        v1 = Vec3(1.0, 2.0, 3.0)
        v2 = Vec3(4.0, 6.0, 3.0)
        dist_sq = v1.distance_squared(v2)
        assert dist_sq == 25.0
    
    def test_midpoint(self):
        """Test midpoint calculation."""
        v1 = Vec3(0.0, 0.0, 0.0)
        v2 = Vec3(4.0, 6.0, 8.0)
        mid = Vec3.midpoint(v1, v2)
        assert mid.x == 2.0
        assert mid.y == 3.0
        assert mid.z == 4.0


class TestVec3Arithmetic:
    """Test Vec3 arithmetic operations."""
    
    def test_addition(self):
        """Test vector addition."""
        v1 = Vec3(1.0, 2.0, 3.0)
        v2 = Vec3(4.0, 5.0, 6.0)
        result = v1 + v2
        assert result.x == 5.0
        assert result.y == 7.0
        assert result.z == 9.0
    
    def test_subtraction(self):
        """Test vector subtraction."""
        v1 = Vec3(5.0, 7.0, 9.0)
        v2 = Vec3(2.0, 3.0, 4.0)
        result = v1 - v2
        assert result.x == 3.0
        assert result.y == 4.0
        assert result.z == 5.0
    
    def test_multiplication(self):
        """Test scalar multiplication."""
        v = Vec3(2.0, 3.0, 4.0)
        result1 = v * 2.0
        assert result1.x == 4.0
        assert result1.y == 6.0
        assert result1.z == 8.0
        
        result2 = 3.0 * v
        assert result2.x == 6.0
        assert result2.y == 9.0
        assert result2.z == 12.0
    
    def test_division(self):
        """Test scalar division."""
        v = Vec3(6.0, 8.0, 10.0)
        result = v / 2.0
        assert result.x == 3.0
        assert result.y == 4.0
        assert result.z == 5.0
    
    def test_unary_operators(self):
        """Test unary operators."""
        v = Vec3(3.0, -4.0, 5.0)
        
        pos_v = +v
        assert pos_v.x == 3.0
        assert pos_v.y == -4.0
        assert pos_v.z == 5.0
        
        neg_v = -v
        assert neg_v.x == -3.0
        assert neg_v.y == 4.0
        assert neg_v.z == -5.0


class TestVec3Utilities:
    """Test Vec3 utility functions."""
    
    def test_min_max(self):
        """Test component-wise min/max."""
        v1 = Vec3(1.0, 4.0, 2.0)
        v2 = Vec3(3.0, 2.0, 5.0)
        
        min_v = Vec3.min(v1, v2)
        assert min_v.x == 1.0
        assert min_v.y == 2.0
        assert min_v.z == 2.0
        
        max_v = Vec3.max(v1, v2)
        assert max_v.x == 3.0
        assert max_v.y == 4.0
        assert max_v.z == 5.0
    
    def test_to_array(self):
        """Test conversion to array."""
        v = Vec3(1.0, 2.0, 3.0)
        arr = v.to_array()
        assert arr == [1.0, 2.0, 3.0]
    
    def test_string_representation(self):
        """Test string representations."""
        v = Vec3(1.5, 2.5, 3.5)
        assert str(v) == "(1.5,2.5,3.5)"
        assert repr(v) == "Vec3(x=1.5, y=2.5, z=3.5)"


class TestVec3EdgeCases:
    """Test edge cases and special values."""
    
    def test_zero_vector_operations(self):
        """Test operations with zero vector."""
        zero = Vec3(0.0, 0.0, 0.0)
        v = Vec3(3.0, 4.0, 5.0)
        
        assert (v + zero) == v
        assert (v - zero) == v
        assert Vec3.dot(v, zero) == 0.0
        
        # Cross product with zero
        cross = Vec3.cross(v, zero)
        assert cross.x == 0.0
        assert cross.y == 0.0
        assert cross.z == 0.0
    
    def test_negative_values(self):
        """Test operations with negative values."""
        v = Vec3(-3.0, -4.0, 0.0)
        assert v.length() == 5.0
        assert v.length_squared() == 25.0
    
    def test_very_small_values(self):
        """Test with very small values."""
        epsilon = 1e-10
        v = Vec3(epsilon, epsilon, epsilon)
        assert v.length() > 0
        assert v.length_squared() > 0
        
        normalized = Vec3.normal(v)
        assert math.isclose(normalized.length(), 1.0, rel_tol=1e-9)
    
    def test_very_large_values(self):
        """Test with very large values."""
        large = 1e100
        v = Vec3(large, large, large)
        normalized = Vec3.normal(v)
        assert math.isclose(normalized.length(), 1.0, rel_tol=1e-9)
    
    def test_cross_product_properties(self):
        """Test cross product properties."""
        v1 = Vec3(1.0, 2.0, 3.0)
        v2 = Vec3(4.0, 5.0, 6.0)
        
        # Anti-commutativity: v1 × v2 = -(v2 × v1)
        cross1 = Vec3.cross(v1, v2)
        cross2 = Vec3.cross(v2, v1)
        assert cross1 == -cross2
        
        # Self cross product is zero
        self_cross = Vec3.cross(v1, v1)
        assert self_cross.x == 0.0
        assert self_cross.y == 0.0
        assert self_cross.z == 0.0
        
        # Cross product is perpendicular to both vectors
        assert math.isclose(Vec3.dot(cross1, v1), 0.0, abs_tol=1e-10)
        assert math.isclose(Vec3.dot(cross1, v2), 0.0, abs_tol=1e-10)


class TestVec3Equality:
    """Test Vec3 equality comparisons."""
    
    def test_equality(self):
        """Test vector equality."""
        v1 = Vec3(1.0, 2.0, 3.0)
        v2 = Vec3(1.0, 2.0, 3.0)
        v3 = Vec3(1.0, 2.0, 3.1)
        
        assert v1 == v2
        assert v1 != v3
        assert not (v1 == v3)
        assert not (v1 != v2)