"""
Test suite for Plane3 class.

Tests 3D plane operations and intersections.
"""

import pytest
import math
from src.geometry.plane import Plane3
from src.geometry.primitives import Vec3
from src.geometry.lines import Line3


class TestPlane3:
    """Test Plane3 functionality."""
    
    def test_creation_from_normal_and_distance(self):
        """Test plane creation from normal and distance."""
        normal = Vec3(0, 0, 1)  # XY plane
        distance = 5.0
        
        plane = Plane3(normal, distance)
        assert plane.normal == normal
        assert plane.d == distance
    
    def test_creation_from_point_and_normal(self):
        """Test plane creation from point and normal."""
        point = Vec3(0, 0, 5)
        normal = Vec3(0, 0, 1)
        
        plane = Plane3.from_point_and_normal(point, normal)
        assert plane.normal == normal
        assert plane.d == 5.0  # Distance should be 5
    
    def test_creation_from_three_points(self):
        """Test plane creation from three points."""
        # Three points in XY plane at Z=2
        p1 = Vec3(0, 0, 2)
        p2 = Vec3(1, 0, 2)
        p3 = Vec3(0, 1, 2)
        
        plane = Plane3.from_three_points(p1, p2, p3)
        
        # Normal should point in Z direction
        assert abs(plane.normal.x) < 1e-10
        assert abs(plane.normal.y) < 1e-10
        assert abs(abs(plane.normal.z) - 1.0) < 1e-10
        
        # Distance should be 2
        assert abs(abs(plane.d) - 2.0) < 1e-10
    
    def test_point_distance(self):
        """Test distance from point to plane."""
        # XY plane at Z=0
        plane = Plane3(Vec3(0, 0, 1), 0)
        
        # Point above plane
        point1 = Vec3(5, 5, 10)
        assert plane.distance_to_point(point1) == pytest.approx(10.0)
        
        # Point below plane
        point2 = Vec3(5, 5, -10)
        assert plane.distance_to_point(point2) == pytest.approx(-10.0)
        
        # Point on plane
        point3 = Vec3(5, 5, 0)
        assert plane.distance_to_point(point3) == pytest.approx(0.0)
    
    def test_point_projection(self):
        """Test projecting point onto plane."""
        # XY plane at Z=5
        plane = Plane3(Vec3(0, 0, 1), 5)
        
        # Project point onto plane
        point = Vec3(3, 4, 10)
        projected = plane.project_point(point)
        
        assert projected == Vec3(3, 4, 5)
        assert plane.distance_to_point(projected) == pytest.approx(0.0)
    
    def test_contains_point(self):
        """Test if plane contains point."""
        plane = Plane3(Vec3(0, 0, 1), 5)
        
        # Points on plane
        assert plane.contains_point(Vec3(0, 0, 5)) == True
        assert plane.contains_point(Vec3(100, -50, 5)) == True
        
        # Points not on plane
        assert plane.contains_point(Vec3(0, 0, 4.9)) == False
        assert plane.contains_point(Vec3(0, 0, 5.1)) == False
    
    def test_line_intersection(self):
        """Test line-plane intersection."""
        # XY plane at Z=5
        plane = Plane3(Vec3(0, 0, 1), 5)
        
        # Line perpendicular to plane
        line1 = Line3(Vec3(0, 0, 0), Vec3(0, 0, 10))
        intersection1 = plane.intersect_line(line1)
        assert intersection1 is not None
        assert intersection1 == Vec3(0, 0, 5)
        
        # Line at angle to plane
        line2 = Line3(Vec3(0, 0, 0), Vec3(10, 10, 10))
        intersection2 = plane.intersect_line(line2)
        assert intersection2 is not None
        assert intersection2.z == pytest.approx(5.0)
        assert intersection2.x == pytest.approx(5.0)
        assert intersection2.y == pytest.approx(5.0)
        
        # Line parallel to plane (no intersection)
        line3 = Line3(Vec3(0, 0, 10), Vec3(10, 0, 10))
        intersection3 = plane.intersect_line(line3)
        assert intersection3 is None
        
        # Line in plane (infinite intersections, returns None)
        line4 = Line3(Vec3(0, 0, 5), Vec3(10, 0, 5))
        intersection4 = plane.intersect_line(line4)
        assert intersection4 is None
    
    def test_ray_intersection(self):
        """Test ray-plane intersection (half-line)."""
        plane = Plane3(Vec3(0, 0, 1), 5)
        
        # Ray pointing toward plane
        ray_origin = Vec3(0, 0, 0)
        ray_direction = Vec3(0, 0, 1)
        intersection = plane.intersect_ray(ray_origin, ray_direction)
        assert intersection is not None
        assert intersection == Vec3(0, 0, 5)
        
        # Ray pointing away from plane
        ray_origin2 = Vec3(0, 0, 0)
        ray_direction2 = Vec3(0, 0, -1)
        intersection2 = plane.intersect_ray(ray_origin2, ray_direction2)
        assert intersection2 is None  # No intersection in ray direction
    
    def test_plane_plane_intersection(self):
        """Test intersection of two planes."""
        # XY plane at Z=0
        plane1 = Plane3(Vec3(0, 0, 1), 0)
        
        # YZ plane at X=0
        plane2 = Plane3(Vec3(1, 0, 0), 0)
        
        # Should intersect along Y axis
        line = plane1.intersect_plane(plane2)
        assert line is not None
        
        # Check that line is along Y axis
        direction = line.direction()
        assert abs(direction.x) < 1e-10
        assert abs(abs(direction.y) - 1.0) < 1e-10
        assert abs(direction.z) < 1e-10
        
        # Parallel planes (no intersection)
        plane3 = Plane3(Vec3(0, 0, 1), 5)  # XY plane at Z=5
        line2 = plane1.intersect_plane(plane3)
        assert line2 is None
    
    def test_three_plane_intersection(self):
        """Test intersection of three planes."""
        # XY plane at Z=5
        plane1 = Plane3(Vec3(0, 0, 1), 5)
        
        # YZ plane at X=3
        plane2 = Plane3(Vec3(1, 0, 0), 3)
        
        # XZ plane at Y=4
        plane3 = Plane3(Vec3(0, 1, 0), 4)
        
        # Should intersect at point (3, 4, 5)
        point = Plane3.intersect_three_planes(plane1, plane2, plane3)
        assert point is not None
        assert point == Vec3(3, 4, 5)
        
        # Three parallel planes (no intersection)
        plane4 = Plane3(Vec3(0, 0, 1), 0)
        plane5 = Plane3(Vec3(0, 0, 1), 1)
        plane6 = Plane3(Vec3(0, 0, 1), 2)
        point2 = Plane3.intersect_three_planes(plane4, plane5, plane6)
        assert point2 is None
    
    def test_reflection(self):
        """Test vector reflection across plane."""
        # XY plane (normal pointing up)
        plane = Plane3(Vec3(0, 0, 1), 0)
        
        # Vector pointing down and right
        vector = Vec3(1, 0, -1)
        reflected = plane.reflect_vector(vector)
        
        # Should be pointing up and right
        assert reflected == Vec3(1, 0, 1)
        
        # Vector parallel to plane should not change
        parallel = Vec3(1, 1, 0)
        reflected2 = plane.reflect_vector(parallel)
        assert reflected2 == parallel
    
    def test_angle_between_planes(self):
        """Test angle calculation between planes."""
        # XY plane
        plane1 = Plane3(Vec3(0, 0, 1), 0)
        
        # XZ plane
        plane2 = Plane3(Vec3(0, 1, 0), 0)
        
        # Should be 90 degrees
        angle = plane1.angle_to(plane2)
        assert angle == pytest.approx(math.pi / 2)
        
        # Same plane
        angle2 = plane1.angle_to(plane1)
        assert angle2 == pytest.approx(0.0)
        
        # Opposite facing planes
        plane3 = Plane3(Vec3(0, 0, -1), 0)
        angle3 = plane1.angle_to(plane3)
        assert angle3 == pytest.approx(math.pi)
    
    def test_signed_distance(self):
        """Test signed distance from point to plane."""
        # XY plane at Z=0, normal pointing up
        plane = Plane3(Vec3(0, 0, 1), 0)
        
        # Point above plane (positive distance)
        point1 = Vec3(0, 0, 5)
        assert plane.signed_distance_to_point(point1) == pytest.approx(5.0)
        
        # Point below plane (negative distance)
        point2 = Vec3(0, 0, -5)
        assert plane.signed_distance_to_point(point2) == pytest.approx(-5.0)
    
    def test_plane_from_normal_forms(self):
        """Test different normal forms of plane equation."""
        # Hesse normal form: ax + by + cz + d = 0
        # For XY plane at Z=5: 0x + 0y + 1z - 5 = 0
        plane = Plane3.from_coefficients(0, 0, 1, -5)
        
        assert plane.normal == Vec3(0, 0, 1)
        assert plane.d == 5.0
    
    def test_degenerate_cases(self):
        """Test degenerate plane cases."""
        # Three collinear points (should return None or raise)
        p1 = Vec3(0, 0, 0)
        p2 = Vec3(1, 0, 0)
        p3 = Vec3(2, 0, 0)
        
        with pytest.raises(ValueError):
            Plane3.from_three_points(p1, p2, p3)
        
        # Normal vector of zero length
        with pytest.raises(ValueError):
            Plane3(Vec3(0, 0, 0), 5)
    
    def test_plane_basis(self):
        """Test getting basis vectors in plane."""
        # XY plane
        plane = Plane3(Vec3(0, 0, 1), 0)
        
        u, v = plane.get_basis_vectors()
        
        # Both should be perpendicular to normal
        assert abs(Vec3.dot(u, plane.normal)) < 1e-10
        assert abs(Vec3.dot(v, plane.normal)) < 1e-10
        
        # Should be perpendicular to each other
        assert abs(Vec3.dot(u, v)) < 1e-10
        
        # Should be unit vectors
        assert abs(u.length() - 1.0) < 1e-10
        assert abs(v.length() - 1.0) < 1e-10
    
    def test_string_representation(self):
        """Test string representation."""
        plane = Plane3(Vec3(0, 0, 1), 5)
        s = str(plane)
        assert "normal=" in s
        assert "d=5" in s or "d=5.0" in s


if __name__ == "__main__":
    pytest.main([__file__, "-v"])