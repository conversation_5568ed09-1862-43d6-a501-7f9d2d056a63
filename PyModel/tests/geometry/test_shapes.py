"""
Test suite for shape classes (Triangle, Triangle3D).

Tests triangle operations and properties.
"""

import pytest
import math
from src.geometry.shapes import Triangle, Triangle3D
from src.geometry.primitives import Vec2, Vec3


class TestTriangle:
    """Test 2D Triangle functionality."""
    
    def test_creation(self):
        """Test triangle creation."""
        a = Vec2(0, 0)
        b = Vec2(3, 0)
        c = Vec2(0, 4)
        
        tri = Triangle(a, b, c)
        assert tri.a == a
        assert tri.b == b
        assert tri.c == c
    
    def test_area(self):
        """Test area calculation."""
        # Right triangle 3-4-5
        tri = Triangle(
            Vec2(0, 0),
            Vec2(3, 0),
            Vec2(0, 4)
        )
        
        assert tri.area() == pytest.approx(6.0)  # 0.5 * 3 * 4
        
        # Equilateral triangle
        side = 2.0
        height = side * math.sqrt(3) / 2
        tri2 = Triangle(
            Vec2(0, 0),
            Vec2(side, 0),
            Vec2(side/2, height)
        )
        
        expected_area = (side * height) / 2
        assert tri2.area() == pytest.approx(expected_area)
        
        # Degenerate triangle (collinear points)
        tri3 = Triangle(
            Vec2(0, 0),
            Vec2(1, 1),
            Vec2(2, 2)
        )
        assert tri3.area() == pytest.approx(0.0)
    
    def test_perimeter(self):
        """Test perimeter calculation."""
        # Right triangle 3-4-5
        tri = Triangle(
            Vec2(0, 0),
            Vec2(3, 0),
            Vec2(0, 4)
        )
        
        assert tri.perimeter() == pytest.approx(12.0)  # 3 + 4 + 5
    
    def test_centroid(self):
        """Test centroid calculation."""
        tri = Triangle(
            Vec2(0, 0),
            Vec2(6, 0),
            Vec2(0, 6)
        )
        
        centroid = tri.centroid()
        assert centroid.x == pytest.approx(2.0)  # (0 + 6 + 0) / 3
        assert centroid.y == pytest.approx(2.0)  # (0 + 0 + 6) / 3
    
    def test_circumcenter(self):
        """Test circumcenter calculation."""
        # Right triangle - circumcenter at midpoint of hypotenuse
        tri = Triangle(
            Vec2(0, 0),
            Vec2(4, 0),
            Vec2(0, 3)
        )
        
        circumcenter = tri.circumcenter()
        # Should be at midpoint of hypotenuse
        assert circumcenter.x == pytest.approx(2.0)
        assert circumcenter.y == pytest.approx(1.5)
        
        # Verify all vertices are equidistant
        r1 = Vec2.distance(circumcenter, tri.a)
        r2 = Vec2.distance(circumcenter, tri.b)
        r3 = Vec2.distance(circumcenter, tri.c)
        
        assert r1 == pytest.approx(r2)
        assert r2 == pytest.approx(r3)
    
    def test_incenter(self):
        """Test incenter calculation."""
        # Equilateral triangle has incenter at centroid
        side = 6.0
        height = side * math.sqrt(3) / 2
        tri = Triangle(
            Vec2(0, 0),
            Vec2(side, 0),
            Vec2(side/2, height)
        )
        
        incenter = tri.incenter()
        centroid = tri.centroid()
        
        # For equilateral triangle, incenter = centroid
        assert incenter.x == pytest.approx(centroid.x)
        assert incenter.y == pytest.approx(centroid.y)
    
    def test_contains_point(self):
        """Test point containment."""
        tri = Triangle(
            Vec2(0, 0),
            Vec2(4, 0),
            Vec2(0, 3)
        )
        
        # Points inside
        assert tri.contains(Vec2(1, 1)) == True
        assert tri.contains(Vec2(2, 1)) == True
        
        # Point on edge
        assert tri.contains(Vec2(2, 0)) == True
        assert tri.contains(Vec2(0, 1.5)) == True
        
        # Vertices
        assert tri.contains(Vec2(0, 0)) == True
        assert tri.contains(Vec2(4, 0)) == True
        assert tri.contains(Vec2(0, 3)) == True
        
        # Points outside
        assert tri.contains(Vec2(-1, 1)) == False
        assert tri.contains(Vec2(5, 1)) == False
        assert tri.contains(Vec2(3, 3)) == False
    
    def test_barycentric_coordinates(self):
        """Test barycentric coordinate calculation."""
        tri = Triangle(
            Vec2(0, 0),
            Vec2(4, 0),
            Vec2(0, 4)
        )
        
        # Centroid has barycentric coords (1/3, 1/3, 1/3)
        centroid = tri.centroid()
        u, v, w = tri.barycentric(centroid)
        assert u == pytest.approx(1/3)
        assert v == pytest.approx(1/3)
        assert w == pytest.approx(1/3)
        
        # Vertex A has coords (1, 0, 0)
        u, v, w = tri.barycentric(tri.a)
        assert u == pytest.approx(1.0)
        assert v == pytest.approx(0.0)
        assert w == pytest.approx(0.0)
        
        # Point outside should have negative coordinate
        outside = Vec2(-1, -1)
        u, v, w = tri.barycentric(outside)
        assert u + v + w == pytest.approx(1.0)  # Always sum to 1
        assert min(u, v, w) < 0  # At least one negative
    
    def test_orientation(self):
        """Test triangle orientation."""
        # Counter-clockwise triangle
        tri_ccw = Triangle(
            Vec2(0, 0),
            Vec2(1, 0),
            Vec2(0, 1)
        )
        assert tri_ccw.is_counter_clockwise() == True
        
        # Clockwise triangle
        tri_cw = Triangle(
            Vec2(0, 0),
            Vec2(0, 1),
            Vec2(1, 0)
        )
        assert tri_cw.is_counter_clockwise() == False
    
    def test_angles(self):
        """Test angle calculations."""
        # Right triangle
        tri = Triangle(
            Vec2(0, 0),
            Vec2(3, 0),
            Vec2(0, 4)
        )
        
        angles = tri.angles()
        
        # Should have one 90-degree angle
        assert any(abs(angle - math.pi/2) < 1e-10 for angle in angles)
        
        # Sum of angles should be π
        assert sum(angles) == pytest.approx(math.pi)
    
    def test_edge_lengths(self):
        """Test edge length calculations."""
        # 3-4-5 right triangle
        tri = Triangle(
            Vec2(0, 0),
            Vec2(3, 0),
            Vec2(0, 4)
        )
        
        lengths = tri.edge_lengths()
        lengths_sorted = sorted(lengths)
        
        assert lengths_sorted[0] == pytest.approx(3.0)
        assert lengths_sorted[1] == pytest.approx(4.0)
        assert lengths_sorted[2] == pytest.approx(5.0)
    
    def test_is_degenerate(self):
        """Test degenerate triangle detection."""
        # Normal triangle
        tri1 = Triangle(
            Vec2(0, 0),
            Vec2(1, 0),
            Vec2(0, 1)
        )
        assert tri1.is_degenerate() == False
        
        # Collinear points
        tri2 = Triangle(
            Vec2(0, 0),
            Vec2(1, 1),
            Vec2(2, 2)
        )
        assert tri2.is_degenerate() == True
        
        # Two vertices coincident
        tri3 = Triangle(
            Vec2(0, 0),
            Vec2(0, 0),
            Vec2(1, 1)
        )
        assert tri3.is_degenerate() == True
    
    def test_bounding_box(self):
        """Test bounding box calculation."""
        tri = Triangle(
            Vec2(1, 2),
            Vec2(5, 3),
            Vec2(2, 7)
        )
        
        bbox = tri.bounding_box()
        assert bbox.min == Vec2(1, 2)
        assert bbox.max == Vec2(5, 7)


class TestTriangle3D:
    """Test 3D Triangle functionality."""
    
    def test_creation(self):
        """Test 3D triangle creation."""
        a = Vec3(0, 0, 0)
        b = Vec3(1, 0, 0)
        c = Vec3(0, 1, 0)
        
        tri = Triangle3D(a, b, c)
        assert tri.a == a
        assert tri.b == b
        assert tri.c == c
    
    def test_area(self):
        """Test 3D triangle area."""
        # Triangle in XY plane
        tri = Triangle3D(
            Vec3(0, 0, 0),
            Vec3(3, 0, 0),
            Vec3(0, 4, 0)
        )
        
        assert tri.area() == pytest.approx(6.0)
        
        # Triangle in 3D space
        tri2 = Triangle3D(
            Vec3(0, 0, 0),
            Vec3(1, 0, 0),
            Vec3(0, 1, 1)
        )
        
        # Area = 0.5 * |AB × AC|
        expected = 0.5 * math.sqrt(2)
        assert tri2.area() == pytest.approx(expected)
    
    def test_normal(self):
        """Test normal vector calculation."""
        # Triangle in XY plane, normal should point in Z
        tri = Triangle3D(
            Vec3(0, 0, 0),
            Vec3(1, 0, 0),
            Vec3(0, 1, 0)
        )
        
        normal = tri.normal()
        assert normal.x == pytest.approx(0.0)
        assert normal.y == pytest.approx(0.0)
        assert normal.z == pytest.approx(1.0)
        
        # Reversed winding
        tri2 = Triangle3D(
            Vec3(0, 0, 0),
            Vec3(0, 1, 0),
            Vec3(1, 0, 0)
        )
        
        normal2 = tri2.normal()
        assert normal2.z == pytest.approx(-1.0)
    
    def test_centroid(self):
        """Test 3D centroid calculation."""
        tri = Triangle3D(
            Vec3(0, 0, 0),
            Vec3(3, 0, 0),
            Vec3(0, 3, 3)
        )
        
        centroid = tri.centroid()
        assert centroid.x == pytest.approx(1.0)
        assert centroid.y == pytest.approx(1.0)
        assert centroid.z == pytest.approx(1.0)
    
    def test_plane(self):
        """Test getting triangle's plane."""
        tri = Triangle3D(
            Vec3(0, 0, 1),
            Vec3(1, 0, 1),
            Vec3(0, 1, 1)
        )
        
        plane = tri.plane()
        
        # Normal should be (0, 0, 1)
        assert plane.normal.z == pytest.approx(1.0)
        
        # Distance should be 1
        assert plane.d == pytest.approx(1.0)
        
        # All vertices should lie on plane
        assert plane.contains_point(tri.a) == True
        assert plane.contains_point(tri.b) == True
        assert plane.contains_point(tri.c) == True
    
    def test_barycentric_3d(self):
        """Test 3D barycentric coordinates."""
        tri = Triangle3D(
            Vec3(0, 0, 0),
            Vec3(4, 0, 0),
            Vec3(0, 4, 0)
        )
        
        # Point in triangle
        point = Vec3(1, 1, 0)
        u, v, w = tri.barycentric(point)
        
        assert u + v + w == pytest.approx(1.0)
        assert u >= 0 and v >= 0 and w >= 0
        
        # Reconstruct point from barycentric
        reconstructed = Vec3(
            u * tri.a.x + v * tri.b.x + w * tri.c.x,
            u * tri.a.y + v * tri.b.y + w * tri.c.y,
            u * tri.a.z + v * tri.b.z + w * tri.c.z
        )
        assert (reconstructed - point).length() < 1e-10
    
    def test_ray_intersection(self):
        """Test ray-triangle intersection."""
        tri = Triangle3D(
            Vec3(0, 0, 0),
            Vec3(1, 0, 0),
            Vec3(0, 1, 0)
        )
        
        # Ray hitting triangle
        origin = Vec3(0.25, 0.25, 1)
        direction = Vec3(0, 0, -1)
        
        hit, t = tri.ray_intersection(origin, direction)
        assert hit == True
        assert t == pytest.approx(1.0)
        
        # Ray missing triangle
        origin2 = Vec3(2, 2, 1)
        hit2, t2 = tri.ray_intersection(origin2, direction)
        assert hit2 == False
        
        # Ray parallel to triangle
        origin3 = Vec3(0, 0, 0)
        direction3 = Vec3(1, 0, 0)
        hit3, t3 = tri.ray_intersection(origin3, direction3)
        assert hit3 == False
    
    def test_project_to_2d(self):
        """Test projecting 3D triangle to 2D."""
        # Triangle in YZ plane
        tri = Triangle3D(
            Vec3(0, 0, 0),
            Vec3(0, 3, 0),
            Vec3(0, 0, 4)
        )
        
        # Project to YZ plane (remove X)
        tri2d = tri.project_to_plane('x')
        
        assert isinstance(tri2d, Triangle)
        assert tri2d.a == Vec2(0, 0)
        assert tri2d.b == Vec2(3, 0)
        assert tri2d.c == Vec2(0, 4)
    
    def test_edge_vectors(self):
        """Test edge vector calculations."""
        tri = Triangle3D(
            Vec3(0, 0, 0),
            Vec3(1, 0, 0),
            Vec3(0, 1, 0)
        )
        
        ab, bc, ca = tri.edge_vectors()
        
        assert ab == Vec3(1, 0, 0)
        assert bc == Vec3(-1, 1, 0)
        assert ca == Vec3(0, -1, 0)
    
    def test_is_degenerate_3d(self):
        """Test 3D degenerate triangle detection."""
        # Normal triangle
        tri1 = Triangle3D(
            Vec3(0, 0, 0),
            Vec3(1, 0, 0),
            Vec3(0, 1, 0)
        )
        assert tri1.is_degenerate() == False
        
        # Collinear points
        tri2 = Triangle3D(
            Vec3(0, 0, 0),
            Vec3(1, 1, 1),
            Vec3(2, 2, 2)
        )
        assert tri2.is_degenerate() == True
    
    def test_distance_to_point(self):
        """Test distance from point to triangle."""
        tri = Triangle3D(
            Vec3(0, 0, 0),
            Vec3(1, 0, 0),
            Vec3(0, 1, 0)
        )
        
        # Point directly above triangle center
        point = Vec3(0.333, 0.333, 1)
        dist = tri.distance_to_point(point)
        assert dist == pytest.approx(1.0)
        
        # Point on triangle
        point2 = Vec3(0.5, 0.5, 0)
        dist2 = tri.distance_to_point(point2)
        assert dist2 == pytest.approx(0.0)
    
    def test_string_representation(self):
        """Test string representations."""
        tri = Triangle3D(
            Vec3(0, 0, 0),
            Vec3(1, 0, 0),
            Vec3(0, 1, 0)
        )
        
        s = str(tri)
        assert "Triangle3D" in s
        assert "(0,0,0)" in s


if __name__ == "__main__":
    pytest.main([__file__, "-v"])