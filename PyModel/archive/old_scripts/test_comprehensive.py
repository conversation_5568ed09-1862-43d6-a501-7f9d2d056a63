#!/usr/bin/env python3
"""
Comprehensive Test Runner for BIM Backend

This script runs all tests and provides detailed reporting on test coverage,
missing tests, and implementation status.
"""

import sys
import os
import subprocess
import json
import time
from pathlib import Path
from typing import Dict, List, Tuple, Any
import importlib.util

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))


class ComprehensiveTestRunner:
    """Run comprehensive tests and analysis on the BIM Backend."""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.src_path = self.project_root / "src"
        self.test_path = self.project_root / "tests"
        self.results = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "python_version": sys.version,
            "modules_found": 0,
            "modules_tested": 0,
            "import_errors": [],
            "test_results": {},
            "coverage": {}
        }
        
    def find_all_python_modules(self) -> Dict[str, List[Path]]:
        """Find all Python modules in src directory."""
        modules = {}
        
        for category in ["geometry", "materials", "bim", "business", "api", "services", "output"]:
            category_path = self.src_path / category
            if category_path.exists():
                py_files = []
                for file in category_path.rglob("*.py"):
                    if file.name != "__init__.py" and not file.name.startswith("_"):
                        py_files.append(file.relative_to(self.src_path))
                modules[category] = py_files
                
        return modules
        
    def find_test_files(self) -> Dict[str, List[Path]]:
        """Find all test files."""
        test_files = {}
        
        for test_dir in self.test_path.iterdir():
            if test_dir.is_dir() and not test_dir.name.startswith("_"):
                py_files = []
                for file in test_dir.glob("test_*.py"):
                    py_files.append(file.relative_to(self.test_path))
                if py_files:
                    test_files[test_dir.name] = py_files
                    
        return test_files
        
    def check_module_imports(self, modules: Dict[str, List[Path]]) -> Dict[str, bool]:
        """Check if modules can be imported successfully."""
        import_status = {}
        
        for category, files in modules.items():
            for file_path in files:
                module_path = str(file_path).replace("/", ".").replace("\\", ".").replace(".py", "")
                
                try:
                    importlib.import_module(module_path)
                    import_status[module_path] = True
                except Exception as e:
                    import_status[module_path] = False
                    self.results["import_errors"].append({
                        "module": module_path,
                        "error": str(e)
                    })
                    
        return import_status
        
    def analyze_test_coverage(self, modules: Dict[str, List[Path]], 
                            test_files: Dict[str, List[Path]]) -> Dict[str, Any]:
        """Analyze which modules have tests."""
        coverage = {
            "by_category": {},
            "missing_tests": [],
            "total_modules": 0,
            "tested_modules": 0
        }
        
        # Flatten module list
        all_modules = []
        for category, files in modules.items():
            coverage["by_category"][category] = {
                "total": len(files),
                "tested": 0,
                "missing": []
            }
            for file in files:
                module_name = file.stem
                all_modules.append((category, module_name, file))
                
        coverage["total_modules"] = len(all_modules)
        
        # Check for corresponding tests
        for category, module_name, file_path in all_modules:
            test_name = f"test_{module_name}.py"
            has_test = False
            
            # Check if test exists in corresponding test directory
            if category in test_files:
                for test_file in test_files[category]:
                    if test_file.name == test_name:
                        has_test = True
                        coverage["by_category"][category]["tested"] += 1
                        break
                        
            if not has_test:
                coverage["missing_tests"].append(str(file_path))
                coverage["by_category"][category]["missing"].append(module_name)
            else:
                coverage["tested_modules"] += 1
                
        return coverage
        
    def run_pytest(self) -> Dict[str, Any]:
        """Run pytest and capture results."""
        print("\nRunning pytest...")
        
        try:
            # Run pytest with JSON report
            result = subprocess.run(
                [sys.executable, "-m", "pytest", "-v", "--tb=short", 
                 "--json-report", "--json-report-file=test_report.json"],
                capture_output=True,
                text=True,
                cwd=self.project_root
            )
            
            # Parse JSON report if it exists
            report_path = self.project_root / "test_report.json"
            if report_path.exists():
                with open(report_path) as f:
                    test_report = json.load(f)
                return {
                    "success": result.returncode == 0,
                    "summary": test_report.get("summary", {}),
                    "tests": len(test_report.get("tests", [])),
                    "stdout": result.stdout,
                    "stderr": result.stderr
                }
            else:
                return {
                    "success": False,
                    "error": "No test report generated",
                    "stdout": result.stdout,
                    "stderr": result.stderr
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
            
    def check_documentation(self) -> Dict[str, Any]:
        """Check documentation status."""
        docs = {
            "markdown_files": [],
            "api_docs": False,
            "test_docs": False,
            "readme": False
        }
        
        # Find all MD files
        for md_file in self.project_root.glob("*.md"):
            docs["markdown_files"].append(md_file.name)
            
        # Check for specific docs
        docs["readme"] = (self.project_root / "README.md").exists()
        docs["api_docs"] = (self.project_root / "API_DOCUMENTATION.md").exists()
        docs["test_docs"] = (self.project_root / "SESSION_TESTING_GUIDE.md").exists()
        
        return docs
        
    def generate_report(self) -> str:
        """Generate comprehensive test report."""
        report = []
        report.append("=" * 80)
        report.append("BIM BACKEND COMPREHENSIVE TEST REPORT")
        report.append("=" * 80)
        report.append(f"Generated: {self.results['timestamp']}")
        report.append(f"Python Version: {sys.version.split()[0]}")
        report.append("")
        
        # Module Summary
        report.append("MODULE SUMMARY")
        report.append("-" * 40)
        modules = self.find_all_python_modules()
        total_modules = sum(len(files) for files in modules.values())
        report.append(f"Total Modules: {total_modules}")
        
        for category, files in modules.items():
            report.append(f"  {category}: {len(files)} modules")
            
        # Import Status
        report.append("\nIMPORT STATUS")
        report.append("-" * 40)
        import_status = self.check_module_imports(modules)
        successful_imports = sum(1 for status in import_status.values() if status)
        report.append(f"Successful Imports: {successful_imports}/{len(import_status)}")
        
        if self.results["import_errors"]:
            report.append("\nImport Errors:")
            for error in self.results["import_errors"][:5]:
                report.append(f"  - {error['module']}: {error['error'][:50]}...")
                
        # Test Coverage
        report.append("\nTEST COVERAGE")
        report.append("-" * 40)
        test_files = self.find_test_files()
        coverage = self.analyze_test_coverage(modules, test_files)
        
        report.append(f"Modules with Tests: {coverage['tested_modules']}/{coverage['total_modules']} "
                     f"({coverage['tested_modules']/coverage['total_modules']*100:.1f}%)")
        
        report.append("\nBy Category:")
        for category, stats in coverage["by_category"].items():
            tested_pct = (stats["tested"] / stats["total"] * 100) if stats["total"] > 0 else 0
            report.append(f"  {category}: {stats['tested']}/{stats['total']} ({tested_pct:.1f}%)")
            
        # Missing Tests (top 10)
        if coverage["missing_tests"]:
            report.append("\nModules Missing Tests (top 10):")
            for module in coverage["missing_tests"][:10]:
                report.append(f"  - {module}")
                
        # Documentation Status
        report.append("\nDOCUMENTATION STATUS")
        report.append("-" * 40)
        docs = self.check_documentation()
        report.append(f"Markdown Files: {len(docs['markdown_files'])}")
        report.append(f"README.md: {'✓' if docs['readme'] else '✗'}")
        report.append(f"API Documentation: {'✓' if docs['api_docs'] else '✗'}")
        report.append(f"Testing Guide: {'✓' if docs['test_docs'] else '✓'}")
        
        # PyTest Results
        report.append("\nPYTEST RESULTS")
        report.append("-" * 40)
        pytest_results = self.run_pytest()
        
        if pytest_results.get("success"):
            report.append("Status: PASSED ✓")
            if "summary" in pytest_results:
                summary = pytest_results["summary"]
                report.append(f"Tests Run: {pytest_results.get('tests', 'Unknown')}")
        else:
            report.append("Status: FAILED ✗")
            report.append(f"Error: {pytest_results.get('error', 'Unknown error')}")
            
        # Recommendations
        report.append("\nRECOMMENDATIONS")
        report.append("-" * 40)
        
        if coverage['tested_modules'] / coverage['total_modules'] < 0.8:
            report.append("1. CRITICAL: Increase test coverage to at least 80%")
            
        if len(self.results["import_errors"]) > 0:
            report.append("2. HIGH: Fix import errors in modules")
            
        if not docs["api_docs"]:
            report.append("3. MEDIUM: Create API documentation")
            
        report.append("\n" + "=" * 80)
        
        return "\n".join(report)
        
    def save_results(self):
        """Save detailed results to JSON."""
        with open(self.project_root / "test_results.json", "w") as f:
            json.dump(self.results, f, indent=2)
            

def main():
    """Run comprehensive testing."""
    runner = ComprehensiveTestRunner()
    report = runner.generate_report()
    
    print(report)
    
    # Save results
    runner.save_results()
    
    # Save report
    with open(runner.project_root / "COMPREHENSIVE_TEST_REPORT.md", "w") as f:
        f.write("# Comprehensive Test Report\n\n")
        f.write("```\n")
        f.write(report)
        f.write("\n```\n")
        
    print(f"\nDetailed results saved to test_results.json")
    print(f"Report saved to COMPREHENSIVE_TEST_REPORT.md")
    

if __name__ == "__main__":
    main()