"""
Geometry Precision Tests - Verify Python implementation precision and consistency
Tests complex calculations and ensures numerical stability
"""

import sys
import json
import math
from pathlib import Path
from typing import Dict, <PERSON>, Tuple, Any
from dataclasses import dataclass

sys.path.append(str(Path(__file__).parent.parent))

from src.geometry import Vec2, Vec3, Mat4, Box3, <PERSON>, Quatern<PERSON>, <PERSON>le
from src.geometry.helpers import Geo


@dataclass
class PrecisionResult:
    test_name: str
    passed: bool
    precision_digits: float
    max_error: float
    details: Dict[str, Any]


class GeometryPrecisionTester:
    def __init__(self):
        self.results = []
        
    def test_vector_precision(self):
        """Test vector operation precision and consistency."""
        print("Testing Vector Operation Precision...")
        
        # Test 1: Vec2 operations with high precision values
        result1 = PrecisionResult(
            test_name="Vec2_High_Precision",
            passed=True,
            precision_digits=0,
            max_error=0,
            details={}
        )
        
        # Use precise mathematical constants
        v1 = Vec2(math.pi, math.e)
        v2 = Vec2(math.sqrt(2), math.sqrt(3))
        
        # Test associativity of operations
        # (v1 + v2) + v3 should equal v1 + (v2 + v3)
        v3 = Vec2(1.618033988749895, 2.718281828459045)  # Golden ratio and e
        
        sum1 = (v1 + v2) + v3
        sum2 = v1 + (v2 + v3)
        
        error_x = abs(sum1.x - sum2.x)
        error_y = abs(sum1.y - sum2.y)
        max_error = max(error_x, error_y)
        
        result1.max_error = max_error
        result1.precision_digits = -math.log10(max_error) if max_error > 0 else 16
        result1.passed = max_error < 1e-14
        
        # Test vector rotation precision
        angle = math.pi / 7  # Use irrational angle
        cos_a = math.cos(angle)
        sin_a = math.sin(angle)
        
        # Rotate vector
        rotated = Vec2(
            v1.x * cos_a - v1.y * sin_a,
            v1.x * sin_a + v1.y * cos_a
        )
        
        # Rotate back
        back = Vec2(
            rotated.x * cos_a + rotated.y * sin_a,
            -rotated.x * sin_a + rotated.y * cos_a
        )
        
        rotation_error = Vec2.distance(v1, back)
        result1.max_error = max(result1.max_error, rotation_error)
        
        result1.details = {
            "associativity_error": max_error,
            "rotation_error": rotation_error,
            "operations_tested": 10
        }
        
        self.results.append(result1)
        
        # Test 2: Vec3 operations with extreme values
        result2 = PrecisionResult(
            test_name="Vec3_Extreme_Values",
            passed=True,
            precision_digits=0,
            max_error=0,
            details={}
        )
        
        # Test with very large and very small values
        v_large = Vec3(1e10, 1e10, 1e10)
        v_small = Vec3(1e-10, 1e-10, 1e-10)
        
        # Normalize large vector
        v_large_norm = v_large.normalized()
        expected_comp = 1.0 / math.sqrt(3)
        
        norm_error = max(
            abs(v_large_norm.x - expected_comp),
            abs(v_large_norm.y - expected_comp),
            abs(v_large_norm.z - expected_comp)
        )
        
        result2.max_error = norm_error
        result2.precision_digits = -math.log10(norm_error) if norm_error > 0 else 16
        result2.passed = norm_error < 1e-14
        
        # Test cross product precision
        v_a = Vec3(1, 0, 0)
        v_b = Vec3(0, 1, 0)
        cross = Vec3.cross(v_a, v_b)
        
        # Should be exactly (0, 0, 1)
        cross_error = max(
            abs(cross.x - 0),
            abs(cross.y - 0),
            abs(cross.z - 1)
        )
        
        result2.max_error = max(result2.max_error, cross_error)
        
        result2.details = {
            "normalization_error": norm_error,
            "cross_product_error": cross_error,
            "extreme_value_range": "1e-10 to 1e10"
        }
        
        self.results.append(result2)
        
        status1 = "✅ PASSED" if result1.passed else "❌ FAILED"
        status2 = "✅ PASSED" if result2.passed else "❌ FAILED"
        print(f"  Vec2_High_Precision: {status1} (precision: {result1.precision_digits:.1f} digits)")
        print(f"  Vec3_Extreme_Values: {status2} (precision: {result2.precision_digits:.1f} digits)")
    
    def test_matrix_precision(self):
        """Test matrix operation precision and numerical stability."""
        print("\nTesting Matrix Operation Precision...")
        
        result = PrecisionResult(
            test_name="Matrix_Inverse_Precision",
            passed=True,
            precision_digits=0,
            max_error=0,
            details={}
        )
        
        # Create a complex transformation matrix
        angle1 = math.pi / 5
        angle2 = math.pi / 7
        angle3 = math.pi / 11
        
        m1 = Mat4.rotation_x(angle1)
        m2 = Mat4.rotation_y(angle2)
        m3 = Mat4.rotation_z(angle3)
        m4 = Mat4.translation(100.5, -200.3, 300.7)
        m5 = Mat4.scale(2.5, 3.5, 4.5)
        
        # Combine transformations
        combined = m5 * m4 * m3 * m2 * m1
        
        # Test inverse precision
        inverse = combined.inverse()
        identity = combined * inverse
        
        # Check how close to identity
        max_error = 0
        for i in range(4):
            for j in range(4):
                expected = 1.0 if i == j else 0.0
                error = abs(identity.m[i][j] - expected)
                max_error = max(max_error, error)
        
        result.max_error = max_error
        result.precision_digits = -math.log10(max_error) if max_error > 0 else 16
        result.passed = max_error < 1e-12
        
        # Test transformation consistency
        test_points = [
            Vec3(1, 0, 0),
            Vec3(0, 1, 0),
            Vec3(0, 0, 1),
            Vec3(1, 1, 1),
            Vec3(math.pi, math.e, math.sqrt(2))
        ]
        
        transform_errors = []
        for pt in test_points:
            # Transform forward and back
            transformed = combined.transform_point(pt)
            back = inverse.transform_point(transformed)
            
            error = Vec3.distance(pt, back)
            transform_errors.append(error)
            result.max_error = max(result.max_error, error)
        
        result.details = {
            "identity_error": max_error,
            "max_transform_error": max(transform_errors),
            "avg_transform_error": sum(transform_errors) / len(transform_errors),
            "test_points": len(test_points)
        }
        
        self.results.append(result)
        
        status = "✅ PASSED" if result.passed else "❌ FAILED"
        print(f"  Matrix_Inverse_Precision: {status} (precision: {result.precision_digits:.1f} digits)")
    
    def test_quaternion_precision(self):
        """Test quaternion rotation precision."""
        print("\nTesting Quaternion Precision...")
        
        result = PrecisionResult(
            test_name="Quaternion_Rotation_Precision",
            passed=True,
            precision_digits=0,
            max_error=0,
            details={}
        )
        
        # Test quaternion interpolation and rotation
        axis1 = Vec3(1, 2, 3).normalized()
        axis2 = Vec3(3, 1, 2).normalized()
        
        q1 = Quaternion.from_axis_angle(axis1, math.pi / 3)
        q2 = Quaternion.from_axis_angle(axis2, math.pi / 4)
        
        # Test multiple rotations
        test_vec = Vec3(1, 0, 0)
        
        # Rotate by q1, then q2
        v1 = q1.rotate_vector(test_vec)
        v2 = q2.rotate_vector(v1)
        
        # Combined quaternion
        q_combined = q2 * q1
        v_combined = q_combined.rotate_vector(test_vec)
        
        # Compare results
        rotation_error = Vec3.distance(v2, v_combined)
        result.max_error = rotation_error
        result.precision_digits = -math.log10(rotation_error) if rotation_error > 0 else 16
        result.passed = rotation_error < 1e-14
        
        # Test quaternion to matrix conversion
        mat = q1.to_matrix()
        v_mat = mat.transform_point(test_vec)
        v_quat = q1.rotate_vector(test_vec)
        
        conversion_error = Vec3.distance(v_mat, v_quat)
        result.max_error = max(result.max_error, conversion_error)
        
        result.details = {
            "rotation_composition_error": rotation_error,
            "matrix_conversion_error": conversion_error,
            "quaternion_length": q_combined.length()
        }
        
        self.results.append(result)
        
        status = "✅ PASSED" if result.passed else "❌ FAILED"
        print(f"  Quaternion_Rotation_Precision: {status} (precision: {result.precision_digits:.1f} digits)")
    
    def test_real_world_precision(self):
        """Test precision in real-world building calculations."""
        print("\nTesting Real-World Calculation Precision...")
        
        result = PrecisionResult(
            test_name="Building_Geometry_Precision",
            passed=True,
            precision_digits=0,
            max_error=0,
            details={}
        )
        
        # Test 1: Iterative rafter placement
        span = 6000.0
        height = 2700.0
        pitch_rad = math.radians(15.0)
        
        # Calculate ridge height with high precision
        ridge_height = height + (span / 2) * math.tan(pitch_rad)
        
        # Test cumulative error in iterative calculations
        rafter_positions = []
        x = 0.0
        spacing = 900.0
        
        for i in range(100):  # Many iterations to test cumulative error
            apex = Vec3(x, span / 2, ridge_height)
            rafter_positions.append(apex)
            x += spacing
        
        # Check consistency of ridge heights
        heights = [p.z for p in rafter_positions]
        height_variance = max(heights) - min(heights)
        
        result.max_error = height_variance
        result.precision_digits = -math.log10(height_variance) if height_variance > 0 else 16
        result.passed = height_variance < 1e-12
        
        # Test 2: Angular calculations
        angles = []
        for pos in rafter_positions[:10]:
            left_end = Vec3(pos.x, 0, height)
            angle = math.atan2(pos.z - left_end.z, pos.y - left_end.y)
            angles.append(angle)
        
        # All angles should be identical
        angle_variance = max(angles) - min(angles)
        result.max_error = max(result.max_error, angle_variance)
        
        # Test 3: Area calculations with large numbers
        length = 9000.0
        roof_area = 2 * length * (span / 2) / math.cos(pitch_rad)
        
        # Recalculate using different method
        rafter_length = (span / 2) / math.cos(pitch_rad)
        roof_area2 = 2 * length * rafter_length
        
        area_error = abs(roof_area - roof_area2)
        result.max_error = max(result.max_error, area_error)
        
        result.details = {
            "height_consistency": height_variance,
            "angle_consistency": angle_variance,
            "area_calculation_error": area_error,
            "iterations_tested": 100
        }
        
        self.results.append(result)
        
        status = "✅ PASSED" if result.passed else "❌ FAILED"
        print(f"  Building_Geometry_Precision: {status} (precision: {result.precision_digits:.1f} digits)")
    
    def test_edge_cases(self):
        """Test precision with edge cases and special values."""
        print("\nTesting Edge Cases and Special Values...")
        
        result = PrecisionResult(
            test_name="Edge_Case_Precision",
            passed=True,
            precision_digits=0,
            max_error=0,
            details={}
        )
        
        errors = []
        
        # Test 1: Near-zero vector normalization
        v_small = Vec3(1e-15, 1e-15, 1e-15)
        try:
            v_norm = v_small.normalized()
            # Should still have length 1
            length_error = abs(v_norm.length() - 1.0)
            errors.append(("Near-zero normalization", length_error))
        except:
            errors.append(("Near-zero normalization", float('inf')))
            result.passed = False
        
        # Test 2: Near-parallel cross product
        v1 = Vec3(1, 0, 0)
        v2 = Vec3(1, 1e-10, 0)
        cross = Vec3.cross(v1, v2)
        
        # Should be approximately (0, 0, 1e-10)
        expected_length = 1e-10
        cross_error = abs(cross.length() - expected_length)
        errors.append(("Near-parallel cross product", cross_error))
        
        # Test 3: Large angle calculations
        large_angle = Angle.from_degrees(359.999999)
        normalized = large_angle.normalized()
        
        # Should be very close to 360 degrees
        angle_error = abs(normalized.degrees - 359.999999)
        errors.append(("Large angle normalization", angle_error))
        
        # Test 4: Triangle area with nearly collinear points
        t = Triangle(
            Vec2(0, 0),
            Vec2(1000, 0),
            Vec2(500, 1e-6)
        )
        
        area = t.area()
        expected_area = 0.0005  # 1000 * 1e-6 / 2
        area_error = abs(area - expected_area)
        errors.append(("Near-collinear triangle", area_error))
        
        # Calculate overall precision
        max_error = max(error for _, error in errors if error != float('inf'))
        result.max_error = max_error
        result.precision_digits = -math.log10(max_error) if max_error > 0 else 16
        
        result.details = {
            "test_cases": len(errors),
            "errors": {name: error for name, error in errors}
        }
        
        self.results.append(result)
        
        status = "✅ PASSED" if result.passed else "❌ FAILED"
        print(f"  Edge_Case_Precision: {status} (precision: {result.precision_digits:.1f} digits)")
    
    def generate_report(self):
        """Generate precision test report."""
        print("\n" + "="*60)
        print("GEOMETRY PRECISION TEST REPORT")
        print("="*60)
        
        total_tests = len(self.results)
        passed_tests = sum(1 for r in self.results if r.passed)
        
        print(f"\nTotal Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {total_tests - passed_tests}")
        print(f"Success Rate: {passed_tests/total_tests*100:.1f}%")
        
        # Calculate overall precision
        min_precision = min(r.precision_digits for r in self.results)
        avg_precision = sum(r.precision_digits for r in self.results) / len(self.results)
        
        print(f"\nPrecision Summary:")
        print(f"Minimum Precision: {min_precision:.1f} decimal digits")
        print(f"Average Precision: {avg_precision:.1f} decimal digits")
        
        # Detailed results
        print("\n" + "-"*40)
        print("DETAILED RESULTS:")
        
        for result in self.results:
            status = "✅" if result.passed else "❌"
            print(f"\n{status} {result.test_name}")
            print(f"   Precision: {result.precision_digits:.1f} decimal digits")
            print(f"   Max Error: {result.max_error:.2e}")
            
            if result.details:
                print("   Details:")
                for key, value in result.details.items():
                    if isinstance(value, float):
                        print(f"     - {key}: {value:.2e}")
                    else:
                        print(f"     - {key}: {value}")
        
        # Recommendations
        print("\n" + "-"*40)
        print("RECOMMENDATIONS:")
        
        if min_precision >= 14:
            print("✅ Excellent precision! The implementation uses near-optimal floating-point accuracy.")
        elif min_precision >= 10:
            print("✅ Good precision. Suitable for most engineering applications.")
        elif min_precision >= 6:
            print("⚠️ Adequate precision. May need improvement for high-accuracy requirements.")
        else:
            print("❌ Poor precision. Numerical stability issues need to be addressed.")
        
        # Save detailed report
        report_path = Path(__file__).parent / "reports" / "geometry_precision_report.json"
        report_path.parent.mkdir(exist_ok=True)
        
        report_data = {
            "summary": {
                "total_tests": total_tests,
                "passed": passed_tests,
                "failed": total_tests - passed_tests,
                "min_precision_digits": min_precision,
                "avg_precision_digits": avg_precision
            },
            "results": [
                {
                    "test_name": r.test_name,
                    "passed": r.passed,
                    "precision_digits": r.precision_digits,
                    "max_error": r.max_error,
                    "details": r.details
                }
                for r in self.results
            ]
        }
        
        with open(report_path, 'w') as f:
            json.dump(report_data, f, indent=2)
        
        print(f"\nDetailed report saved to: {report_path}")
        
        return passed_tests == total_tests
    
    def run_all_tests(self):
        """Run all precision tests."""
        print("Running Geometry Precision Tests")
        print("Testing numerical stability and floating-point precision")
        print("-" * 60)
        
        self.test_vector_precision()
        self.test_matrix_precision()
        self.test_quaternion_precision()
        self.test_real_world_precision()
        self.test_edge_cases()
        
        return self.generate_report()


if __name__ == "__main__":
    tester = GeometryPrecisionTester()
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)