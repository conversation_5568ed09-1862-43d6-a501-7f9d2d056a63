{"summary": {"total_tests": 6, "passed": 6, "failed": 0, "min_precision_digits": 12.64325981788721, "avg_precision_digits": 15.391351236872884}, "results": [{"test_name": "Vec2_High_Precision", "passed": true, "precision_digits": 16, "max_error": 0.0, "details": {"associativity_error": 0.0, "rotation_error": 0.0, "operations_tested": 10}}, {"test_name": "Vec3_Extreme_Values", "passed": true, "precision_digits": 15.954589770191003, "max_error": 1.1102230246251565e-16, "details": {"normalization_error": 1.1102230246251565e-16, "cross_product_error": 0, "extreme_value_range": "1e-10 to 1e10"}}, {"test_name": "Matrix_Inverse_Precision", "passed": true, "precision_digits": 12.64325981788721, "max_error": 2.2737367544323206e-13, "details": {"identity_error": 2.2737367544323206e-13, "max_transform_error": 8.526512829121202e-14, "avg_transform_error": 3.634509129318178e-14, "test_points": 5}}, {"test_name": "Quaternion_Rotation_Precision", "passed": true, "precision_digits": 15.750257833159099, "max_error": 1.7772239894833365e-16, "details": {"rotation_composition_error": 1.7772239894833365e-16, "matrix_conversion_error": 1.1102230246251565e-16, "quaternion_length": 1.0}}, {"test_name": "Building_Geometry_Precision", "passed": true, "precision_digits": 16, "max_error": 0.0, "details": {"height_consistency": 0.0, "angle_consistency": 0.0, "area_calculation_error": 0.0, "iterations_tested": 100}}, {"test_name": "Edge_Case_Precision", "passed": true, "precision_digits": 16, "max_error": 0.0, "details": {"test_cases": 4, "errors": {"Near-zero normalization": 0.0, "Near-parallel cross product": 0.0, "Large angle normalization": 0.0, "Near-collinear triangle": 0.0}}}]}