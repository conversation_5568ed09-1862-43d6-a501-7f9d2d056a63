"""
Geometry Shapes Alignment Tests - Session 2
Tests BoundingBox, Rect, Triangle, and other shape classes for C#/Python alignment.
"""

import sys
import math
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent.parent))

from src.geometry import Vec2, Vec3, Box2, Box3, Line1, Line2, Line3, TriIndex
from src.geometry.boxes import Rect
from src.geometry.shapes import Triangle
import json


class AlignmentTestResult:
    def __init__(self, test_name: str):
        self.test_name = test_name
        self.passed = True
        self.details = []
        self.failures = []
    
    def assert_equal(self, actual, expected, tolerance=1e-6, message=""):
        """Assert values are equal within tolerance."""
        if isinstance(actual, (int, float)) and isinstance(expected, (int, float)):
            if abs(actual - expected) > tolerance:
                self.passed = False
                self.failures.append(f"{message}: Expected {expected}, got {actual}")
                return False
        elif actual != expected:
            self.passed = False
            self.failures.append(f"{message}: Expected {expected}, got {actual}")
            return False
        return True
    
    def assert_true(self, condition: bool, message=""):
        """Assert condition is true."""
        if not condition:
            self.passed = False
            self.failures.append(f"{message}: Expected True, got False")
            return False
        return True
    
    def assert_false(self, condition: bool, message=""):
        """Assert condition is false."""
        if condition:
            self.passed = False
            self.failures.append(f"{message}: Expected False, got True")
            return False
        return True
    
    def add_detail(self, detail: str):
        self.details.append(detail)


class GeometryShapesAlignmentTests:
    def __init__(self):
        self.results = []
    
    def test_box2_operations(self):
        """Test Box2 (2D bounding box) operations match C# implementation."""
        result = AlignmentTestResult("Box2 Operations")
        
        # Test creation from min/max
        box = Box2(Vec2(1.0, 2.0), Vec2(5.0, 7.0))
        result.assert_equal(box.min.x, 1.0, message="Box2 min.x")
        result.assert_equal(box.min.y, 2.0, message="Box2 min.y")
        result.assert_equal(box.max.x, 5.0, message="Box2 max.x")
        result.assert_equal(box.max.y, 7.0, message="Box2 max.y")
        
        # Test size and center
        size = box.size()
        result.assert_equal(size.x, 4.0, message="Box2 size x")
        result.assert_equal(size.y, 5.0, message="Box2 size y")
        
        center = box.center()
        result.assert_equal(center.x, 3.0, message="Box2 center x")
        result.assert_equal(center.y, 4.5, message="Box2 center y")
        
        # Test area
        result.assert_equal(box.area(), 20.0, message="Box2 area")
        
        # Test contains point
        result.assert_true(box.contains(Vec2(3.0, 4.0)), "Box2 contains point inside")
        result.assert_false(box.contains(Vec2(0.0, 0.0)), "Box2 contains point outside")
        result.assert_true(box.contains(Vec2(1.0, 2.0)), "Box2 contains min corner")
        result.assert_true(box.contains(Vec2(5.0, 7.0)), "Box2 contains max corner")
        
        # Test intersects
        box2 = Box2(Vec2(3.0, 4.0), Vec2(8.0, 10.0))
        result.assert_true(box.intersects(box2), "Box2 intersects overlapping")
        
        box3 = Box2(Vec2(10.0, 10.0), Vec2(15.0, 15.0))
        result.assert_false(box.intersects(box3), "Box2 intersects non-overlapping")
        
        # Test union
        union_box = Box2.union(box, box2)
        result.assert_equal(union_box.min.x, 1.0, message="Box2 union min.x")
        result.assert_equal(union_box.min.y, 2.0, message="Box2 union min.y")
        result.assert_equal(union_box.max.x, 8.0, message="Box2 union max.x")
        result.assert_equal(union_box.max.y, 10.0, message="Box2 union max.y")
        
        # Test expand
        expanded = box.expanded(1.0)
        result.assert_equal(expanded.min.x, 0.0, message="Box2 expanded min.x")
        result.assert_equal(expanded.min.y, 1.0, message="Box2 expanded min.y")
        result.assert_equal(expanded.max.x, 6.0, message="Box2 expanded max.x")
        result.assert_equal(expanded.max.y, 8.0, message="Box2 expanded max.y")
        
        # Test from points
        points = [Vec2(3.0, 1.0), Vec2(7.0, 4.0), Vec2(2.0, 9.0)]
        from_points = Box2.from_points(points)
        result.assert_equal(from_points.min.x, 2.0, message="Box2 from points min.x")
        result.assert_equal(from_points.min.y, 1.0, message="Box2 from points min.y")
        result.assert_equal(from_points.max.x, 7.0, message="Box2 from points max.x")
        result.assert_equal(from_points.max.y, 9.0, message="Box2 from points max.y")
        
        result.add_detail(f"Tested {20} Box2 operations")
        self.results.append(result)
    
    def test_box3_operations(self):
        """Test Box3 (3D bounding box) operations match C# implementation."""
        result = AlignmentTestResult("Box3 Operations")
        
        # Test creation
        box = Box3(Vec3(1.0, 2.0, 3.0), Vec3(5.0, 7.0, 9.0))
        result.assert_equal(box.min.x, 1.0, message="Box3 min.x")
        result.assert_equal(box.min.y, 2.0, message="Box3 min.y")
        result.assert_equal(box.min.z, 3.0, message="Box3 min.z")
        result.assert_equal(box.max.x, 5.0, message="Box3 max.x")
        result.assert_equal(box.max.y, 7.0, message="Box3 max.y")
        result.assert_equal(box.max.z, 9.0, message="Box3 max.z")
        
        # Test size and center
        size = box.size()
        result.assert_equal(size.x, 4.0, message="Box3 size x")
        result.assert_equal(size.y, 5.0, message="Box3 size y")
        result.assert_equal(size.z, 6.0, message="Box3 size z")
        
        center = box.center()
        result.assert_equal(center.x, 3.0, message="Box3 center x")
        result.assert_equal(center.y, 4.5, message="Box3 center y")
        result.assert_equal(center.z, 6.0, message="Box3 center z")
        
        # Test volume
        result.assert_equal(box.volume(), 120.0, message="Box3 volume")
        
        # Test contains
        result.assert_true(box.contains(Vec3(3.0, 4.0, 5.0)), "Box3 contains point inside")
        result.assert_false(box.contains(Vec3(0.0, 0.0, 0.0)), "Box3 contains point outside")
        
        # Test expand
        expanded = box.expanded(1.0)
        result.assert_equal(expanded.min.x, 0.0, message="Box3 expanded min.x")
        result.assert_equal(expanded.max.x, 6.0, message="Box3 expanded max.x")
        
        # Test from points
        points = [Vec3(3.0, 1.0, 2.0), Vec3(7.0, 4.0, 5.0), Vec3(2.0, 9.0, 1.0)]
        from_points = Box3.from_points(points)
        result.assert_equal(from_points.min.x, 2.0, message="Box3 from points min.x")
        result.assert_equal(from_points.max.z, 5.0, message="Box3 from points max.z")
        
        result.add_detail(f"Tested {16} Box3 operations")
        self.results.append(result)
    
    def test_rect_operations(self):
        """Test Rect operations match C# implementation."""
        result = AlignmentTestResult("Rect Operations")
        
        # Test creation
        rect = Rect(10.0, 20.0, 30.0, 40.0)
        result.assert_equal(rect.x, 10.0, message="Rect x")
        result.assert_equal(rect.y, 20.0, message="Rect y")
        result.assert_equal(rect.width, 30.0, message="Rect width")
        result.assert_equal(rect.height, 40.0, message="Rect height")
        
        # Test computed properties
        result.assert_equal(rect.left, 10.0, message="Rect left")
        result.assert_equal(rect.top, 20.0, message="Rect top")
        result.assert_equal(rect.right, 40.0, message="Rect right")
        result.assert_equal(rect.bottom, 60.0, message="Rect bottom")
        
        # Test center
        center = rect.center()
        result.assert_equal(center.x, 25.0, message="Rect center x")
        result.assert_equal(center.y, 40.0, message="Rect center y")
        
        # Test area
        result.assert_equal(rect.area(), 1200.0, message="Rect area")
        
        # Test contains
        result.assert_true(rect.contains(Vec2(25.0, 40.0)), "Rect contains center")
        result.assert_false(rect.contains(Vec2(5.0, 5.0)), "Rect contains outside")
        result.assert_true(rect.contains(Vec2(10.0, 20.0)), "Rect contains top-left")
        
        # Test intersects
        rect2 = Rect(30.0, 40.0, 20.0, 30.0)
        result.assert_true(rect.intersects(rect2), "Rect intersects overlapping")
        
        rect3 = Rect(50.0, 70.0, 10.0, 10.0)
        result.assert_false(rect.intersects(rect3), "Rect intersects non-overlapping")
        
        # Test from corners
        from_corners = Rect.from_corners(Vec2(5.0, 10.0), Vec2(15.0, 25.0))
        result.assert_equal(from_corners.x, 5.0, message="Rect from corners x")
        result.assert_equal(from_corners.y, 10.0, message="Rect from corners y")
        result.assert_equal(from_corners.width, 10.0, message="Rect from corners width")
        result.assert_equal(from_corners.height, 15.0, message="Rect from corners height")
        
        result.add_detail(f"Tested {18} Rect operations")
        self.results.append(result)
    
    def test_line_operations(self):
        """Test Line1, Line2, and Line3 operations match C# implementation."""
        result = AlignmentTestResult("Line Operations")
        
        # Test Line1 (1D line segment)
        line1 = Line1(2.0, 8.0)
        result.assert_equal(line1.a, 2.0, message="Line1 a")
        result.assert_equal(line1.b, 8.0, message="Line1 b")
        result.assert_equal(line1.length(), 6.0, message="Line1 length")
        result.assert_equal(line1.center(), 5.0, message="Line1 center")
        result.assert_true(line1.contains(5.0), "Line1 contains point")
        result.assert_false(line1.contains(10.0), "Line1 not contains point")
        
        # Test Line2 (2D line segment)
        line2 = Line2(Vec2(1.0, 2.0), Vec2(4.0, 6.0))
        result.assert_equal(line2.a.x, 1.0, message="Line2 a.x")
        result.assert_equal(line2.b.y, 6.0, message="Line2 b.y")
        result.assert_equal(line2.length(), 5.0, message="Line2 length")
        
        center2 = line2.center()
        result.assert_equal(center2.x, 2.5, message="Line2 center x")
        result.assert_equal(center2.y, 4.0, message="Line2 center y")
        
        # Test Line3 (3D line segment)
        line3 = Line3(Vec3(0.0, 0.0, 0.0), Vec3(3.0, 4.0, 0.0))
        result.assert_equal(line3.length(), 5.0, message="Line3 length")
        
        center3 = line3.center()
        result.assert_equal(center3.x, 1.5, message="Line3 center x")
        result.assert_equal(center3.y, 2.0, message="Line3 center y")
        result.assert_equal(center3.z, 0.0, message="Line3 center z")
        
        # Test direction
        direction = line3.direction()
        result.assert_equal(direction.x, 0.6, message="Line3 direction x")
        result.assert_equal(direction.y, 0.8, message="Line3 direction y")
        
        result.add_detail(f"Tested {20} Line operations")
        self.results.append(result)
    
    def test_triangle_operations(self):
        """Test Triangle operations match C# implementation."""
        result = AlignmentTestResult("Triangle Operations")
        
        # Test basic triangle
        t = Triangle(Vec2(0.0, 0.0), Vec2(4.0, 0.0), Vec2(0.0, 3.0))
        
        # Test area (should be 6.0 for a 3-4-5 right triangle)
        result.assert_equal(t.area(), 6.0, message="Triangle area")
        
        # Test perimeter
        result.assert_equal(t.perimeter(), 12.0, message="Triangle perimeter")
        
        # Test centroid
        centroid = t.centroid()
        result.assert_equal(centroid.x, 4.0/3.0, tolerance=1e-6, message="Triangle centroid x")
        result.assert_equal(centroid.y, 1.0, message="Triangle centroid y")
        
        # Test contains point
        result.assert_true(t.contains(Vec2(1.0, 1.0)), "Triangle contains point inside")
        result.assert_false(t.contains(Vec2(3.0, 3.0)), "Triangle not contains point outside")
        result.assert_true(t.contains(Vec2(0.0, 0.0)), "Triangle contains vertex")
        
        # Test circumcircle
        center, radius = t.circumcircle()
        result.assert_equal(center.x, 2.0, message="Circumcircle center x")
        result.assert_equal(center.y, 1.5, message="Circumcircle center y")
        result.assert_equal(radius, 2.5, message="Circumcircle radius")
        
        # Test is_clockwise
        result.assert_false(t.is_clockwise(), "Triangle counter-clockwise")
        t_cw = Triangle(Vec2(0.0, 0.0), Vec2(0.0, 3.0), Vec2(4.0, 0.0))
        result.assert_true(t_cw.is_clockwise(), "Triangle clockwise")
        
        result.add_detail(f"Tested {12} Triangle operations")
        self.results.append(result)
    
    def test_triindex_operations(self):
        """Test TriIndex operations match C# implementation."""
        result = AlignmentTestResult("TriIndex Operations")
        
        # Test creation
        tri = TriIndex(0, 1, 2)
        result.assert_equal(tri.a, 0, message="TriIndex a")
        result.assert_equal(tri.b, 1, message="TriIndex b")
        result.assert_equal(tri.c, 2, message="TriIndex c")
        
        # Test flipped
        flipped = tri.flipped()
        result.assert_equal(flipped.a, 0, message="TriIndex flipped a")
        result.assert_equal(flipped.b, 2, message="TriIndex flipped b")
        result.assert_equal(flipped.c, 1, message="TriIndex flipped c")
        
        # Test contains
        result.assert_true(tri.contains(0), "TriIndex contains vertex 0")
        result.assert_true(tri.contains(1), "TriIndex contains vertex 1")
        result.assert_true(tri.contains(2), "TriIndex contains vertex 2")
        result.assert_false(tri.contains(3), "TriIndex not contains vertex 3")
        
        # Test to_array
        arr = tri.to_array()
        result.assert_equal(arr[0], 0, message="TriIndex array[0]")
        result.assert_equal(arr[1], 1, message="TriIndex array[1]")
        result.assert_equal(arr[2], 2, message="TriIndex array[2]")
        
        result.add_detail(f"Tested {10} TriIndex operations")
        self.results.append(result)
    
    def generate_report(self):
        """Generate detailed test report."""
        total_tests = len(self.results)
        passed_tests = sum(1 for r in self.results if r.passed)
        
        report = [
            "# Geometry Shapes Alignment Test Report",
            f"\nTotal Tests: {total_tests}",
            f"Passed: {passed_tests}",
            f"Failed: {total_tests - passed_tests}",
            f"Success Rate: {(passed_tests / total_tests * 100):.1f}%\n",
            "## Test Results\n"
        ]
        
        for result in self.results:
            status = "✅ PASSED" if result.passed else "❌ FAILED"
            report.append(f"### {result.test_name}: {status}")
            
            if result.details:
                report.append("\n**Details:**")
                for detail in result.details:
                    report.append(f"- {detail}")
            
            if result.failures:
                report.append("\n**Failures:**")
                for failure in result.failures:
                    report.append(f"- ❌ {failure}")
            
            report.append("")
        
        # Add summary
        if passed_tests == total_tests:
            report.append("\n## Summary")
            report.append("✅ **All tests passed!** The Python geometry shapes implementation is 100% aligned with the C# version.")
        else:
            report.append("\n## Summary")
            report.append("❌ **Some tests failed.** Please review the failures above and fix the implementation.")
        
        return "\n".join(report)
    
    def run_all_tests(self):
        """Run all shape tests."""
        print("Running Geometry Shapes Alignment Tests...")
        print("-" * 50)
        
        self.test_box2_operations()
        print("✓ Box2 operations tested")
        
        self.test_box3_operations()
        print("✓ Box3 operations tested")
        
        self.test_rect_operations()
        print("✓ Rect operations tested")
        
        self.test_line_operations()
        print("✓ Line operations tested")
        
        self.test_triangle_operations()
        print("✓ Triangle operations tested")
        
        self.test_triindex_operations()
        print("✓ TriIndex operations tested")
        
        print("-" * 50)
        
        # Save report
        report = self.generate_report()
        report_path = Path(__file__).parent / "results" / "shapes_report.md"
        report_path.parent.mkdir(exist_ok=True)
        report_path.write_text(report)
        
        print(f"\nReport saved to: {report_path}")
        
        # Print summary
        total = len(self.results)
        passed = sum(1 for r in self.results if r.passed)
        print(f"\nSummary: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
        
        return passed == total


if __name__ == "__main__":
    tester = GeometryShapesAlignmentTests()
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)