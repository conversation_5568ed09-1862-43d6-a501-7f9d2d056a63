#!/usr/bin/env python3
"""
Comprehensive test script for all 4 roof types with proper C# alignment.

This script generates carports with all 4 roof types and validates:
1. Flat - with overhang
2. Gable - no overhang
3. Awning - no overhang
4. AttachedAwning - no overhang, no right columns

Run: python test_all_roof_types_comprehensive.py
"""

import os
import sys
from pathlib import Path
from datetime import datetime

# Add the project to path
sys.path.insert(0, str(Path(__file__).parent))

from src.business.building_input import BuildingInput, CarportRoofType
from src.business.engineering import EngData
from src.ifc_generation.carport_generator import CarportIFCGenerator


def generate_all_roof_types():
    """Generate comprehensive test set for all 4 roof types."""
    
    generator = CarportIFCGenerator()
    print("=" * 80)
    print("COMPREHENSIVE ROOF TYPE TEST - C# ALIGNED")
    print("=" * 80)
    print(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Test configurations for each roof type
    test_configs = [
        # FLAT ROOF TESTS - WITH OVERHANG
        {
            "name": "Flat Roof - Small with 300mm overhang",
            "span": 6000,
            "length": 6000,
            "height": 2400,
            "roof_type": CarportRoofType.FLAT,
            "pitch": 5,
            "bays": 2,
            "overhang": 300,
            "expected": {
                "rafter_count": 3,  # bays + 1
                "purlin_count_per_bay": 5,  # default for flat
                "left_columns": 3,
                "right_columns": 3,
                "has_overhang": True
            }
        },
        {
            "name": "Flat Roof - Large with 600mm overhang",
            "span": 9000,
            "length": 12000,
            "height": 3000,
            "roof_type": CarportRoofType.FLAT,
            "pitch": 10,
            "bays": 4,
            "overhang": 600,
            "expected": {
                "rafter_count": 5,
                "purlin_count_per_bay": 5,
                "left_columns": 5,
                "right_columns": 5,
                "has_overhang": True
            }
        },
        
        # GABLE ROOF TESTS - NO OVERHANG
        {
            "name": "Gable Roof - Standard (no overhang)",
            "span": 6000,
            "length": 9000,
            "height": 2700,
            "roof_type": CarportRoofType.GABLE,
            "pitch": 15,
            "bays": 3,
            "overhang": 0,  # Should be ignored for gable
            "expected": {
                "rafter_count": 8,  # 2 per frame (left + right) * 4 frames
                "purlin_count_per_bay": 3,  # default for gable
                "left_columns": 4,
                "right_columns": 4,
                "has_overhang": False
            }
        },
        {
            "name": "Gable Roof - Steep pitch",
            "span": 7000,
            "length": 7000,
            "height": 2700,
            "roof_type": CarportRoofType.GABLE,
            "pitch": 25,
            "bays": 2,
            "overhang": 300,  # Should be ignored
            "expected": {
                "rafter_count": 6,  # 2 per frame * 3 frames
                "purlin_count_per_bay": 3,
                "left_columns": 3,
                "right_columns": 3,
                "has_overhang": False
            }
        },
        
        # AWNING ROOF TESTS - NO OVERHANG
        {
            "name": "Awning Roof - Basic (no overhang)",
            "span": 6000,
            "length": 6000,
            "height": 2400,
            "roof_type": CarportRoofType.AWNING,
            "pitch": 5,
            "bays": 2,
            "overhang": 0,
            "expected": {
                "rafter_count": 3,
                "purlin_count_per_bay": 5,
                "left_columns": 3,
                "right_columns": 3,
                "has_overhang": False
            }
        },
        {
            "name": "Awning Roof - Large",
            "span": 8000,
            "length": 10000,
            "height": 2700,
            "roof_type": CarportRoofType.AWNING,
            "pitch": 7,
            "bays": 4,
            "overhang": 400,  # Should be ignored
            "expected": {
                "rafter_count": 5,
                "purlin_count_per_bay": 5,
                "left_columns": 5,
                "right_columns": 5,
                "has_overhang": False
            }
        },
        
        # ATTACHED AWNING TESTS - NO OVERHANG, NO RIGHT COLUMNS
        {
            "name": "Attached Awning - Small (no right columns)",
            "span": 5000,
            "length": 6000,
            "height": 2400,
            "roof_type": CarportRoofType.ATTACHED_AWNING,
            "pitch": 5,
            "bays": 2,
            "overhang": 0,
            "expected": {
                "rafter_count": 3,
                "purlin_count_per_bay": 5,
                "left_columns": 3,
                "right_columns": 0,  # No right columns!
                "has_overhang": False
            }
        },
        {
            "name": "Attached Awning - Wide span",
            "span": 9000,
            "length": 9000,
            "height": 2700,
            "roof_type": CarportRoofType.ATTACHED_AWNING,
            "pitch": 8,
            "bays": 3,
            "overhang": 500,  # Should be ignored
            "expected": {
                "rafter_count": 4,
                "purlin_count_per_bay": 5,
                "left_columns": 4,
                "right_columns": 0,  # No right columns!
                "has_overhang": False
            }
        }
    ]
    
    # Create output directory
    output_dir = Path("test_output/comprehensive_roof_types")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    results = []
    
    for i, config in enumerate(test_configs, 1):
        print(f"\n[{i}/{len(test_configs)}] Generating: {config['name']}")
        print(f"  Type: {config['roof_type'].name}")
        print(f"  Dimensions: {config['span']}mm x {config['length']}mm x {config['height']}mm")
        print(f"  Pitch: {config['pitch']}°, Bays: {config['bays']}")
        print(f"  Overhang: {config['overhang']}mm (effective: {'YES' if config['expected']['has_overhang'] else 'NO'})")
        
        try:
            # Generate carport
            filename = f"{i:02d}_{config['roof_type'].name.lower()}_{config['span']}x{config['length']}_b{config['bays']}.ifc"
            path = generator.generate_carport(
                span=config['span'],
                length=config['length'],
                height=config['height'],
                roof_type=config['roof_type'],
                pitch=config['pitch'],
                bays=config['bays'],
                overhang=config['overhang'],
                slab=True,
                output_name=filename
            )
            
            # Move to test directory
            new_path = output_dir / filename
            if path.exists():
                # Remove target if it already exists
                if new_path.exists():
                    new_path.unlink()
                path.rename(new_path)
                
            print(f"  ✅ Generated: {new_path}")
            
            # Validate expectations
            print(f"  Expected:")
            print(f"    - Rafters: {config['expected']['rafter_count']}")
            print(f"    - Purlins per bay: {config['expected']['purlin_count_per_bay']}")
            print(f"    - Left columns: {config['expected']['left_columns']}")
            print(f"    - Right columns: {config['expected']['right_columns']}")
            
            results.append({
                "name": config['name'],
                "file": str(new_path),
                "success": True
            })
            
        except Exception as e:
            print(f"  ❌ Failed: {str(e)}")
            results.append({
                "name": config['name'],
                "file": None,
                "success": False,
                "error": str(e)
            })
    
    # Generate summary report
    print("\n" + "=" * 80)
    print("SUMMARY")
    print("=" * 80)
    
    success_count = sum(1 for r in results if r['success'])
    print(f"Total tests: {len(results)}")
    print(f"Successful: {success_count}")
    print(f"Failed: {len(results) - success_count}")
    
    # Create summary report file
    report_path = output_dir / "test_summary.txt"
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("COMPREHENSIVE ROOF TYPE TEST REPORT\n")
        f.write("=" * 80 + "\n")
        f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        f.write("KEY FINDINGS:\n")
        f.write("1. FLAT ROOF: Supports overhang, rafters span full width + 2*overhang\n")
        f.write("2. GABLE ROOF: No overhang (forced to 0), two rafters meet at apex\n")
        f.write("3. AWNING ROOF: No overhang (forced to 0), similar to flat but no overhang\n")
        f.write("4. ATTACHED AWNING: No overhang, NO RIGHT COLUMNS (attached to wall)\n\n")
        
        f.write("PURLIN BEHAVIOR:\n")
        f.write("- FLAT/AWNING: Purlins segmented between rafters (no overlap)\n")
        f.write("- GABLE: Purlins overlap rafters for continuity\n\n")
        
        f.write("RAFTER ROTATION:\n")
        f.write("- All types: Last frame rafter rotated 180 degrees (pi radians)\n")
        f.write("- C-section web faces align with SHS column faces\n\n")
        
        f.write("TEST RESULTS:\n")
        for r in results:
            status = "✅ PASS" if r['success'] else "❌ FAIL"
            f.write(f"{status} - {r['name']}\n")
            if r['success']:
                f.write(f"       File: {r['file']}\n")
            else:
                f.write(f"       Error: {r.get('error', 'Unknown')}\n")
    
    print(f"\n📄 Report saved to: {report_path}")
    print(f"🏗️  IFC files saved to: {output_dir}")
    
    # Generate validation script
    validation_script = output_dir / "validate_in_viewer.txt"
    with open(validation_script, 'w', encoding='utf-8') as f:
        f.write("VALIDATION CHECKLIST FOR IFC VIEWER\n")
        f.write("=" * 80 + "\n\n")
        
        f.write("1. FLAT ROOF VALIDATION:\n")
        f.write("   □ Rafters extend beyond columns by overhang distance\n")
        f.write("   □ Eave purlins at edges of extended rafters\n")
        f.write("   □ Purlins segmented between rafters (no overlap)\n")
        f.write("   □ Right column taller than left (creates slope)\n\n")
        
        f.write("2. GABLE ROOF VALIDATION:\n")
        f.write("   □ No overhang visible (rafters start at columns)\n")
        f.write("   □ Left and right rafters meet at apex\n")
        f.write("   □ Purlins overlap rafters (continuous)\n")
        f.write("   □ Columns same height on both sides\n\n")
        
        f.write("3. AWNING ROOF VALIDATION:\n")
        f.write("   □ No overhang (rafters start at X=0)\n")
        f.write("   □ Single slope from left to right\n")
        f.write("   □ Purlins segmented between rafters\n")
        f.write("   □ Both left and right columns present\n\n")
        
        f.write("4. ATTACHED AWNING VALIDATION:\n")
        f.write("   □ No overhang\n")
        f.write("   □ NO RIGHT COLUMNS (attached to wall)\n")
        f.write("   □ Rafters end before full span\n")
        f.write("   □ Only left columns present\n")
    
    print(f"📋 Validation checklist: {validation_script}")


def generate_engineering_override_test():
    """Test with engineering data overrides."""
    print("\n" + "=" * 80)
    print("ENGINEERING OVERRIDE TEST")
    print("=" * 80)
    
    generator = CarportIFCGenerator()
    
    # Create engineering data with available materials
    eng = EngData(
        ENG_COLUMN="SHS10010040",      # 100x100x4.0mm (available)
        ENG_RAFTER="C20019",           # 200x76x1.9mm (available)
        ENG_PURLINSIZE="C15019",       # 150x65x1.9mm (available)
        ENG_PURLINROW=7,                # 7 purlin rows
        ENG_FOOTINGTYPE="block",       # Block footing
        ENG_FOOTINGDIA="400",          # 400mm (as string)
        ENG_FOOTINGDEPTH="600"         # 600mm depth (as string)
    )
    
    # Building input - must provide values during initialization
    building = BuildingInput(
        span=8000,
        length=12000,
        height=3000,
        roof_type=CarportRoofType.FLAT,
        pitch=10,
        bays=4
    )
    # Set overhang after initialization (due to validation logic)
    building.overhang = 450
    
    print("Engineering Overrides:")
    print(f"  Column: {eng.ENG_COLUMN}")
    print(f"  Rafter: {eng.ENG_RAFTER}")
    print(f"  Purlin: {eng.ENG_PURLINSIZE}")
    print(f"  Purlin rows: {eng.ENG_PURLINROW}")
    print(f"  Footing: {eng.ENG_FOOTINGTYPE} {eng.ENG_FOOTINGDIA}x{eng.ENG_FOOTINGDEPTH}")
    
    # For gable roof, change purlin to TopHat
    if building.roof_type == CarportRoofType.GABLE:
        building.roof_type = CarportRoofType.FLAT  # Change to FLAT for this test
        print("  (Changed to FLAT roof for engineering test)")
    
    try:
        path = generator.generate_carport(
            span=building.span,
            length=building.length,
            height=building.height,
            roof_type=building.roof_type,
            pitch=building.pitch,
            bays=building.bays,
            overhang=building.overhang,
            eng_data=eng,
            output_name="engineering_override_test.ifc"
        )
        
        # Move to test directory
        output_dir = Path("test_output/comprehensive_roof_types")
        new_path = output_dir / "engineering_override_test.ifc"
        if path.exists():
            # Remove target if it already exists
            if new_path.exists():
                new_path.unlink()
            path.rename(new_path)
        
        print(f"✅ Generated with engineering overrides: {new_path}")
        
    except Exception as e:
        print(f"❌ Failed: {str(e)}")


if __name__ == "__main__":
    # Run comprehensive tests
    generate_all_roof_types()
    
    # Run engineering override test
    generate_engineering_override_test()
    
    print("\n✅ All tests completed!")
    print("\nNext steps:")
    print("1. Open IFC files in viewer (IfcOpenShell, Solibri, etc.)")
    print("2. Use validation checklist to verify each roof type")
    print("3. Compare with C# generated models")