#!/usr/bin/env python3
"""
Output Generation Module

This module provides export functionality for BIM models to various formats:
- GLTF: 3D model format for web visualization
- DXF: AutoCAD drawing exchange format
- IFC: Industry Foundation Classes for BIM interoperability
"""

# Export main classes
from .base.output_base import OutputGenerator, OutputFormat, OutputResult
from .gltf.gltf_generator import GLTFGenerator
from .dxf.dxf_generator import DXFGenerator

# Try to import IFC generator, fall back to BREP generator if ifcopenshell is not available
try:
    from .ifc.ifc_generator import IFCGenerator
except ImportError:
    # Fall back to BREP generator if ifcopenshell is not available
    from ..ifc_generation.ifc_brep_generator import IFCBREPGenerator
    from .base.output_base import OutputGenerator, OutputFormat, OutputResult
    from pathlib import Path
    from typing import List

    class IFCGenerator(OutputGenerator):
        """Adapter for IFCBREPGenerator to match OutputGenerator interface."""

        def __init__(self, output_dir: Path = None):
            super().__init__(output_dir)
            self.brep_generator = IFCBREPGenerator()

        def generate(self, bim, filename: str, **options) -> OutputResult:
            """Generate IFC file using BREP generator."""
            try:
                output_path = self.output_dir / f"{filename}.ifc"
                # The BREP generator has a different interface, so we'll need to adapt
                # For now, return a placeholder result
                return OutputResult(
                    success=False,
                    format=OutputFormat.IFC,
                    errors=["IFC generation not fully implemented - BREP generator needs adaptation"]
                )
            except Exception as e:
                return OutputResult(
                    success=False,
                    format=OutputFormat.IFC,
                    errors=[f"IFC generation failed: {str(e)}"]
                )

        def get_supported_formats(self) -> List[OutputFormat]:
            return [OutputFormat.IFC, OutputFormat.IFC4]

__all__ = [
    'OutputGenerator',
    'OutputFormat',
    'OutputResult',
    'GLTFGenerator',
    'DXFGenerator',
    'IFCGenerator'
]