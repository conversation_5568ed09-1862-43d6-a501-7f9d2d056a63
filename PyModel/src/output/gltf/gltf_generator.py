"""
GLTF/GLB output generator for 3D visualization.

This module converts BIM models to GLTF 2.0 format for web-based
3D visualization and other applications.
"""

import json
import struct
from pathlib import Path
from typing import Dict, Any, List, Optional
import numpy as np
from datetime import datetime

from src.bim.shed_bim import ShedBim
from src.bim.components import ShedBimColumn, ShedBimSection
from src.geometry.primitives import Vec3
from src.geometry.matrix import Mat4
from src.geometry.helpers import Geo
from src.materials.helpers import FrameMaterialHelper
from src.output.base.output_base import OutputGenerator, OutputFormat, OutputResult
from src.output.base.mesh_builder import MeshBuilder, MeshData


class GLTFGenerator(OutputGenerator):
    """
    Generator for GLTF 2.0 format files.
    
    GLTF (GL Transmission Format) is a royalty-free specification for
    efficient transmission and loading of 3D scenes and models.
    """
    
    def __init__(self, output_dir: Path = None):
        """Initialize GLTF generator."""
        super().__init__(output_dir)
        self.frame_helper = FrameMaterialHelper()
        
    def get_supported_formats(self) -> List[OutputFormat]:
        """Get supported formats."""
        return [OutputFormat.GLTF, OutputFormat.GLB]
        
    def generate(self, bim: ShedBim, filename: str, **options) -> OutputResult:
        """
        Generate GLTF file from BIM model.
        
        Args:
            bim: The BIM model to export
            filename: Output filename (without extension)
            **options: Additional options:
                - binary: Generate GLB instead of GLTF (default: False)
                - embed_images: Embed images in GLTF (default: True)
                - y_up: Use Y-up coordinate system (default: True)
                
        Returns:
            OutputResult with generation status
        """
        # Validate BIM
        errors = self.validate_bim(bim)
        if errors:
            return OutputResult(
                success=False,
                format=OutputFormat.GLTF,
                errors=errors
            )
            
        try:
            # Determine format
            binary = options.get('binary', False)
            format = OutputFormat.GLB if binary else OutputFormat.GLTF
            
            # Build GLTF structure
            gltf = self._build_gltf_structure(bim, **options)
            
            # Write file
            output_path = self.get_output_path(filename, format)
            
            if binary:
                file_size = self._write_glb(gltf, output_path)
            else:
                file_size = self._write_gltf(gltf, output_path)
                
            # Create result
            return OutputResult(
                success=True,
                format=format,
                file_path=output_path,
                file_size=file_size,
                metadata=self.create_metadata(bim)
            )
            
        except Exception as e:
            self.logger.error(f"Failed to generate GLTF: {str(e)}")
            return OutputResult(
                success=False,
                format=format,
                errors=[f"Generation failed: {str(e)}"]
            )
            
    def _build_gltf_structure(self, bim: ShedBim, **options) -> Dict[str, Any]:
        """Build GLTF JSON structure from BIM."""
        # Initialize GLTF structure
        gltf = {
            "asset": {
                "version": "2.0",
                "generator": "BIM Backend Python GLTF Generator",
                "copyright": "Generated BIM Model",
                "minVersion": "2.0"
            },
            "scene": 0,
            "scenes": [{"nodes": []}],
            "nodes": [],
            "meshes": [],
            "accessors": [],
            "bufferViews": [],
            "buffers": [],
            "materials": [],
            "samplers": [],
            "images": [],
            "textures": []
        }
        
        # Coordinate system adjustment
        y_up = options.get('y_up', True)
        if y_up:
            # Convert from Z-up to Y-up
            root_transform = Mat4.create_rotation_x(-Geo.DEG90)
        else:
            root_transform = Mat4.identity()
            
        # Build scene graph
        self._build_scene(gltf, bim, root_transform)
        
        # Pack binary data
        self._pack_binary_data(gltf)
        
        return gltf
        
    def _build_scene(self, gltf: Dict, bim: ShedBim, root_transform: Mat4):
        """Build GLTF scene from BIM structure."""
        # Create root node
        root_node_idx = len(gltf["nodes"])
        gltf["nodes"].append({
            "name": "BIM_Root",
            "children": [],
            "matrix": root_transform.to_list()
        })
        gltf["scenes"][0]["nodes"].append(root_node_idx)
        
        if not bim.main:
            return
            
        # Process columns
        sides = bim.main.get_sides()
        for side_idx, side in enumerate(sides):
            if hasattr(side, 'columns'):
                for col_idx, column in enumerate(side.columns):
                    self._add_column(gltf, column, root_node_idx, 
                                   f"Column_S{side_idx}_C{col_idx}")
                    
        # Process rafters
        roofs = bim.main.get_roofs()
        for roof_idx, roof in enumerate(roofs):
            if hasattr(roof, 'rafters'):
                for raf_idx, rafter in enumerate(roof.rafters):
                    self._add_rafter(gltf, rafter, root_node_idx,
                                   f"Rafter_R{roof_idx}_R{raf_idx}")
                    
        # Process purlins
        for roof_idx, roof in enumerate(roofs):
            if hasattr(roof, 'purlins'):
                for pur_idx, purlin in enumerate(roof.purlins):
                    self._add_purlin(gltf, purlin, root_node_idx,
                                   f"Purlin_R{roof_idx}_P{pur_idx}")
                    
    def _add_column(self, gltf: Dict, column: ShedBimColumn, parent_idx: int, name: str):
        """Add a column to the GLTF scene."""
        if not column.column:
            return
            
        # Get column geometry from the section
        section = column.column
        direction = (section.end_pos - section.start_pos).normalized()
        length = (section.end_pos - section.start_pos).length()
        
        # Create transformation matrix
        # Position at base, oriented along column direction
        transform = self._create_orientation_matrix(section.start_pos, direction, Vec3.unit_z())
        
        # Get frame material
        material_name = section.material.name if section.material else None
        frame_mat = self.frame_helper.get_frame_material(material_name) if material_name else None
        if not frame_mat:
            return
            
        # Create mesh
        mesh_data = MeshBuilder.create_frame_profile(frame_mat, length)
        mesh_idx = self._add_mesh(gltf, mesh_data, f"{name}_mesh")
        
        # Get or create material
        mat_idx = self._get_or_create_material(gltf, section.material.name if section.material else None)
        
        # Create node
        node_idx = len(gltf["nodes"])
        gltf["nodes"].append({
            "name": name,
            "mesh": mesh_idx,
            "matrix": transform.to_list()
        })
        
        # Add to parent
        gltf["nodes"][parent_idx]["children"].append(node_idx)
        
        # Set material on mesh
        if mat_idx is not None:
            gltf["meshes"][mesh_idx]["primitives"][0]["material"] = mat_idx
            
    def _add_rafter(self, gltf: Dict, rafter: ShedBimSection, parent_idx: int, name: str):
        """Add a rafter to the GLTF scene."""
        # Similar to column but with rafter-specific geometry
        direction = (rafter.end_pos - rafter.start_pos).normalized()
        length = (rafter.end_pos - rafter.start_pos).length()
        
        transform = self._create_orientation_matrix(rafter.start_pos, direction, Vec3.unit_y())
        
        material_name = rafter.material.name if rafter.material else None
        frame_mat = self.frame_helper.get_frame_material(material_name) if material_name else None
        if not frame_mat:
            return
            
        mesh_data = MeshBuilder.create_frame_profile(frame_mat, length)
        mesh_idx = self._add_mesh(gltf, mesh_data, f"{name}_mesh")
        
        mat_idx = self._get_or_create_material(gltf, rafter.material.name if rafter.material else None)
        
        node_idx = len(gltf["nodes"])
        gltf["nodes"].append({
            "name": name,
            "mesh": mesh_idx,
            "matrix": transform.to_list()
        })
        
        gltf["nodes"][parent_idx]["children"].append(node_idx)
        
        if mat_idx is not None:
            gltf["meshes"][mesh_idx]["primitives"][0]["material"] = mat_idx
            
    def _add_purlin(self, gltf: Dict, purlin: ShedBimSection, parent_idx: int, name: str):
        """Add a purlin to the GLTF scene."""
        direction = (purlin.end_pos - purlin.start_pos).normalized()
        length = (purlin.end_pos - purlin.start_pos).length()
        
        transform = self._create_orientation_matrix(purlin.start_pos, direction, Vec3.unit_z())
        
        material_name = purlin.material.name if purlin.material else None
        frame_mat = self.frame_helper.get_frame_material(material_name) if material_name else None
        if not frame_mat:
            return
            
        mesh_data = MeshBuilder.create_frame_profile(frame_mat, length)
        mesh_idx = self._add_mesh(gltf, mesh_data, f"{name}_mesh")
        
        mat_idx = self._get_or_create_material(gltf, purlin.material.name if purlin.material else None)
        
        node_idx = len(gltf["nodes"])
        gltf["nodes"].append({
            "name": name,
            "mesh": mesh_idx,
            "matrix": transform.to_list()
        })
        
        gltf["nodes"][parent_idx]["children"].append(node_idx)
        
        if mat_idx is not None:
            gltf["meshes"][mesh_idx]["primitives"][0]["material"] = mat_idx
            
    def _add_mesh(self, gltf: Dict, mesh_data: MeshData, name: str) -> int:
        """Add mesh data to GLTF and return mesh index."""
        mesh_idx = len(gltf["meshes"])
        
        # Convert vertices to flat array
        positions = []
        for v in mesh_data.vertices:
            positions.extend([v.x, v.y, v.z])
            
        normals = []
        for n in mesh_data.normals:
            normals.extend([n.x, n.y, n.z])
            
        # Convert triangles to flat array
        indices = []
        for tri in mesh_data.triangles:
            indices.extend(tri)
            
        # Create accessors for vertex data
        pos_accessor_idx = self._add_accessor(gltf, positions, "VEC3", "FLOAT", 
                                            count=len(mesh_data.vertices))
        norm_accessor_idx = self._add_accessor(gltf, normals, "VEC3", "FLOAT",
                                             count=len(mesh_data.normals))
        idx_accessor_idx = self._add_accessor(gltf, indices, "SCALAR", "UNSIGNED_SHORT",
                                            count=len(indices))
        
        # Create mesh
        gltf["meshes"].append({
            "name": name,
            "primitives": [{
                "attributes": {
                    "POSITION": pos_accessor_idx,
                    "NORMAL": norm_accessor_idx
                },
                "indices": idx_accessor_idx,
                "mode": 4  # TRIANGLES
            }]
        })
        
        return mesh_idx
        
    def _add_accessor(self, gltf: Dict, data: List[float], type: str, 
                     component_type: str, count: int) -> int:
        """Add accessor for binary data."""
        accessor_idx = len(gltf["accessors"])
        
        # Component type mapping
        comp_types = {
            "BYTE": 5120,
            "UNSIGNED_BYTE": 5121,
            "SHORT": 5122,
            "UNSIGNED_SHORT": 5123,
            "UNSIGNED_INT": 5125,
            "FLOAT": 5126
        }
        
        # Calculate bounds for POSITION accessors
        min_vals = None
        max_vals = None
        if type == "VEC3":
            # Reshape data for bounds calculation
            vec_data = np.array(data).reshape(-1, 3)
            min_vals = vec_data.min(axis=0).tolist()
            max_vals = vec_data.max(axis=0).tolist()
            
        accessor = {
            "bufferView": accessor_idx,  # Will be updated later
            "componentType": comp_types[component_type],
            "count": count,
            "type": type
        }
        
        if min_vals is not None:
            accessor["min"] = min_vals
            accessor["max"] = max_vals
            
        gltf["accessors"].append(accessor)
        
        # Store data for later binary packing
        if "_binary_data" not in gltf:
            gltf["_binary_data"] = []
        gltf["_binary_data"].append((accessor_idx, data, component_type))
        
        return accessor_idx
        
    def _get_or_create_material(self, gltf: Dict, material_name: str) -> Optional[int]:
        """Get existing material index or create new one."""
        # Check if material already exists
        for idx, mat in enumerate(gltf["materials"]):
            if mat["name"] == material_name:
                return idx
                
        # Create new material
        mat_idx = len(gltf["materials"])
        
        # Use default gray color for now
        # TODO: Add proper color material support
        color = [0.7, 0.7, 0.7, 1.0]  # Default gray
            
        gltf["materials"].append({
            "name": material_name,
            "pbrMetallicRoughness": {
                "baseColorFactor": color,
                "metallicFactor": 0.5,
                "roughnessFactor": 0.5
            },
            "doubleSided": True
        })
        
        return mat_idx
        
    def _create_orientation_matrix(self, position: Vec3, forward: Vec3, up: Vec3) -> Mat4:
        """Create transformation matrix from position and orientation."""
        # Ensure vectors are normalized
        forward = forward.normalized()
        up = up.normalized()
        
        # Calculate right vector
        right = Vec3.cross(forward, up).normalized()
        
        # Recalculate up to ensure orthogonality
        up = Vec3.cross(right, forward).normalized()
        
        # Build matrix using Mat4 constructor
        # Mat4 constructor takes elements in row-major order
        matrix = Mat4(
            right.x,     up.x,      -forward.x,    position.x,
            right.y,     up.y,      -forward.y,    position.y,
            right.z,     up.z,      -forward.z,    position.z,
            0.0,         0.0,        0.0,          1.0
        )
        
        return matrix
        
    def _pack_binary_data(self, gltf: Dict):
        """Pack all binary data into buffer."""
        if "_binary_data" not in gltf:
            return
            
        binary_data = bytearray()
        buffer_views = []
        
        for accessor_idx, data, component_type in gltf["_binary_data"]:
            # Convert data to bytes
            if component_type == "FLOAT":
                data_bytes = struct.pack(f"{len(data)}f", *data)
            elif component_type == "UNSIGNED_SHORT":
                data_bytes = struct.pack(f"{len(data)}H", *data)
            else:
                raise ValueError(f"Unsupported component type: {component_type}")
                
            # Align to 4-byte boundary
            while len(binary_data) % 4 != 0:
                binary_data.append(0)
                
            # Record buffer view
            offset = len(binary_data)
            binary_data.extend(data_bytes)
            
            buffer_views.append({
                "buffer": 0,
                "byteOffset": offset,
                "byteLength": len(data_bytes)
            })
            
            # Update accessor
            gltf["accessors"][accessor_idx]["bufferView"] = len(buffer_views) - 1
            
        # Add buffer views to GLTF
        gltf["bufferViews"] = buffer_views
        
        # Store binary data
        gltf["_binary"] = bytes(binary_data)
        gltf["buffers"] = [{
            "byteLength": len(binary_data)
        }]
        
        # Clean up temporary data
        del gltf["_binary_data"]
        
    def _write_gltf(self, gltf: Dict, output_path: Path) -> int:
        """Write GLTF JSON file."""
        # Extract binary data
        binary_data = gltf.get("_binary", b"")
        if "_binary" in gltf:
            del gltf["_binary"]
            
        # Write binary file if present
        if binary_data:
            bin_path = output_path.with_suffix(".bin")
            bin_path.write_bytes(binary_data)
            gltf["buffers"][0]["uri"] = bin_path.name
            
        # Write JSON
        json_str = json.dumps(gltf, indent=2)
        output_path.write_text(json_str)
        
        return len(json_str) + len(binary_data)
        
    def _write_glb(self, gltf: Dict, output_path: Path) -> int:
        """Write GLB binary file."""
        # Extract binary data
        binary_data = gltf.get("_binary", b"")
        if "_binary" in gltf:
            del gltf["_binary"]
            
        # Convert JSON to bytes
        json_str = json.dumps(gltf, separators=(',', ':'))
        json_bytes = json_str.encode('utf-8')
        
        # Pad JSON to 4-byte boundary
        while len(json_bytes) % 4 != 0:
            json_bytes += b' '
            
        # Pad binary to 4-byte boundary
        while len(binary_data) % 4 != 0:
            binary_data += b'\0'
            
        # GLB header
        magic = b'glTF'
        version = 2
        total_length = 12 + 8 + len(json_bytes) + 8 + len(binary_data)
        
        with open(output_path, 'wb') as f:
            # Write header
            f.write(magic)
            f.write(struct.pack('<II', version, total_length))
            
            # Write JSON chunk
            f.write(struct.pack('<II', len(json_bytes), 0x4E4F534A))  # JSON
            f.write(json_bytes)
            
            # Write binary chunk
            if binary_data:
                f.write(struct.pack('<II', len(binary_data), 0x004E4942))  # BIN
                f.write(binary_data)
                
        return total_length