"""
IFC generation exception hierarchy.

This module defines custom exceptions for IFC generation to provide
clear error messages and proper error handling.
"""

from typing import Optional, Any


class IFCGenerationError(Exception):
    """Base exception for all IFC generation errors."""
    
    def __init__(self, message: str, details: Optional[dict] = None):
        """
        Initialize IFC generation error.
        
        Args:
            message: Error message
            details: Optional dictionary with additional error details
        """
        super().__init__(message)
        self.details = details or {}


class InvalidGeometryError(IFCGenerationError):
    """Raised when geometry calculations produce invalid results."""
    
    def __init__(self, message: str, geometry_data: Optional[Any] = None):
        """
        Initialize invalid geometry error.
        
        Args:
            message: Error description
            geometry_data: The invalid geometry data
        """
        details = {"geometry_data": str(geometry_data)} if geometry_data else {}
        super().__init__(message, details)


class InvalidMaterialError(IFCGenerationError):
    """Raised when an unknown or invalid material is referenced."""
    
    def __init__(self, material_name: str, available_materials: Optional[list] = None):
        """
        Initialize invalid material error.
        
        Args:
            material_name: The invalid material name
            available_materials: List of valid material names
        """
        message = f"Invalid material: {material_name}"
        if available_materials:
            message += f". Available materials: {', '.join(available_materials)}"
        
        details = {
            "material_name": material_name,
            "available_materials": available_materials or []
        }
        super().__init__(message, details)


class InvalidInputError(IFCGenerationError):
    """Raised when input parameters are invalid."""
    
    def __init__(self, parameter: str, value: Any, reason: str):
        """
        Initialize invalid input error.
        
        Args:
            parameter: Parameter name
            value: Invalid value
            reason: Reason why value is invalid
        """
        message = f"Invalid {parameter}: {value}. {reason}"
        details = {
            "parameter": parameter,
            "value": value,
            "reason": reason
        }
        super().__init__(message, details)


class IFCFileError(IFCGenerationError):
    """Raised when file operations fail."""
    
    def __init__(self, operation: str, file_path: str, original_error: Optional[Exception] = None):
        """
        Initialize IFC file error.
        
        Args:
            operation: File operation that failed (read/write)
            file_path: Path to the file
            original_error: Original exception if any
        """
        message = f"Failed to {operation} IFC file: {file_path}"
        if original_error:
            message += f". Error: {str(original_error)}"
        
        details = {
            "operation": operation,
            "file_path": file_path,
            "original_error": str(original_error) if original_error else None
        }
        super().__init__(message, details)


class ProfileGenerationError(IFCGenerationError):
    """Raised when profile generation fails."""
    
    def __init__(self, profile_type: str, profile_name: str, reason: str):
        """
        Initialize profile generation error.
        
        Args:
            profile_type: Type of profile (C, Z, SHS, etc.)
            profile_name: Name of the profile
            reason: Reason for failure
        """
        message = f"Failed to generate {profile_type} profile '{profile_name}': {reason}"
        details = {
            "profile_type": profile_type,
            "profile_name": profile_name,
            "reason": reason
        }
        super().__init__(message, details)


class TransformationError(IFCGenerationError):
    """Raised when coordinate transformation fails."""
    
    def __init__(self, operation: str, data: Any, reason: str):
        """
        Initialize transformation error.
        
        Args:
            operation: Transformation operation
            data: Data being transformed
            reason: Reason for failure
        """
        message = f"Transformation '{operation}' failed: {reason}"
        details = {
            "operation": operation,
            "data": str(data),
            "reason": reason
        }
        super().__init__(message, details)