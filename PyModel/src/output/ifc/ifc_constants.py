"""
IFC generation constants and configuration.

This module defines all constants used in IFC generation to avoid magic numbers
and ensure consistency across the codebase.
"""

from typing import Final


class IFCConstants:
    """Constants for IFC generation matching C# defaults."""
    
    # Geometric constants
    DEFAULT_SLAB_DEPTH: Final[float] = 100.0  # mm
    DEFAULT_SLAB_EXTENSION: Final[float] = 100.0  # mm
    DEFAULT_FOOTING_DIAMETER: Final[float] = 450.0  # mm
    DEFAULT_FOOTING_DEPTH: Final[float] = 600.0  # mm
    DEFAULT_CORNER_RADIUS: Final[float] = 2.5  # mm
    
    # Material dimensions (matching C# defaults)
    DEFAULT_COLUMN_WIDTH: Final[float] = 75.0  # mm
    DEFAULT_COLUMN_HEIGHT: Final[float] = 75.0  # mm
    DEFAULT_RAFTER_FLANGE: Final[float] = 76.0  # mm
    DEFAULT_RAFTER_WEB: Final[float] = 203.0  # mm
    DEFAULT_PURLIN_FLANGE: Final[float] = 64.0  # mm
    DEFAULT_PURLIN_WEB: Final[float] = 152.0  # mm
    
    # IFC specific constants
    IFC_VERSION: Final[str] = "IFC4"
    IFC_MODEL_VIEW: Final[str] = "ViewDefinition [Ifc4NotAssigned]"
    IFC_SCHEMA_VERSION: Final[str] = "2;1"
    GEOMETRIC_TOLERANCE: Final[float] = 1.0E-5
    
    # Entity type constants
    MEMBER_TYPE_COLUMN: Final[str] = ".COLUMN."
    MEMBER_TYPE_BEAM: Final[str] = ".BEAM."
    MEMBER_TYPE_MEMBER: Final[str] = ".MEMBER."
    MEMBER_TYPE_RAFTER: Final[str] = ".BEAM."
    MEMBER_TYPE_PURLIN: Final[str] = ".MEMBER."
    MEMBER_TYPE_GIRT: Final[str] = ".MEMBER."
    MEMBER_TYPE_BRACE: Final[str] = ".MEMBER."
    
    # Property set names (matching C#)
    PROPSET_MATERIAL: Final[str] = "MaterialProfile"
    PROPSET_LOCATION: Final[str] = "LocationProfile"
    PROPSET_BOM: Final[str] = "BOMProfile"
    PROPSET_ROOF_TYPE: Final[str] = "MainRoofTypeProfile"
    
    # Default material names (C# alignment)
    MATERIAL_COLUMN_DEFAULT: Final[str] = "SHS07507525"
    MATERIAL_RAFTER_DEFAULT: Final[str] = "C20020"
    MATERIAL_PURLIN_FLAT_DEFAULT: Final[str] = "C15012"
    MATERIAL_PURLIN_GABLE_DEFAULT: Final[str] = "TH064100"
    MATERIAL_EAVE_PURLIN_DEFAULT: Final[str] = "C15015"
    
    # Numeric precision
    COORDINATE_PRECISION: Final[int] = 1  # decimal places
    ANGLE_PRECISION: Final[int] = 6  # decimal places for angles
    
    # GUID generation
    GUID_LENGTH: Final[int] = 22
    GUID_CHARS: Final[str] = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz_$"