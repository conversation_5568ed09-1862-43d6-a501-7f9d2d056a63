"""
IFC output generator using ifcopenshell library.

This module converts BIM models to IFC (Industry Foundation Classes)
format using the proper ifcopenshell library for full IFC compliance.
"""

from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
import uuid
import math

try:
    import ifcopenshell
    import ifcopenshell.api
    import ifcopenshell.util.element
    import ifcopenshell.util.placement
    import ifcopenshell.util.shape
except ImportError:
    raise ImportError(
        "ifcopenshell is required for IFC generation. "
        "Install it with: pip install ifcopenshell"
    )

from ...bim.shed_bim import ShedBim
from ...bim.components import ShedBimColumn, ShedBimSection
from ...geometry.primitives import Vec3
from ...geometry.matrix import Mat4
from ...materials.helpers import FrameMaterialHelper
from ..base.output_base import OutputGenerator, OutputFormat, OutputResult


class IFCGenerator(OutputGenerator):
    """
    Generator for IFC (Industry Foundation Classes) files using ifcopenshell.
    
    This implementation uses the proper ifcopenshell library for full
    IFC compliance and proper geometry representation.
    """
    
    def __init__(self, output_dir: Path = None):
        """Initialize IFC generator."""
        super().__init__(output_dir)
        self.material_library = MaterialLibrary.get_default()
        
    def get_supported_formats(self) -> List[OutputFormat]:
        """Get supported formats."""
        return [OutputFormat.IFC, OutputFormat.IFC4]
        
    def generate(self, bim: ShedBim, filename: str, **options) -> OutputResult:
        """
        Generate IFC file from BIM model using ifcopenshell.
        
        Args:
            bim: The BIM model to export
            filename: Output filename (without extension)
            **options: Additional options:
                - version: IFC version ('IFC2X3', 'IFC4', 'IFC4X3') (default: 'IFC4')
                - author: Author name
                - organization: Organization name
                
        Returns:
            OutputResult with generation status
        """
        # Validate BIM
        errors = self.validate_bim(bim)
        if errors:
            return OutputResult(
                success=False,
                format=OutputFormat.IFC,
                errors=errors
            )
            
        try:
            # Get options
            version = options.get('version', 'IFC4')
            author = options.get('author', 'BIM Backend')
            organization = options.get('organization', 'Generated')
            
            format = OutputFormat.IFC4 if version == 'IFC4' else OutputFormat.IFC
            
            # Create IFC file using ifcopenshell
            ifc_file = self._create_ifc_file(version)
            
            # Run the IFC generation
            self._generate_ifc_content(ifc_file, bim, author, organization)
            
            # Write file
            output_path = self.get_output_path(filename, format)
            ifc_file.write(str(output_path))
            
            # Get file size
            file_size = output_path.stat().st_size
            
            return OutputResult(
                success=True,
                format=format,
                file_path=output_path,
                file_size=file_size,
                metadata=self.create_metadata(bim)
            )
            
        except Exception as e:
            self.logger.error(f"Failed to generate IFC: {str(e)}")
            return OutputResult(
                success=False,
                format=format,
                errors=[f"Generation failed: {str(e)}"]
            )
            
    def _create_ifc_file(self, version: str) -> ifcopenshell.file:
        """Create new IFC file with specified version."""
        # Map version strings to schema identifiers
        schema_map = {
            'IFC2X3': 'IFC2X3',
            'IFC4': 'IFC4',
            'IFC4X3': 'IFC4X3_ADD2'
        }
        
        schema = schema_map.get(version, 'IFC4')
        
        # Create new IFC file
        ifc = ifcopenshell.api.run("project.create_file", version=schema)
        
        return ifc
        
    def _generate_ifc_content(self, ifc: ifcopenshell.file, bim: ShedBim,
                            author: str, organization: str):
        """Generate IFC content from BIM model."""
        # Create project
        project = ifcopenshell.api.run("root.create_entity", ifc, 
                                     ifc_class="IfcProject", 
                                     name="BIM Project")
        
        # Set project units (millimeters)
        ifcopenshell.api.run("unit.assign_unit", ifc, 
                           length={"type": "IfcSIUnit", "unit": "METRE", "prefix": "MILLI"})
        
        # Create site
        site = ifcopenshell.api.run("root.create_entity", ifc,
                                  ifc_class="IfcSite",
                                  name="Site")
        ifcopenshell.api.run("aggregate.assign_object", ifc,
                           product=site,
                           relating_object=project)
        
        # Create building
        building = ifcopenshell.api.run("root.create_entity", ifc,
                                      ifc_class="IfcBuilding",
                                      name="Building")
        ifcopenshell.api.run("aggregate.assign_object", ifc,
                           product=building,
                           relating_object=site)
        
        # Create building storey
        storey = ifcopenshell.api.run("root.create_entity", ifc,
                                    ifc_class="IfcBuildingStorey",
                                    name="Ground Floor")
        ifcopenshell.api.run("aggregate.assign_object", ifc,
                           product=storey,
                           relating_object=building)
        
        # Set storey elevation
        placement = ifcopenshell.api.run("geometry.edit_object_placement", ifc,
                                       product=storey)
        ifcopenshell.api.run("geometry.edit_object_placement", ifc,
                           product=storey,
                           matrix=[[1, 0, 0, 0],
                                  [0, 1, 0, 0],
                                  [0, 0, 1, 0],
                                  [0, 0, 0, 1]])
        
        # Create materials
        materials = self._create_materials(ifc)
        
        # Add BIM components
        if bim.main:
            self._add_structural_members(ifc, bim, storey, materials)
            
    def _create_materials(self, ifc: ifcopenshell.file) -> Dict[str, Any]:
        """Create IFC materials from material library."""
        materials = {}
        
        # Create common steel material
        steel = ifcopenshell.api.run("material.add_material", ifc, name="Steel")
        materials['default'] = steel
        
        # Create materials for each frame material
        for frame_mat in self.material_library.get_frame_materials():
            material = ifcopenshell.api.run("material.add_material", ifc, 
                                          name=frame_mat.name)
            materials[frame_mat.name] = material
            
        return materials
        
    def _add_structural_members(self, ifc: ifcopenshell.file, bim: ShedBim,
                              storey: Any, materials: Dict[str, Any]):
        """Add structural members (columns, beams, etc.) to IFC."""
        # Process columns
        sides = bim.main.get_sides()
        for side_idx, side in enumerate(sides):
            if hasattr(side, 'columns'):
                for col_idx, column in enumerate(side.columns):
                    self._add_column(ifc, column, storey, materials,
                                   f"Column_S{side_idx}_C{col_idx}")
                    
        # Process rafters
        roofs = bim.main.get_roofs()
        for roof_idx, roof in enumerate(roofs):
            if hasattr(roof, 'rafters'):
                for raf_idx, rafter in enumerate(roof.rafters):
                    self._add_beam(ifc, rafter, storey, materials,
                                 f"Rafter_R{roof_idx}_R{raf_idx}", "RAFTER")
                    
        # Process purlins
        for roof_idx, roof in enumerate(roofs):
            if hasattr(roof, 'purlins'):
                for pur_idx, purlin in enumerate(roof.purlins):
                    self._add_beam(ifc, purlin, storey, materials,
                                 f"Purlin_R{roof_idx}_P{pur_idx}", "PURLIN")
                    
    def _add_column(self, ifc: ifcopenshell.file, column: ShedBimColumn,
                   storey: Any, materials: Dict[str, Any], name: str):
        """Add a column to IFC model."""
        # Create column entity
        ifc_column = ifcopenshell.api.run("root.create_entity", ifc,
                                         ifc_class="IfcColumn",
                                         name=name)
        
        # Assign to storey
        ifcopenshell.api.run("spatial.assign_container", ifc,
                           product=ifc_column,
                           relating_structure=storey)
        
        # Create column geometry
        # Get column direction and length
        direction = column.top - column.base
        length = direction.length()
        
        # Get frame material for profile
        frame_mat = self.material_library.get_frame_material(column.frame_material)
        if frame_mat:
            # Create profile
            profile = self._create_profile(ifc, frame_mat)
            
            # Create extruded area solid
            solid = ifcopenshell.api.run("geometry.add_profile_representation", ifc,
                                       context=ifc.by_type("IfcGeometricRepresentationContext")[0],
                                       profile=profile,
                                       depth=length)
            
            # Assign representation
            ifcopenshell.api.run("geometry.assign_representation", ifc,
                               product=ifc_column,
                               representation=solid)
            
        # Set column placement
        matrix = self._create_placement_matrix(column.base, direction)
        ifcopenshell.api.run("geometry.edit_object_placement", ifc,
                           product=ifc_column,
                           matrix=matrix)
        
        # Assign material
        material = materials.get(column.frame_material, materials['default'])
        ifcopenshell.api.run("material.assign_material", ifc,
                           product=ifc_column,
                           material=material)
                           
    def _add_beam(self, ifc: ifcopenshell.file, member: Any, storey: Any,
                 materials: Dict[str, Any], name: str, predefined_type: str):
        """Add a beam/rafter/purlin to IFC model."""
        # Create beam entity
        ifc_beam = ifcopenshell.api.run("root.create_entity", ifc,
                                      ifc_class="IfcBeam",
                                      name=name,
                                      predefined_type=predefined_type)
        
        # Assign to storey
        ifcopenshell.api.run("spatial.assign_container", ifc,
                           product=ifc_beam,
                           relating_structure=storey)
        
        # Create beam geometry
        direction = member.end - member.start
        length = direction.length()
        
        # Get frame material for profile
        frame_mat = self.material_library.get_frame_material(member.frame_material)
        if frame_mat:
            # Create profile
            profile = self._create_profile(ifc, frame_mat)
            
            # Create extruded area solid
            solid = ifcopenshell.api.run("geometry.add_profile_representation", ifc,
                                       context=ifc.by_type("IfcGeometricRepresentationContext")[0],
                                       profile=profile,
                                       depth=length)
            
            # Assign representation
            ifcopenshell.api.run("geometry.assign_representation", ifc,
                               product=ifc_beam,
                               representation=solid)
            
        # Set beam placement
        matrix = self._create_placement_matrix(member.start, direction)
        ifcopenshell.api.run("geometry.edit_object_placement", ifc,
                           product=ifc_beam,
                           matrix=matrix)
        
        # Assign material
        material = materials.get(member.frame_material, materials['default'])
        ifcopenshell.api.run("material.assign_material", ifc,
                           product=ifc_beam,
                           material=material)
                           
    def _create_profile(self, ifc: ifcopenshell.file, frame_mat: 'FrameMaterial') -> Any:
        """Create IFC profile definition from frame material."""
        # For simplicity, create rectangular profiles
        # In production, create accurate profiles based on material type
        
        width = frame_mat.width
        height = frame_mat.height
        
        # Create rectangular profile
        profile = ifcopenshell.api.run("profile.add_parameterized_profile", ifc,
                                     ifc_class="IfcRectangleProfileDef",
                                     name=frame_mat.name,
                                     x=width,
                                     y=height)
        
        return profile
        
    def _create_placement_matrix(self, position: Vec3, direction: Vec3) -> List[List[float]]:
        """Create 4x4 transformation matrix for IFC placement."""
        # Normalize direction
        direction = direction.normalized()
        
        # Calculate perpendicular vectors
        if abs(direction.z) < 0.9:
            up = Vec3(0, 0, 1)
        else:
            up = Vec3(0, 1, 0)
            
        right = Vec3.cross(direction, up).normalized()
        up = Vec3.cross(right, direction).normalized()
        
        # Build 4x4 matrix
        matrix = [
            [right.x, up.x, direction.x, position.x],
            [right.y, up.y, direction.y, position.y],
            [right.z, up.z, direction.z, position.z],
            [0.0, 0.0, 0.0, 1.0]
        ]
        
        return matrix