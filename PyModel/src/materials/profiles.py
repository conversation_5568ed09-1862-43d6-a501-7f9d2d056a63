"""Mesh profile generation for frame materials.

This module corresponds to MeshProfileHelper.cs from the C# implementation.
Provides functionality to create 2D profiles for 3D mesh generation.
"""

# Fix imports to use absolute paths
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from dataclasses import dataclass, field
from typing import List, Tuple, Dict, Optional, Union
import math

from src.geometry.primitives import Vec2, Vec3
from .base import FrameMaterial, FrameMaterialType, PunchingWhere


@dataclass
class MeshProfileItem:
    """Single item in a mesh profile (outer or hollow section).
    
    C# Reference: MeshProfileHelper internal structures
    """
    vertexes: List[Vec2] = field(default_factory=list)
    inner_vertexes: Optional[List[Vec2]] = None  # For hollow sections
    
    def is_hollow(self) -> bool:
        """Check if this is a hollow profile."""
        return self.inner_vertexes is not None


@dataclass 
class PunchingMap:
    """Maps punching locations to mesh coordinates.
    
    C# Reference: MeshProfileHelper punching structures
    """
    where: PunchingWhere
    position: Vec2
    angle: float = 0.0


@dataclass
class MeshProfile:
    """Complete mesh profile with items and punching maps.
    
    C# Reference: MeshProfileHelper return type
    """
    items: List[MeshProfileItem] = field(default_factory=list)
    punching_map: List[PunchingMap] = field(default_factory=list)


class VertexBuilder:
    """Builder for creating profile vertices with arc support.
    
    C# Reference: MeshProfileHelper.VertexBuilder class
    """
    
    def __init__(self, draw_profile_arcs: bool, thickness: float):
        self.draw_profile_arcs = draw_profile_arcs
        self.thickness = thickness
        self.vertexes: List[Vec2] = []
    
    def add_pt(self, x: Union[float, Vec2], y: Optional[float] = None) -> None:
        """Add a point to the vertex list.
        
        C# Ref: AddPt methods
        """
        if isinstance(x, Vec2):
            self.vertexes.append(x)
        else:
            self.vertexes.append(Vec2(x, y))
    
    def add_arc(self, x: float, y: float, arc1: float, arc2: float, is_inner: bool) -> None:
        """Add an arc to the vertex list.
        
        C# Ref: AddArc method
        """
        if self.draw_profile_arcs:
            if arc1 > arc2:
                arc1, arc2 = arc2, arc1
            
            dist = self.thickness if is_inner else self.thickness * 2
            center = Vec2(x, y) - MeshProfileHelper._polar(arc1, dist) - MeshProfileHelper._polar(arc2, dist)
            arc = MeshProfileHelper._generate_arc(center, dist, arc1, arc2, segments=3, clockwise=is_inner)
            
            for pt in arc:
                self.vertexes.append(pt)
        else:
            self.add_pt(x, y)
    
    def pivot(self) -> None:
        """Pivot vertices to center.
        
        C# Ref: Pivot method
        """
        if not self.vertexes:
            return
        
        max_x = max(v.x for v in self.vertexes)
        max_y = max(v.y for v in self.vertexes)
        pivot = Vec2(max_x / 2, max_y / 2)
        
        for i in range(len(self.vertexes)):
            self.vertexes[i] = Vec2(self.vertexes[i].x - pivot.x, 
                                   self.vertexes[i].y - pivot.y)
    
    def mirror(self, mirror_x: bool = False, mirror_y: bool = False, reverse: bool = False) -> None:
        """Mirror vertices.
        
        C# Ref: Mirror method
        """
        for i in range(len(self.vertexes)):
            vertex = self.vertexes[i]
            if mirror_x:
                vertex = Vec2(-vertex.x, vertex.y)
            if mirror_y:
                vertex = Vec2(vertex.x, -vertex.y)
            self.vertexes[i] = vertex
        
        if reverse:
            self.vertexes.reverse()


class MeshProfileHelper:
    """Helper for creating mesh profiles from frame materials.
    
    C# Reference: MeshProfileHelper.cs
    """
    
    # Constants for arc generation
    DEG0 = 0.0
    DEG90 = math.pi / 2
    DEG180 = math.pi
    DEG270 = 3 * math.pi / 2
    DEG360 = 2 * math.pi
    
    @classmethod
    def create_mesh_profile(cls, material: FrameMaterial, 
                           draw_profile_arcs: bool = True) -> MeshProfile:
        """Create mesh profile for a frame material.
        
        C# Ref: Lines 12-38
        """
        # Handle both enum and string types
        material_type = material.material_type
        if isinstance(material_type, str):
            material_type_str = material_type.upper()
        else:
            material_type_str = material_type.name
            
        if material_type_str == "C" or material_type == FrameMaterialType.C:
            # Check if is_b2b attribute exists
            is_b2b = getattr(material, 'is_b2b', False)
            if is_b2b:
                return cls._create_mesh_profile_c2(material, draw_profile_arcs)
            else:
                return cls._create_mesh_profile_c(material, draw_profile_arcs)
        elif material_type_str == "TH" or material_type == FrameMaterialType.TH:
            return cls._create_mesh_profile_th(material)
        elif material_type_str == "SHS" or material_type == FrameMaterialType.SHS:
            return cls._create_mesh_profile_shs(material, draw_profile_arcs)
        elif material_type_str == "Z" or material_type == FrameMaterialType.Z:
            return cls._create_mesh_profile_z(material, draw_profile_arcs)
        elif material_type_str == "PAD" or material_type == FrameMaterialType.PAD:
            return cls._create_mesh_profile_pad(material, draw_profile_arcs)
        elif material_type_str == "SRDJ" or material_type == FrameMaterialType.SRDJ:
            return cls._create_mesh_profile_srdj(material, draw_profile_arcs)
        else:
            raise ValueError(f"Unsupported material type: {material.material_type}")
    
    @classmethod
    def _create_mesh_profile_c(cls, material: FrameMaterial, 
                              draw_profile_arcs: bool) -> MeshProfile:
        """Create C-section profile.
        
        C# Ref: CreateMeshProfile_C and CreateVertexesC methods
        """
        # Get vertex builder
        vertex_builder = cls._create_vertexes_c(material, draw_profile_arcs)
        
        # Create profile item
        item = MeshProfileItem(vertexes=vertex_builder.vertexes)
        
        # Create punching maps
        punching_map = cls._get_mesh_punchings_c(material)
        
        return MeshProfile(items=[item], punching_map=punching_map)
    
    @classmethod
    def _create_vertexes_c(cls, material: FrameMaterial, draw_profile_arcs: bool) -> VertexBuilder:
        """Create vertices for C-section.
        
        Based on THREE.js implementation from user.
        """
        # Cold-formed C-section using THREE.js approach
        # Starting at (0,0) and building the shape
        
        vertex_builder = VertexBuilder(draw_profile_arcs, material.thickness)
        
        # Get dimensions
        # Check if properties exist (from base.py) or use direct attributes (from fixed geometry)
        if hasattr(material, 'flange_single'):
            geo_x = material.flange_single  # Flange width from property
        else:
            geo_x = material.width  # Direct flange width
            
        if hasattr(material, 'web'):
            geo_y = material.web  # Web height from property
        else:
            geo_y = material.height  # Direct web height
            
        lip = getattr(material, 'lip', 15.0) if getattr(material, 'lip', 0) > 0 else 15.0  # Default 15mm lip
        thickness = material.thickness
        
        # Build C-section based on THREE.js pattern
        # Start at bottom left (0,0)
        vertex_builder.add_pt(0, 0)
        vertex_builder.add_pt(geo_x, 0)  # Bottom flange
        vertex_builder.add_pt(geo_x, lip)  # Bottom lip up
        vertex_builder.add_pt(geo_x - thickness, lip)  # Bottom lip inner
        vertex_builder.add_pt(geo_x - thickness, thickness)  # Bottom flange inner
        vertex_builder.add_pt(thickness, thickness)  # Bottom web connection
        vertex_builder.add_pt(thickness, geo_y - thickness)  # Web inner up
        vertex_builder.add_pt(geo_x - thickness, geo_y - thickness)  # Top flange inner
        vertex_builder.add_pt(geo_x - thickness, geo_y - lip)  # Top lip inner
        vertex_builder.add_pt(geo_x, geo_y - lip)  # Top lip outer
        vertex_builder.add_pt(geo_x, geo_y)  # Top flange
        vertex_builder.add_pt(0, geo_y)  # Top web
        vertex_builder.add_pt(0, 0)  # Close shape
        
        # Center the profile
        vertex_builder.pivot()
        
        if getattr(material, 'flipped', False):
            vertex_builder.mirror(mirror_x=True, reverse=True)
        
        return vertex_builder
    
    @classmethod
    def _create_mesh_profile_c2(cls, material: FrameMaterial,
                                draw_profile_arcs: bool) -> MeshProfile:
        """Create back-to-back C-section profile.
        
        C# Ref: CreateMeshProfile_C2 method lines 485-503
        """
        # Create left C profile
        vertex_builder1 = cls._create_vertexes_c(material, draw_profile_arcs)
        vertex_builder1.mirror(mirror_x=False, mirror_y=False, reverse=False)  # Normal orientation
        
        # Create right C profile (mirrored)
        vertex_builder2 = cls._create_vertexes_c(material, draw_profile_arcs)
        vertex_builder2.mirror(mirror_x=True, mirror_y=False, reverse=True)  # Mirror X and reverse
        
        # Create profile items
        items = [
            MeshProfileItem(vertexes=vertex_builder1.vertexes),
            MeshProfileItem(vertexes=vertex_builder2.vertexes)
        ]
        
        # Create punching maps
        punching_map = cls._get_mesh_punchings_c2(material)
        
        return MeshProfile(items=items, punching_map=punching_map)
    
    @classmethod
    def _get_mesh_punchings_c2(cls, material: FrameMaterial) -> List[PunchingMap]:
        """Get punching map for back-to-back C-section.
        
        C# Ref: GetMeshPunchings_C2 method lines 605-628
        """
        # Get dimensions safely
        if hasattr(material, 'web'):
            hw = material.web / 2
        else:
            hw = material.height / 2
            
        if hasattr(material, 'flange_single'):
            hf = material.flange_single / 2  
        else:
            hf = material.width / 2
        ht = material.thickness / 2
        wpo = cls._get_web_punching_offset(material)
        
        flange_x = hf
        flange_y = hw - ht
        
        return [
            PunchingMap(PunchingWhere.WEB, Vec2(-ht, wpo), cls.DEG180),
            PunchingMap(PunchingWhere.WEB, Vec2(-ht, -wpo), cls.DEG180),
            PunchingMap(PunchingWhere.WEB, Vec2(ht, wpo), cls.DEG0),
            PunchingMap(PunchingWhere.WEB, Vec2(ht, -wpo), cls.DEG0),
            PunchingMap(PunchingWhere.CENTER, Vec2(-ht, 0), cls.DEG180),
            PunchingMap(PunchingWhere.CENTER, Vec2(ht, 0), cls.DEG0),
            PunchingMap(PunchingWhere.FLANGE, Vec2(-flange_x, flange_y), cls.DEG90),
            PunchingMap(PunchingWhere.FLANGE, Vec2(-flange_x, -flange_y), cls.DEG270),
            PunchingMap(PunchingWhere.FLANGE, Vec2(flange_x, flange_y), cls.DEG90),
            PunchingMap(PunchingWhere.FLANGE, Vec2(flange_x, -flange_y), cls.DEG270),
        ]
    
    @classmethod
    def _create_mesh_profile_z(cls, material: FrameMaterial,
                              draw_profile_arcs: bool) -> MeshProfile:
        """Create Z-section profile.
        
        C# Ref: CreateMeshProfile_Z method
        """
        # Get vertex builder
        vertex_builder = cls._create_vertexes_z(material, draw_profile_arcs)
        
        # Create profile item
        item = MeshProfileItem(vertexes=vertex_builder.vertexes)
        
        # Create punching maps
        punching_map = cls._get_mesh_punchings_z(material)
        
        return MeshProfile(items=[item], punching_map=punching_map)
    
    @classmethod
    def _create_vertexes_z(cls, material: FrameMaterial, draw_profile_arcs: bool) -> VertexBuilder:
        """Create vertices for Z-section.
        
        Based on THREE.js implementation from user.
        """
        vertex_builder = VertexBuilder(draw_profile_arcs, material.thickness)
        
        # Get dimensions for Z-section
        # For Z-section, web is in the middle with flanges on opposite sides
        web_height = material.height  # Total height (e.g., 203mm)
        flange_width = material.width  # Flange width (e.g., 76mm)  
        lip = material.lip if material.lip > 0 else 15.0  # Default 15mm lip
        thickness = material.thickness
        
        # Build Z-section based on THREE.js pattern
        # Start at bottom left flange
        vertex_builder.add_pt(0, 0)
        vertex_builder.add_pt(0, lip)  # Bottom lip up
        vertex_builder.add_pt(thickness, lip)  # Bottom lip inner
        vertex_builder.add_pt(thickness, thickness)  # Bottom flange inner
        vertex_builder.add_pt(flange_width - thickness, thickness)  # To web bottom
        vertex_builder.add_pt(flange_width - thickness, web_height - thickness)  # Web up
        vertex_builder.add_pt(thickness, web_height - thickness)  # Top flange inner start
        vertex_builder.add_pt(thickness, web_height - lip)  # Top lip inner
        vertex_builder.add_pt(0, web_height - lip)  # Top lip outer
        vertex_builder.add_pt(0, web_height)  # Top flange outer
        vertex_builder.add_pt(flange_width, web_height)  # Top flange right
        vertex_builder.add_pt(flange_width, web_height - thickness)  # Top flange inner
        vertex_builder.add_pt(flange_width, thickness)  # Web down
        vertex_builder.add_pt(flange_width, 0)  # Bottom right
        vertex_builder.add_pt(0, 0)  # Close shape
        
        # Center the profile
        vertex_builder.pivot()
        
        # Handle flipped orientation
        if getattr(material, 'flipped', False):
            vertex_builder.mirror(mirror_x=True, reverse=True)
        
        return vertex_builder
    
    @classmethod
    def _create_mesh_profile_th(cls, material: FrameMaterial) -> MeshProfile:
        """Create TopHat (TH) profile.
        
        Based on THREE.js implementation from user.
        """
        vertex_builder = VertexBuilder(False, material.thickness)
        
        # Get dimensions for TH profile
        # Based on user's THREE.js: TH has angled sides
        width = material.width  # Base width (e.g., 64mm for TH064100)
        height = material.height  # Height (e.g., 100mm)
        thickness = material.thickness
        
        # From THREE.js pattern: 
        # finLength = 22, topLength = 36 for TH064100
        # This creates the trapezoidal shape
        fin_length = 22.0  # Side extension at base
        top_length = 36.0  # Top width
        
        # Build TH profile based on THREE.js pattern
        # Start at bottom left
        vertex_builder.add_pt(0, 0)
        vertex_builder.add_pt(0 + fin_length, height)  # Angled left side
        vertex_builder.add_pt(0 + fin_length + top_length, height)  # Top
        vertex_builder.add_pt(0 + fin_length + top_length + fin_length, 0)  # Angled right side
        vertex_builder.add_pt(0 + fin_length + top_length + fin_length - thickness, 0)  # Inner right bottom
        vertex_builder.add_pt(0 + fin_length + top_length + fin_length - thickness - fin_length, height - thickness)  # Inner right top
        vertex_builder.add_pt(0 + fin_length + thickness, height - thickness)  # Inner left top
        vertex_builder.add_pt(0 + thickness, 0)  # Inner left bottom
        vertex_builder.add_pt(0, 0)  # Close shape
        
        # Center the profile
        vertex_builder.pivot()
        
        # Create profile item
        item = MeshProfileItem(vertexes=vertex_builder.vertexes)
        
        return MeshProfile(items=[item], punching_map=[])
    
    @classmethod
    def _create_mesh_profile_shs(cls, material: FrameMaterial,
                                draw_profile_arcs: bool) -> MeshProfile:
        """Create Square Hollow Section profile.
        
        C# Ref: Lines 96-100+
        """
        # SHS dimensions
        hw = material.width / 2
        hh = material.height / 2
        t = material.thickness
        
        # Outer profile
        vertices = []
        vertices.append(Vec2(-hw, -hh))
        vertices.append(Vec2(-hw, hh))
        vertices.append(Vec2(hw, hh))
        vertices.append(Vec2(hw, -hh))
        
        # Inner profile (hollow) - clockwise winding (opposite of outer)
        inner_vertices = []
        inner_vertices.append(Vec2(-hw + t, -hh + t))
        inner_vertices.append(Vec2(hw - t, -hh + t))
        inner_vertices.append(Vec2(hw - t, hh - t))
        inner_vertices.append(Vec2(-hw + t, hh - t))
        
        # Create hollow profile item
        item = MeshProfileItem(vertexes=vertices, inner_vertexes=inner_vertices)
        
        return MeshProfile(items=[item], punching_map=[])
    
    @classmethod
    def _create_mesh_profile_pad(cls, material: FrameMaterial,
                                draw_profile_arcs: bool) -> MeshProfile:
        """Create Personal Access Door stile profile.
        
        C# Ref: Lines 82-87
        """
        # PAD stile dimensions
        hw = material.width / 2
        hh = material.height / 2
        t = material.thickness
        
        # Rebate dimensions
        rebate_w = getattr(material, 'pad_rebate_width', 0)
        rebate_h = getattr(material, 'pad_rebate_height', 0)
        rebate_tail = getattr(material, 'pad_rebate_tail', 0)
        
        # Create profile with rebate if specified
        vertices = []
        
        if rebate_w > 0 and rebate_h > 0:
            # Complex profile with rebate
            # Start from bottom left outer
            vertices.append(Vec2(-hw, -hh))
            vertices.append(Vec2(-hw, hh - rebate_tail))
            vertices.append(Vec2(-hw + rebate_w, hh - rebate_tail))
            vertices.append(Vec2(-hw + rebate_w, hh - rebate_tail - rebate_h))
            vertices.append(Vec2(-hw + t, hh - rebate_tail - rebate_h))
            vertices.append(Vec2(-hw + t, -hh + t))
            vertices.append(Vec2(hw - t, -hh + t))
            vertices.append(Vec2(hw - t, hh - rebate_tail - rebate_h))
            vertices.append(Vec2(hw - rebate_w, hh - rebate_tail - rebate_h))
            vertices.append(Vec2(hw - rebate_w, hh - rebate_tail))
            vertices.append(Vec2(hw, hh - rebate_tail))
            vertices.append(Vec2(hw, -hh))
        else:
            # Simple rectangular hollow profile
            vertices.append(Vec2(-hw, -hh))
            vertices.append(Vec2(-hw, hh))
            vertices.append(Vec2(hw, hh))
            vertices.append(Vec2(hw, -hh))
            
            # Inner hollow
            inner_vertices = []
            inner_vertices.append(Vec2(-hw + t, -hh + t))
            inner_vertices.append(Vec2(-hw + t, hh - t))
            inner_vertices.append(Vec2(hw - t, hh - t))
            inner_vertices.append(Vec2(hw - t, -hh + t))
            
            item = MeshProfileItem(vertexes=vertices, inner_vertexes=inner_vertices)
            return MeshProfile(items=[item], punching_map=[])
        
        # Create profile item
        item = MeshProfileItem(vertexes=vertices)
        
        return MeshProfile(items=[item], punching_map=[])
    
    @classmethod
    def _create_mesh_profile_srdj(cls, material: FrameMaterial,
                                 draw_profile_arcs: bool) -> MeshProfile:
        """Create Side Roller Door Jamb profile.
        
        C# Ref: Lines 40-80
        """
        # SRDJ dimensions
        hw = material.width / 2
        hh = material.height / 2
        t = material.thickness
        tail = getattr(material, 'srdj_tail', 0)
        
        # Create profile with tail cutout
        vertices = []
        
        # Start from bottom right with tail
        vertices.append(Vec2(hw, hh - tail))
        vertices.append(Vec2(hw, hh))
        vertices.append(Vec2(-hw, hh))
        vertices.append(Vec2(-hw, -hh))
        vertices.append(Vec2(-hw + t, -hh))
        vertices.append(Vec2(-hw + t, hh - t))
        vertices.append(Vec2(hw - t, hh - t))
        vertices.append(Vec2(hw - t, hh - tail))
        
        # Mirror for flipped orientation
        if getattr(material, 'flipped', False):
            vertices = [Vec2(-v.x, v.y) for v in vertices]
        
        # Create profile item
        item = MeshProfileItem(vertexes=vertices)
        
        return MeshProfile(items=[item], punching_map=[])
    
    @classmethod
    def _get_mesh_punchings_z(cls, material: FrameMaterial) -> List[PunchingMap]:
        """Get punching map for Z-section.
        
        C# Ref: GetMeshPunchings_Z method lines 554-575
        """
        hw = material.width / 2
        hh = material.height / 2
        ht = material.thickness / 2
        
        flange_fx = -hw + material.z_flange_f / 2
        flange_ex = hw - material.z_flange_e / 2
        
        if getattr(material, 'flipped', False):
            flange_ex, flange_fx = flange_fx, flange_ex
        
        flange_fy = -hh + ht
        flange_ey = hh - ht
        
        return [
            PunchingMap(PunchingWhere.FLANGE, Vec2(flange_fx, flange_fy), cls.DEG270),
            PunchingMap(PunchingWhere.FLANGE, Vec2(flange_ex, flange_ey), cls.DEG90),
        ]
    
    @classmethod
    def _polar(cls, angle: float, distance: float) -> Vec2:
        """Create vector from polar coordinates.
        
        C# Ref: Geo.Polar method
        """
        return Vec2(distance * math.cos(angle), distance * math.sin(angle))
    
    @classmethod
    def _generate_arc(cls, center: Vec2, distance: float, arc1: float, arc2: float, 
                     segments: int, clockwise: bool) -> List[Vec2]:
        """Generate arc vertices.
        
        C# Ref: GenerateArc method lines 505-526
        """
        arc = []
        step = (arc2 - arc1) / segments
        
        arc.append(Vec2(center.x, center.y) + cls._polar(arc1, distance))
        
        for i in range(1, segments):
            pt = Vec2(center.x, center.y) + cls._polar(arc1 + i * step, distance)
            arc.append(pt)
        
        arc.append(Vec2(center.x, center.y) + cls._polar(arc2, distance))
        
        if clockwise:
            arc.reverse()
        
        return arc
    
    @classmethod
    def _get_mesh_punchings_c(cls, material: FrameMaterial) -> List[PunchingMap]:
        """Get punching map for C-section.
        
        C# Ref: GetMeshPunchings_C method lines 577-603
        """
        # Get dimensions safely
        if hasattr(material, 'web'):
            hw = material.web / 2
        else:
            hw = material.height / 2
            
        if hasattr(material, 'flange'):
            hf = material.flange / 2
        else:
            hf = material.width / 2
        ht = material.thickness / 2
        
        face_x = -hf + ht
        face_ang = cls.DEG180
        if getattr(material, 'flipped', False):
            face_x = hf - ht
            face_ang = cls.DEG0
        
        wpo = cls._get_web_punching_offset(material)
        
        return [
            PunchingMap(PunchingWhere.WEB, Vec2(face_x, wpo), face_ang),
            PunchingMap(PunchingWhere.WEB, Vec2(face_x, -wpo), face_ang),
            PunchingMap(PunchingWhere.CENTER, Vec2(face_x, 0), face_ang),
            PunchingMap(PunchingWhere.FLANGE, Vec2(0, hw - ht), cls.DEG90),
            PunchingMap(PunchingWhere.FLANGE, Vec2(0, -hw + ht), cls.DEG270),
            PunchingMap(PunchingWhere.WEB_LEFT, Vec2(face_x, -wpo), face_ang),
            PunchingMap(PunchingWhere.WEB_RIGHT, Vec2(face_x, wpo), face_ang),
        ]
    
    @classmethod
    def _get_web_punching_offset(cls, material: FrameMaterial) -> float:
        """Get web punching offset from center.
        
        C# Ref: GetWebPunchingOffset method lines 633-636
        """
        return getattr(material, 'web_hole_centers', 0) / 2