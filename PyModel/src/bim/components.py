"""Structural component classes for the BIM model.

C# Reference: ShedBim.cs Lines 620-877
"""

# Fix imports to use absolute paths
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from dataclasses import dataclass, field
from typing import List, Optional, Generic, TypeVar

from src.geometry.primitives import Vec3
from src.geometry.basis import Basis3
from src.geometry.matrix import Mat4
from src.geometry.boxes import Box3
from src.materials.base import FrameMaterial, FootingMaterial, BracketMaterial


# C# Ref: Lines 675-688 - public class ShedBimSection
@dataclass
class ShedBimSection:
    """Structural section (beam, column, purlin).
    
    C# Reference: ShedBim.cs lines 675-688
    Represents a linear structural member with material and position.
    """
    # C# Ref: Line 677 - public Vec3 StartPos { get; set; }
    start_pos: Optional[Vec3] = None
    
    # C# Ref: Line 678 - public Vec3 EndPos { get; set; }
    end_pos: Optional[Vec3] = None
    
    # C# Ref: Line 679 - public double Rotation { get; set; }
    rotation: float = 0.0
    
    # C# Ref: Line 680 - public FrameMaterial Material { get; set; }
    material: Optional[FrameMaterial] = None
    
    # C# Ref: Line 681 - public string Tag { get; set; }
    tag: str = ""
    
    # C# Ref: Line 682 - public List<Punching> Punchings { get; set; }
    punchings: List['Punching'] = field(default_factory=list)
    
    # C# Ref: Line 683 - public List<ShedBimSectionCut> SectionCuts { get; set; }
    section_cuts: List['ShedBimSectionCut'] = field(default_factory=list)
    
    # C# Ref: Line 685 - public ColorMaterial Color { get; set; }
    color: Optional['ColorMaterial'] = None
    
    # C# Ref: Line 687 - public ShedBimSection CloneShallow()
    def clone_shallow(self) -> 'ShedBimSection':
        """Create a shallow copy of this section."""
        # In Python, we can use the dataclass copy functionality
        from copy import copy
        return copy(self)


# C# Ref: Lines 702-711 - public class ShedBimSectionCut
@dataclass
class ShedBimSectionCut:
    """Cut specification for a section.
    
    C# Reference: ShedBim.cs lines 702-711
    """
    # C# Ref: Line 704 - public Mat4? Transform { get; set; }
    transform: Optional[Mat4] = None
    
    # C# Ref: Line 705 - public Box3? Box { get; set; }
    box: Optional[Box3] = None
    
    # C# Ref: Lines 707-710 - public override string ToString()
    def __str__(self) -> str:
        return f"Transform:{self.transform}, Box:{self.box}"


# C# Ref: Lines 649-657 - public class ShedBimColumn
@dataclass
class ShedBimColumn:
    """Column with footing and brackets.
    
    C# Reference: ShedBim.cs lines 649-657
    """
    # C# Ref: Line 651 - public ShedBimSection Column { get; set; }
    column: Optional[ShedBimSection] = None
    
    # C# Ref: Line 652 - public ShedBimFooting Footing { get; set; }
    footing: Optional['ShedBimFooting'] = None
    
    # C# Ref: Line 654 - public ShedBimPair<ShedBimBracket> BaseBrackets { get; set; }
    base_brackets: Optional['ShedBimPair'] = None
    
    # C# Ref: Line 655 - public ShedBimPair<ShedBimBracket> TopBrackets { get; set; }
    top_brackets: Optional['ShedBimPair'] = None


# C# Ref: Lines 659-663 - public class ShedBimColumnCompositeSection
@dataclass
class ShedBimColumnCompositeSection:
    """Composite column section (two sections combined).
    
    C# Reference: ShedBim.cs lines 659-663
    """
    # C# Ref: Line 661 - public ShedBimSection Section1 { get; set; }
    section1: Optional[ShedBimSection] = None
    
    # C# Ref: Line 662 - public ShedBimSection Section2 { get; set; }
    section2: Optional[ShedBimSection] = None


# C# Ref: Lines 713-721 - public class ShedBimFooting
@dataclass
class ShedBimFooting:
    """Foundation/footing element.
    
    C# Reference: ShedBim.cs lines 713-721
    """
    # C# Ref: Line 718 - public Vec3 Pos { get; set; }
    pos: Optional[Vec3] = None
    
    # C# Ref: Line 719 - public FootingMaterial Footing { get; set; }
    footing: Optional[FootingMaterial] = None
    
    # C# Ref: Line 720 - public string Tag { get; set; }
    tag: str = ""


# C# Ref: Lines 833-867 - public class ShedBimBracket
@dataclass
class ShedBimBracket:
    """Connection bracket with fasteners.
    
    C# Reference: ShedBim.cs lines 833-867
    """
    # C# Ref: Line 835 - public BracketMaterial Material { get; set; }
    material: Optional[BracketMaterial] = None
    
    # C# Ref: Line 837 - public Vec3 Position { get; set; }
    position: Vec3 = field(default_factory=lambda: Vec3(0, 0, 0))
    
    # C# Ref: Line 839 - public Basis3 Basis { get; set; }
    basis: Basis3 = field(default_factory=Basis3.unit_xyz)
    
    # C# Ref: Line 841 - public BracketAttachment Attachment { get; set; }
    attachment: Optional['BracketAttachment'] = None
    
    # C# Ref: Line 843 - public List<ShedBimFastener> Fasteners { get; set; }
    fasteners: List['ShedBimFastener'] = field(default_factory=list)
    
    # C# Ref: Line 845 - public string Location { get; set; }
    location: str = ""
    
    # C# Ref: Lines 846-851 - Constructor
    def __post_init__(self):
        """Initialize bracket with defaults."""
        if self.attachment is None:
            self.attachment = BracketAttachment(Vec3(0, 0, 0))
    
    # C# Ref: Lines 863-866 - public Vec3 GetFastenerPlacement()
    def get_fastener_placement(self) -> Vec3:
        """Get fastener placement position."""
        return Vec3(0, 0, 0)


# C# Ref: Lines 869-877 - public class ShedBimFastener
@dataclass
class ShedBimFastener:
    """Fastener (bolt, screw) specification.
    
    C# Reference: ShedBim.cs lines 869-877
    """
    # C# Ref: Line 871 - public string Name { get; set; }
    name: str = ""


# Type variable for generic pair
T = TypeVar('T')


# C# Ref: Lines 262-302 - public class ShedBimPair<TItem>
@dataclass
class ShedBimPair(Generic[T]):
    """A pair of items for cases where attachment section is B2B.
    
    C# Reference: ShedBim.cs lines 262-302
    Used for brackets and other paired components.
    """
    # C# Ref: Line 264 - public TItem Item1 { get; set; }
    item1: Optional[T] = None
    
    # C# Ref: Line 266 - public TItem Item2 { get; set; }
    item2: Optional[T] = None
    
    # C# Ref: Lines 281-301 - public void Add(TItem item)
    def add(self, item: T) -> None:
        """Add an item to the pair."""
        if item is None:
            raise ValueError("Item cannot be None")
        
        if self.item1 is None:
            self.item1 = item
            return
        
        if self.item2 is None:
            self.item2 = item
            return
        
        raise RuntimeError("Pair is full")


# BracketAttachment placeholder
@dataclass
class BracketAttachment:
    """Bracket attachment point specification.
    
    Not in the original C# ShedBim.cs but referenced in ShedBimBracket.
    """
    position: Vec3 = field(default_factory=lambda: Vec3(0, 0, 0))


# ColorMaterial placeholder (referenced but not in ShedBim.cs)
@dataclass
class ColorMaterial:
    """Color specification for materials.
    
    Referenced in ShedBim.cs but defined elsewhere.
    """
    name: str = ""
    rgb: tuple[int, int, int] = (255, 255, 255)


# Punching placeholder (referenced in ShedBimSection)
@dataclass
class Punching:
    """Hole punching specification.
    
    Referenced in ShedBim.cs but defined in Materials.cs.
    """
    position: float = 0.0
    diameter: float = 0.0
    where: str = "WEB"  # PunchingWhere enum