"""Wall and roof component classes for the BIM model.

C# Reference: ShedBim.cs Lines 304-559
"""

from dataclasses import dataclass, field
from typing import List, Optional, TYPE_CHECKING

# Avoid circular imports
if TYPE_CHECKING:
    from .components import ShedBimSection, ShedBimColumn, ShedBimBracket, ShedBimPair
    from .accessories import ShedBimFlashing


# C# Ref: Lines 487-491 - public class ShedBimWallGirtBay
@dataclass
class ShedBimWallGirtBay:
    """Wall girt bay with girts and bridging.
    
    C# Reference: ShedBim.cs lines 487-491
    """
    # C# Ref: Line 489 - public List<ShedBimSection> Girts { get; set; }
    girts: List['ShedBimSection'] = field(default_factory=list)
    
    # C# Ref: Line 490 - public List<ShedBimSection> Bridgings { get; set; }
    bridgings: List['ShedBimSection'] = field(default_factory=list)


# C# Ref: Lines 513-518 - public class ShedBimRoofPurlinBay
@dataclass
class ShedBimRoofPurlinBay:
    """Roof purlin bay with purlins and bridging.
    
    C# Reference: ShedBim.cs lines 513-518
    """
    # C# Ref: Line 515 - public List<ShedBimRoofPurlin> RoofPurlins { get; set; }
    roof_purlins: List['ShedBimRoofPurlin'] = field(default_factory=list)
    
    # C# Ref: Line 517 - public List<ShedBimSection> Bridgings { get; set; }
    bridgings: List['ShedBimSection'] = field(default_factory=list)


# C# Ref: Lines 523-531 - public class ShedBimRoofPurlin
@dataclass
class ShedBimRoofPurlin:
    """Roof purlin with optional overhang cap.
    
    C# Reference: ShedBim.cs lines 523-531
    """
    # C# Ref: Line 525 - public ShedBimSection RoofPurlin { get; set; }
    roof_purlin: Optional['ShedBimSection'] = None
    
    # C# Ref: Line 530 - public ShedBimSection OverhangCap { get; set; }
    overhang_cap: Optional['ShedBimSection'] = None


# C# Ref: Lines 533-559 - public class ShedBimEaveBeam
@dataclass
class ShedBimEaveBeam:
    """Eave beam or header beam configuration.
    
    C# Reference: ShedBim.cs lines 533-559
    """
    # C# Ref: Line 535 - public int BayFirst { get; set; }
    bay_first: int = 0
    
    # C# Ref: Line 537 - public int BayLast { get; set; }
    bay_last: int = 0
    
    # C# Ref: Line 542 - public ShedBimSection Beam { get; set; }
    beam: Optional['ShedBimSection'] = None
    
    # C# Ref: Line 547 - public List<ShedBimSection> Girts { get; set; }
    girts: List['ShedBimSection'] = field(default_factory=list)
    
    # C# Ref: Line 552 - public ShedBimSection Slider { get; set; }
    slider: Optional['ShedBimSection'] = None
    
    # C# Ref: Line 558 - public ShedBimPair<ShedBimFlashing> Flashing { get; set; }
    flashing: Optional['ShedBimPair'] = None


# C# Ref: Lines 561-565 - public class ShedBimLongitudinalBraceBay
@dataclass
class ShedBimLongitudinalBraceBay:
    """Longitudinal brace bay configuration.
    
    C# Reference: ShedBim.cs lines 561-565
    """
    # C# Ref: Line 563 - public ShedBimSection BraceLeft { get; set; }
    brace_left: Optional['ShedBimSection'] = None
    
    # C# Ref: Line 564 - public ShedBimSection BraceRight { get; set; }
    brace_right: Optional['ShedBimSection'] = None


# C# Ref: Lines 342-347 - public class ShedBimMullion
@dataclass
class ShedBimMullion:
    """End wall mullion configuration.
    
    C# Reference: ShedBim.cs lines 342-347
    """
    # C# Ref: Line 344 - public ShedBimColumn Mullion { get; set; }
    mullion: Optional['ShedBimColumn'] = None
    
    # C# Ref: Line 345 - public ShedBimColumn RotatedMullion1 { get; set; }
    rotated_mullion1: Optional['ShedBimColumn'] = None
    
    # C# Ref: Line 346 - public ShedBimColumn RotatedMullion2 { get; set; }
    rotated_mullion2: Optional['ShedBimColumn'] = None


# C# Ref: Lines 304-313 - public class ShedBimOutrigger
@dataclass
class ShedBimOutrigger:
    """Outrigger structure for additional support.
    
    C# Reference: ShedBim.cs lines 304-313
    """
    # C# Ref: Line 306 - public ShedBimSection Header { get; set; }
    header: Optional['ShedBimSection'] = None
    
    # C# Ref: Line 308 - public ShedBimColumn Column { get; set; }
    column: Optional['ShedBimColumn'] = None
    
    # C# Ref: Line 310 - public ShedBimColumn Brace { get; set; }
    brace: Optional['ShedBimColumn'] = None
    
    # C# Ref: Line 312 - public ShedBimPair<ShedBimBracket> HeaderBracket { get; set; }
    header_bracket: Optional['ShedBimPair'] = None