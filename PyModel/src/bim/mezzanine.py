"""Mezzanine component classes for the BIM model.

C# Reference: ShedBim.cs Lines 356-368
"""

# Fix imports to use absolute paths
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from dataclasses import dataclass, field
from typing import List, Optional, TYPE_CHECKING

from src.geometry.primitives import Vec3
from .openings import MezzanineStairsInfo

# Avoid circular imports
if TYPE_CHECKING:
    from .components import ShedBimSection, ShedBimColumn


# C# Ref: Lines 356-368 - public class ShedBimMezzStairs
@dataclass
class ShedBimMezzStairs:
    """Mezzanine stairs structure.
    
    C# Reference: ShedBim.cs lines 356-368
    """
    # C# Ref: Line 358 - public MezzanineStairsInfo Info { get; set; }
    info: Optional[MezzanineStairsInfo] = None
    
    # C# Ref: Line 360 - public Vec3 ClearanceUpper { get; set; }
    clearance_upper: Optional[Vec3] = None
    
    # C# Ref: Line 361 - public Vec3 StairsUpper { get; set; }
    stairs_upper: Optional[Vec3] = None
    
    # C# Ref: Line 362 - public Vec3 StairsLower { get; set; }
    stairs_lower: Optional[Vec3] = None
    
    # C# Ref: Line 363 - public Vec3 ClearanceLower { get; set; }
    clearance_lower: Optional[Vec3] = None
    
    # C# Ref: Line 365 - public ShedBimSection Bearer { get; set; }
    bearer: Optional['ShedBimSection'] = None
    
    # C# Ref: Line 366 - public List<ShedBimColumn> Posts { get; set; }
    posts: List['ShedBimColumn'] = field(default_factory=list)