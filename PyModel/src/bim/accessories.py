"""Accessory component classes for the BIM model.

C# Reference: ShedBim.cs Lines 690-831
"""

# Fix imports to use absolute paths
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from dataclasses import dataclass, field
from typing import List, Optional, TYPE_CHECKING

from src.geometry.primitives import Vec3
from src.geometry.lines import Line3
from src.geometry.matrix import Mat4
from src.geometry.boxes import Box3
from src.materials.base import FlashingMaterial, DownpipeMaterial, StrapMaterial
from .components import ColorMaterial

# Avoid circular imports
if TYPE_CHECKING:
    from .components import ShedBimBracket


# C# Ref: Lines 783-811 - public class ShedBimFlashing
@dataclass
class ShedBimFlashing:
    """Flashing component specification.
    
    C# Reference: ShedBim.cs lines 783-811
    """
    # C# Ref: Line 785 - public FlashingMaterial Material { get; set; }
    material: Optional[FlashingMaterial] = None
    
    # C# Ref: Line 786 - public ColorMaterial Color { get; set; }
    color: Optional[ColorMaterial] = None
    
    # C# Ref: Line 787 - public Line3 Position { get; set; }
    position: Optional[Line3] = None
    
    # C# Ref: Line 788 - public double Rotation { get; set; }
    rotation: float = 0.0
    
    # C# Ref: Line 789 - public bool CapStart { get; set; }
    cap_start: bool = False
    
    # C# Ref: Line 790 - public bool CapEnd { get; set; }
    cap_end: bool = False
    
    # C# Ref: Line 791 - public List<ShedBimBracket> Ends { get; set; }
    ends: List['ShedBimBracket'] = field(default_factory=list)
    
    # C# Ref: Line 792 - public List<ShedBimBracket> Stiffeners { get; set; }
    stiffeners: List['ShedBimBracket'] = field(default_factory=list)
    
    # C# Ref: Line 793 - public List<ShedBimFlashingCut> PlumbCuts { get; set; }
    plumb_cuts: List['ShedBimFlashingCut'] = field(default_factory=list)
    
    # C# Ref: Line 801 - public Line3 PresentationOffset { get; set; }
    presentation_offset: Optional[Line3] = None
    
    # C# Ref: Lines 803-808 - public Line3 GetPresentationPosition()
    def get_presentation_position(self) -> Optional[Line3]:
        """Get final presentation position with offset applied."""
        if self.position is None:
            return None
        
        if self.presentation_offset is None:
            return self.position
        
        start_pos = self.position.start + self.presentation_offset.start
        end_pos = self.position.end + self.presentation_offset.end
        return Line3(start_pos, end_pos)
    
    # C# Ref: Line 810 - public ShedBimFlashing CloneShallow()
    def clone_shallow(self) -> 'ShedBimFlashing':
        """Create a shallow copy of this flashing."""
        from copy import copy
        return copy(self)


# C# Ref: Lines 812-821 - public class ShedBimFlashingCut
@dataclass
class ShedBimFlashingCut:
    """Flashing cut specification.
    
    C# Reference: ShedBim.cs lines 812-821
    """
    # C# Ref: Line 814 - public Mat4? Transform { get; set; }
    transform: Optional[Mat4] = None
    
    # C# Ref: Line 815 - public Box3? Box { get; set; }
    box: Optional[Box3] = None
    
    # C# Ref: Lines 817-820 - public override string ToString()
    def __str__(self) -> str:
        return f"Transform:{self.transform}, Box:{self.box}"


# C# Ref: Lines 822-831 - public class ShedBimDownpipe
@dataclass
class ShedBimDownpipe:
    """Downpipe specification.
    
    C# Reference: ShedBim.cs lines 822-831
    """
    # C# Ref: Line 824 - public DownpipeMaterial Material { get; set; }
    material: Optional[DownpipeMaterial] = None
    
    # C# Ref: Line 825 - public ColorMaterial Color { get; set; }
    color: Optional[ColorMaterial] = None
    
    # C# Ref: Line 826 - public Vec3 Position { get; set; }
    position: Optional[Vec3] = None
    
    # C# Ref: Line 827 - public List<Vec3> Points { get; set; }
    points: List[Vec3] = field(default_factory=list)
    
    # C# Ref: Line 828 - public string Tag { get; set; }
    tag: str = ""
    
    # C# Ref: Line 829 - public List<ShedBimBracket> Strap { get; set; }
    strap: List['ShedBimBracket'] = field(default_factory=list)
    
    # C# Ref: Line 830 - public ShedBimBracket Nozzle { get; set; }
    nozzle: Optional['ShedBimBracket'] = None


# C# Ref: Lines 762-767 - public class ShedBimStrapBrace
@dataclass
class ShedBimStrapBrace:
    """Strap brace specification.
    
    C# Reference: ShedBim.cs lines 762-767
    """
    # C# Ref: Line 764 - public List<ShedBimStrapLine> Lines { get; set; }
    lines: List['ShedBimStrapLine'] = field(default_factory=list)
    
    # C# Ref: Line 765 - public StrapMaterial Material { get; set; }
    material: Optional[StrapMaterial] = None
    
    # C# Ref: Line 766 - public string Tag { get; set; }
    tag: str = ""


# C# Ref: Lines 769-773 - public class ShedBimStrapLine
@dataclass
class ShedBimStrapLine:
    """Strap line geometry.
    
    C# Reference: ShedBim.cs lines 769-773
    """
    # C# Ref: Line 771 - public List<Vec3> Points { get; set; }
    points: List[Vec3] = field(default_factory=list)
    
    # C# Ref: Line 772 - public Vec3 Normal { get; set; }
    normal: Optional[Vec3] = None


# C# Ref: Lines 690-693 - public class ShedBimRollerDoorCylinder
@dataclass
class ShedBimRollerDoorCylinder:
    """Roller door cylinder specification.
    
    C# Reference: ShedBim.cs lines 690-693
    """
    # C# Ref: Line 691 - public Vec3 StartPos { get; set; }
    start_pos: Optional[Vec3] = None
    
    # C# Ref: Line 692 - public Vec3 EndPos { get; set; }
    end_pos: Optional[Vec3] = None
    
    # C# Ref: Line 693 - public double DrumRadius { get; set; }
    drum_radius: float = 0.0