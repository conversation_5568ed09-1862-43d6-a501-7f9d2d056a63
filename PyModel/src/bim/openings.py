"""Opening component classes for the BIM model.

C# Reference: ShedBim.cs Lines 567-610 and Openings.cs
"""

# Fix imports to use absolute paths
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from dataclasses import dataclass, field
from typing import List, Optional, TYPE_CHECKING
from enum import Enum

from src.geometry.primitives import Vec3
from .components import ColorMaterial

# Avoid circular imports
if TYPE_CHECKING:
    from .components import ShedBimSection, ShedBimColumn, ShedBimBracket


# C# Ref: Lines 567-574 - public class ShedBimOpenBay
@dataclass
class ShedBimOpenBay:
    """Open bay specification.
    
    C# Reference: ShedBim.cs lines 567-574
    """
    # C# Ref: Line 570 - public int BayNum { get; set; }
    bay_num: int = 0
    
    # C# Ref: Line 572 - public int FrameNum { get; set; }
    frame_num: int = 0
    
    # C# Ref: Line 573 - public List<Vec3> Points { get; set; }
    points: List[Vec3] = field(default_factory=list)


# C# Ref: Lines 576-610 - public class ShedBimOpening
@dataclass
class ShedBimOpening:
    """Wall opening (door/window) specification.
    
    C# Reference: ShedBim.cs lines 576-610
    """
    # C# Ref: Line 578 - public OpeningInfo Info { get; set; }
    info: Optional['OpeningInfo'] = None
    
    # C# Ref: Line 580 - public List<Vec3> Outline { get; set; }
    outline: List[Vec3] = field(default_factory=list)
    
    # C# Ref: Line 581 - public Vec3 Normal { get; set; }
    normal: Optional[Vec3] = None
    
    # C# Ref: Line 583 - public ShedBimColumn Jamb1 { get; set; }
    jamb1: Optional['ShedBimColumn'] = None
    
    # C# Ref: Line 584 - public ShedBimColumn Jamb2 { get; set; }
    jamb2: Optional['ShedBimColumn'] = None
    
    # C# Ref: Line 586 - public ShedBimSection Header { get; set; }
    header: Optional['ShedBimSection'] = None
    
    # C# Ref: Line 587 - public ShedBimBracket HeaderBracket1 { get; set; }
    header_bracket1: Optional['ShedBimBracket'] = None
    
    # C# Ref: Line 588 - public ShedBimBracket HeaderBracket2 { get; set; }
    header_bracket2: Optional['ShedBimBracket'] = None
    
    # C# Ref: Line 590 - public ShedBimSection Sill { get; set; }
    sill: Optional['ShedBimSection'] = None
    
    # C# Ref: Line 592 - public ShedBimColumn StrongJamb1 { get; set; }
    strong_jamb1: Optional['ShedBimColumn'] = None
    
    # C# Ref: Line 593 - public ShedBimColumn StrongJamb2 { get; set; }
    strong_jamb2: Optional['ShedBimColumn'] = None
    
    # C# Ref: Line 595 - public ShedBimSection StrongHeader { get; set; }
    strong_header: Optional['ShedBimSection'] = None
    
    # C# Ref: Line 596 - public ShedBimBracket StrongHeaderBracket1 { get; set; }
    strong_header_bracket1: Optional['ShedBimBracket'] = None
    
    # C# Ref: Line 597 - public ShedBimBracket StrongHeaderBracket2 { get; set; }
    strong_header_bracket2: Optional['ShedBimBracket'] = None
    
    # C# Ref: Line 599 - public ShedBimSection Stiffener1 { get; set; }
    stiffener1: Optional['ShedBimSection'] = None
    
    # C# Ref: Line 600 - public ShedBimSection Stiffener2 { get; set; }
    stiffener2: Optional['ShedBimSection'] = None
    
    # C# Ref: Line 602 - public ShedBimFlashing VSlider { get; set; }
    v_slider: Optional['ShedBimFlashing'] = None
    
    # C# Ref: Line 604 - public List<ShedBimBracket> OpeningBrackets { get; set; }
    opening_brackets: List['ShedBimBracket'] = field(default_factory=list)
    
    # C# Ref: Line 609 - public ShedBimRollerDoorCylinder RollerDoorCylinder { get; set; }
    roller_door_cylinder: Optional['ShedBimRollerDoorCylinder'] = None


# Placeholder for ShedBimRoofOpening (referenced in ShedBimRoof)
@dataclass
class ShedBimRoofOpening:
    """Roof opening specification.
    
    Referenced in ShedBimRoof but not defined in visible code.
    """
    info: Optional['RoofOpeningInfo'] = None
    outline: List[Vec3] = field(default_factory=list)
    normal: Optional[Vec3] = None


# From Openings.cs file

# C# Ref: Openings.cs Lines 46-54 - public enum OpeningInfoDesign
class OpeningInfoDesign(Enum):
    """Opening design types.
    
    C# Reference: Openings.cs lines 46-54
    """
    NONE = 0
    ROLLER_DOOR = 1
    PA_DOOR = 2
    WINDOW = 3
    GLASS_SLIDING_DOOR = 4
    INDUSTRIAL_SLIDING_DOOR = 5


# C# Ref: Openings.cs Lines 9-44 - public class OpeningInfo
@dataclass
class OpeningInfo:
    """Opening information and specifications.
    
    C# Reference: Openings.cs lines 9-44
    """
    # C# Ref: Line 11 - public string Id { get; set; }
    id: str = ""
    
    # C# Ref: Line 13 - public OpeningInfoDesign Design { get; set; }
    design: OpeningInfoDesign = OpeningInfoDesign.NONE
    
    # C# Ref: Line 15 - public string Description { get; set; }
    description: str = ""
    
    # C# Ref: Line 17 - public ColorMaterial Color { get; set; }
    color: Optional[ColorMaterial] = None
    
    # C# Ref: Line 19 - public double Width { get; set; }
    width: float = 0.0
    
    # C# Ref: Line 20 - public double Height { get; set; }
    height: float = 0.0
    
    # C# Ref: Line 22 - public double OpeningWidth { get; set; }
    opening_width: float = 0.0
    
    # C# Ref: Line 23 - public double OpeningHeight { get; set; }
    opening_height: float = 0.0
    
    # C# Ref: Line 25 - public bool RD_HasWindLock { get; set; }
    rd_has_wind_lock: bool = False
    
    # C# Ref: Line 27 - public bool PA_IsDouble { get; set; }
    pa_is_double: bool = False
    
    # C# Ref: Line 28 - public bool PA_IsStable { get; set; }
    pa_is_stable: bool = False
    
    # C# Ref: Line 29 - public bool PA_IsSwingInward { get; set; }
    pa_is_swing_inward: bool = False
    
    # C# Ref: Line 30 - public bool PA_IsSwingRight { get; set; }
    pa_is_swing_right: bool = False
    
    # C# Ref: Line 32 - public bool WI_IsBarnWindow { get; set; }
    wi_is_barn_window: bool = False
    
    # C# Ref: Line 33 - public double WI_BarnWindowPitchDeg { get; set; }
    wi_barn_window_pitch_deg: float = 0.0
    
    # C# Ref: Line 35 - public IndustrialSlidingDoorDesign IND_Design { get; set; }
    ind_design: Optional['IndustrialSlidingDoorDesign'] = None
    
    # C# Ref: Line 36 - public int IND_BaySlide { get; set; }
    ind_bay_slide: int = 0
    
    # C# Ref: Line 37 - public int IND_Track { get; set; }
    ind_track: int = 0
    
    # C# Ref: Line 38 - public double IND_CladdingOffset { get; set; }
    ind_cladding_offset: float = 0.0
    
    # C# Ref: Line 43 - public bool IND_IsApexSlidingDoor { get; set; }
    ind_is_apex_sliding_door: bool = False


# C# Ref: Openings.cs Lines 67-71 - public enum RoofOpeningInfoDesign
class RoofOpeningInfoDesign(Enum):
    """Roof opening design types.
    
    C# Reference: Openings.cs lines 67-71
    """
    SKYLIGHT = 0
    ROOF_VENT = 1


# C# Ref: Openings.cs Lines 56-65 - public class RoofOpeningInfo
@dataclass
class RoofOpeningInfo:
    """Roof opening information.
    
    C# Reference: Openings.cs lines 56-65
    """
    # C# Ref: Line 58 - public RoofOpeningInfoDesign Design { get; set; }
    design: RoofOpeningInfoDesign = RoofOpeningInfoDesign.SKYLIGHT
    
    # C# Ref: Line 60 - public ColorMaterial Color { get; set; }
    color: Optional[ColorMaterial] = None
    
    # C# Ref: Line 62 - public double RV_HeadSize { get; set; }
    rv_head_size: float = 0.0
    
    # C# Ref: Line 63 - public double RV_HeadDepth { get; set; }
    rv_head_depth: float = 0.0
    
    # C# Ref: Line 64 - public double RV_NeckSize { get; set; }
    rv_neck_size: float = 0.0


# C# Ref: Openings.cs Lines 96-106 - public enum MezzanineStairsOrientation
class MezzanineStairsOrientation(Enum):
    """Mezzanine stairs orientation.
    
    C# Reference: Openings.cs lines 96-106
    """
    # C# Ref: Line 100 - Upper=Back, Lower=Front
    BACK_TO_FRONT = 0
    
    # C# Ref: Line 104 - Upper=Front, Lower=Back
    FRONT_TO_BACK = 1


# C# Ref: Openings.cs Lines 73-94 - public class MezzanineStairsInfo
@dataclass
class MezzanineStairsInfo:
    """Mezzanine stairs specifications.
    
    C# Reference: Openings.cs lines 73-94
    """
    # C# Ref: Line 75 - public string Description { get; set; }
    description: str = ""
    
    # C# Ref: Line 76 - public MezzanineStairsOrientation Orientation { get; set; }
    orientation: MezzanineStairsOrientation = MezzanineStairsOrientation.BACK_TO_FRONT
    
    # C# Ref: Line 77 - public double Height { get; set; }
    height: float = 0.0
    
    # C# Ref: Line 78 - public double Length { get; set; }
    length: float = 0.0
    
    # C# Ref: Line 79 - public double Width { get; set; }
    width: float = 0.0
    
    # C# Ref: Line 83 - public int NumTreads { get; set; }
    num_treads: int = 0
    
    # C# Ref: Line 88 - public bool BalustradeLeft { get; set; }
    balustrade_left: bool = False
    
    # C# Ref: Line 93 - public bool BalustradeRight { get; set; }
    balustrade_right: bool = False
    
    # C# Ref: Line 81 - public double GetLengthWithClearance()
    def get_length_with_clearance(self) -> float:
        """Get total length including clearance areas."""
        return self.length + self.width * 2


# Placeholder for IndustrialSlidingDoorDesign (referenced but not defined)
class IndustrialSlidingDoorDesign(Enum):
    """Industrial sliding door design types."""
    STANDARD = 0
    HEAVY_DUTY = 1


# Import to resolve forward references
from .accessories import ShedBimFlashing, ShedBimRollerDoorCylinder