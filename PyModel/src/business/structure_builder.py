"""Structure Builder module - Core builder pattern implementation.

This module implements:
- StructureBuilderBase: Abstract base class for all builders (C# StructureBuilderBase.cs)
- CarportBuilder: Concrete implementation for carports (C# CarportBuilder.cs)
- CarportProduct: Output product containing the BIM model
"""

# Fix imports to use absolute paths
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from typing import Optional, List, Dict, Tuple
import math
import uuid

from src.geometry.primitives import Vec3
from src.geometry.matrix import Mat4
from src.geometry.lines import Line1, Line3
from src.materials.base import (
    FrameMaterial, FrameMaterialType, CladdingMaterial, FlashingMaterial,
    FootingMaterial, FootingMaterialType, DownpipeMaterial, ColorMaterial,
    BracketMaterial, Punching, PunchingWhere
)
from src.materials.helpers import (
    FrameMaterialHelper, CladdingMaterialHelper, FootingMaterialHelper,
    <PERSON>ingMaterial<PERSON>elper, BracketMaterialHelper
)
from src.bim.shed_bim import (
    <PERSON><PERSON><PERSON><PERSON>, ShedBimPartMain, ShedBimPartLeanto, ShedBimSide, ShedBimRoof,
    ShedBimSlab, ShedBimSlabPiece, ShedBimWall
)
from src.bim.components import (
    ShedBimSection, ShedBimColumn, ShedBimFooting, ShedBimBracket,
    ShedBimFastener
)
from src.bim.cladding import (
    ShedBimCladding, ShedBimCladdingSegment
)
from src.bim.accessories import (
    ShedBimFlashing, ShedBimDownpipe
)
from src.bim.wall_roof import (
    ShedBimRoofPurlinBay, ShedBimRoofPurlin
)

from .building_input import BuildingInput, BuildingType, CarportRoofType, ShedInput
from .engineering import EngData
from .helpers import CarportConfig, CarportHelpers


@dataclass
class CarportProduct:
    """Product output from CarportBuilder.
    
    Contains the complete BIM model and metadata.
    """
    job_number: str = field(default_factory=lambda: str(uuid.uuid4()))
    main: Optional[ShedBimPartMain] = None
    leanto_left: Optional[ShedBimPartLeanto] = None
    leanto_right: Optional[ShedBimPartLeanto] = None
    outline_frames: List[List[Vec3]] = field(default_factory=list)
    
    def to_shed_bim(self) -> ShedBim:
        """Convert to ShedBim for compatibility."""
        bim = ShedBim()
        bim.main = self.main
        bim.leanto_left = self.leanto_left
        bim.leanto_right = self.leanto_right
        bim.outline_frames = self.outline_frames
        return bim


class StructureBuilderBase(ABC):
    """Abstract base class for all structure builders.
    
    Implements the 14-step construction pipeline and provides common
    functionality for all building types.
    
    C# Ref: StructureBuilderBase.cs (4054 lines)
    """
    
    def __init__(self, building_input: Optional[BuildingInput] = None, 
                 shed_input: Optional[ShedInput] = None,
                 eng_data: Optional[EngData] = None):
        """Initialize the structure builder.
        
        Args:
            building_input: Input for carport building
            shed_input: Input for shed building  
            eng_data: Engineering validation data
        """
        # Determine building type
        if building_input:
            self.building_type = building_input.building_type
            self.building_input = building_input
            self.shed_input = None
            
            # Calculate common values
            self.bay_size = building_input.length / building_input.bays
            self.tan_ratio = math.tan(math.radians(building_input.pitch))
            self.cos_ratio = math.cos(math.radians(building_input.pitch))
            self.sin_ratio = math.sin(math.radians(building_input.pitch))
            
            # Material selection
            self.is_corrugated = building_input.cladding_type.lower().startswith("corr")
            
            # Common dimensions
            self.height = building_input.height
            self.span = building_input.span
            
        elif shed_input:
            self.building_type = BuildingType.SHED
            self.building_input = None
            self.shed_input = shed_input
            
            # Calculate common values for shed
            self.bay_size = shed_input.length / shed_input.main.endbay_num
            self.tan_ratio = math.tan(math.radians(shed_input.main.roof_pitch))
            self.cos_ratio = math.cos(math.radians(shed_input.main.roof_pitch))
            self.sin_ratio = math.sin(math.radians(shed_input.main.roof_pitch))
            
            # Material selection
            self.is_corrugated = True  # Default for sheds
            
            # Common dimensions
            self.height = 2400  # Default shed height
            self.span = shed_input.main.span
            
        else:
            raise ValueError("Either building_input or shed_input must be provided")
        
        # Initialize default materials
        self._initialize_default_materials()
        
        # Apply engineering data if provided
        self.has_eng_data = eng_data is not None
        if eng_data:
            self._apply_engineering_data(eng_data)
        
        # Apply overrides if specified
        if building_input and building_input.frame_override:
            self._apply_frame_overrides()
        
        # Initialize caches for performance
        self.cache_apex_punching_points: Dict[Tuple[str, str], List[int]] = {}
        self.cache_haunch_rafter_punching_points: Dict[Tuple[str, str], List[int]] = {}
        self.cache_haunch_column_punching_points: Dict[Tuple[str, str], List[int]] = {}
        
        # Configuration
        self.end_bays = building_input.end_bays if building_input else 1
    
    def _initialize_default_materials(self):
        """Initialize default materials for construction.
        
        C# Ref: StructureBuilderBase.cs lines 40-54
        """
        # Frame materials
        self.purlin_rows = 3
        self.rafter = FrameMaterialHelper.get_frame_material("C15015")
        self.column = FrameMaterialHelper.get_frame_material("SHS07507525")
        self.purlin = FrameMaterialHelper.get_frame_material("TH064100")
        self.eave_purlin = FrameMaterialHelper.get_frame_material("C15015")
        self.apex_brace = FrameMaterialHelper.get_frame_material("C10010")
        self.knee_brace = FrameMaterialHelper.get_frame_material("C10010")
        
        # Footing
        self.footing = FootingMaterialHelper.create_bored(300, 300)
        
        # Cladding materials
        self.corrugated_roof = CladdingMaterialHelper.CORRUGATED
        self.monoclad_roof = CladdingMaterialHelper.MONOCLAD
        self.roof_material = self.corrugated_roof if self.is_corrugated else self.monoclad_roof
        self.wall_material = self.roof_material
        
        # Flashing materials
        self.barge_flashing_material = (
            FlashingMaterialHelper.create_flashing("Barge Capping Corrugated") 
            if self.is_corrugated 
            else FlashingMaterialHelper.create_flashing("Barge Capping Monoclad")
        )
        self.gutter_support_flashing_material = FlashingMaterialHelper.create_flashing("Gutter Support")
        self.gutters = FlashingMaterialHelper.create_flashing("Quad Gutter")
        
        # Downpipes
        self.rect_100x50_downpipe = DownpipeMaterial(
            name="RECT_100x50",
            description="100x50 Rectangular Downpipe"
        )
        self.rect_100x75_downpipe = DownpipeMaterial(
            name="RECT_100x75",
            description="100x75 Rectangular Downpipe"
        )
        
        # Adjust defaults based on roof type
        if self.building_input and self.building_input.roof_type == CarportRoofType.GABLE:
            self.purlin = FrameMaterialHelper.get_frame_material("TH064100")
        else:
            self.purlin = FrameMaterialHelper.get_frame_material("C15015")
    
    def _apply_engineering_data(self, eng_data: EngData):
        """Apply engineering validation data to materials.
        
        C# Ref: StructureBuilderBase.cs lines 86-109
        """
        # Frame materials from engineering
        self.rafter = FrameMaterialHelper.get_frame_material(eng_data.ENG_RAFTER)
        self.purlin = FrameMaterialHelper.get_frame_material(eng_data.ENG_PURLINSIZE)
        self.purlin_rows = eng_data.ENG_PURLINROW
        
        # Eave purlin special handling for TopHat
        if eng_data.ENG_PURLINSIZE.startswith("TH"):
            self.eave_purlin = FrameMaterialHelper.get_frame_material("C15015")
        else:
            self.eave_purlin = FrameMaterialHelper.get_frame_material(eng_data.ENG_PURLINSIZE)
        
        # Column
        self.column = FrameMaterialHelper.get_frame_material(eng_data.ENG_COLUMN)
        
        # Bracing
        if eng_data.ENG_APEXBRACE:
            self.apex_brace = FrameMaterialHelper.get_frame_material(eng_data.ENG_APEXBRACE)
            self.knee_brace = self.apex_brace
        
        # Footing
        is_bored = eng_data.ENG_FOOTINGTYPE.lower() == "bored"
        dia = float(eng_data.ENG_FOOTINGDIA)
        depth = float(eng_data.ENG_FOOTINGDEPTH)
        
        if is_bored:
            self.footing = FootingMaterialHelper.create_bored(dia, depth)
        else:
            self.footing = FootingMaterialHelper.create_block(dia, dia, depth)
    
    def _apply_frame_overrides(self):
        """Apply user-specified frame overrides.
        
        C# Ref: StructureBuilderBase.cs lines 115-155
        """
        if not self.building_input:
            return
        
        # Post override
        if self.building_input.post_override:
            self.column = FrameMaterialHelper.get_frame_material(self.building_input.post_override)
        
        # Eave purlin override
        if self.building_input.eave_purlin_override:
            self.eave_purlin = FrameMaterialHelper.get_frame_material(self.building_input.eave_purlin_override)
        
        # Purlin override
        if self.building_input.purlin_override:
            self.purlin = FrameMaterialHelper.get_frame_material(self.building_input.purlin_override)
        
        # Rafter override
        if self.building_input.rafter_override:
            self.rafter = FrameMaterialHelper.get_frame_material(self.building_input.rafter_override)
        
        # Purlin row calculation
        if self.building_input.max_purlin_space <= 0 and self.building_input.extra_purlins == 1:
            self.purlin_rows = 4 if self.building_input.roof_type == CarportRoofType.GABLE else 6
        elif self.building_input.max_purlin_space > 0:
            self.purlin_rows = int(self.building_input.span / self.building_input.max_purlin_space)
        
        # End bays override
        if self.building_input.end_bays > 1:
            self.end_bays = self.building_input.end_bays
        
        # Footing type override
        if self.building_input.footing_type_override:
            is_bored = self.building_input.footing_type_override.lower() == "bored"
            if is_bored:
                self.footing = FootingMaterialHelper.create_bored(300, 300)
            else:
                self.footing = FootingMaterialHelper.create_block(300, 300, 300)
    
    # Abstract methods that must be implemented by subclasses
    @abstractmethod
    def find_slab_structure(self, main: ShedBimPartMain):
        """Find and create slab structure."""
        pass
    
    @abstractmethod
    def find_brackets_structure(self, main: ShedBimPartMain):
        """Find and create bracket structures."""
        pass
    
    @abstractmethod
    def find_columns_and_footings_structure(self, main: ShedBimPartMain):
        """Find and create columns and footings."""
        pass
    
    @abstractmethod
    def find_rafter_structure(self, main: ShedBimPartMain):
        """Find and create rafter structure."""
        pass
    
    @abstractmethod
    def find_roof_claddings_structure(self, main: ShedBimPartMain):
        """Find and create roof cladding."""
        pass
    
    @abstractmethod
    def find_purlins_structure(self, main: ShedBimPartMain):
        """Find and create purlins."""
        pass
    
    @abstractmethod
    def find_eave_purlin_structure(self, main: ShedBimPartMain):
        """Find and create eave purlins."""
        pass
    
    @abstractmethod
    def find_attached_awning_wall_structure(self, leanto: Optional[ShedBimPartLeanto]):
        """Find and create attached awning wall structure."""
        pass
    
    @abstractmethod
    def find_flashings_structure(self, main: ShedBimPartMain):
        """Find and create flashings."""
        pass
    
    @abstractmethod
    def find_gutters_and_downpipes_structure(self, main: ShedBimPartMain):
        """Find and create gutters and downpipes."""
        pass
    
    @abstractmethod
    def find_braces_structure(self, main: ShedBimPartMain):
        """Find and create bracing."""
        pass
    
    @abstractmethod
    def find_punchings_structure(self, main: ShedBimPartMain):
        """Find and create punching holes."""
        pass
    
    @abstractmethod
    def find_fasteners_structure(self, main: ShedBimPartMain):
        """Find and create fasteners."""
        pass
    
    @abstractmethod
    def find_wall_claddings_parent(self, main: ShedBimPartMain):
        """Find and create wall claddings.
        
        C# Ref: IStructureBuilder.cs line 25, StructureBuilderBase.cs line 881
        Note: This is the 15th step in the full construction pipeline.
        """
        pass
    
    # Common helper methods
    def get_color_material(self, color_name: str) -> Optional[ColorMaterial]:
        """Get color material by name.
        
        C# Ref: Common helper method used throughout
        """
        return CarportHelpers.get_color_material(color_name)
    
    def calculate_punching_positions(self, material_name: str, member_type: str, 
                                   length: float, connection_count: int) -> List[int]:
        """Calculate punching positions with caching.
        
        C# Ref: Caching logic in StructureBuilderBase
        """
        cache_key = (material_name, member_type)
        
        # Check appropriate cache
        if member_type == "apex" and cache_key in self.cache_apex_punching_points:
            return self.cache_apex_punching_points[cache_key]
        elif member_type == "haunch_rafter" and cache_key in self.cache_haunch_rafter_punching_points:
            return self.cache_haunch_rafter_punching_points[cache_key]
        elif member_type == "haunch_column" and cache_key in self.cache_haunch_column_punching_points:
            return self.cache_haunch_column_punching_points[cache_key]
        
        # Calculate positions
        positions = CarportHelpers.calculate_punching_points(
            FrameMaterialHelper.get_frame_material(material_name),
            length,
            connection_count
        )
        
        # Cache results
        if member_type == "apex":
            self.cache_apex_punching_points[cache_key] = positions
        elif member_type == "haunch_rafter":
            self.cache_haunch_rafter_punching_points[cache_key] = positions
        elif member_type == "haunch_column":
            self.cache_haunch_column_punching_points[cache_key] = positions
        
        return positions


class CarportBuilder(StructureBuilderBase):
    """Concrete builder implementation for carport structures.
    
    Implements the 14-step construction pipeline for carports.
    
    C# Ref: CarportBuilder.cs (lines 1-182+)
    """
    
    def __init__(self, building_input: BuildingInput, eng_data: Optional[EngData] = None):
        """Initialize carport builder.
        
        Args:
            building_input: Carport building input
            eng_data: Optional engineering validation data
        """
        super().__init__(building_input=building_input, eng_data=eng_data)
        
        # Initialize product
        self.bim = CarportProduct()
        
        # Apply carport-specific defaults
        if not self.has_eng_data:
            if building_input.roof_type == CarportRoofType.GABLE:
                self.purlin_rows = 3
                self.purlin = FrameMaterialHelper.get_frame_material("TH064100")
            else:
                self.purlin_rows = 5
                self.purlin = FrameMaterialHelper.get_frame_material("C15015")
    
    @staticmethod
    def create_carport(building_input: BuildingInput, eng_data: Optional[EngData] = None) -> CarportProduct:
        """Static factory method to create a carport.
        
        C# Ref: CarportBuilder.cs lines 30-35
        
        Args:
            building_input: Carport building input
            eng_data: Optional engineering validation data
            
        Returns:
            CarportProduct containing the complete BIM model
        """
        builder = CarportBuilder(building_input, eng_data)
        return builder._prepare_carport()
    
    def _prepare_carport(self) -> CarportProduct:
        """Execute the 14-step construction pipeline.
        
        C# Ref: CarportBuilder.cs lines 111-142
        
        Returns:
            Complete carport product
        """
        # Step 1: Initialize BIM structure
        self._initialize_bim()
        
        # Step 2: Create slab if required
        self.find_slab_structure(self.bim.main)
        
        # Step 3: Create brackets
        self.find_brackets_structure(self.bim.main)
        
        # Step 4: Create columns and footings
        self.find_columns_and_footings_structure(self.bim.main)
        
        # Step 5: Create rafters
        self.find_rafter_structure(self.bim.main)
        
        # Step 6: Create roof cladding
        self.find_roof_claddings_structure(self.bim.main)
        
        # Step 7: Create purlins
        self.find_purlins_structure(self.bim.main)
        
        # Step 8: Create eave purlins
        self.find_eave_purlin_structure(self.bim.main)
        
        # Step 9: Create attached awning wall if needed
        self.find_attached_awning_wall_structure(self.bim.leanto_right)
        
        # Step 10: Create flashings
        self.find_flashings_structure(self.bim.main)
        
        # Step 11: Create gutters and downpipes
        self.find_gutters_and_downpipes_structure(self.bim.main)
        
        # Step 12: Create braces
        self.find_braces_structure(self.bim.main)
        
        # Step 13: Create punchings
        self.find_punchings_structure(self.bim.main)
        
        # Step 14: Create fasteners
        self.find_fasteners_structure(self.bim.main)
        
        return self.bim
    
    def _initialize_bim(self):
        """Initialize the BIM structure.
        
        C# Ref: CarportBuilder.cs lines 144-180
        """
        # Create main part
        self.bim.main = ShedBimPartMain()
        self.bim.main.side_left = ShedBimSide()
        self.bim.main.side_right = ShedBimSide()
        self.bim.outline_frames = []
        
        # Set roof type
        self.bim.main.roof_type = self.building_input.roof_type.value
        
        # Create left roof
        self.bim.main.roof_left = ShedBimRoof()
        
        # Create right roof for gable
        if self.building_input.roof_type == CarportRoofType.GABLE:
            self.bim.main.roof_right = ShedBimRoof()
        
        # Create leanto parts for attached awning
        if self.building_input.roof_type == CarportRoofType.ATTACHED_AWNING:
            self.bim.leanto_right = ShedBimPartLeanto()
            self.bim.leanto_left = ShedBimPartLeanto()
        
        # Set dimensions
        self.bim.main.wall_span_extents = Line1(0, self.building_input.span)
        self.bim.main.wall_length_extents = Line1(0, self.building_input.length)
        self.bim.main.wall_height = self.building_input.height
    
    def find_slab_structure(self, main: ShedBimPartMain):
        """Create slab structure if required.
        
        C# Ref: StructureBuilderBase.cs lines 185-216
        """
        if not self.building_input.slab:
            return
        
        overhang = self.building_input.overhang
        span = self.building_input.span
        length = self.building_input.length
        
        main.slab = ShedBimSlab()
        main.slab.middle = ShedBimSlabPiece(
            thickness=CarportConfig.SLAB_THICKNESS,
            bottom_left=Vec3(overhang, 0, 0),
            bottom_right=Vec3(overhang + span, 0, 0),
            top_right=Vec3(overhang + span, length, 0),
            top_left=Vec3(overhang, length, 0)
        )
    
    def find_brackets_structure(self, main: ShedBimPartMain):
        """Create bracket structures.
        
        C# Ref: Implementation in CarportBuilder
        """
        # Brackets will be created based on connection requirements
        # This is typically done after columns and rafters are placed
        pass  # Implemented after columns and rafters
    
    def find_columns_and_footings_structure(self, main: ShedBimPartMain):
        """Create columns and footings with proper C# alignment.
        
        C# Ref: FindColumnsAndFootingsStructureBuilder.cs
        """
        left_columns = []
        right_columns = []
        
        # Get overhang based on roof type
        overhang = self._get_overhang_for_roof_type()
        
        # Calculate X offsets based on roof type
        if self.building_input.roof_type == CarportRoofType.GABLE:
            # Gable uses eave purlin flange
            left_x_offset = overhang + self.column.width / 2 + self.eave_purlin.flange
        else:
            # Flat/Awning uses rafter flange
            left_x_offset = overhang + self.column.width / 2 + self.rafter.flange
        
        # Calculate column heights
        left_column_end_z = self.building_input.height + self.rafter.flange * self.tan_ratio
        right_column_end_z = left_column_end_z
        
        if self.building_input.roof_type != CarportRoofType.GABLE:
            right_column_end_z += (self.building_input.span - self.column.width) * self.tan_ratio
        
        # Y offset calculations
        start_end_y_offset = 0.5 * self.column.width
        if self.building_input.roof_type != CarportRoofType.GABLE:
            middle_y_offset = 0.5 * (self.rafter.flange + self.column.width)
        else:
            middle_y_offset = 0
        
        # Get color
        color = self.get_color_material(self.building_input.post_color)
        
        # Create columns for each frame
        for i in range(self.building_input.bays + 1):
            # Calculate Y offset based on frame position
            if i == 0:
                left_y_offset = start_end_y_offset
            elif i == self.building_input.bays:
                left_y_offset = -start_end_y_offset
            else:
                left_y_offset = middle_y_offset
            
            # Skip right columns for attached awning
            create_right_column = self.building_input.roof_type != CarportRoofType.ATTACHED_AWNING
            
            # Left column
            left_column_start = Vec3(left_x_offset, left_y_offset + i * self.bay_size, 0)
            left_column_end = Vec3(left_x_offset, left_y_offset + i * self.bay_size, left_column_end_z)
            
            left_section = ShedBimSection(
                material=self.column,
                start_pos=left_column_start,
                end_pos=left_column_end,
                tag=f"COL_MAIN_L_{i + 1}",
                color=color
            )
            
            left_footing = ShedBimFooting(
                footing=self.footing,
                pos=left_column_start,
                tag=f"FOOT_MAIN_L_{i + 1}"
            )
            
            left_column = ShedBimColumn(
                column=left_section,
                footing=left_footing
            )
            left_columns.append(left_column)
            
            # Right column (if not attached awning)
            if create_right_column:
                right_x_offset = overhang + self.building_input.span - self.column.width / 2
                right_column_start = Vec3(right_x_offset, left_y_offset + i * self.bay_size, 0)
                right_column_end = Vec3(right_x_offset, left_y_offset + i * self.bay_size, right_column_end_z)
            
                right_section = ShedBimSection(
                    material=self.column,
                    start_pos=right_column_start,
                    end_pos=right_column_end,
                    tag=f"COL_MAIN_R_{i + 1}",
                    color=color
                )
                
                right_footing = ShedBimFooting(
                    footing=self.footing,
                    pos=right_column_start,
                    tag=f"FOOT_MAIN_R_{i + 1}"
                )
                
                right_column = ShedBimColumn(
                    column=right_section,
                    footing=right_footing
                )
                right_columns.append(right_column)
        
        # Assign to BIM
        main.side_left.columns = left_columns
        main.side_right.columns = right_columns
    
    def find_rafter_structure(self, main: ShedBimPartMain):
        """Create rafter structure based on roof type.
        
        C# Ref: FindRafterStructureBuilder.cs
        """
        if self.building_input.roof_type == CarportRoofType.GABLE:
            self._find_gable_rafters(main)
        else:
            self._find_flat_rafters(main)
    
    def _find_flat_rafters(self, main: ShedBimPartMain):
        """Create flat/awning rafters spanning full width.
        
        C# Ref: FindFlatRafters method
        """
        rafters = []
        color = self.get_color_material(self.building_input.rafter_color)
        overhang = self._get_overhang_for_roof_type()
        
        # Rafter Z offset calculation
        web_cos = 0.5 * self.rafter.web * self.cos_ratio
        
        for i in range(self.building_input.bays + 1):
            # Y position
            y_pos = i * self.bay_size
            
            # Rafter spans from X=0 to X=span+2*overhang
            start_x = 0
            end_x = self.building_input.span + 2 * overhang
            
            # For attached awning, reduce end X by purlin flange
            if self.building_input.roof_type == CarportRoofType.ATTACHED_AWNING:
                end_x -= self.purlin.flange
            
            # Z positions including pitch
            start_z = self.building_input.height - overhang * self.tan_ratio - web_cos
            end_z = self.building_input.height + (self.building_input.span + overhang) * self.tan_ratio - web_cos
            
            # Rotation: 0 for normal frames, π for last frame
            rotation = math.pi if i == self.building_input.bays else 0
            
            rafter = ShedBimSection(
                material=self.rafter,
                start_pos=Vec3(start_x, y_pos, start_z),
                end_pos=Vec3(end_x, y_pos, end_z),
                tag=f"RAF_MAIN_{i + 1}",
                color=color,
                rotation=rotation
            )
            rafters.append(rafter)
        
        # Store all rafters in left roof
        if main.roof_left:
            main.roof_left.rafters = rafters
    
    def _find_gable_rafters(self, main: ShedBimPartMain):
        """Create gable rafters (left and right meeting at apex).
        
        C# Ref: FindGableRafters method
        """
        left_rafters = []
        right_rafters = []
        color = self.get_color_material(self.building_input.rafter_color)
        
        # Calculate apex position
        apex_x = 0.5 * self.building_input.span
        
        # Web dimensions for positioning
        web_sin = 0.5 * self.rafter.web * self.sin_ratio
        web_cos = 0.5 * self.rafter.web * self.cos_ratio
        purlin_web_cos = self.purlin.web / self.cos_ratio
        
        for i in range(self.building_input.bays + 1):
            y_pos = i * self.bay_size
            
            # Left rafter
            left_start_x = self.column.width + self.eave_purlin.flange + web_sin
            left_end_x = apex_x - web_sin
            
            left_start_z = (self.building_input.height + 
                           (self.column.width + self.eave_purlin.flange) * self.tan_ratio -
                           purlin_web_cos - web_cos)
            left_end_z = left_start_z + (left_end_x - left_start_x) * self.tan_ratio
            
            # Rotation: 0 for normal frames, π for last frame
            rotation = math.pi if i == self.building_input.bays else 0
            
            left_rafter = ShedBimSection(
                material=self.rafter,
                start_pos=Vec3(left_start_x, y_pos, left_start_z),
                end_pos=Vec3(left_end_x, y_pos, left_end_z),
                tag=f"RAF_MAIN_L_{i + 1}",
                color=color,
                rotation=rotation
            )
            left_rafters.append(left_rafter)
            
            # Right rafter (mirror of left)
            right_start_x = self.building_input.span - self.column.width - self.eave_purlin.flange - web_sin
            right_end_x = apex_x + web_sin
            
            right_start_z = left_start_z  # Same height as left
            right_end_z = left_end_z      # Same apex height
            
            right_rafter = ShedBimSection(
                material=self.rafter,
                start_pos=Vec3(right_start_x, y_pos, right_start_z),
                end_pos=Vec3(right_end_x, y_pos, right_end_z),
                tag=f"RAF_MAIN_R_{i + 1}",
                color=color,
                rotation=rotation
            )
            right_rafters.append(right_rafter)
        
        # Store rafters in respective roof objects
        if main.roof_left:
            main.roof_left.rafters = left_rafters
        if main.roof_right:
            main.roof_right.rafters = right_rafters
    
    def find_roof_claddings_structure(self, main: ShedBimPartMain):
        """Create roof cladding.
        
        C# Ref: Cladding generation logic
        """
        # Create cladding for left roof
        if main.roof_left:
            main.roof_left.wall = ShedBimWall()
            main.roof_left.wall.cladding = ShedBimCladding(
                cladding=self.roof_material,
                color=self.get_color_material(self.building_input.roof_color)
            )
            
            # Add cladding segments
            segments = []
            for i in range(self.building_input.bays):
                segment = ShedBimCladdingSegment(
                    start_offset=i * self.bay_size,
                    end_offset=(i + 1) * self.bay_size,
                    width=self.building_input.span
                )
                segments.append(segment)
            
            main.roof_left.wall.cladding.cladding_segments = segments
        
        # Create cladding for right roof (gable only)
        if main.roof_right:
            main.roof_right.wall = ShedBimWall()
            main.roof_right.wall.cladding = ShedBimCladding(
                cladding=self.roof_material,
                color=self.get_color_material(self.building_input.roof_color)
            )
            # Similar segment creation for right side
    
    def find_purlins_structure(self, main: ShedBimPartMain):
        """Create purlins based on roof type.
        
        C# Ref: FindPurlinsStructureBuilder.cs
        """
        if self.building_input.roof_type == CarportRoofType.GABLE:
            self._find_gable_purlins(main)
        else:
            self._find_flat_purlins(main)
    
    def _find_flat_purlins(self, main: ShedBimPartMain):
        """Create segmented purlins between rafters for flat/awning roofs.
        
        C# Ref: FindPurlinsFlat method
        """
        from ..bim.wall_roof import ShedBimRoofPurlin
        
        color = self.get_color_material(self.building_input.roof_color)
        overhang = self._get_overhang_for_roof_type()
        
        # Calculate X positions for purlins
        x_interval = (self.building_input.span - self.eave_purlin.flange) / (self.purlin_rows - 1)
        
        # Create purlin bays for each section between rafters
        purlin_bays = []
        
        for bay_id in range(self.building_input.bays):
            bay = ShedBimRoofPurlinBay()
            bay.roof_purlins = []
            
            # Y-axis segmentation (between rafters)
            if bay_id == 0:
                # First bay
                y_start = self.rafter.width
                y_end = (bay_id + 1) * self.bay_size - 0.5 * self.rafter.width
            elif bay_id == self.building_input.bays - 1:
                # Last bay
                y_start = bay_id * self.bay_size + 0.5 * self.rafter.width
                y_end = (bay_id + 1) * self.bay_size - self.rafter.width
            else:
                # Middle bays
                y_start = bay_id * self.bay_size + 0.5 * self.rafter.width
                y_end = (bay_id + 1) * self.bay_size - 0.5 * self.rafter.width
            
            # Adjust for single bay
            if self.building_input.bays == 1:
                y_end -= 0.5 * self.rafter.width
            
            # Create purlins at each X position
            for purlin_id in range(1, self.purlin_rows + 1):
                x_pos = (purlin_id - 1) * x_interval + 0.5 * self.rafter.flange + overhang
                z_interval = (purlin_id - 1) * x_interval
                z_pos = self.building_input.height + z_interval * self.tan_ratio
                
                # Adjust Z for eave purlin web
                adjusted_z = z_pos - 0.5 * self.eave_purlin.web / self.cos_ratio
                
                # Rotation: 0 for edge purlins, pitch angle for middle purlins
                if purlin_id == 1 or purlin_id == self.purlin_rows:
                    rotation = 0
                else:
                    rotation = math.radians(self.building_input.pitch)
                
                purlin_section = ShedBimSection(
                    material=self.purlin,
                    start_pos=Vec3(x_pos, y_start, adjusted_z),
                    end_pos=Vec3(x_pos, y_end, adjusted_z),
                    tag=f"PUR_MAIN_L_{bay_id + 1}_{purlin_id}",
                    color=color,
                    rotation=rotation
                )
                
                purlin = ShedBimRoofPurlin(roof_purlin=purlin_section)
                bay.roof_purlins.append(purlin)
            
            purlin_bays.append(bay)
        
        if main.roof_left:
            main.roof_left.roof_purlin_bays = purlin_bays
    
    def _find_gable_purlins(self, main: ShedBimPartMain):
        """Create overlapping purlins for gable roofs.
        
        C# Ref: FindPurlinsGable method
        """
        from ..bim.wall_roof import ShedBimRoofPurlin
        
        color = self.get_color_material(self.building_input.roof_color)
        
        # Calculate gable purlin positions
        post_offset = 0.5 * self.column.width
        gable_start_x = 2 * post_offset + self.eave_purlin.flange + 0.5 * self.rafter.width * self.sin_ratio
        gable_end_x = 0.5 * self.building_input.span - 0.5 * self.rafter.width * self.sin_ratio
        x_interval = (gable_end_x - gable_start_x) / self.purlin_rows
        
        # Rotation angle matching roof pitch
        rotation = math.radians(self.building_input.pitch)
        
        # Create left roof purlins
        left_purlin_bays = []
        
        for bay_id in range(self.building_input.bays):
            bay = ShedBimRoofPurlinBay()
            bay.roof_purlins = []
            
            # Y-axis overlap (extends beyond rafters)
            if bay_id == 0:
                # First bay
                y_start = 0
                y_end = (bay_id + 1) * self.bay_size + self.rafter.flange
            elif bay_id == self.building_input.bays - 1:
                # Last bay
                y_start = bay_id * self.bay_size - self.rafter.width
                y_end = self.building_input.length
            else:
                # Middle bays
                y_start = bay_id * self.bay_size - self.rafter.flange
                y_end = (bay_id + 1) * self.bay_size + self.rafter.flange
            
            # Adjust for single bay
            if self.building_input.bays == 1:
                y_end -= self.rafter.flange
            
            # Create purlins along the slope
            for purlin_id in range(1, self.purlin_rows + 1):
                x_offset = (purlin_id - 1) * x_interval
                x_pos = x_offset + self.building_input.overhang
                
                # Calculate Z based on roof slope
                z_base = self.building_input.height
                z_offset = x_offset * self.tan_ratio
                z_adjustment = self.purlin.web / self.cos_ratio + 0.5 * self.rafter.web * self.cos_ratio
                z_pos = z_base + z_offset - z_adjustment
                
                purlin_section = ShedBimSection(
                    material=self.purlin,
                    start_pos=Vec3(x_pos, y_start, z_pos),
                    end_pos=Vec3(x_pos, y_end, z_pos),
                    tag=f"PUR_MAIN_L_{bay_id + 1}_{purlin_id}",
                    color=color,
                    rotation=rotation
                )
                
                purlin = ShedBimRoofPurlin(roof_purlin=purlin_section)
                bay.roof_purlins.append(purlin)
            
            left_purlin_bays.append(bay)
        
        # Create right roof purlins (mirror of left)
        right_purlin_bays = []
        
        for bay_id in range(self.building_input.bays):
            bay = ShedBimRoofPurlinBay()
            bay.roof_purlins = []
            
            # Same Y-axis overlap as left side
            if bay_id == 0:
                y_start = 0
                y_end = (bay_id + 1) * self.bay_size + self.rafter.flange
            elif bay_id == self.building_input.bays - 1:
                y_start = bay_id * self.bay_size - self.rafter.width
                y_end = self.building_input.length
            else:
                y_start = bay_id * self.bay_size - self.rafter.flange
                y_end = (bay_id + 1) * self.bay_size + self.rafter.flange
            
            if self.building_input.bays == 1:
                y_end -= self.rafter.flange
            
            # Create purlins along the right slope
            for purlin_id in range(1, self.purlin_rows + 1):
                x_offset = (purlin_id - 1) * x_interval
                x_pos = self.building_input.span - x_offset + self.building_input.overhang
                
                # Same Z calculation as left side
                z_pos = (self.building_input.height + x_offset * self.tan_ratio - 
                        self.purlin.web / self.cos_ratio - 0.5 * self.rafter.web * self.cos_ratio)
                
                # Negative rotation for right side
                purlin_section = ShedBimSection(
                    material=self.purlin,
                    start_pos=Vec3(x_pos, y_start, z_pos),
                    end_pos=Vec3(x_pos, y_end, z_pos),
                    tag=f"PUR_MAIN_R_{bay_id + 1}_{purlin_id}",
                    color=color,
                    rotation=-rotation
                )
                
                purlin = ShedBimRoofPurlin(roof_purlin=purlin_section)
                bay.roof_purlins.append(purlin)
            
            right_purlin_bays.append(bay)
        
        # Assign to roof objects
        if main.roof_left:
            main.roof_left.roof_purlin_bays = left_purlin_bays
        if main.roof_right:
            main.roof_right.roof_purlin_bays = right_purlin_bays
    
    def find_eave_purlin_structure(self, main: ShedBimPartMain):
        """Create eave purlins.
        
        C# Ref: FindEavePurlinStructureBuilder in StructureBuilderBase.cs
        """
        # Import here to avoid circular imports
        from ..bim.wall_roof import ShedBimEaveBeam
        
        # Eave purlins are created for all roof types
        color = self.get_color_material(self.building_input.roof_color)
        
        if self.building_input.roof_type == CarportRoofType.GABLE:
            # Gable roof eave purlin positions
            left_x = self.eave_purlin.flange * 0.5
            right_x = self.building_input.span - (self.eave_purlin.flange / 2)
            z_pos = self.building_input.height - 0.5 * self.eave_purlin.web
            
            # Rotations for gable
            left_rotation = math.pi  # 180 degrees
            right_rotation = 0       # 0 degrees
            
            # Left eave beam
            left_eave_section = ShedBimSection(
                material=self.eave_purlin,
                start_pos=Vec3(left_x, 0, z_pos),
                end_pos=Vec3(left_x, self.building_input.length, z_pos),
                tag="EAVE_BEAM_L",
                color=color,
                rotation=left_rotation
            )
            
            # Right eave beam
            right_eave_section = ShedBimSection(
                material=self.eave_purlin,
                start_pos=Vec3(right_x, 0, z_pos),
                end_pos=Vec3(right_x, self.building_input.length, z_pos),
                tag="EAVE_BEAM_R",
                color=color,
                rotation=right_rotation
            )
        else:
            # Flat/Awning roof eave purlin positions
            pitch_radians = self.building_input.pitch * math.pi / 180
            
            # Left eave purlin
            left_x = 0 + (self.eave_purlin.width / 2)
            left_eave_height = self.building_input.height - (self.building_input.overhang * math.tan(pitch_radians))
            left_z = left_eave_height - (0.5 * self.eave_purlin.web)
            
            # Right eave purlin
            right_x = self.building_input.span + 2 * self.building_input.overhang - (self.eave_purlin.width / 2)
            right_eave_height = self.building_input.height + ((self.building_input.span + self.building_input.overhang) * math.tan(pitch_radians))
            right_z = right_eave_height - (0.5 * self.eave_purlin.web)
            
            # Rotations for flat/awning
            if self.building_input.overhang > 0:
                left_rotation = math.pi * (self.building_input.pitch + 180) / 180
                right_rotation = math.pi * self.building_input.pitch / 180
            else:
                left_rotation = math.pi  # 180 degrees
                right_rotation = 0       # 0 degrees
            
            # Left eave beam
            left_eave_section = ShedBimSection(
                material=self.eave_purlin,
                start_pos=Vec3(left_x, 0, left_z),
                end_pos=Vec3(left_x, self.building_input.length, left_z),
                tag="EAVE_BEAM_L",
                color=color,
                rotation=left_rotation
            )
            
            # Right eave beam
            right_eave_section = ShedBimSection(
                material=self.eave_purlin,
                start_pos=Vec3(right_x, 0, right_z),
                end_pos=Vec3(right_x, self.building_input.length, right_z),
                tag="EAVE_BEAM_R",
                color=color,
                rotation=right_rotation
            )
        
        # Create eave beam objects
        left_eave_beam = ShedBimEaveBeam(
            bay_first=0,
            bay_last=self.building_input.bays - 1,
            beam=left_eave_section
        )
        
        right_eave_beam = ShedBimEaveBeam(
            bay_first=0,
            bay_last=self.building_input.bays - 1,
            beam=right_eave_section
        )
        
        # Assign to sides
        if main.side_left:
            main.side_left.eave_beams = [left_eave_beam]
        if main.side_right:
            main.side_right.eave_beams = [right_eave_beam]
    
    def find_attached_awning_wall_structure(self, leanto: Optional[ShedBimPartLeanto]):
        """Create attached awning wall structure.
        
        C# Ref: Attached awning specific implementation
        """
        if not leanto or self.building_input.roof_type != CarportRoofType.ATTACHED_AWNING:
            return
        
        # Implementation for attached awning walls
        # This would create the wall attachment structure
        pass
    
    def find_flashings_structure(self, main: ShedBimPartMain):
        """Create flashings.
        
        C# Ref: Flashing placement logic
        """
        color = self.get_color_material(self.building_input.flashing_color)
        
        if self.building_input.roof_type == CarportRoofType.FLAT:
            # Barge flashing for roof edges
            flashings = []
            
            # Front barge
            front_flashing = ShedBimFlashing(
                material=self.barge_flashing_material,
                color=color,
                position=Line3(
                    Vec3(self.building_input.overhang, 0, self.building_input.height),
                    Vec3(
                        self.building_input.overhang + self.building_input.span,
                        0,
                        self.building_input.height + self.building_input.span * self.tan_ratio
                    )
                )
            )
            flashings.append(front_flashing)
            
            # Back barge
            back_flashing = ShedBimFlashing(
                material=self.barge_flashing_material,
                color=color,
                position=Line3(
                    Vec3(self.building_input.overhang, self.building_input.length, self.building_input.height),
                    Vec3(
                        self.building_input.overhang + self.building_input.span,
                        self.building_input.length,
                        self.building_input.height + self.building_input.span * self.tan_ratio
                    )
                )
            )
            flashings.append(back_flashing)
            
            # Add flashings to roof
            if main.roof_left:
                main.roof_left.barge_flashings = flashings
                
        elif self.building_input.roof_type == CarportRoofType.GABLE:
            # Ridge flashing
            ridge_x = self.building_input.overhang + self.building_input.span / 2
            ridge_height = self.building_input.height + (self.building_input.span / 2) * self.tan_ratio
            
            ridge_flashing = ShedBimFlashing(
                material=FlashingMaterialHelper.create_flashing("Ridge Capping"),
                color=color,
                position=Line3(
                    Vec3(ridge_x, 0, ridge_height),
                    Vec3(ridge_x, self.building_input.length, ridge_height)
                )
            )
            
            # Store ridge flashing in main
            main.ridge_flashings = [ridge_flashing]
            
            # Barge flashings for gable ends
            left_barge_flashings = []
            right_barge_flashings = []
            
            # Front gable barges (left and right slopes)
            # Left slope front
            left_front_barge = ShedBimFlashing(
                material=self.barge_flashing_material,
                color=color,
                position=Line3(
                    Vec3(self.building_input.overhang, 0, self.building_input.height),
                    Vec3(ridge_x, 0, ridge_height)
                )
            )
            left_barge_flashings.append(left_front_barge)
            
            # Right slope front
            right_front_barge = ShedBimFlashing(
                material=self.barge_flashing_material,
                color=color,
                position=Line3(
                    Vec3(ridge_x, 0, ridge_height),
                    Vec3(self.building_input.overhang + self.building_input.span, 0, self.building_input.height)
                )
            )
            right_barge_flashings.append(right_front_barge)
            
            # Back gable barges
            # Left slope back
            left_back_barge = ShedBimFlashing(
                material=self.barge_flashing_material,
                color=color,
                position=Line3(
                    Vec3(self.building_input.overhang, self.building_input.length, self.building_input.height),
                    Vec3(ridge_x, self.building_input.length, ridge_height)
                )
            )
            left_barge_flashings.append(left_back_barge)
            
            # Right slope back
            right_back_barge = ShedBimFlashing(
                material=self.barge_flashing_material,
                color=color,
                position=Line3(
                    Vec3(ridge_x, self.building_input.length, ridge_height),
                    Vec3(self.building_input.overhang + self.building_input.span, self.building_input.length, self.building_input.height)
                )
            )
            right_barge_flashings.append(right_back_barge)
            
            # Assign flashings to roofs
            if main.roof_left:
                main.roof_left.barge_flashings = left_barge_flashings
            if main.roof_right:
                main.roof_right.barge_flashings = right_barge_flashings
    
    def find_gutters_and_downpipes_structure(self, main: ShedBimPartMain):
        """Create gutters and downpipes.
        
        C# Ref: Gutter and downpipe logic
        """
        color = self.get_color_material(self.building_input.gutter_color)
        
        # Create gutters
        if self.building_input.roof_type == CarportRoofType.FLAT:
            # Low side gutter
            gutter = ShedBimFlashing(
                material=self.gutters,
                color=color,
                position=Line3(
                    Vec3(self.building_input.overhang, 0, self.building_input.height),
                    Vec3(self.building_input.overhang, self.building_input.length, self.building_input.height)
                )
            )
            
            # Add gutter to low side
            if main.side_left:
                main.side_left.eave_flashings.append(gutter)
        
        # Create downpipes
        downpipe_positions = CarportHelpers.get_downpipe_positions(self.building_input.length)
        downpipes = []
        
        for i, pos_y in enumerate(downpipe_positions):
            downpipe = ShedBimDownpipe(
                material=self.rect_100x75_downpipe if self.building_input.downpipe_size == "large" else self.rect_100x50_downpipe,
                color=self.get_color_material(self.building_input.downpipe_color),
                position=Vec3(self.building_input.overhang, pos_y, self.building_input.height),
                tag=f"DOWNPIPE_{i + 1}"
            )
            downpipes.append(downpipe)
        
        # Add downpipes to low side
        if main.side_left:
            main.side_left.downpipes = downpipes
    
    def find_braces_structure(self, main: ShedBimPartMain):
        """Create bracing.
        
        C# Ref: Bracing logic based on engineering
        """
        if not self.has_eng_data or not self.apex_brace:
            return
        
        # Apex bracing for gable roofs
        if self.building_input.roof_type == CarportRoofType.GABLE:
            # Implementation for apex bracing
            pass
        
        # Knee bracing for larger structures
        # Implementation depends on engineering requirements
    
    def find_punchings_structure(self, main: ShedBimPartMain):
        """Create punching holes for connections.
        
        C# Ref: Punching placement logic
        """
        # Punchings are added to members that need bolt holes
        # This is typically done by modifying existing members
        
        # Example: Add punchings to columns for bracket connections
        for column in main.side_left.columns:
            if not hasattr(column.column, 'punchings'):
                column.column.punchings = []
            
            # Add punching near top for rafter connection
            punching = Punching(
                position=column.column.end_pos.z - 100,  # 100mm from top
                where=PunchingWhere.WEB
            )
            column.column.punchings.append(punching)
    
    def find_fasteners_structure(self, main: ShedBimPartMain):
        """Create fasteners.
        
        C# Ref: Fastener placement logic
        """
        # Fasteners connect components together
        # Implementation would create fastener objects at connection points
        pass
    
    def find_wall_claddings_parent(self, main: ShedBimPartMain):
        """Create wall claddings.
        
        C# Ref: StructureBuilderBase.cs line 881
        Note: Wall claddings are optional for carports (usually open structures).
        """
        # Carports typically don't have wall cladding
        # This would be implemented for enclosed structures
        # Note: has_walls property not defined in BuildingInput - skipping for now
        pass
    
    def _get_overhang_for_roof_type(self) -> float:
        """Get appropriate overhang based on roof type.
        
        C# logic:
        - Flat: Uses specified overhang
        - Gable: No overhang (0)
        - Awning: No overhang (0)
        - AttachedAwning: No overhang (0)
        """
        if self.building_input.roof_type == CarportRoofType.FLAT:
            return self.building_input.overhang
        else:
            return 0
    
    def _get_beam_lengths(self, total_length: float, beam_for_each_bay: bool) -> List[float]:
        """Calculate beam segment lengths based on delivery constraints.
        
        C# Ref: GetBeamLengths method in StructureBuilderBase.cs
        """
        max_delivery_length = 12600  # mm
        
        if total_length <= max_delivery_length:
            return [total_length]
        
        if beam_for_each_bay:
            return [self.bay_size] * self.building_input.bays
        
        # Calculate segments with overlap for splice plates
        num_segments = math.ceil(total_length / max_delivery_length)
        segment_length = total_length / num_segments * 1.2  # 20% overlap
        
        lengths = []
        remaining = total_length
        for i in range(num_segments):
            if i == num_segments - 1:
                lengths.append(remaining)
            else:
                lengths.append(min(segment_length, remaining))
                remaining -= segment_length * 0.8  # Account for overlap
        
        return lengths