"""
Enhanced API Endpoints with Comprehensive Swagger Documentation

This module provides enhanced API endpoints with better Swagger/OpenAPI documentation
for direct IFC and GLB file generation.
"""

from fastapi import APIRouter, HTTPException, File, UploadFile, Query, Path
from fastapi.responses import FileResponse, StreamingResponse
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
import tempfile
import json
import os
import uuid
from datetime import datetime
from pathlib import Path as PathLib
import logging
import io

# Import core modules
import sys
sys.path.insert(0, str(PathLib(__file__).parent.parent.parent))

from src.business.building_input import BuildingInput, CarportRoofType
from src.business.engineering import EngData
from src.ifc_generation.carport_generator import CarportIFCGenerator
from src.glb_generation.carport_glb_generator import CarportGLBGenerator

logger = logging.getLogger(__name__)

# Create enhanced router
enhanced_router = APIRouter()

# Enhanced models with better documentation
class DirectCarportRequest(BaseModel):
    """Direct carport generation request without encryption"""
    
    name: str = Field(
        default="Carport",
        description="Name of the carport structure",
        example="My Carport"
    )
    roof_type: str = Field(
        default="FLAT",
        description="Type of roof: FLAT, GABLE, AWNING, ATTACHED_AWNING",
        example="GABLE"
    )
    span: float = Field(
        ge=3000,
        le=12000,
        description="Width of the carport in millimeters",
        example=6000
    )
    length: float = Field(
        ge=3000,
        le=15000,
        description="Length of the carport in millimeters",
        example=9000
    )
    height: float = Field(
        ge=2000,
        le=4000,
        description="Height of the carport in millimeters",
        example=2700
    )
    bays: int = Field(
        ge=1,
        le=10,
        description="Number of bays (sections)",
        example=3
    )
    pitch: float = Field(
        ge=0,
        le=30,
        description="Roof pitch in degrees (0 for flat roof)",
        example=15
    )
    overhang: float = Field(
        ge=0,
        le=1500,
        description="Roof overhang in millimeters (only for FLAT roof)",
        example=300
    )
    slab: bool = Field(
        default=True,
        description="Include concrete slab foundation",
        example=True
    )
    slab_thickness: float = Field(
        default=100,
        ge=50,
        le=300,
        description="Slab thickness in millimeters",
        example=100
    )

    class Config:
        schema_extra = {
            "example": {
                "name": "Modern Carport",
                "roof_type": "GABLE",
                "span": 6000,
                "length": 9000,
                "height": 2700,
                "bays": 3,
                "pitch": 15,
                "overhang": 0,
                "slab": True,
                "slab_thickness": 100
            }
        }


class EngineeringOverride(BaseModel):
    """Engineering data override for custom materials"""
    
    column_material: Optional[str] = Field(
        default=None,
        description="Column material code (e.g., SHS10010030)",
        example="SHS10010030"
    )
    rafter_material: Optional[str] = Field(
        default=None,
        description="Rafter material code (e.g., C20319)",
        example="C20319"
    )
    purlin_material: Optional[str] = Field(
        default=None,
        description="Purlin material code (e.g., TH075125)",
        example="TH075125"
    )
    purlin_rows: Optional[int] = Field(
        default=None,
        ge=2,
        le=10,
        description="Number of purlin rows",
        example=4
    )
    footing_type: Optional[str] = Field(
        default=None,
        description="Footing type: bored or block",
        example="bored"
    )
    footing_size: Optional[float] = Field(
        default=None,
        ge=200,
        le=1000,
        description="Footing diameter/width in millimeters",
        example=450
    )
    footing_depth: Optional[float] = Field(
        default=None,
        ge=300,
        le=2000,
        description="Footing depth in millimeters",
        example=600
    )


class GenerationResponse(BaseModel):
    """Response for file generation endpoints"""
    
    success: bool = Field(description="Whether generation was successful")
    filename: str = Field(description="Generated filename")
    file_size: int = Field(description="File size in bytes")
    generation_time: float = Field(description="Generation time in seconds")
    download_url: str = Field(description="URL to download the file")
    expires_at: str = Field(description="When the download link expires")


# Temporary file management
TEMP_DIR = PathLib(tempfile.gettempdir()) / "bim-swagger-files"
TEMP_DIR.mkdir(exist_ok=True)

# Store generated files info
generated_files: Dict[str, Dict[str, Any]] = {}


def cleanup_old_files():
    """Clean up files older than 1 hour"""
    cutoff = datetime.now().timestamp() - 3600  # 1 hour
    for file_id, info in list(generated_files.items()):
        if info["created_at"] < cutoff:
            try:
                PathLib(info["path"]).unlink()
                del generated_files[file_id]
            except:
                pass


@enhanced_router.post(
    "/generate/ifc",
    response_model=GenerationResponse,
    summary="Generate IFC file",
    description="Generate an Industry Foundation Classes (IFC) file for a carport structure",
    tags=["Direct Generation"]
)
async def generate_ifc_direct(
    request: DirectCarportRequest,
    engineering: Optional[EngineeringOverride] = None,
    ifc_version: str = Query(
        default="IFC2X3",
        description="IFC version to generate",
        enum=["IFC2X3", "IFC4"]
    )
):
    """
    Generate an IFC file directly from carport parameters.
    
    This endpoint creates a standards-compliant IFC file that can be opened
    in any BIM software supporting IFC format (Revit, ArchiCAD, etc.).
    
    ### Features:
    - BREP-based geometry for universal compatibility
    - Support for all roof types
    - Optional engineering overrides for custom materials
    - IFC2X3 and IFC4 version support
    
    ### Example Usage:
    ```python
    import requests
    
    response = requests.post(
        "http://localhost:8000/api/v2/generate/ifc",
        json={
            "roof_type": "GABLE",
            "span": 6000,
            "length": 9000,
            "height": 2700,
            "bays": 3,
            "pitch": 15
        }
    )
    ```
    """
    cleanup_old_files()
    start_time = datetime.now()
    
    try:
        # Create IFC generator
        generator = CarportIFCGenerator()
        
        # Prepare engineering data if provided
        eng_data = None
        if engineering:
            eng_data = EngData()
            if engineering.column_material:
                eng_data.ENG_COLUMN = engineering.column_material
            if engineering.rafter_material:
                eng_data.ENG_RAFTER = engineering.rafter_material
            if engineering.purlin_material:
                eng_data.ENG_PURLINSIZE = engineering.purlin_material
            if engineering.purlin_rows:
                eng_data.ENG_PURLINROW = engineering.purlin_rows
            if engineering.footing_type:
                eng_data.ENG_FOOTINGTYPE = engineering.footing_type
            if engineering.footing_size:
                eng_data.ENG_FOOTINGSIZE = engineering.footing_size
            if engineering.footing_depth:
                eng_data.ENG_FOOTINGDEPTH = engineering.footing_depth
        
        # Generate IFC file
        file_id = str(uuid.uuid4())
        filename = f"carport_{file_id}.ifc"
        filepath = TEMP_DIR / filename
        
        # Convert roof type string to enum
        roof_type_enum = getattr(CarportRoofType, request.roof_type)
        
        # Generate the carport
        generated_path = generator.generate_carport(
            span=request.span,
            length=request.length,
            height=request.height,
            roof_type=roof_type_enum,
            pitch=request.pitch,
            bays=request.bays,
            overhang=request.overhang,
            slab=request.slab,
            slab_thickness=request.slab_thickness,
            eng_data=eng_data,
            output_path=str(filepath)
        )
        
        # Get file info
        file_size = filepath.stat().st_size
        generation_time = (datetime.now() - start_time).total_seconds()
        
        # Store file info
        generated_files[file_id] = {
            "path": str(filepath),
            "created_at": datetime.now().timestamp(),
            "filename": filename
        }
        
        return GenerationResponse(
            success=True,
            filename=filename,
            file_size=file_size,
            generation_time=generation_time,
            download_url=f"/api/v2/download/{file_id}",
            expires_at=(datetime.now().timestamp() + 3600)
        )
        
    except Exception as e:
        logger.error(f"IFC generation failed: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                "error": "generation_failed",
                "message": f"Failed to generate IFC file: {str(e)}"
            }
        )


@enhanced_router.post(
    "/generate/glb",
    response_model=GenerationResponse,
    summary="Generate GLB file",
    description="Generate a 3D GLB file for web visualization",
    tags=["Direct Generation"]
)
async def generate_glb_direct(
    request: DirectCarportRequest,
    include_materials: bool = Query(
        default=True,
        description="Include material properties and colors"
    ),
    optimize_for_web: bool = Query(
        default=True,
        description="Optimize file size for web delivery"
    )
):
    """
    Generate a GLB (binary GLTF) file for 3D visualization.
    
    This endpoint creates a compact 3D model suitable for:
    - Web viewers (Three.js, Babylon.js)
    - AR/VR applications
    - Mobile applications
    - Quick previews
    
    ### Features:
    - Compact binary format
    - Material properties included
    - Web optimization available
    - Compatible with all modern 3D viewers
    """
    cleanup_old_files()
    start_time = datetime.now()
    
    try:
        # Create building input
        building_input = BuildingInput()
        building_input.name = request.name
        building_input.roof_type = request.roof_type
        building_input.span = request.span
        building_input.length = request.length
        building_input.height = request.height
        building_input.bays = request.bays
        building_input.pitch = request.pitch
        building_input.overhang = request.overhang
        building_input.slab = request.slab
        building_input.slab_thickness = request.slab_thickness
        
        # Generate GLB file
        file_id = str(uuid.uuid4())
        filename = f"carport_{file_id}.glb"
        filepath = TEMP_DIR / filename
        
        # Use new GLB generator
        generator = CarportGLBGenerator()
        
        # Convert roof type string to enum
        roof_type_enum = getattr(CarportRoofType, request.roof_type)
        
        # Generate GLB file
        generated_path = generator.generate_carport_glb(
            span=request.span,
            length=request.length,
            height=request.height,
            roof_type=roof_type_enum,
            pitch=request.pitch,
            bays=request.bays,
            overhang=request.overhang,
            slab=request.slab,
            slab_thickness=request.slab_thickness,
            output_path=str(filepath)
        )
        
        # Get file info
        file_size = filepath.stat().st_size
        generation_time = (datetime.now() - start_time).total_seconds()
        
        # Store file info
        generated_files[file_id] = {
            "path": str(filepath),
            "created_at": datetime.now().timestamp(),
            "filename": filename
        }
        
        return GenerationResponse(
            success=True,
            filename=filename,
            file_size=file_size,
            generation_time=generation_time,
            download_url=f"/api/v2/download/{file_id}",
            expires_at=(datetime.now().timestamp() + 3600)
        )
        
    except Exception as e:
        logger.error(f"GLB generation failed: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                "error": "generation_failed",
                "message": f"Failed to generate GLB file: {str(e)}"
            }
        )


@enhanced_router.post(
    "/generate/batch",
    summary="Batch generate multiple formats",
    description="Generate multiple file formats in one request",
    tags=["Direct Generation"]
)
async def generate_batch(
    request: DirectCarportRequest,
    formats: List[str] = Query(
        default=["ifc", "glb"],
        description="List of formats to generate",
        example=["ifc", "glb", "dxf"]
    )
):
    """
    Generate multiple file formats in a single request.
    
    This endpoint is useful for:
    - Getting both visualization (GLB) and BIM (IFC) files
    - Comparing different formats
    - Bulk processing
    
    Returns URLs for all generated files.
    """
    results = {}
    
    for fmt in formats:
        if fmt.lower() == "ifc":
            result = await generate_ifc_direct(request)
            results["ifc"] = result.dict()
        elif fmt.lower() == "glb":
            result = await generate_glb_direct(request)
            results["glb"] = result.dict()
        else:
            results[fmt] = {"error": f"Format '{fmt}' not supported"}
    
    return {
        "requested_formats": formats,
        "results": results,
        "total_generation_time": sum(
            r.get("generation_time", 0) for r in results.values() 
            if isinstance(r, dict) and "generation_time" in r
        )
    }


@enhanced_router.get(
    "/download/{file_id}",
    summary="Download generated file",
    description="Download a previously generated file",
    tags=["Downloads"]
)
async def download_generated_file(
    file_id: str = Path(description="Unique file identifier")
):
    """Download a generated file using its unique ID."""
    if file_id not in generated_files:
        raise HTTPException(404, detail="File not found or expired")
    
    file_info = generated_files[file_id]
    filepath = PathLib(file_info["path"])
    
    if not filepath.exists():
        del generated_files[file_id]
        raise HTTPException(404, detail="File no longer exists")
    
    return FileResponse(
        path=str(filepath),
        filename=file_info["filename"],
        media_type="application/octet-stream"
    )


@enhanced_router.get(
    "/materials",
    summary="List available materials",
    description="Get list of all available material codes",
    tags=["Reference Data"]
)
async def list_materials():
    """
    Get a comprehensive list of available materials.
    
    Returns material codes organized by type:
    - Columns (SHS profiles)
    - Rafters (C-sections)
    - Purlins (C-sections and TopHat)
    """
    return {
        "columns": {
            "SHS07507525": "75x75x2.5mm Square Hollow Section",
            "SHS10010030": "100x100x3.0mm Square Hollow Section",
            "SHS10010040": "100x100x4.0mm Square Hollow Section",
            "SHS12512540": "125x125x4.0mm Square Hollow Section"
        },
        "rafters": {
            "C15015": "C-section 150x64x1.5mm",
            "C20319": "C-section 203x89x1.9mm",
            "C25024": "C-section 250x90x2.4mm"
        },
        "purlins": {
            "C15015": "C-section 150x64x1.5mm",
            "TH064100": "TopHat 64x100x1.5mm",
            "TH075125": "TopHat 75x125x1.5mm"
        },
        "footings": {
            "bored": ["300", "450", "600", "750"],
            "block": ["400", "600", "800", "1000"]
        }
    }


@enhanced_router.get(
    "/roof-types",
    summary="Get roof type information",
    description="Get detailed information about available roof types",
    tags=["Reference Data"]
)
async def get_roof_types():
    """
    Get detailed information about each roof type.
    
    Includes:
    - Description
    - Supported features
    - Parameter constraints
    """
    return {
        "FLAT": {
            "description": "Flat roof with optional overhang",
            "supports_overhang": True,
            "recommended_pitch": 0,
            "max_pitch": 5,
            "features": ["Simple design", "Cost effective", "Supports overhang"]
        },
        "GABLE": {
            "description": "Peaked roof with two sloping sides",
            "supports_overhang": False,
            "recommended_pitch": 15,
            "min_pitch": 10,
            "max_pitch": 30,
            "features": ["Classic design", "Good water runoff", "More headroom"]
        },
        "AWNING": {
            "description": "Single slope roof",
            "supports_overhang": False,
            "recommended_pitch": 10,
            "min_pitch": 5,
            "max_pitch": 20,
            "features": ["Modern look", "Directional water runoff"]
        },
        "ATTACHED_AWNING": {
            "description": "Single slope attached to building",
            "supports_overhang": False,
            "recommended_pitch": 10,
            "min_pitch": 5,
            "max_pitch": 20,
            "features": ["No right columns", "Building attachment", "Space saving"]
        }
    }


@enhanced_router.get(
    "/examples",
    summary="Get example configurations",
    description="Get example carport configurations",
    tags=["Examples"]
)
async def get_examples():
    """Get example carport configurations for different use cases."""
    return {
        "single_car": {
            "name": "Single Car Carport",
            "roof_type": "FLAT",
            "span": 3000,
            "length": 6000,
            "height": 2400,
            "bays": 2,
            "pitch": 0,
            "overhang": 300
        },
        "double_car": {
            "name": "Double Car Carport",
            "roof_type": "GABLE",
            "span": 6000,
            "length": 6000,
            "height": 2700,
            "bays": 2,
            "pitch": 15,
            "overhang": 0
        },
        "rv_carport": {
            "name": "RV Carport",
            "roof_type": "FLAT",
            "span": 4000,
            "length": 12000,
            "height": 3600,
            "bays": 4,
            "pitch": 2,
            "overhang": 600
        },
        "attached_lean_to": {
            "name": "Attached Lean-To",
            "roof_type": "ATTACHED_AWNING",
            "span": 3000,
            "length": 6000,
            "height": 2400,
            "bays": 2,
            "pitch": 10,
            "overhang": 0
        }
    }


# Export the enhanced router
__all__ = ["enhanced_router"]