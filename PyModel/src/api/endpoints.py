"""
API Endpoints

This module defines all the API endpoints for the BIM Backend system.

C# Reference: BimAPI/Controllers/CarportController.cs, CarportEndpoints.cs
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends
from fastapi.responses import FileResponse
import tempfile
import json
import os
import uuid
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional, Dict, Any
import logging
from pathlib import Path

# Fix imports
import sys
from pathlib import Path as PathLib
sys.path.insert(0, str(PathLib(__file__).parent.parent.parent))

from src.api.models import CarportRequest, CarportResponse, ResponseStatus, ErrorResponse
from src.services.encryption import AESEncryption
from src.services.output_service import OutputService, OutputManager
from src.business.building_input import BuildingInput
from src.business.structure_builder import CarportBuilder
from src.business.engineering import EngData, MockEngineeringService as EngineeringService
from src.output import OutputFormat

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(tags=["Carport"])

# Temporary file storage (in production, use proper storage like S3)
TEMP_FILE_DIR = os.path.join(tempfile.gettempdir(), "bim-api-files")
os.makedirs(TEMP_FILE_DIR, exist_ok=True)

# File cleanup configuration
FILE_RETENTION_HOURS = 24
generated_files: Dict[str, Dict[str, Any]] = {}

# Initialize output service
output_service = OutputService(temp_dir=Path(TEMP_FILE_DIR))
output_manager = OutputManager(output_service)


def get_encryption_key() -> str:
    """Get encryption key from environment or use default for development"""
    key = os.getenv("API_ENCRYPTION_KEY", "development-key-change-in-production")
    if key == "development-key-change-in-production":
        logger.warning("Using default encryption key - change this in production!")
    return key


def cleanup_old_files():
    """Remove temporary files older than retention period"""
    cutoff_time = datetime.utcnow() - timedelta(hours=FILE_RETENTION_HOURS)
    
    for file_id, file_info in list(generated_files.items()):
        if file_info["created_at"] < cutoff_time:
            try:
                os.remove(file_info["path"])
                del generated_files[file_id]
                logger.info(f"Cleaned up old file: {file_id}")
            except Exception as e:
                logger.error(f"Failed to clean up file {file_id}: {e}")


@router.post("/carport/create", response_model=CarportResponse)
async def create_carport(
    request: CarportRequest,
    background_tasks: BackgroundTasks
) -> CarportResponse:
    """
    Create a new carport design.
    
    This endpoint accepts an encrypted BuildingInput payload, validates it,
    optionally performs engineering validation, generates the 3D model,
    and returns a download URL for the generated file.
    
    C# Reference: CarportEndpoints.cs - CreateCarport method
    """
    try:
        # Schedule cleanup of old files
        background_tasks.add_task(cleanup_old_files)
        
        # Initialize encryption service
        aes = AESEncryption(get_encryption_key())
        
        # Decrypt the request
        try:
            decrypted_json = aes.decrypt(request.encrypted_data)
            input_data = json.loads(decrypted_json)
        except Exception as e:
            logger.error(f"Decryption failed: {e}")
            raise HTTPException(
                status_code=400,
                detail=ErrorResponse(
                    error="decryption_error",
                    message="Failed to decrypt request data",
                    details={"reason": str(e)}
                ).model_dump()
            )
        
        # Parse and validate building input
        try:
            building_input = BuildingInput(**input_data)
        except Exception as e:
            logger.error(f"Validation failed: {e}")
            raise HTTPException(
                status_code=400,
                detail=ErrorResponse(
                    error="validation_error",
                    message="Invalid building input parameters",
                    details={"validation_errors": str(e)}
                ).model_dump()
            )
        
        # Perform engineering validation if requested
        eng_data = None
        if building_input.validate_engineering:
            logger.info("Performing engineering validation...")
            eng_service = EngineeringService(
                base_url=os.getenv("ENGINEERING_API_URL", "http://localhost:5000"),
                api_key=os.getenv("ENGINEERING_API_KEY", "")
            )
            
            try:
                async with eng_service:
                    eng_data = await eng_service.validate_design(building_input)
                    if not eng_data:
                        raise ValueError("Engineering validation failed")
            except Exception as e:
                logger.error(f"Engineering validation error: {e}")
                return CarportResponse(
                    success=False,
                    status=ResponseStatus.ERROR,
                    message="Engineering validation failed",
                    error_details={"reason": str(e)}
                )
        
        # Generate the carport model
        logger.info("Generating carport model...")
        try:
            carport = CarportBuilder.create_carport(building_input, eng_data)
        except Exception as e:
            logger.error(f"Model generation failed: {e}")
            raise HTTPException(
                status_code=500,
                detail=ErrorResponse(
                    error="generation_error",
                    message="Failed to generate carport model",
                    details={"reason": str(e)}
                ).model_dump()
            )
        
        # Generate output files based on request
        logger.info("Generating output files...")
        file_id = str(uuid.uuid4())
        base_filename = f"carport_{file_id}"
        
        # Determine output format from request (default to GLTF)
        output_format = input_data.get("output_format", "gltf")
        
        # Generate the requested format
        try:
            result = await output_service.generate_output(
                carport,
                base_filename,
                output_format
            )
            
            if not result.success:
                logger.error(f"Output generation failed: {result.errors}")
                raise HTTPException(
                    status_code=500,
                    detail=ErrorResponse(
                        error="output_error",
                        message="Failed to generate output file",
                        details={"errors": result.errors}
                    ).model_dump()
                )
            
            # Track the generated file
            generated_files[file_id] = {
                "path": str(result.file_path),
                "created_at": datetime.utcnow(),
                "filename": result.file_path.name
            }
            
        except Exception as e:
            logger.error(f"Output generation exception: {e}")
            raise HTTPException(
                status_code=500,
                detail=ErrorResponse(
                    error="output_error",
                    message="Failed to generate output file",
                    details={"reason": str(e)}
                ).model_dump()
            )
        
        # Return success response
        return CarportResponse(
            success=True,
            status=ResponseStatus.SUCCESS,
            message="Carport generated successfully",
            file_url=f"/api/download/{file_id}"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=ErrorResponse(
                error="internal_error",
                message="An unexpected error occurred",
                details={"type": type(e).__name__}
            ).model_dump()
        )


@router.get("/download/{file_id}")
async def download_file(file_id: str):
    """
    Download a generated file.
    
    Files are automatically cleaned up after the retention period.
    
    C# Reference: CarportEndpoints.cs - DownloadFile method
    """
    # Check if file exists
    if file_id not in generated_files:
        raise HTTPException(
            status_code=404,
            detail=ErrorResponse(
                error="not_found",
                message="File not found or has expired"
            ).model_dump()
        )
    
    file_info = generated_files[file_id]
    file_path = file_info["path"]
    
    # Verify file still exists on disk
    if not os.path.exists(file_path):
        # Clean up the reference
        del generated_files[file_id]
        raise HTTPException(
            status_code=404,
            detail=ErrorResponse(
                error="not_found",
                message="File not found or has been deleted"
            ).model_dump()
        )
    
    # Return the file
    return FileResponse(
        path=file_path,
        filename=file_info["filename"],
        media_type="application/octet-stream"
    )


@router.post("/carport/export", tags=["Export"])
async def export_carport(
    request: CarportRequest,
    background_tasks: BackgroundTasks,
    formats: str = "gltf,dxf,ifc"
):
    """
    Export carport in multiple formats.
    
    This endpoint generates the carport and exports it in multiple formats
    as a downloadable ZIP package.
    
    Args:
        request: Encrypted carport request
        formats: Comma-separated list of formats (gltf,dxf,ifc)
    """
    try:
        # Decrypt and validate input (similar to create_carport)
        aes = AESEncryption(get_encryption_key())
        decrypted_json = aes.decrypt(request.encrypted_data)
        input_data = json.loads(decrypted_json)
        building_input = BuildingInput(**input_data)
        
        # Generate carport
        carport = CarportBuilder.create_carport(building_input)
        
        # Parse requested formats
        format_list = [f.strip() for f in formats.split(",")]
        
        # Create export package
        package_name = f"export_{uuid.uuid4()}"
        zip_path = await output_manager.create_download_package(
            carport,
            package_name,
            format_list
        )
        
        # Track the file
        file_id = str(uuid.uuid4())
        generated_files[file_id] = {
            "path": str(zip_path),
            "created_at": datetime.utcnow(),
            "filename": zip_path.name
        }
        
        return {
            "success": True,
            "message": f"Export package created with formats: {', '.join(format_list)}",
            "download_url": f"/api/download/{file_id}",
            "formats": format_list
        }
        
    except Exception as e:
        logger.error(f"Export failed: {e}")
        raise HTTPException(
            status_code=500,
            detail=ErrorResponse(
                error="export_error",
                message="Failed to create export package",
                details={"reason": str(e)}
            ).model_dump()
        )


@router.get("/carport/formats", tags=["Info"])
async def get_available_formats():
    """Get list of available export formats."""
    return {
        "formats": output_service.get_available_formats(),
        "default": "gltf",
        "description": {
            "gltf": "GL Transmission Format - 3D model for web visualization",
            "glb": "Binary GLTF - Compact 3D format",
            "dxf": "Drawing Exchange Format - CAD drawings",
            "ifc": "Industry Foundation Classes - BIM interoperability",
            "ifc4": "IFC version 4 - Latest BIM standard"
        }
    }


@router.get("/carport/sample-request", tags=["Examples"])
async def get_sample_request():
    """
    Get a sample encrypted carport request for testing.
    
    This endpoint is useful for development and testing purposes.
    """
    # Sample building input
    sample_input = {
        "building_type": "CARPORT",
        "name": "Sample Carport",
        "roof_type": "FLAT",
        "validate_engineering": False,
        "bays": 2,
        "span": 6000.0,
        "length": 6000.0,
        "height": 2400.0,
        "wind_speed": 32,
        "pitch": 0.0,
        "slab": True,
        "slab_thickness": 100.0,
        "soil": "SAND",
        "overhang": 600.0
    }
    
    # Encrypt the sample
    aes = AESEncryption(get_encryption_key())
    encrypted_data = aes.encrypt(json.dumps(sample_input))
    
    return {
        "description": "Sample carport creation request",
        "decrypted_payload": sample_input,
        "encrypted_request": {
            "encrypted_data": encrypted_data
        }
    }