"""
AES Encryption Service

This module provides AES encryption/decryption functionality for secure API communication.

C# Reference: BimAPI/Services/Encryption/AESEncryption.cs
"""

from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives import padding
import base64
import hashlib
import os
from typing import Union


class AESEncryption:
    """
    AES encryption service using CBC mode with PKCS7 padding.
    
    This class provides methods to encrypt and decrypt strings using AES-256-CBC.
    It's designed to be compatible with the .NET implementation.
    
    C# Reference: AESEncryption.cs
    """
    
    def __init__(self, key: str):
        """
        Initialize the AES encryption service.
        
        Args:
            key: The encryption key (will be hashed to 256 bits)
        """
        # Hash the key to ensure it's 256 bits (32 bytes)
        self.key = hashlib.sha256(key.encode('utf-8')).digest()
        self.backend = default_backend()
    
    def encrypt(self, plaintext: str) -> str:
        """
        Encrypt a plaintext string using AES-256-CBC.
        
        Args:
            plaintext: The string to encrypt
            
        Returns:
            Base64-encoded encrypted data (IV + ciphertext)
            
        C# Reference: Encrypt method
        """
        # Generate a random 16-byte IV
        iv = os.urandom(16)
        
        # Create cipher
        cipher = Cipher(
            algorithms.AES(self.key),
            modes.CBC(iv),
            backend=self.backend
        )
        encryptor = cipher.encryptor()
        
        # Pad the plaintext using PKCS7
        padder = padding.PKCS7(128).padder()
        padded_data = padder.update(plaintext.encode('utf-8')) + padder.finalize()
        
        # Encrypt the padded data
        ciphertext = encryptor.update(padded_data) + encryptor.finalize()
        
        # Combine IV and ciphertext
        encrypted_data = iv + ciphertext
        
        # Return base64-encoded result
        return base64.b64encode(encrypted_data).decode('utf-8')
    
    def decrypt(self, ciphertext: str) -> str:
        """
        Decrypt a base64-encoded AES-256-CBC encrypted string.
        
        Args:
            ciphertext: Base64-encoded encrypted data (IV + ciphertext)
            
        Returns:
            Decrypted plaintext string
            
        Raises:
            ValueError: If decryption fails or padding is invalid
            
        C# Reference: Decrypt method
        """
        try:
            # Decode from base64
            encrypted_data = base64.b64decode(ciphertext.encode('utf-8'))
            
            # Extract IV (first 16 bytes) and ciphertext
            if len(encrypted_data) < 16:
                raise ValueError("Invalid encrypted data: too short")
            
            iv = encrypted_data[:16]
            actual_ciphertext = encrypted_data[16:]
            
            # Create cipher
            cipher = Cipher(
                algorithms.AES(self.key),
                modes.CBC(iv),
                backend=self.backend
            )
            decryptor = cipher.decryptor()
            
            # Decrypt
            padded_plaintext = decryptor.update(actual_ciphertext) + decryptor.finalize()
            
            # Remove padding
            unpadder = padding.PKCS7(128).unpadder()
            plaintext = unpadder.update(padded_plaintext) + unpadder.finalize()
            
            return plaintext.decode('utf-8')
            
        except Exception as e:
            raise ValueError(f"Decryption failed: {str(e)}")
    
    @staticmethod
    def generate_key() -> str:
        """
        Generate a random encryption key.
        
        Returns:
            A random 32-character key suitable for AES encryption
        """
        return base64.urlsafe_b64encode(os.urandom(24)).decode('utf-8')
    
    def encrypt_json(self, data: dict) -> str:
        """
        Convenience method to encrypt a dictionary as JSON.
        
        Args:
            data: Dictionary to encrypt
            
        Returns:
            Base64-encoded encrypted JSON string
        """
        import json
        json_str = json.dumps(data, separators=(',', ':'))
        return self.encrypt(json_str)
    
    def decrypt_json(self, ciphertext: str) -> dict:
        """
        Convenience method to decrypt and parse JSON data.
        
        Args:
            ciphertext: Base64-encoded encrypted JSON
            
        Returns:
            Decrypted and parsed dictionary
        """
        import json
        plaintext = self.decrypt(ciphertext)
        return json.loads(plaintext)