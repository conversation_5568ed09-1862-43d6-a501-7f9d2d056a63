#!/usr/bin/env python3
"""
IFC BREP Generator with C# alignment.

This module generates IFC files using BREP representation with millimeter units,
ensuring complete alignment with the C# BimCoreLibrary implementation.
"""

import math
import uuid
from datetime import datetime
from pathlib import Path
from typing import List, Optional, Tuple

# Add PyModel to path for imports
import sys
pymodel_path = Path(__file__).parent.parent.parent / "PyModel"
sys.path.insert(0, str(pymodel_path))

from src.business.building_input import BuildingInput, CarportRoofType
from src.business.structure_builder import CarportBuilder, CarportProduct
from src.geometry.primitives import Vec3
from src.bim.components import ShedBimSection, ShedBimColumn, ShedBimFooting
from src.bim.wall_roof import Shed<PERSON>imRoofPurlin
from src.materials.base import FootingMaterialType, FrameMaterialType


class IFCBREPGenerator:
    """IFC generator using BREP representation aligned with C# implementation."""
    
    def __init__(self):
        self.entity_counter = 0
        self.entities = []
        self.context_id = None
        self.owner_history_id = None
        self.global_placement_id = None
        self.subcontext_id = None
        
    def next_id(self):
        """Get next entity ID."""
        self.entity_counter += 1
        return f"#{self.entity_counter}"
    
    def add_entity(self, entity_type: str, *args) -> str:
        """Add an IFC entity."""
        entity_id = self.next_id()
        args_str = ','.join(str(arg) for arg in args)
        self.entities.append(f"{entity_id}= {entity_type}({args_str});")
        return entity_id
    
    def create_point(self, x: float, y: float, z: float) -> str:
        """Create cartesian point in mm (no conversion needed)."""
        return self.add_entity('IFCCARTESIANPOINT', f"({x:.6f},{y:.6f},{z:.6f})")
    
    def create_direction(self, x: float, y: float, z: float) -> str:
        """Create direction vector."""
        return self.add_entity('IFCDIRECTION', f"({x:.6f},{y:.6f},{z:.6f})")
    
    def create_shs_brep(self, width: float, height: float, thickness: float, length: float) -> str:
        """Create BREP for Square Hollow Section at origin."""
        w2, h2 = width/2, height/2
        wi2, hi2 = w2 - thickness, h2 - thickness
        
        # Create all geometry centered at origin, extending along Z axis
        # Outer vertices (start at z=0)
        outer_start = [
            self.create_point(-w2, -h2, 0),
            self.create_point(w2, -h2, 0),
            self.create_point(w2, h2, 0),
            self.create_point(-w2, h2, 0)
        ]
        
        # Inner vertices (start) 
        inner_start = [
            self.create_point(-wi2, -hi2, 0),
            self.create_point(wi2, -hi2, 0),
            self.create_point(wi2, hi2, 0),
            self.create_point(-wi2, hi2, 0)
        ]
        
        # Outer vertices (end at z=length)
        outer_end = [
            self.create_point(-w2, -h2, length),
            self.create_point(w2, -h2, length),
            self.create_point(w2, h2, length),
            self.create_point(-w2, h2, length)
        ]
        
        # Inner vertices (end)
        inner_end = [
            self.create_point(-wi2, -hi2, length),
            self.create_point(wi2, -hi2, length),
            self.create_point(wi2, hi2, length),
            self.create_point(-wi2, hi2, length)
        ]
        
        faces = []
        
        # Start face with hole
        outer_loop = self.add_entity('IFCPOLYLOOP', f"({','.join(outer_start)})")
        outer_bound = self.add_entity('IFCFACEOUTERBOUND', outer_loop, '.T.')
        inner_loop = self.add_entity('IFCPOLYLOOP', f"({','.join(inner_start[::-1])})")
        inner_bound = self.add_entity('IFCFACEBOUND', inner_loop, '.F.')
        faces.append(self.add_entity('IFCFACE', f"({outer_bound},{inner_bound})"))
        
        # End face with hole (reversed)
        outer_loop = self.add_entity('IFCPOLYLOOP', f"({','.join(outer_end[::-1])})")
        outer_bound = self.add_entity('IFCFACEOUTERBOUND', outer_loop, '.T.')
        inner_loop = self.add_entity('IFCPOLYLOOP', f"({','.join(inner_end)})")
        inner_bound = self.add_entity('IFCFACEBOUND', inner_loop, '.F.')
        faces.append(self.add_entity('IFCFACE', f"({outer_bound},{inner_bound})"))
        
        # Outer side faces
        for i in range(4):
            j = (i + 1) % 4
            verts = [outer_start[i], outer_start[j], outer_end[j], outer_end[i]]
            loop = self.add_entity('IFCPOLYLOOP', f"({','.join(verts)})")
            bound = self.add_entity('IFCFACEOUTERBOUND', loop, '.T.')
            faces.append(self.add_entity('IFCFACE', f"({bound})"))
        
        # Inner side faces (reversed winding)
        for i in range(4):
            j = (i + 1) % 4
            verts = [inner_start[j], inner_start[i], inner_end[i], inner_end[j]]
            loop = self.add_entity('IFCPOLYLOOP', f"({','.join(verts)})")
            bound = self.add_entity('IFCFACEOUTERBOUND', loop, '.T.')
            faces.append(self.add_entity('IFCFACE', f"({bound})"))
        
        # Create shell and BREP
        shell = self.add_entity('IFCCLOSEDSHELL', f"({','.join(faces)})")
        return self.add_entity('IFCFACETEDBREP', shell)
    
    def create_c_section_brep(self, width: float, height: float, thickness: float, lip: float, length: float, axis: str = 'Z') -> str:
        """Create C-section BREP at origin aligned with C# approach.
        
        Args:
            axis: 'Z' for column/rafter (default), 'Y' for purlin/eave purlin
        """
        # C-section vertices (looking from end, web facing right)
        # All dimensions in mm
        w = width
        h2 = height / 2
        t = thickness
        
        # Define vertices (12 points for C-section profile)
        start_verts = []
        # Bottom flange
        if axis == 'Y':
            # For purlins extending along Y
            start_verts.append(self.create_point(0, 0, -h2))           # 0: bottom left outer
            start_verts.append(self.create_point(w, 0, -h2))           # 1: bottom right outer
            start_verts.append(self.create_point(w, 0, -h2 + lip))     # 2: bottom right lip end
            start_verts.append(self.create_point(w - t, 0, -h2 + lip)) # 3: bottom right lip inner
            start_verts.append(self.create_point(w - t, 0, -h2 + t))   # 4: bottom flange inner right
            start_verts.append(self.create_point(t, 0, -h2 + t))       # 5: bottom flange inner left
            # Web
            start_verts.append(self.create_point(t, 0, h2 - t))        # 6: top flange inner left
            start_verts.append(self.create_point(w - t, 0, h2 - t))    # 7: top flange inner right
            # Top flange
            start_verts.append(self.create_point(w - t, 0, h2 - lip))  # 8: top right lip inner
            start_verts.append(self.create_point(w, 0, h2 - lip))      # 9: top right lip end
            start_verts.append(self.create_point(w, 0, h2))            # 10: top right outer
            start_verts.append(self.create_point(0, 0, h2))            # 11: top left outer
        else:
            # For columns/rafters extending along Z (original)
            start_verts.append(self.create_point(0, -h2, 0))           # 0: bottom left outer
            start_verts.append(self.create_point(w, -h2, 0))           # 1: bottom right outer
            start_verts.append(self.create_point(w, -h2 + lip, 0))     # 2: bottom right lip end
            start_verts.append(self.create_point(w - t, -h2 + lip, 0)) # 3: bottom right lip inner
            start_verts.append(self.create_point(w - t, -h2 + t, 0))   # 4: bottom flange inner right
            start_verts.append(self.create_point(t, -h2 + t, 0))       # 5: bottom flange inner left
            # Web
            start_verts.append(self.create_point(t, h2 - t, 0))        # 6: top flange inner left
            start_verts.append(self.create_point(w - t, h2 - t, 0))    # 7: top flange inner right
            # Top flange
            start_verts.append(self.create_point(w - t, h2 - lip, 0))  # 8: top right lip inner
            start_verts.append(self.create_point(w, h2 - lip, 0))      # 9: top right lip end
            start_verts.append(self.create_point(w, h2, 0))            # 10: top right outer
            start_verts.append(self.create_point(0, h2, 0))            # 11: top left outer
        
        # End vertices at length
        end_verts = []
        if axis == 'Y':
            # For purlins extending along Y
            end_verts.append(self.create_point(0, length, -h2))             # 0: bottom left outer
            end_verts.append(self.create_point(w, length, -h2))             # 1: bottom right outer
            end_verts.append(self.create_point(w, length, -h2 + lip))       # 2: bottom right lip end
            end_verts.append(self.create_point(w - t, length, -h2 + lip))   # 3: bottom right lip inner
            end_verts.append(self.create_point(w - t, length, -h2 + t))     # 4: bottom flange inner right
            end_verts.append(self.create_point(t, length, -h2 + t))         # 5: bottom flange inner left
            end_verts.append(self.create_point(t, length, h2 - t))          # 6: top flange inner left
            end_verts.append(self.create_point(w - t, length, h2 - t))      # 7: top flange inner right
            end_verts.append(self.create_point(w - t, length, h2 - lip))    # 8: top right lip inner
            end_verts.append(self.create_point(w, length, h2 - lip))        # 9: top right lip end
            end_verts.append(self.create_point(w, length, h2))              # 10: top right outer
            end_verts.append(self.create_point(0, length, h2))              # 11: top left outer
        else:
            # For columns/rafters extending along Z
            end_verts.append(self.create_point(0, -h2, length))
            end_verts.append(self.create_point(w, -h2, length))
            end_verts.append(self.create_point(w, -h2 + lip, length))
            end_verts.append(self.create_point(w - t, -h2 + lip, length))
            end_verts.append(self.create_point(w - t, -h2 + t, length))
            end_verts.append(self.create_point(t, -h2 + t, length))
            end_verts.append(self.create_point(t, h2 - t, length))
            end_verts.append(self.create_point(w - t, h2 - t, length))
            end_verts.append(self.create_point(w - t, h2 - lip, length))
            end_verts.append(self.create_point(w, h2 - lip, length))
            end_verts.append(self.create_point(w, h2, length))
            end_verts.append(self.create_point(0, h2, length))
        
        faces = []
        
        # Start face
        loop = self.add_entity('IFCPOLYLOOP', f"({','.join(start_verts)})")
        bound = self.add_entity('IFCFACEOUTERBOUND', loop, '.T.')
        faces.append(self.add_entity('IFCFACE', f"({bound})"))
        
        # End face (reversed winding)
        loop = self.add_entity('IFCPOLYLOOP', f"({','.join(end_verts[::-1])})")
        bound = self.add_entity('IFCFACEOUTERBOUND', loop, '.T.')
        faces.append(self.add_entity('IFCFACE', f"({bound})"))
        
        # Side faces - create 12 faces connecting the edges
        for i in range(12):
            j = (i + 1) % 12
            verts = [start_verts[i], start_verts[j], end_verts[j], end_verts[i]]
            loop = self.add_entity('IFCPOLYLOOP', f"({','.join(verts)})")
            bound = self.add_entity('IFCFACEOUTERBOUND', loop, '.T.')
            faces.append(self.add_entity('IFCFACE', f"({bound})"))
        
        # Create shell and BREP
        shell = self.add_entity('IFCCLOSEDSHELL', f"({','.join(faces)})")
        return self.add_entity('IFCFACETEDBREP', shell)
    
    def create_tophat_brep(self, width: float, height: float, thickness: float, base_width: float, length: float, axis: str = 'Z') -> str:
        """Create TopHat section BREP at origin.
        
        Args:
            axis: 'Z' for column/rafter (default), 'Y' for purlin
        """
        # TopHat profile (centered at origin)
        w2 = width / 2
        h = height
        t = thickness
        bw2 = base_width / 2
        
        # Define vertices (8 points for TopHat profile)
        start_verts = []
        if axis == 'Y':
            # For purlins extending along Y-axis
            # Base at bottom (negative Z)
            start_verts.append(self.create_point(-bw2, 0, 0))        # 0: base left outer
            start_verts.append(self.create_point(bw2, 0, 0))         # 1: base right outer
            start_verts.append(self.create_point(bw2, 0, t))         # 2: base right inner
            start_verts.append(self.create_point(w2, 0, t))          # 3: web bottom right
            start_verts.append(self.create_point(w2, 0, h - t))      # 4: web top right
            start_verts.append(self.create_point(-w2, 0, h - t))     # 5: web top left
            start_verts.append(self.create_point(-w2, 0, t))         # 6: web bottom left
            start_verts.append(self.create_point(-bw2, 0, t))        # 7: base left inner
        else:
            # For columns/rafters extending along Z-axis
            # Base
            start_verts.append(self.create_point(-bw2, 0, 0))        # 0: base left outer
            start_verts.append(self.create_point(bw2, 0, 0))         # 1: base right outer
            start_verts.append(self.create_point(bw2, t, 0))         # 2: base right inner
            start_verts.append(self.create_point(w2, t, 0))          # 3: web bottom right
            start_verts.append(self.create_point(w2, h - t, 0))      # 4: web top right
            start_verts.append(self.create_point(-w2, h - t, 0))     # 5: web top left
            start_verts.append(self.create_point(-w2, t, 0))         # 6: web bottom left
            start_verts.append(self.create_point(-bw2, t, 0))        # 7: base left inner
        
        # End vertices at length
        end_verts = []
        if axis == 'Y':
            # For purlins extending along Y-axis
            end_verts.append(self.create_point(-bw2, length, 0))        # 0: base left outer
            end_verts.append(self.create_point(bw2, length, 0))         # 1: base right outer
            end_verts.append(self.create_point(bw2, length, t))         # 2: base right inner
            end_verts.append(self.create_point(w2, length, t))          # 3: web bottom right
            end_verts.append(self.create_point(w2, length, h - t))      # 4: web top right
            end_verts.append(self.create_point(-w2, length, h - t))     # 5: web top left
            end_verts.append(self.create_point(-w2, length, t))         # 6: web bottom left
            end_verts.append(self.create_point(-bw2, length, t))        # 7: base left inner
        else:
            # For columns/rafters extending along Z-axis
            end_verts.append(self.create_point(-bw2, 0, length))        # 0: base left outer
            end_verts.append(self.create_point(bw2, 0, length))         # 1: base right outer
            end_verts.append(self.create_point(bw2, t, length))         # 2: base right inner
            end_verts.append(self.create_point(w2, t, length))          # 3: web bottom right
            end_verts.append(self.create_point(w2, h - t, length))      # 4: web top right
            end_verts.append(self.create_point(-w2, h - t, length))     # 5: web top left
            end_verts.append(self.create_point(-w2, t, length))         # 6: web bottom left
            end_verts.append(self.create_point(-bw2, t, length))        # 7: base left inner
        
        faces = []
        
        # Start face
        loop = self.add_entity('IFCPOLYLOOP', f"({','.join(start_verts)})")
        bound = self.add_entity('IFCFACEOUTERBOUND', loop, '.T.')
        faces.append(self.add_entity('IFCFACE', f"({bound})"))
        
        # End face (reversed winding)
        loop = self.add_entity('IFCPOLYLOOP', f"({','.join(end_verts[::-1])})")
        bound = self.add_entity('IFCFACEOUTERBOUND', loop, '.T.')
        faces.append(self.add_entity('IFCFACE', f"({bound})"))
        
        # Side faces
        for i in range(8):
            j = (i + 1) % 8
            verts = [start_verts[i], start_verts[j], end_verts[j], end_verts[i]]
            loop = self.add_entity('IFCPOLYLOOP', f"({','.join(verts)})")
            bound = self.add_entity('IFCFACEOUTERBOUND', loop, '.T.')
            faces.append(self.add_entity('IFCFACE', f"({bound})"))
        
        # Create shell and BREP
        shell = self.add_entity('IFCCLOSEDSHELL', f"({','.join(faces)})")
        return self.add_entity('IFCFACETEDBREP', shell)
    
    def create_footing_brep(self, diameter: float, depth: float, is_bored: bool = True) -> str:
        """Create footing BREP (cylindrical for bored, rectangular for block)."""
        if is_bored:
            # Create cylindrical footing
            segments = 16  # Number of segments for cylinder approximation
            angle_step = 2 * math.pi / segments
            radius = diameter / 2
            
            # Create top circle vertices
            top_verts = []
            for i in range(segments):
                angle = i * angle_step
                x = radius * math.cos(angle)
                y = radius * math.sin(angle)
                top_verts.append(self.create_point(x, y, 0))
            
            # Create bottom circle vertices
            bottom_verts = []
            for i in range(segments):
                angle = i * angle_step
                x = radius * math.cos(angle)
                y = radius * math.sin(angle)
                bottom_verts.append(self.create_point(x, y, -depth))
            
            faces = []
            
            # Top face
            loop = self.add_entity('IFCPOLYLOOP', f"({','.join(top_verts)})")
            bound = self.add_entity('IFCFACEOUTERBOUND', loop, '.T.')
            faces.append(self.add_entity('IFCFACE', f"({bound})"))
            
            # Bottom face (reversed)
            loop = self.add_entity('IFCPOLYLOOP', f"({','.join(bottom_verts[::-1])})")
            bound = self.add_entity('IFCFACEOUTERBOUND', loop, '.T.')
            faces.append(self.add_entity('IFCFACE', f"({bound})"))
            
            # Side faces
            for i in range(segments):
                j = (i + 1) % segments
                verts = [top_verts[i], top_verts[j], bottom_verts[j], bottom_verts[i]]
                loop = self.add_entity('IFCPOLYLOOP', f"({','.join(verts)})")
                bound = self.add_entity('IFCFACEOUTERBOUND', loop, '.T.')
                faces.append(self.add_entity('IFCFACE', f"({bound})"))
            
            shell = self.add_entity('IFCCLOSEDSHELL', f"({','.join(faces)})")
            return self.add_entity('IFCFACETEDBREP', shell)
        else:
            # Create rectangular block footing
            size2 = diameter / 2
            
            # Top vertices
            top_verts = [
                self.create_point(-size2, -size2, 0),
                self.create_point(size2, -size2, 0),
                self.create_point(size2, size2, 0),
                self.create_point(-size2, size2, 0)
            ]
            
            # Bottom vertices
            bottom_verts = [
                self.create_point(-size2, -size2, -depth),
                self.create_point(size2, -size2, -depth),
                self.create_point(size2, size2, -depth),
                self.create_point(-size2, size2, -depth)
            ]
            
            faces = []
            
            # Top face
            loop = self.add_entity('IFCPOLYLOOP', f"({','.join(top_verts)})")
            bound = self.add_entity('IFCFACEOUTERBOUND', loop, '.T.')
            faces.append(self.add_entity('IFCFACE', f"({bound})"))
            
            # Bottom face (reversed)
            loop = self.add_entity('IFCPOLYLOOP', f"({','.join(bottom_verts[::-1])})")
            bound = self.add_entity('IFCFACEOUTERBOUND', loop, '.T.')
            faces.append(self.add_entity('IFCFACE', f"({bound})"))
            
            # Side faces
            for i in range(4):
                j = (i + 1) % 4
                verts = [top_verts[i], top_verts[j], bottom_verts[j], bottom_verts[i]]
                loop = self.add_entity('IFCPOLYLOOP', f"({','.join(verts)})")
                bound = self.add_entity('IFCFACEOUTERBOUND', loop, '.T.')
                faces.append(self.add_entity('IFCFACE', f"({bound})"))
            
            shell = self.add_entity('IFCCLOSEDSHELL', f"({','.join(faces)})")
            return self.add_entity('IFCFACETEDBREP', shell)
    
    def create_slab_brep(self, bottom_left: Vec3, bottom_right: Vec3, top_right: Vec3, top_left: Vec3, thickness: float) -> str:
        """Create slab BREP with proper Z positioning aligned with C#.
        
        Note: The parameter names refer to plan view positions:
        - 'bottom' means front edge (Y=0)
        - 'top' means back edge (Y=length)
        The slab extends from Z=-thickness to Z=0 (top surface at ground level)
        """
        # Create vertices for the slab
        # Bottom surface (Z = -thickness)
        bl_bottom = self.create_point(bottom_left.x, bottom_left.y, -thickness)
        br_bottom = self.create_point(bottom_right.x, bottom_right.y, -thickness)
        tr_bottom = self.create_point(top_right.x, top_right.y, -thickness)
        tl_bottom = self.create_point(top_left.x, top_left.y, -thickness)
        
        # Top surface (Z = 0, ground level)
        bl_top = self.create_point(bottom_left.x, bottom_left.y, 0)
        br_top = self.create_point(bottom_right.x, bottom_right.y, 0)
        tr_top = self.create_point(top_right.x, top_right.y, 0)
        tl_top = self.create_point(top_left.x, top_left.y, 0)
        
        faces = []
        
        # Bottom face (Z = -thickness)
        loop = self.add_entity('IFCPOLYLOOP', f"({bl_bottom},{br_bottom},{tr_bottom},{tl_bottom})")
        bound = self.add_entity('IFCFACEOUTERBOUND', loop, '.T.')
        faces.append(self.add_entity('IFCFACE', f"({bound})"))
        
        # Top face (Z = 0)
        loop = self.add_entity('IFCPOLYLOOP', f"({bl_top},{tl_top},{tr_top},{br_top})")
        bound = self.add_entity('IFCFACEOUTERBOUND', loop, '.T.')
        faces.append(self.add_entity('IFCFACE', f"({bound})"))
        
        # Side faces
        # Front (Y = 0)
        loop = self.add_entity('IFCPOLYLOOP', f"({bl_bottom},{bl_top},{br_top},{br_bottom})")
        bound = self.add_entity('IFCFACEOUTERBOUND', loop, '.T.')
        faces.append(self.add_entity('IFCFACE', f"({bound})"))
        
        # Right (X = span)
        loop = self.add_entity('IFCPOLYLOOP', f"({br_bottom},{br_top},{tr_top},{tr_bottom})")
        bound = self.add_entity('IFCFACEOUTERBOUND', loop, '.T.')
        faces.append(self.add_entity('IFCFACE', f"({bound})"))
        
        # Back (Y = length)
        loop = self.add_entity('IFCPOLYLOOP', f"({tr_bottom},{tr_top},{tl_top},{tl_bottom})")
        bound = self.add_entity('IFCFACEOUTERBOUND', loop, '.T.')
        faces.append(self.add_entity('IFCFACE', f"({bound})"))
        
        # Left (X = 0)
        loop = self.add_entity('IFCPOLYLOOP', f"({tl_bottom},{tl_top},{bl_top},{bl_bottom})")
        bound = self.add_entity('IFCFACEOUTERBOUND', loop, '.T.')
        faces.append(self.add_entity('IFCFACE', f"({bound})"))
        
        shell = self.add_entity('IFCCLOSEDSHELL', f"({','.join(faces)})")
        return self.add_entity('IFCFACETEDBREP', shell)
    
    def generate_ifc_content(self, carport: CarportProduct, output_path: Path) -> str:
        """Generate complete IFC file content from carport structure."""
        # Reset state
        self.entity_counter = 0
        self.entities = []
        
        # IFC Header
        header = f"""ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('ViewDefinition [Ifc4NotAssigned]'),'2;1');
FILE_NAME('{output_path.name}','{datetime.now().isoformat()}',('PyModel'),('GeometryGym'),'PyModel IFC','PyModel','');
FILE_SCHEMA(('IFC4'));
ENDSEC;
DATA;
"""
        
        # Basic setup entities
        origin = self.add_entity('IFCCARTESIANPOINT', '(0.0,0.0,0.0)')
        axis2_3d_global = self.add_entity('IFCAXIS2PLACEMENT3D', origin, '$', '$')
        self.global_placement_id = self.add_entity('IFCLOCALPLACEMENT', '$', axis2_3d_global)
        
        # Organization and history
        person = self.add_entity('IFCPERSON', "'PyModel'", '$', '$', '$', '$', '$', '$', '$')
        org = self.add_entity('IFCORGANIZATION', '$', "'PyModel'", '$', '$', '$')
        person_org = self.add_entity('IFCPERSONANDORGANIZATION', person, org, '$')
        app_org = self.add_entity('IFCORGANIZATION', '$', "'Geometry Gym Pty Ltd'", '$', '$', '$')
        app = self.add_entity('IFCAPPLICATION', app_org, "'v1.0.0.0'", "'PyModel'", "'PyModel'")
        self.owner_history_id = self.add_entity('IFCOWNERHISTORY', person_org, app, '$', '.ADDED.', '1750747168', '$', '$', '1750747168')
        
        # Units - MILLIMETERS as base unit
        dim = self.add_entity('IFCDIMENSIONALEXPONENTS', '0', '0', '0', '0', '0', '0', '0')
        len_unit = self.add_entity('IFCSIUNIT', '*', '.LENGTHUNIT.', '.MILLI.', '.METRE.')
        area_unit = self.add_entity('IFCSIUNIT', '*', '.AREAUNIT.', '$', '.SQUARE_METRE.')
        vol_unit = self.add_entity('IFCSIUNIT', '*', '.VOLUMEUNIT.', '$', '.CUBIC_METRE.')
        angle_unit = self.add_entity('IFCSIUNIT', '*', '.PLANEANGLEUNIT.', '$', '.RADIAN.')
        time_unit = self.add_entity('IFCSIUNIT', '*', '.TIMEUNIT.', '$', '.SECOND.')
        units = self.add_entity('IFCUNITASSIGNMENT', f"({len_unit},{area_unit},{vol_unit},{angle_unit},{time_unit})")
        
        # Geometric context
        dir_2d = self.add_entity('IFCDIRECTION', '(0.0,1.0)')
        self.context_id = self.add_entity('IFCGEOMETRICREPRESENTATIONCONTEXT', "'3D'", "'Model'", '3', '0.0001', axis2_3d_global, origin)
        self.subcontext_id = self.add_entity('IFCGEOMETRICREPRESENTATIONSUBCONTEXT', "'Body'", "'Model'", '*', '*', '*', '*', self.context_id, '$', '.MODEL_VIEW.', '$')
        
        # Project structure
        project = self.add_entity('IFCPROJECT', f"'{str(uuid.uuid4())}'", self.owner_history_id, "'IfcProject'", '$', '$', '$', '$', f"({self.context_id})", units)
        
        address = self.add_entity('IFCPOSTALADDRESS', '$', '$', '$', '$', "('Unknown')", '$', "'Unknown'", '$', "'Unknown'", "'Unknown'")
        building = self.add_entity('IFCBUILDING', f"'{str(uuid.uuid4())}'", self.owner_history_id, "'IfcBuilding'", '$', '$', self.global_placement_id, '$', '$', '$', '$', '$', address)
        
        # Aggregation relationship
        self.add_entity('IFCRELAGGREGATES', f"'{str(uuid.uuid4())}'", self.owner_history_id, '$', '$', project, f"({building})")
        
        # Materials
        steel = self.add_entity('IFCMATERIAL', "'Steel'", '$', '$')
        concrete = self.add_entity('IFCMATERIAL', "'Concrete'", '$', '$')
        
        # Collect all element IDs
        element_ids = []
        
        # Add slab if present
        if carport.main and carport.main.slab and carport.main.slab.middle:
            slab = carport.main.slab.middle
            brep = self.create_slab_brep(
                slab.bottom_left,
                slab.bottom_right,
                slab.top_right,
                slab.top_left,
                slab.thickness
            )
            
            shape_rep = self.add_entity('IFCSHAPEREPRESENTATION', self.subcontext_id, "'Body'", "'Brep'", f"({brep})")
            prod_shape = self.add_entity('IFCPRODUCTDEFINITIONSHAPE', '$', '$', f"({shape_rep})")
            
            slab_elem = self.add_entity('IFCSLAB', f"'{str(uuid.uuid4())}'", self.owner_history_id,
                                       "'SLAB'", "'Concrete Slab'", '$', self.global_placement_id, 
                                       prod_shape, '$', '.FLOOR.')
            element_ids.append(slab_elem)
            
            # Material association
            self.add_entity('IFCRELASSOCIATESMATERIAL', f"'{str(uuid.uuid4())}'", self.owner_history_id,
                           '$', '$', f"({slab_elem})", concrete)
        
        # Process footings and columns
        if carport.main and carport.main.side_left and carport.main.side_left.columns:
            for i, col in enumerate(carport.main.side_left.columns):
                if col.footing and col.footing.footing:
                    # Create footing BREP
                    is_bored = col.footing.footing.footing_type == FootingMaterialType.BORED
                    footing_brep = self.create_footing_brep(
                        col.footing.footing.width,  # For bored, width = diameter
                        col.footing.footing.depth,
                        is_bored
                    )
                    
                    # Create local placement at footing position
                    pt = self.create_point(col.footing.pos.x, col.footing.pos.y, col.footing.pos.z)
                    local_axis = self.add_entity('IFCAXIS2PLACEMENT3D', pt, '$', '$')
                    local_pl = self.add_entity('IFCLOCALPLACEMENT', self.global_placement_id, local_axis)
                    
                    # Create shape representation
                    shape_rep = self.add_entity('IFCSHAPEREPRESENTATION', self.subcontext_id, "'Body'", "'Brep'", f"({footing_brep})")
                    prod_shape = self.add_entity('IFCPRODUCTDEFINITIONSHAPE', '$', '$', f"({shape_rep})")
                    
                    # Create footing element (IFC4: GlobalId, OwnerHistory, Name, Description, ObjectType, ObjectPlacement, Representation, Tag, PredefinedType)
                    footing_elem = self.add_entity('IFCFOOTING', f"'{str(uuid.uuid4())}'", self.owner_history_id,
                                                  f"'{col.footing.tag}'", "'Concrete Footing'", '$', local_pl, prod_shape, '$', '.NOTDEFINED.')
                    element_ids.append(footing_elem)
                    
                    # Material association
                    self.add_entity('IFCRELASSOCIATESMATERIAL', f"'{str(uuid.uuid4())}'", self.owner_history_id,
                                   '$', '$', f"({footing_elem})", concrete)
                
                if col.column:
                    # Get dimensions from material
                    width = col.column.material.width if hasattr(col.column.material, 'width') else 75
                    height = col.column.material.height if hasattr(col.column.material, 'height') else width
                    thickness = col.column.material.thickness if hasattr(col.column.material, 'thickness') else 2.5
                    length = col.column.end_pos.z - col.column.start_pos.z
                    
                    # Create column BREP
                    brep = self.create_shs_brep(width, height, thickness, length)
                    
                    # Create local placement at column position
                    pt = self.create_point(col.column.start_pos.x, col.column.start_pos.y, col.column.start_pos.z)
                    local_axis = self.add_entity('IFCAXIS2PLACEMENT3D', pt, '$', '$')
                    local_pl = self.add_entity('IFCLOCALPLACEMENT', self.global_placement_id, local_axis)
                    
                    # Create shape representation
                    shape_rep = self.add_entity('IFCSHAPEREPRESENTATION', self.subcontext_id, "'Body'", "'Brep'", f"({brep})")
                    prod_shape = self.add_entity('IFCPRODUCTDEFINITIONSHAPE', '$', '$', f"({shape_rep})")
                    
                    # Create member
                    member = self.add_entity('IFCMEMBER', f"'{str(uuid.uuid4())}'", self.owner_history_id,
                                           f"'{col.column.tag}'", "'Column'", '$', local_pl, prod_shape, '$', '.NOTDEFINED.')
                    element_ids.append(member)
                    
                    # Add property set
                    mat_code = self.add_entity('IFCPROPERTYSINGLEVALUE', "'Material Code'", '$', 
                                              f"IFCLABEL('{col.column.material.name}')", '$')
                    prop_set = self.add_entity('IFCPROPERTYSET', f"'{str(uuid.uuid4())}'", 
                                              self.owner_history_id, "'MaterialProfile'", '$', f"({mat_code})")
                    self.add_entity('IFCRELDEFINESBYPROPERTIES', f"'{str(uuid.uuid4())}'", 
                                   self.owner_history_id, '$', '$', f"({member})", prop_set)
        
        # Right side columns and footings (similar logic)
        if carport.main and carport.main.side_right and carport.main.side_right.columns:
            for i, col in enumerate(carport.main.side_right.columns):
                if col.footing and col.footing.footing:
                    # Create footing (same as left side)
                    is_bored = col.footing.footing.footing_type == FootingMaterialType.BORED
                    footing_brep = self.create_footing_brep(
                        col.footing.footing.width,  # For bored, width = diameter
                        col.footing.footing.depth,
                        is_bored
                    )
                    
                    pt = self.create_point(col.footing.pos.x, col.footing.pos.y, col.footing.pos.z)
                    local_axis = self.add_entity('IFCAXIS2PLACEMENT3D', pt, '$', '$')
                    local_pl = self.add_entity('IFCLOCALPLACEMENT', self.global_placement_id, local_axis)
                    
                    shape_rep = self.add_entity('IFCSHAPEREPRESENTATION', self.subcontext_id, "'Body'", "'Brep'", f"({footing_brep})")
                    prod_shape = self.add_entity('IFCPRODUCTDEFINITIONSHAPE', '$', '$', f"({shape_rep})")
                    
                    footing_elem = self.add_entity('IFCFOOTING', f"'{str(uuid.uuid4())}'", self.owner_history_id,
                                                  f"'{col.footing.tag}'", "'Concrete Footing'", '$', local_pl, prod_shape, '$', '.NOTDEFINED.')
                    element_ids.append(footing_elem)
                    
                    self.add_entity('IFCRELASSOCIATESMATERIAL', f"'{str(uuid.uuid4())}'", self.owner_history_id,
                                   '$', '$', f"({footing_elem})", concrete)
                
                if col.column:
                    width = col.column.material.width if hasattr(col.column.material, 'width') else 75
                    height = col.column.material.height if hasattr(col.column.material, 'height') else width
                    thickness = col.column.material.thickness if hasattr(col.column.material, 'thickness') else 2.5
                    length = col.column.end_pos.z - col.column.start_pos.z
                    
                    brep = self.create_shs_brep(width, height, thickness, length)
                    
                    pt = self.create_point(col.column.start_pos.x, col.column.start_pos.y, col.column.start_pos.z)
                    local_axis = self.add_entity('IFCAXIS2PLACEMENT3D', pt, '$', '$')
                    local_pl = self.add_entity('IFCLOCALPLACEMENT', self.global_placement_id, local_axis)
                    
                    shape_rep = self.add_entity('IFCSHAPEREPRESENTATION', self.subcontext_id, "'Body'", "'Brep'", f"({brep})")
                    prod_shape = self.add_entity('IFCPRODUCTDEFINITIONSHAPE', '$', '$', f"({shape_rep})")
                    
                    member = self.add_entity('IFCMEMBER', f"'{str(uuid.uuid4())}'", self.owner_history_id,
                                           f"'{col.column.tag}'", "'Column'", '$', local_pl, prod_shape, '$', '.NOTDEFINED.')
                    element_ids.append(member)
                    
                    mat_code = self.add_entity('IFCPROPERTYSINGLEVALUE', "'Material Code'", '$', 
                                              f"IFCLABEL('{col.column.material.name}')", '$')
                    prop_set = self.add_entity('IFCPROPERTYSET', f"'{str(uuid.uuid4())}'", 
                                              self.owner_history_id, "'MaterialProfile'", '$', f"({mat_code})")
                    self.add_entity('IFCRELDEFINESBYPROPERTIES', f"'{str(uuid.uuid4())}'", 
                                   self.owner_history_id, '$', '$', f"({member})", prop_set)
        
        # Add rafters
        self._add_rafters(carport, element_ids)
        
        # Add purlins
        self._add_purlins(carport, element_ids)
        
        # Add eave purlins
        self._add_eave_purlins(carport, element_ids)
        
        # Add all elements to building
        if element_ids:
            self.add_entity('IFCRELCONTAINEDINSPATIALSTRUCTURE', f"'{str(uuid.uuid4())}'", 
                           self.owner_history_id, "'Building'", "'Building Container for Elements'", 
                           f"({','.join(element_ids)})", building)
        
        # Material associations
        steel_members = [e for e in element_ids if 'MEMBER' in e or 'BEAM' in e]
        if steel_members:
            self.add_entity('IFCRELASSOCIATESMATERIAL', f"'{str(uuid.uuid4())}'", 
                           self.owner_history_id, '$', '$', f"({','.join(steel_members)})", steel)
        
        # Combine all content
        content = header
        for entity in self.entities:
            content += entity + "\n"
        content += "ENDSEC;\nEND-ISO-10303-21;"
        
        return content
    
    def _add_rafters(self, carport: CarportProduct, element_ids: List[str]):
        """Add rafters to the IFC model."""
        # Left side rafters
        if carport.main and carport.main.roof_left and carport.main.roof_left.rafters:
            for i, rafter in enumerate(carport.main.roof_left.rafters):
                # Calculate length
                dx = rafter.end_pos.x - rafter.start_pos.x
                dy = rafter.end_pos.y - rafter.start_pos.y
                dz = rafter.end_pos.z - rafter.start_pos.z
                length = math.sqrt(dx*dx + dy*dy + dz*dz)
                
                # Get material dimensions
                width = rafter.material.width if hasattr(rafter.material, 'width') else 64
                height = rafter.material.height if hasattr(rafter.material, 'height') else 203
                thickness = rafter.material.thickness if hasattr(rafter.material, 'thickness') else 1.9
                lip = rafter.material.lip if hasattr(rafter.material, 'lip') else 15
                
                # Create BREP
                brep = self.create_c_section_brep(width, height, thickness, lip, length)
                
                # Create placement with rotation
                pt = self.create_point(rafter.start_pos.x, rafter.start_pos.y, rafter.start_pos.z)
                
                if length > 0:
                    # Z-axis direction (along rafter)
                    z_dir = self.create_direction(dx/length, dy/length, dz/length)
                    # X-axis direction (perpendicular to rafter, in horizontal plane)
                    x_dir = self.create_direction(dy/length, -dx/length, 0)
                    local_axis = self.add_entity('IFCAXIS2PLACEMENT3D', pt, z_dir, x_dir)
                else:
                    local_axis = self.add_entity('IFCAXIS2PLACEMENT3D', pt, '$', '$')
                
                local_pl = self.add_entity('IFCLOCALPLACEMENT', self.global_placement_id, local_axis)
                
                shape_rep = self.add_entity('IFCSHAPEREPRESENTATION', self.subcontext_id, "'Body'", "'Brep'", f"({brep})")
                prod_shape = self.add_entity('IFCPRODUCTDEFINITIONSHAPE', '$', '$', f"({shape_rep})")
                
                member = self.add_entity('IFCMEMBER', f"'{str(uuid.uuid4())}'", self.owner_history_id,
                                       f"'{rafter.tag}'", "'Rafter'", '$', local_pl, prod_shape, '$', '.NOTDEFINED.')
                element_ids.append(member)
                
                mat_code = self.add_entity('IFCPROPERTYSINGLEVALUE', "'Material Code'", '$', 
                                          f"IFCLABEL('{rafter.material.name}')", '$')
                prop_set = self.add_entity('IFCPROPERTYSET', f"'{str(uuid.uuid4())}'", 
                                          self.owner_history_id, "'MaterialProfile'", '$', f"({mat_code})")
                self.add_entity('IFCRELDEFINESBYPROPERTIES', f"'{str(uuid.uuid4())}'", 
                               self.owner_history_id, '$', '$', f"({member})", prop_set)
        
        # Right side rafters (similar logic)
        if carport.main and carport.main.roof_right and carport.main.roof_right.rafters:
            for i, rafter in enumerate(carport.main.roof_right.rafters):
                dx = rafter.end_pos.x - rafter.start_pos.x
                dy = rafter.end_pos.y - rafter.start_pos.y
                dz = rafter.end_pos.z - rafter.start_pos.z
                length = math.sqrt(dx*dx + dy*dy + dz*dz)
                
                width = rafter.material.width if hasattr(rafter.material, 'width') else 64
                height = rafter.material.height if hasattr(rafter.material, 'height') else 203
                thickness = rafter.material.thickness if hasattr(rafter.material, 'thickness') else 1.9
                lip = rafter.material.lip if hasattr(rafter.material, 'lip') else 15
                
                brep = self.create_c_section_brep(width, height, thickness, lip, length)
                
                pt = self.create_point(rafter.start_pos.x, rafter.start_pos.y, rafter.start_pos.z)
                
                if length > 0:
                    z_dir = self.create_direction(dx/length, dy/length, dz/length)
                    x_dir = self.create_direction(dy/length, -dx/length, 0)
                    local_axis = self.add_entity('IFCAXIS2PLACEMENT3D', pt, z_dir, x_dir)
                else:
                    local_axis = self.add_entity('IFCAXIS2PLACEMENT3D', pt, '$', '$')
                
                local_pl = self.add_entity('IFCLOCALPLACEMENT', self.global_placement_id, local_axis)
                
                shape_rep = self.add_entity('IFCSHAPEREPRESENTATION', self.subcontext_id, "'Body'", "'Brep'", f"({brep})")
                prod_shape = self.add_entity('IFCPRODUCTDEFINITIONSHAPE', '$', '$', f"({shape_rep})")
                
                member = self.add_entity('IFCMEMBER', f"'{str(uuid.uuid4())}'", self.owner_history_id,
                                       f"'{rafter.tag}'", "'Rafter'", '$', local_pl, prod_shape, '$', '.NOTDEFINED.')
                element_ids.append(member)
                
                mat_code = self.add_entity('IFCPROPERTYSINGLEVALUE', "'Material Code'", '$', 
                                          f"IFCLABEL('{rafter.material.name}')", '$')
                prop_set = self.add_entity('IFCPROPERTYSET', f"'{str(uuid.uuid4())}'", 
                                          self.owner_history_id, "'MaterialProfile'", '$', f"({mat_code})")
                self.add_entity('IFCRELDEFINESBYPROPERTIES', f"'{str(uuid.uuid4())}'", 
                               self.owner_history_id, '$', '$', f"({member})", prop_set)
    
    def _add_purlins(self, carport: CarportProduct, element_ids: List[str]):
        """Add purlins to the IFC model."""
        # Process purlin bays for left roof
        if carport.main and carport.main.roof_left and carport.main.roof_left.roof_purlin_bays:
            for bay in carport.main.roof_left.roof_purlin_bays:
                if bay.roof_purlins:
                    for purlin in bay.roof_purlins:
                        # Calculate length
                        dx = purlin.roof_purlin.end_pos.x - purlin.roof_purlin.start_pos.x
                        dy = purlin.roof_purlin.end_pos.y - purlin.roof_purlin.start_pos.y
                        dz = purlin.roof_purlin.end_pos.z - purlin.roof_purlin.start_pos.z
                        length = math.sqrt(dx*dx + dy*dy + dz*dz)
                        
                        # Get material type and create appropriate BREP
                        mat = purlin.roof_purlin.material
                        if mat.material_type == FrameMaterialType.TH:
                            # TopHat profile
                            width = mat.width if hasattr(mat, 'width') else 64
                            height = mat.height if hasattr(mat, 'height') else 100
                            thickness = mat.thickness if hasattr(mat, 'thickness') else 1.5
                            base_width = width * 1.5  # Approximate base width
                            brep = self.create_tophat_brep(width, height, thickness, base_width, length, axis='Y')
                        else:
                            # C-section profile
                            width = mat.width if hasattr(mat, 'width') else 64
                            height = mat.height if hasattr(mat, 'height') else 152
                            thickness = mat.thickness if hasattr(mat, 'thickness') else 1.5
                            lip = mat.lip if hasattr(mat, 'lip') else 15
                            brep = self.create_c_section_brep(width, height, thickness, lip, length, axis='Y')
                        
                        # Create placement with rotation
                        pt = self.create_point(purlin.roof_purlin.start_pos.x, purlin.roof_purlin.start_pos.y, 
                                             purlin.roof_purlin.start_pos.z)
                        
                        # Apply rotation if specified
                        if hasattr(purlin.roof_purlin, 'rotation') and purlin.roof_purlin.rotation != 0:
                            # Purlin runs along Y-axis, rotation is around Y-axis
                            angle = purlin.roof_purlin.rotation
                            # Default: Z-axis points up, X-axis points horizontally
                            # Rotation around Y rotates the X-Z plane
                            x_dir = self.create_direction(math.cos(angle), 0, -math.sin(angle))
                            z_dir = self.create_direction(math.sin(angle), 0, math.cos(angle))
                            local_axis = self.add_entity('IFCAXIS2PLACEMENT3D', pt, z_dir, x_dir)
                        else:
                            local_axis = self.add_entity('IFCAXIS2PLACEMENT3D', pt, '$', '$')
                        
                        local_pl = self.add_entity('IFCLOCALPLACEMENT', self.global_placement_id, local_axis)
                        
                        shape_rep = self.add_entity('IFCSHAPEREPRESENTATION', self.subcontext_id, 
                                                   "'Body'", "'Brep'", f"({brep})")
                        prod_shape = self.add_entity('IFCPRODUCTDEFINITIONSHAPE', '$', '$', f"({shape_rep})")
                        
                        member = self.add_entity('IFCMEMBER', f"'{str(uuid.uuid4())}'", self.owner_history_id,
                                               f"'{purlin.roof_purlin.tag}'", "'Purlin'", '$', local_pl, prod_shape, '$', '.NOTDEFINED.')
                        element_ids.append(member)
                        
                        mat_code = self.add_entity('IFCPROPERTYSINGLEVALUE', "'Material Code'", '$', 
                                                  f"IFCLABEL('{mat.name}')", '$')
                        prop_set = self.add_entity('IFCPROPERTYSET', f"'{str(uuid.uuid4())}'", 
                                                  self.owner_history_id, "'MaterialProfile'", '$', f"({mat_code})")
                        self.add_entity('IFCRELDEFINESBYPROPERTIES', f"'{str(uuid.uuid4())}'", 
                                       self.owner_history_id, '$', '$', f"({member})", prop_set)
        
        # Process purlin bays for right roof (similar logic)
        if carport.main and carport.main.roof_right and carport.main.roof_right.roof_purlin_bays:
            for bay in carport.main.roof_right.roof_purlin_bays:
                if bay.roof_purlins:
                    for purlin in bay.roof_purlins:
                        dx = purlin.roof_purlin.end_pos.x - purlin.roof_purlin.start_pos.x
                        dy = purlin.roof_purlin.end_pos.y - purlin.roof_purlin.start_pos.y
                        dz = purlin.roof_purlin.end_pos.z - purlin.roof_purlin.start_pos.z
                        length = math.sqrt(dx*dx + dy*dy + dz*dz)
                        
                        mat = purlin.roof_purlin.material
                        if mat.material_type == FrameMaterialType.TH:
                            width = mat.width if hasattr(mat, 'width') else 64
                            height = mat.height if hasattr(mat, 'height') else 100
                            thickness = mat.thickness if hasattr(mat, 'thickness') else 1.5
                            base_width = width * 1.5
                            brep = self.create_tophat_brep(width, height, thickness, base_width, length, axis='Y')
                        else:
                            width = mat.width if hasattr(mat, 'width') else 64
                            height = mat.height if hasattr(mat, 'height') else 152
                            thickness = mat.thickness if hasattr(mat, 'thickness') else 1.5
                            lip = mat.lip if hasattr(mat, 'lip') else 15
                            brep = self.create_c_section_brep(width, height, thickness, lip, length)
                        
                        pt = self.create_point(purlin.roof_purlin.start_pos.x, purlin.roof_purlin.start_pos.y, 
                                             purlin.roof_purlin.start_pos.z)
                        
                        if hasattr(purlin.roof_purlin, 'rotation') and purlin.roof_purlin.rotation != 0:
                            angle = purlin.roof_purlin.rotation
                            x_dir = self.create_direction(math.cos(angle), 0, math.sin(angle))
                            z_dir = self.create_direction(-math.sin(angle), 0, math.cos(angle))
                            local_axis = self.add_entity('IFCAXIS2PLACEMENT3D', pt, z_dir, x_dir)
                        else:
                            local_axis = self.add_entity('IFCAXIS2PLACEMENT3D', pt, '$', '$')
                        
                        local_pl = self.add_entity('IFCLOCALPLACEMENT', self.global_placement_id, local_axis)
                        
                        shape_rep = self.add_entity('IFCSHAPEREPRESENTATION', self.subcontext_id, 
                                                   "'Body'", "'Brep'", f"({brep})")
                        prod_shape = self.add_entity('IFCPRODUCTDEFINITIONSHAPE', '$', '$', f"({shape_rep})")
                        
                        member = self.add_entity('IFCMEMBER', f"'{str(uuid.uuid4())}'", self.owner_history_id,
                                               f"'{purlin.roof_purlin.tag}'", "'Purlin'", '$', local_pl, prod_shape, '$', '.NOTDEFINED.')
                        element_ids.append(member)
                        
                        mat_code = self.add_entity('IFCPROPERTYSINGLEVALUE', "'Material Code'", '$', 
                                                  f"IFCLABEL('{mat.name}')", '$')
                        prop_set = self.add_entity('IFCPROPERTYSET', f"'{str(uuid.uuid4())}'", 
                                                  self.owner_history_id, "'MaterialProfile'", '$', f"({mat_code})")
                        self.add_entity('IFCRELDEFINESBYPROPERTIES', f"'{str(uuid.uuid4())}'", 
                                       self.owner_history_id, '$', '$', f"({member})", prop_set)
    
    def _add_eave_purlins(self, carport: CarportProduct, element_ids: List[str]):
        """Add eave purlins to the IFC model."""
        # Left side eave beams
        if carport.main and carport.main.side_left and carport.main.side_left.eave_beams:
            for eave_beam in carport.main.side_left.eave_beams:
                if not eave_beam.beam:
                    continue
                    
                beam = eave_beam.beam  # Get the actual section
                # Calculate length
                dx = beam.end_pos.x - beam.start_pos.x
                dy = beam.end_pos.y - beam.start_pos.y
                dz = beam.end_pos.z - beam.start_pos.z
                length = math.sqrt(dx*dx + dy*dy + dz*dz)
                
                # Get material dimensions
                mat = beam.material
                width = mat.width if hasattr(mat, 'width') else 64
                height = mat.height if hasattr(mat, 'height') else 150
                thickness = mat.thickness if hasattr(mat, 'thickness') else 1.5
                lip = mat.lip if hasattr(mat, 'lip') else 15
                
                # Create BREP
                brep = self.create_c_section_brep(width, height, thickness, lip, length, axis='Y')
                
                # Create placement with rotation
                pt = self.create_point(beam.start_pos.x, beam.start_pos.y, beam.start_pos.z)
                
                # Apply rotation if specified
                if hasattr(beam, 'rotation') and beam.rotation != 0:
                    # Eave purlin runs along Y-axis, rotation is around Y-axis
                    angle = beam.rotation
                    # Rotation around Y rotates the X-Z plane
                    x_dir = self.create_direction(math.cos(angle), 0, -math.sin(angle))
                    z_dir = self.create_direction(math.sin(angle), 0, math.cos(angle))
                    local_axis = self.add_entity('IFCAXIS2PLACEMENT3D', pt, z_dir, x_dir)
                else:
                    local_axis = self.add_entity('IFCAXIS2PLACEMENT3D', pt, '$', '$')
                
                local_pl = self.add_entity('IFCLOCALPLACEMENT', self.global_placement_id, local_axis)
                
                shape_rep = self.add_entity('IFCSHAPEREPRESENTATION', self.subcontext_id, 
                                           "'Body'", "'Brep'", f"({brep})")
                prod_shape = self.add_entity('IFCPRODUCTDEFINITIONSHAPE', '$', '$', f"({shape_rep})")
                
                member = self.add_entity('IFCBEAM', f"'{str(uuid.uuid4())}'", self.owner_history_id,
                                       f"'{beam.tag}'", "'Eave Purlin'", '$', local_pl, prod_shape, '$', '.NOTDEFINED.')
                element_ids.append(member)
                
                mat_code = self.add_entity('IFCPROPERTYSINGLEVALUE', "'Material Code'", '$', 
                                          f"IFCLABEL('{mat.name}')", '$')
                prop_set = self.add_entity('IFCPROPERTYSET', f"'{str(uuid.uuid4())}'", 
                                          self.owner_history_id, "'MaterialProfile'", '$', f"({mat_code})")
                self.add_entity('IFCRELDEFINESBYPROPERTIES', f"'{str(uuid.uuid4())}'", 
                               self.owner_history_id, '$', '$', f"({member})", prop_set)
        
        # Right side eave beams (similar logic)
        if carport.main and carport.main.side_right and carport.main.side_right.eave_beams:
            for eave_beam in carport.main.side_right.eave_beams:
                if not eave_beam.beam:
                    continue
                    
                beam = eave_beam.beam  # Get the actual section
                dx = beam.end_pos.x - beam.start_pos.x
                dy = beam.end_pos.y - beam.start_pos.y
                dz = beam.end_pos.z - beam.start_pos.z
                length = math.sqrt(dx*dx + dy*dy + dz*dz)
                
                mat = beam.material
                width = mat.width if hasattr(mat, 'width') else 64
                height = mat.height if hasattr(mat, 'height') else 150
                thickness = mat.thickness if hasattr(mat, 'thickness') else 1.5
                lip = mat.lip if hasattr(mat, 'lip') else 15
                
                brep = self.create_c_section_brep(width, height, thickness, lip, length, axis='Y')
                
                pt = self.create_point(beam.start_pos.x, beam.start_pos.y, beam.start_pos.z)
                
                if hasattr(beam, 'rotation') and beam.rotation != 0:
                    # Eave purlin runs along Y-axis, rotation is around Y-axis
                    angle = beam.rotation
                    # Rotation around Y rotates the X-Z plane
                    x_dir = self.create_direction(math.cos(angle), 0, -math.sin(angle))
                    z_dir = self.create_direction(math.sin(angle), 0, math.cos(angle))
                    local_axis = self.add_entity('IFCAXIS2PLACEMENT3D', pt, z_dir, x_dir)
                else:
                    local_axis = self.add_entity('IFCAXIS2PLACEMENT3D', pt, '$', '$')
                
                local_pl = self.add_entity('IFCLOCALPLACEMENT', self.global_placement_id, local_axis)
                
                shape_rep = self.add_entity('IFCSHAPEREPRESENTATION', self.subcontext_id, 
                                           "'Body'", "'Brep'", f"({brep})")
                prod_shape = self.add_entity('IFCPRODUCTDEFINITIONSHAPE', '$', '$', f"({shape_rep})")
                
                member = self.add_entity('IFCBEAM', f"'{str(uuid.uuid4())}'", self.owner_history_id,
                                       f"'{beam.tag}'", "'Eave Purlin'", '$', local_pl, prod_shape, '$', '.NOTDEFINED.')
                element_ids.append(member)
                
                mat_code = self.add_entity('IFCPROPERTYSINGLEVALUE', "'Material Code'", '$', 
                                          f"IFCLABEL('{mat.name}')", '$')
                prop_set = self.add_entity('IFCPROPERTYSET', f"'{str(uuid.uuid4())}'", 
                                          self.owner_history_id, "'MaterialProfile'", '$', f"({mat_code})")
                self.add_entity('IFCRELDEFINESBYPROPERTIES', f"'{str(uuid.uuid4())}'", 
                               self.owner_history_id, '$', '$', f"({member})", prop_set)