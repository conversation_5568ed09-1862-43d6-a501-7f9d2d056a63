"""Quaternion class for 3D rotations.

This module provides the Quaternion class for representing rotations in 3D space.
"""

from __future__ import annotations
import math
from dataclasses import dataclass
from .primitives import Vec3


@dataclass
class Quaternion:
    """Represents a quaternion for 3D rotations."""
    
    w: float
    x: float  
    y: float
    z: float
    
    @staticmethod
    def identity() -> Quaternion:
        """Return the identity quaternion (no rotation)."""
        return Quaternion(1.0, 0.0, 0.0, 0.0)
    
    @staticmethod
    def from_axis_angle(axis: Vec3, angle: float) -> Quaternion:
        """Create a quaternion from an axis and angle (in radians)."""
        # Normalize the axis
        axis = axis.normalized()
        
        # Calculate quaternion components
        half_angle = angle / 2.0
        sin_half = math.sin(half_angle)
        cos_half = math.cos(half_angle)
        
        return Quaternion(
            cos_half,
            axis.x * sin_half,
            axis.y * sin_half,
            axis.z * sin_half
        )
    
    def length(self) -> float:
        """Return the length (magnitude) of the quaternion."""
        return math.sqrt(self.w**2 + self.x**2 + self.y**2 + self.z**2)
    
    def normalized(self) -> Quaternion:
        """Return a normalized quaternion."""
        length = self.length()
        if length == 0:
            return Quaternion.identity()
        return Quaternion(
            self.w / length,
            self.x / length,
            self.y / length,
            self.z / length
        )
    
    def conjugate(self) -> Quaternion:
        """Return the conjugate of the quaternion."""
        return Quaternion(self.w, -self.x, -self.y, -self.z)
    
    def __mul__(self, other: Quaternion) -> Quaternion:
        """Multiply two quaternions (compose rotations)."""
        return Quaternion(
            self.w * other.w - self.x * other.x - self.y * other.y - self.z * other.z,
            self.w * other.x + self.x * other.w + self.y * other.z - self.z * other.y,
            self.w * other.y - self.x * other.z + self.y * other.w + self.z * other.x,
            self.w * other.z + self.x * other.y - self.y * other.x + self.z * other.w
        )
    
    def rotate_vector(self, v: Vec3) -> Vec3:
        """Rotate a vector by this quaternion."""
        # Convert vector to quaternion
        v_quat = Quaternion(0, v.x, v.y, v.z)
        
        # Rotate: q * v * q^-1
        result = self * v_quat * self.conjugate()
        
        # Extract vector part
        return Vec3(result.x, result.y, result.z)
    
    def to_matrix(self):
        """Convert quaternion to rotation matrix."""
        # Import Mat4 here to avoid circular import
        from .matrix import Mat4
        
        # Normalize first
        q = self.normalized()
        
        xx = q.x * q.x
        yy = q.y * q.y
        zz = q.z * q.z
        xy = q.x * q.y
        xz = q.x * q.z
        yz = q.y * q.z
        wx = q.w * q.x
        wy = q.w * q.y
        wz = q.w * q.z
        
        return Mat4(
            1 - 2 * (yy + zz),     2 * (xy - wz),     2 * (xz + wy), 0,
                2 * (xy + wz), 1 - 2 * (xx + zz),     2 * (yz - wx), 0,
                2 * (xz - wy),     2 * (yz + wx), 1 - 2 * (xx + yy), 0,
                            0,                 0,                 0, 1
        )
    
    def __str__(self) -> str:
        """String representation."""
        return f"Quaternion(w={self.w:.3f}, x={self.x:.3f}, y={self.y:.3f}, z={self.z:.3f})"
    
    def __repr__(self) -> str:
        """Detailed representation."""
        return self.__str__()