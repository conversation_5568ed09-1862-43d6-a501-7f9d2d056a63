"""Geometry module for BIM Backend Python implementation."""

from .primitives import Vec2, Vec3
from .lines import Line1, Line2, Line3, Ray
from .boxes import Box2, Box3, Rect
from .plane import Plane3
from .basis import Basis3
from .matrix import Mat4
from .triangle import TriIndex
from .angle import Angle
from .quaternion import Quaternion
from .shapes import Triangle, Triangle3D
from .helpers import Geo

__all__ = [
    "Vec2",
    "Vec3",
    "Line1",
    "Line2",
    "Line3",
    "Ray",
    "Box2",
    "Box3",
    "Rect",
    "Plane3",
    "Basis3",
    "Mat4",
    "TriIndex",
    "<PERSON>le",
    "Quaternion",
    "Triangle",
    "Triangle3D",
    "Geo",
]