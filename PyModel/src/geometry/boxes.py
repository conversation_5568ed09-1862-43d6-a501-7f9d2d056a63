"""Box primitives for 2D and 3D bounding operations.

This module corresponds to Box2 and Box3 structures from C# Geo.cs:
- Box2: Lines 1137-1261 in Geo.cs
- Box3: Lines 1263-1357 in Geo.cs

Also includes Rect class for rectangle operations.
"""

from __future__ import annotations
from dataclasses import dataclass
from typing import Iterable, Optional
from .primitives import Vec2, Vec3


@dataclass
class Box2:
    """Represents a 2D axis-aligned bounding box.
    
    C# Reference: Geo.cs lines 1137-1261
    C# Definition: public struct Box2 : IEquatable<Box2>
    """
    
    # C# Ref: Lines 1142-1143 - public Vec2 Min/Max { get; set; }
    min: Vec2
    max: Vec2
    
    def __post_init__(self):
        """Ensure min and max are properly ordered."""
        self.min = Vec2.min(self.min, self.max)
        self.max = Vec2.max(self.min, self.max)
    
    def size(self) -> Vec2:
        """Return the size of the box."""
        return self.max - self.min
    
    def middle(self) -> Vec2:
        """Return the center point of the box."""
        return (self.min + self.max) / 2
    
    def center(self) -> Vec2:
        """Return the center point of the box (alias for middle)."""
        return self.middle()
    
    def area(self) -> float:
        """Calculate the area of the box."""
        size = self.size()
        return size.x * size.y
    
    def bottom_left(self) -> Vec2:
        """Return the bottom-left corner."""
        return self.min
    
    def bottom_right(self) -> Vec2:
        """Return the bottom-right corner."""
        return Vec2(self.max.x, self.min.y)
    
    def top_left(self) -> Vec2:
        """Return the top-left corner."""
        return Vec2(self.min.x, self.max.y)
    
    def top_right(self) -> Vec2:
        """Return the top-right corner."""
        return self.max
    
    def bottom(self) -> float:
        """Return the bottom Y coordinate."""
        return self.min.y
    
    def top(self) -> float:
        """Return the top Y coordinate."""
        return self.max.y
    
    def left(self) -> float:
        """Return the left X coordinate."""
        return self.min.x
    
    def right(self) -> float:
        """Return the right X coordinate."""
        return self.max.x
    
    def contains(self, position: Vec2) -> bool:
        """Check if a point is inside the box."""
        return (self.min.x <= position.x <= self.max.x and 
                self.min.y <= position.y <= self.max.y)
    
    def intersects(self, box: Box2) -> bool:
        """Check if this box intersects with another box."""
        def range_overlap(a1: float, a2: float, b1: float, b2: float) -> bool:
            return ((a1 <= b1 <= a2) or (a1 <= b2 <= a2) or 
                    (b1 <= a1 <= b2) or (b1 <= a2 <= b2))
        
        return (range_overlap(self.min.x, self.max.x, box.min.x, box.max.x) and
                range_overlap(self.min.y, self.max.y, box.min.y, box.max.y))
    
    def union_with(self, box: Box2) -> Box2:
        """Return the union of this box with another box."""
        return Box2(Vec2.min(self.min, box.min), Vec2.max(self.max, box.max))
    
    @staticmethod
    def union(box1: Box2, box2: Box2) -> Box2:
        """Return the union of two boxes."""
        return Box2(Vec2.min(box1.min, box2.min), Vec2.max(box1.max, box2.max))
    
    def expanded(self, amount: float) -> Box2:
        """Return a box expanded by the given amount on all sides."""
        expand_vec = Vec2(amount, amount)
        return Box2(self.min - expand_vec, self.max + expand_vec)
    
    @staticmethod
    def from_position_and_size(position: Vec2, size: Vec2) -> Box2:
        """Create a box from bottom-left position and size."""
        return Box2.from_corners(position, position + size)
    
    @staticmethod
    def from_center(center: Vec2, size: Vec2) -> Box2:
        """Create a box from center point and size."""
        half_size = size / 2
        return Box2.from_corners(center - half_size, center + half_size)
    
    @staticmethod
    def from_bottom_center(bottom_center: Vec2, size: Vec2) -> Box2:
        """Create a box from bottom-center point and size."""
        return Box2.from_corners(
            Vec2(bottom_center.x - size.x / 2, bottom_center.y),
            Vec2(bottom_center.x + size.x / 2, bottom_center.y + size.y)
        )
    
    @staticmethod
    def from_corners(ax: float, ay: float, bx: float, by: float) -> Box2:
        """Create a box from corner coordinates."""
        return Box2(Vec2(ax, ay), Vec2(bx, by))
    
    @staticmethod
    def from_corners_vec(a: Vec2, b: Vec2) -> Box2:
        """Create a box from corner vectors."""
        return Box2(a, b)
    
    @staticmethod
    def from_list(vecs: Iterable[Vec2]) -> Box2:
        """Create a box that contains all given points."""
        min_vec = Vec2.max_value()
        max_vec = Vec2.min_value()
        
        for vec in vecs:
            min_vec = Vec2.min(min_vec, vec)
            max_vec = Vec2.max(max_vec, vec)
        
        return Box2(min_vec, max_vec)
    
    @staticmethod
    def from_points(points: Iterable[Vec2]) -> Box2:
        """Create a box that contains all given points (alias for from_list)."""
        return Box2.from_list(points)
    
    def __str__(self) -> str:
        """String representation."""
        return f"[{self.min}, {self.max}]"
    
    def __repr__(self) -> str:
        """Detailed string representation."""
        return f"Box2(min={self.min}, max={self.max})"


@dataclass
class Box3:
    """Represents a 3D axis-aligned bounding box."""
    
    min: Vec3
    max: Vec3
    
    def __post_init__(self):
        """Ensure min and max are properly ordered."""
        self.min = Vec3.min(self.min, self.max)
        self.max = Vec3.max(self.min, self.max)
    
    def size(self) -> Vec3:
        """Return the size of the box."""
        return self.max - self.min
    
    def middle(self) -> Vec3:
        """Return the center point of the box."""
        return (self.min + self.max) / 2
    
    def center(self) -> Vec3:
        """Return the center point of the box (alias for middle)."""
        return self.middle()
    
    def volume(self) -> float:
        """Calculate the volume of the box."""
        size = self.size()
        return size.x * size.y * size.z
    
    def contains(self, position: Vec3) -> bool:
        """Check if a point is inside the box."""
        return (self.min.x <= position.x <= self.max.x and 
                self.min.y <= position.y <= self.max.y and
                self.min.z <= position.z <= self.max.z)
    
    def intersects(self, box: Box3) -> bool:
        """Check if this box intersects with another box."""
        def range_overlap(a1: float, a2: float, b1: float, b2: float) -> bool:
            return ((a1 <= b1 <= a2) or (a1 <= b2 <= a2) or 
                    (b1 <= a1 <= b2) or (b1 <= a2 <= b2))
        
        return (range_overlap(self.min.x, self.max.x, box.min.x, box.max.x) and
                range_overlap(self.min.y, self.max.y, box.min.y, box.max.y) and
                range_overlap(self.min.z, self.max.z, box.min.z, box.max.z))
    
    def union(self, box: Box3) -> Box3:
        """Return the union of this box with another box."""
        return Box3(Vec3.min(self.min, box.min), Vec3.max(self.max, box.max))
    
    def expanded(self, amount: float) -> Box3:
        """Return a box expanded by the given amount on all sides."""
        expand_vec = Vec3(amount, amount, amount)
        return Box3(self.min - expand_vec, self.max + expand_vec)
    
    @staticmethod
    def from_list(vecs: Iterable[Vec3]) -> Box3:
        """Create a box that contains all given points."""
        min_vec = Vec3.max_value()
        max_vec = Vec3.min_value()
        
        for vec in vecs:
            min_vec = Vec3.min(min_vec, vec)
            max_vec = Vec3.max(max_vec, vec)
        
        return Box3(min_vec, max_vec)
    
    @staticmethod
    def from_points(points: Iterable[Vec3]) -> Box3:
        """Create a box that contains all given points (alias for from_list)."""
        return Box3.from_list(points)
    
    @staticmethod
    def from_center(center: Vec3, size: Vec3) -> Box3:
        """Create a box from center point and size."""
        half_size = size / 2
        return Box3(center - half_size, center + half_size)
    
    def __str__(self) -> str:
        """String representation."""
        return f"[{self.min}, {self.max}]"
    
    def __repr__(self) -> str:
        """Detailed string representation."""
        return f"Box3(min={self.min}, max={self.max})"


@dataclass
class Rect:
    """Represents a 2D rectangle with position and size."""
    
    x: float
    y: float
    width: float
    height: float
    
    @property
    def left(self) -> float:
        """Get the left edge coordinate."""
        return self.x
    
    @property
    def top(self) -> float:
        """Get the top edge coordinate."""
        return self.y
    
    @property
    def right(self) -> float:
        """Get the right edge coordinate."""
        return self.x + self.width
    
    @property
    def bottom(self) -> float:
        """Get the bottom edge coordinate."""
        return self.y + self.height
    
    def center(self) -> Vec2:
        """Get the center point of the rectangle."""
        return Vec2(self.x + self.width / 2, self.y + self.height / 2)
    
    def area(self) -> float:
        """Calculate the area of the rectangle."""
        return self.width * self.height
    
    def contains(self, point: Vec2) -> bool:
        """Check if a point is inside the rectangle."""
        return (self.left <= point.x <= self.right and 
                self.top <= point.y <= self.bottom)
    
    def intersects(self, other: Rect) -> bool:
        """Check if this rectangle intersects with another."""
        return not (self.right < other.left or 
                   other.right < self.left or 
                   self.bottom < other.top or 
                   other.bottom < self.top)
    
    def intersection(self, other: Rect) -> Optional[Rect]:
        """Calculate the intersection of two rectangles."""
        if not self.intersects(other):
            return None
        
        left = max(self.left, other.left)
        top = max(self.top, other.top)
        right = min(self.right, other.right)
        bottom = min(self.bottom, other.bottom)
        
        return Rect(left, top, right - left, bottom - top)
    
    def union(self, other: Rect) -> Rect:
        """Calculate the union of two rectangles."""
        left = min(self.left, other.left)
        top = min(self.top, other.top)
        right = max(self.right, other.right)
        bottom = max(self.bottom, other.bottom)
        
        return Rect(left, top, right - left, bottom - top)
    
    def expanded(self, amount: float) -> Rect:
        """Return a rectangle expanded by the given amount on all sides."""
        return Rect(
            self.x - amount,
            self.y - amount,
            self.width + 2 * amount,
            self.height + 2 * amount
        )
    
    def to_box2(self) -> Box2:
        """Convert to a Box2."""
        return Box2(
            Vec2(self.left, self.top),
            Vec2(self.right, self.bottom)
        )
    
    @staticmethod
    def from_corners(corner1: Vec2, corner2: Vec2) -> Rect:
        """Create a rectangle from two opposite corners."""
        min_x = min(corner1.x, corner2.x)
        min_y = min(corner1.y, corner2.y)
        max_x = max(corner1.x, corner2.x)
        max_y = max(corner1.y, corner2.y)
        
        return Rect(min_x, min_y, max_x - min_x, max_y - min_y)
    
    @staticmethod
    def from_box2(box: Box2) -> Rect:
        """Create a rectangle from a Box2."""
        size = box.size()
        return Rect(box.min.x, box.min.y, size.x, size.y)
    
    def __str__(self) -> str:
        """String representation."""
        return f"Rect({self.x}, {self.y}, {self.width}, {self.height})"
    
    def __repr__(self) -> str:
        """Detailed string representation."""
        return self.__str__()