"""Basis vectors for 3D coordinate systems.

This module corresponds to Basis3 structure from C# Geo.cs.
C# Reference: Geo.cs lines 1489-1558
"""

from __future__ import annotations
from dataclasses import dataclass
from .primitives import Vec3


@dataclass
class Basis3:
    """Represents a set of three basis vectors defining a coordinate system.
    
    C# Reference: Geo.cs lines 1489-1558
    C# Definition: public struct Basis3 : IEquatable<Basis3>
    """
    
    # C# Ref: Lines 1491-1493 - public Vec3 X/Y/Z { get; set; }
    x: Vec3
    y: Vec3
    z: Vec3
    
    @staticmethod
    def unit_xyz() -> Basis3:
        """Return the standard basis (identity)."""
        return Basis3(Vec3.unit_x(), Vec3.unit_y(), Vec3.unit_z())
    
    @staticmethod
    def from_xy(basis_x: Vec3, basis_y: Vec3) -> Basis3:
        """Create a basis from X and Y vectors, computing Z as X × Y."""
        basis_z = Vec3.cross(basis_x, basis_y)
        return Basis3(basis_x, basis_y, basis_z)
    
    @staticmethod
    def from_xz(basis_x: Vec3, basis_z: Vec3) -> Basis3:
        """Create a basis from X and Z vectors, computing Y as Z × X."""
        basis_y = Vec3.cross(basis_z, basis_x)
        return Basis3(basis_x, basis_y, basis_z)
    
    @staticmethod
    def from_yz(basis_y: Vec3, basis_z: Vec3) -> Basis3:
        """Create a basis from Y and Z vectors, computing X as Y × Z."""
        basis_x = Vec3.cross(basis_y, basis_z)
        return Basis3(basis_x, basis_y, basis_z)
    
    def __str__(self) -> str:
        """String representation."""
        return f"[{self.x}, {self.y}, {self.z}]"
    
    def __repr__(self) -> str:
        """Detailed string representation."""
        return f"Basis3(x={self.x}, y={self.y}, z={self.z})"