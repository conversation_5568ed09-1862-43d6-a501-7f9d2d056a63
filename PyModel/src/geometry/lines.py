"""Line primitives for 1D, 2D, and 3D geometry.

This module corresponds to Line1, Line2, and Line3 structures from C# Geo.cs:
- Line1: Lines 918-982 in Geo.cs
- Line2: Lines 984-1070 in Geo.cs
- Line3: Lines 1072-1135 in Geo.cs
"""

from __future__ import annotations
import math
from dataclasses import dataclass
from typing import Optional
from .primitives import Vec2, Vec3


@dataclass
class Line1:
    """Represents a 1D line segment.
    
    C# Reference: Geo.cs lines 918-982
    C# Definition: public struct Line1 : IEquatable<Line1>
    """
    
    # C# Ref: Lines 924-925 - public double Start/End { get; set; }
    start: float
    end: float
    
    def __init__(self, a: float, b: float):
        """Initialize with a and b endpoints."""
        self.start = a
        self.end = b
        self.a = a
        self.b = b
    
    def length(self) -> float:
        """Calculate the length of the line segment."""
        return abs(self.end - self.start)
    
    def center(self) -> float:
        """Get the center point of the line segment."""
        return (self.start + self.end) / 2
    
    def contains(self, value: float) -> bool:
        """Check if a value is within the line segment."""
        return min(self.start, self.end) <= value <= max(self.start, self.end)
    
    @staticmethod
    def from_center(center: float, size: float) -> Line1:
        """Create a line segment from center and size."""
        return Line1(center - size / 2, center + size / 2)
    
    def __str__(self) -> str:
        """String representation."""
        return f"({self.start},{self.end})"
    
    def __repr__(self) -> str:
        """Detailed string representation."""
        return f"Line1(start={self.start}, end={self.end})"


@dataclass
class Line2:
    """Represents a 2D line segment.
    
    C# Reference: Geo.cs lines 984-1070
    C# Definition: public struct Line2 : IEquatable<Line2>
    """
    
    # C# Ref: Lines 990-991 - public Vec2 Start/End { get; set; }
    start: Vec2
    end: Vec2
    
    def __init__(self, a: Vec2, b: Vec2):
        """Initialize with a and b endpoints."""
        self.start = a
        self.end = b
        self.a = a
        self.b = b
    
    def vector(self) -> Vec2:
        """Return the vector from start to end."""
        return self.end - self.start
    
    def direction(self) -> Vec2:
        """Return the normalized direction vector."""
        return Vec2.normal(self.vector())
    
    def magnitude(self) -> float:
        """Return the length (magnitude) of the line segment."""
        return self.vector().length()
    
    def length(self) -> float:
        """Return the scalar length of the line segment."""
        return Vec2.distance(self.start, self.end)
    
    def center(self) -> Vec2:
        """Return the center point of the line segment."""
        return (self.start + self.end) / 2
    
    @staticmethod
    def inters(a: Line2, b: Line2, infinite: bool = False) -> Optional[Vec2]:
        """Calculate intersection point of two line segments."""
        return Vec2.inters(a.start, a.end, b.start, b.end, infinite)
    
    @staticmethod
    def inters_must(a: Line2, b: Line2, infinite: bool = False) -> Vec2:
        """Calculate intersection point of two line segments (must exist)."""
        return Vec2.inters_must(a.start, a.end, b.start, b.end, infinite)
    
    @staticmethod
    def from_start(position: Vec2, direction: Vec2) -> Line2:
        """Create a line from start position and direction vector."""
        return Line2(position, position + direction)
    
    @staticmethod
    def from_center(center: Vec2, length: Vec2) -> Line2:
        """Create a line from center point and length vector."""
        return Line2.from_start(center - length / 2, length)
    
    def __add__(self, vec: Vec2) -> Line2:
        """Translate the line by a vector."""
        return Line2(self.start + vec, self.end + vec)
    
    def __sub__(self, vec: Vec2) -> Line2:
        """Translate the line by a negative vector."""
        return Line2(self.start - vec, self.end - vec)
    
    def __str__(self) -> str:
        """String representation."""
        return f"({self.start},{self.end})"
    
    def __repr__(self) -> str:
        """Detailed string representation."""
        return f"Line2(start={self.start}, end={self.end})"


@dataclass
class Line3:
    """Represents a 3D line segment."""
    
    start: Vec3
    end: Vec3
    
    def __init__(self, a: Vec3, b: Vec3):
        """Initialize with a and b endpoints."""
        self.start = a
        self.end = b
        self.a = a
        self.b = b
    
    def vector(self) -> Vec3:
        """Return the vector from start to end."""
        return self.end - self.start
    
    def direction(self) -> Vec3:
        """Return the normalized direction vector."""
        return Vec3.normal(self.vector())
    
    def magnitude(self) -> float:
        """Return the length (magnitude) of the line segment."""
        return self.vector().length()
    
    def length(self) -> float:
        """Return the scalar length of the line segment."""
        return Vec3.distance(self.start, self.end)
    
    def center(self) -> Vec3:
        """Return the center point of the line segment."""
        return (self.start + self.end) / 2
    
    def __add__(self, vec: Vec3) -> Line3:
        """Translate the line by a vector."""
        return Line3(self.start + vec, self.end + vec)
    
    def __sub__(self, vec: Vec3) -> Line3:
        """Translate the line by a negative vector."""
        return Line3(self.start - vec, self.end - vec)
    
    def __str__(self) -> str:
        """String representation."""
        return f"{self.start} => {self.end}"
    
    def __repr__(self) -> str:
        """Detailed string representation."""
        return f"Line3(start={self.start}, end={self.end})"


@dataclass
class Ray:
    """Represents a ray in 3D space with origin and direction."""
    
    origin: Vec3
    direction: Vec3
    
    def __init__(self, origin: Vec3, direction: Vec3):
        """Initialize ray with origin and direction."""
        self.origin = origin
        self.direction = direction.normalized()  # Ensure direction is normalized
    
    def get_point(self, t: float) -> Vec3:
        """Get a point along the ray at parameter t."""
        return self.origin + self.direction * t
    
    def __str__(self) -> str:
        """String representation."""
        return f"Ray(origin={self.origin}, direction={self.direction})"
    
    def __repr__(self) -> str:
        """Detailed string representation."""
        return f"Ray(origin={self.origin}, direction={self.direction})"