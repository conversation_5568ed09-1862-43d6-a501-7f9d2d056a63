#!/usr/bin/env python3
"""
Run the BIM Backend API server.
"""

import os
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Import and run the API
if __name__ == "__main__":
    import uvicorn
    
    # Get port from environment or use default
    port = int(os.environ.get("PORT", 8000))
    
    # Run the server
    print("\n" + "="*60)
    print(f"Starting BIM Backend API v2.0 on port {port}...")
    print("="*60)
    print(f"\n📚 Swagger UI: http://localhost:{port}/api/docs")
    print(f"📝 ReDoc: http://localhost:{port}/api/redoc")
    print(f"\n🆕 Enhanced API v2 Endpoints:")
    print(f"  - Generate IFC: POST /api/v2/generate/ifc")
    print(f"  - Generate GLB: POST /api/v2/generate/glb")
    print(f"  - Batch Generate: POST /api/v2/generate/batch")
    print(f"  - Examples: GET /api/v2/examples")
    print(f"  - Materials: GET /api/v2/materials")
    print(f"  - Roof Types: GET /api/v2/roof-types")
    print(f"\n🔐 Original API v1 (Encrypted):")
    print(f"  - Create Carport: POST /api/carport/create")
    print(f"  - Export Multiple: POST /api/carport/export")
    print("\n" + "="*60 + "\n")
    
    uvicorn.run(
        "src.api.main:app",  # Use import string for reload to work
        host="0.0.0.0",
        port=port,
        reload=True,  # Enable auto-reload during development
        log_level="info"
    )