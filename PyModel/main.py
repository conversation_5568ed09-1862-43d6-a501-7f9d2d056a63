#!/usr/bin/env python3
"""
PyModel - Main entry point for BIM generation.

This module provides a command-line interface for generating
IFC and GLB files for carport structures.
"""

import argparse
import sys
from pathlib import Path
from datetime import datetime

# Add src to path
sys.path.insert(0, str(Path(__file__).parent))

from src.business.carport_builder import CarportBuilder
from src.business.building_input import BuildingInput, BuildingType, RoofType
from src.output.ifc.ifc_generator_brep import IFCBREPGenerator
from src.output.gltf.gltf_generator import GLTFGenerator
import json


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description='PyModel - Generate BIM files for carport structures',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Generate a gable carport with default dimensions
  python main.py --roof-type gable
  
  # Generate a flat carport with custom dimensions
  python main.py --roof-type flat --span 6000 --length 9000 --height 2400
  
  # Generate both IFC and GLB files
  python main.py --roof-type gable --format all --output my_carport
  
  # Load configuration from JSON file
  python main.py --config carport_config.json
        """
    )
    
    # Input options
    input_group = parser.add_argument_group('Input Options')
    input_group.add_argument('--config', type=str, help='Load configuration from JSON file')
    input_group.add_argument('--roof-type', type=str, choices=['flat', 'gable', 'monoslope', 'awning'],
                            default='gable', help='Roof type (default: gable)')
    input_group.add_argument('--span', type=float, help='Building span in mm (default: 8400)')
    input_group.add_argument('--length', type=float, help='Building length in mm (default: 8400)')
    input_group.add_argument('--height', type=float, help='Building height in mm (default: 2713)')
    input_group.add_argument('--pitch', type=float, help='Roof pitch in degrees (default: 10 for gable)')
    input_group.add_argument('--bays', type=int, help='Number of bays (default: 2)')
    
    # Output options
    output_group = parser.add_argument_group('Output Options')
    output_group.add_argument('--format', type=str, choices=['ifc', 'glb', 'all'],
                             default='ifc', help='Output format (default: ifc)')
    output_group.add_argument('--output', type=str, help='Output filename (without extension)')
    output_group.add_argument('--output-dir', type=str, default='output',
                             help='Output directory (default: output)')
    output_group.add_argument('--use-brep', action='store_true', default=True,
                             help='Use BREP representation for IFC (default: True)')
    
    # Display options
    display_group = parser.add_argument_group('Display Options')
    display_group.add_argument('--verbose', '-v', action='store_true',
                              help='Enable verbose output')
    display_group.add_argument('--quiet', '-q', action='store_true',
                              help='Suppress all output except errors')
    
    return parser.parse_args()


def load_config(config_file):
    """Load configuration from JSON file."""
    try:
        with open(config_file, 'r') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading config file: {e}")
        sys.exit(1)


def create_building_input(args, config=None):
    """Create BuildingInput from arguments or config."""
    if config:
        # Load from config
        roof_type_map = {
            'flat': RoofType.FLAT,
            'gable': RoofType.GABLE,
            'monoslope': RoofType.MONOSLOPE,
            'awning': RoofType.AWNING
        }
        
        return BuildingInput(
            building_type=BuildingType.CARPORT,
            span=config.get('span', 8400),
            length=config.get('length', 8400),
            height=config.get('height', 2713),
            roof_type=roof_type_map.get(config.get('roof_type', 'gable'), RoofType.GABLE),
            pitch=config.get('pitch', 10),
            bays=config.get('bays', 2)
        )
    else:
        # Load from command line args
        roof_type_map = {
            'flat': RoofType.FLAT,
            'gable': RoofType.GABLE,
            'monoslope': RoofType.MONOSLOPE,
            'awning': RoofType.AWNING
        }
        
        # Default values based on roof type
        defaults = {
            'flat': {'span': 6000, 'length': 9000, 'height': 2400, 'pitch': 1},
            'gable': {'span': 8400, 'length': 8400, 'height': 2713, 'pitch': 10},
            'monoslope': {'span': 7000, 'length': 7000, 'height': 2400, 'pitch': 5},
            'awning': {'span': 5000, 'length': 6000, 'height': 2400, 'pitch': 3}
        }
        
        roof_type = args.roof_type
        default = defaults[roof_type]
        
        return BuildingInput(
            building_type=BuildingType.CARPORT,
            span=args.span or default['span'],
            length=args.length or default['length'],
            height=args.height or default['height'],
            roof_type=roof_type_map[roof_type],
            pitch=args.pitch or default['pitch'],
            bays=args.bays or 2
        )


def generate_files(carport, args, building_input):
    """Generate output files based on format selection."""
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Base filename
    if args.output:
        base_name = args.output
    else:
        base_name = f"carport_{building_input.roof_type.value.lower()}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    results = []
    
    # Generate IFC
    if args.format in ['ifc', 'all']:
        if not args.quiet:
            print("Generating IFC file...")
        
        if args.use_brep:
            generator = IFCBREPGenerator(output_dir)
        else:
            # Use standard generator if available
            try:
                from src.output.ifc.ifc_generator import IFCGenerator
                generator = IFCGenerator(output_dir)
            except:
                generator = IFCBREPGenerator(output_dir)
        
        result = generator.generate(carport, f"{base_name}.ifc")
        results.append(('IFC', result))
        
        if result.success and not args.quiet:
            print(f"✓ IFC file generated: {result.file_path}")
        elif not result.success:
            print(f"✗ IFC generation failed: {', '.join(result.errors)}")
    
    # Generate GLB
    if args.format in ['glb', 'all']:
        if not args.quiet:
            print("Generating GLB file...")
        
        try:
            generator = GLTFGenerator(output_dir)
            result = generator.generate(carport, f"{base_name}.glb")
            results.append(('GLB', result))
            
            if result.success and not args.quiet:
                print(f"✓ GLB file generated: {result.file_path}")
            elif not result.success:
                print(f"✗ GLB generation failed: {', '.join(result.errors)}")
        except Exception as e:
            print(f"✗ GLB generation error: {str(e)}")
    
    return results


def display_summary(building_input, results, verbose=False):
    """Display generation summary."""
    print("\n" + "=" * 60)
    print("GENERATION SUMMARY")
    print("=" * 60)
    
    # Building parameters
    print(f"\nBuilding Type: Carport")
    print(f"Roof Type: {building_input.roof_type.value}")
    print(f"Dimensions: {building_input.span/1000:.1f}m x {building_input.length/1000:.1f}m x {building_input.height/1000:.1f}m")
    print(f"Pitch: {building_input.pitch}°")
    print(f"Bays: {building_input.bays}")
    
    # Results
    print(f"\nGenerated Files:")
    for format_type, result in results:
        if result.success:
            print(f"  ✓ {format_type}: {result.file_path}")
        else:
            print(f"  ✗ {format_type}: Failed")
            if verbose and result.errors:
                for error in result.errors:
                    print(f"     - {error}")
    
    print("=" * 60)


def main():
    """Main entry point."""
    args = parse_arguments()
    
    # Header
    if not args.quiet:
        print("PyModel BIM Generator v1.0")
        print("=" * 60)
    
    try:
        # Load configuration
        if args.config:
            if not args.quiet:
                print(f"Loading configuration from: {args.config}")
            config = load_config(args.config)
            building_input = create_building_input(args, config)
        else:
            building_input = create_building_input(args)
        
        # Display input parameters
        if args.verbose and not args.quiet:
            print(f"\nBuilding Parameters:")
            print(f"  Roof Type: {building_input.roof_type.value}")
            print(f"  Span: {building_input.span}mm")
            print(f"  Length: {building_input.length}mm")
            print(f"  Height: {building_input.height}mm")
            print(f"  Pitch: {building_input.pitch}°")
            print(f"  Bays: {building_input.bays}")
        
        # Create carport
        if not args.quiet:
            print("\nCreating carport structure...")
        
        carport = CarportBuilder.create_carport(building_input)
        
        if args.verbose and not args.quiet:
            # Display component counts
            column_count = len(carport.calculate_columns()) * 2  # left and right
            rafter_count = len(carport.calculate_gable_rafters() if building_input.roof_type == RoofType.GABLE 
                             else carport.calculate_flat_rafters())
            print(f"\nComponents created:")
            print(f"  Columns: {column_count}")
            print(f"  Rafters: {rafter_count}")
        
        # Generate files
        results = generate_files(carport, args, building_input)
        
        # Display summary
        if not args.quiet:
            display_summary(building_input, results, args.verbose)
        
        # Exit code based on success
        all_success = all(result.success for _, result in results)
        sys.exit(0 if all_success else 1)
        
    except KeyboardInterrupt:
        print("\nOperation cancelled by user")
        sys.exit(130)
    except Exception as e:
        print(f"\nError: {str(e)}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()