# BIM Backend Python/C# Alignment Test Plan

## Overview
This comprehensive test plan ensures 100% alignment between the Python implementation and the original C# BIM Backend for Tasks 1 & 2.

## Test Structure

```
alignment_tests/
├── TEST_PLAN.md (this file)
├── geometry/
│   ├── session1/
│   │   ├── test_core_types.py      # Vec2, Vec3, Mat4
│   │   └── results/
│   │       └── core_types_report.md
│   ├── session2/
│   │   ├── test_shapes.py          # BoundingBox, Rect, Triangle
│   │   └── results/
│   │       └── shapes_report.md
│   └── session3/
│       ├── test_algorithms.py      # Intersections, offsets, convex hull
│       └── results/
│           └── algorithms_report.md
└── materials/
    ├── session1/
    │   ├── test_base_materials.py  # All material types
    │   └── results/
    │       └── base_materials_report.md
    ├── session2/
    │   ├── test_helpers_catalog.py # Material catalogs & helpers
    │   └── results/
    │       └── helpers_catalog_report.md
    └── session3/
        ├── test_profiles_segments.py # Profiles & segmentation
        └── results/
            └── profiles_segments_report.md
```

## Test Sessions

### Geometry Testing (Task 1)

#### Session 1: Core Types
- **Vec2**: All operations, properties, and methods
- **Vec3**: All operations, properties, and methods  
- **Mat4**: Matrix operations, transformations
- **Quaternion**: Rotations and conversions
- **Angle**: Degree/radian conversions and operations

#### Session 2: Shapes
- **BoundingBox**: Creation, operations, intersections
- **Rect**: Creation, operations, contains/intersects
- **Triangle**: Area, contains, circumcircle
- **PlaneIntersection**: Plane-line intersections
- **MeshHelpers**: Mesh generation utilities

#### Session 3: Algorithms
- **IntersectionHelper**: All intersection methods
- **OffsetHelper**: Outline offsetting algorithms
- **ConvexHull**: 2D convex hull generation
- **PolygonHelper**: Area, winding, triangulation
- **Composite shapes**: Combined geometry operations

### Materials Testing (Task 2)

#### Session 1: Base Materials
- **BracketMaterial**: Properties and mesh loading
- **CladdingMaterial**: Validation and properties
- **ColorMaterial**: RGB/CMYK conversions
- **FrameMaterial**: All frame types (C, Z, TH, SHS, etc.)
- **FootingMaterial**: Block and bored types
- **FastenerMaterial**: Bolt specifications
- **All other material types**: Complete property testing

#### Session 2: Helpers & Catalog
- **FrameMaterialHelper**: All 100+ predefined sections
- **CladdingProfileHelper**: All 8 profile generators
- **MaterialSegmentHelper**: Segmentation algorithms
- **Factory methods**: All creation methods
- **Material lookups**: Catalog access methods

#### Session 3: Profiles & Segmentation
- **MeshProfileHelper**: Profile generation for all frame types
- **Profile accuracy**: Verify generated profiles match C#
- **Segmentation logic**: Roof and wall segmentation
- **Visual properties**: Appearance and textures
- **Integration points**: Cross-module functionality

## Validation Criteria

### 1. Numerical Accuracy
- Floating point values must match within 1e-6 tolerance
- Integer values must match exactly
- Angles must match within 0.001 degrees

### 2. Structural Alignment
- All properties must exist and have same types
- All methods must exist with same signatures
- All enums must have same values
- Factory methods must produce identical results

### 3. Behavioral Alignment
- Same validation rules and error messages
- Same computational results
- Same edge case handling
- Same default values

### 4. Catalog Alignment
- All predefined materials must exist
- Material properties must match exactly
- Helper methods must return same materials
- Profile generation must produce same shapes

## Test Implementation

Each test session will:
1. Load corresponding C# test data/expected values
2. Execute Python implementation
3. Compare results with strict validation
4. Generate detailed report with:
   - Pass/fail status
   - Actual vs expected values
   - Any discrepancies found
   - Performance metrics

## Success Criteria

- 100% of tests must pass
- No discrepancies in material properties
- No discrepancies in geometric calculations
- All predefined materials present
- All helper methods functional

## Execution Order

1. Geometry Session 1 → Session 2 → Session 3
2. Materials Session 1 → Session 2 → Session 3
3. Final alignment report generation

This ensures dependencies are tested in proper order and provides clear progress tracking.