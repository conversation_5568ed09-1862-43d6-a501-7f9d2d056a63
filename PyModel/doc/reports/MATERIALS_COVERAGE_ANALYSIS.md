# Materials.cs Complete Structure Analysis

## C# Materials.cs (Lines 1-588)

### 1. Main Material Classes

#### BracketMaterial (Lines 10-43)
- **Properties:**
  - `Name` (string)
  - `MeshName` (string)
  - `_mesh` (private BracketMesh)
- **Methods:**
  - `InternalSetMesh(BracketMesh mesh)`
  - `EnsureMeshCreated()` (private)
  - `GetBracketMesh()`
  - `GetMesh()`
  - `GetBounds()`
  - `GetAttachments()`
- **Python Coverage:** ✅ Complete

#### BracketMesh (Lines 45-50)
- **Properties:**
  - `Id` (string)
  - `Mesh` (Mesh3d)
  - `Attachments` (Dictionary<string, BracketAttachment>)
- **Python Coverage:** ✅ Complete

#### BracketAttachment (Lines 52-66)
- **Struct with:**
  - `Position` (Vec3)
  - `Basis` (Basis3)
- **Constructors:**
  - Default with position only
  - With position and basis
- **Python Coverage:** ✅ Complete

#### CladdingMaterial (Lines 68-95)
- **Properties:**
  - `Name` (string)
  - `Design` (string)
  - `CoverWidth` (double)
  - `RibHeight` (double)
  - `Overlap` (double)
  - `Bmt` (double) - Base metal thickness
  - `Tct` (double) - Total coated thickness
  - `Profile` (Vec2[])
  - `IsProfileRotated` (bool)
- **Python Coverage:** ✅ Complete

#### ColorMaterial (Lines 97-168)
- **Properties:**
  - `Name` (string)
  - `Finish` (string) - e.g., "CB", "ZA"
  - `R`, `G`, `B`, `A` (byte)
- **Static Methods:**
  - `FromHex(name, finish, hex)`
  - `FromRgb(name, finish, r, g, b, a)`
  - `FromCMYK(name, finish, cyan, magenta, yellow, black, alpha)`
- **Python Coverage:** ✅ Complete

#### FlashingMaterial (Lines 170-183)
- **Properties:**
  - `Name` (string)
  - `Description` (string)
  - `Thickness` (double)
  - `Profile` (List<Vec2>)
  - `FrontFaces` (List<int>)
  - `CapOutline` (List<Vec2>)
- **Python Coverage:** ✅ Complete

#### DownpipeMaterial (Lines 186-199)
- **Properties:**
  - `Name` (string)
  - `Description` (string)
  - `Thickness` (double)
  - `Profile` (List<Vec2>)
  - `FrontFaces` (List<int>)
  - `CapOutline` (List<Vec2>)
- **Python Coverage:** ✅ Complete

#### FastenerMaterial (Lines 201-224)
- **Properties:**
  - `Name` (string)
  - `Description` (string)
  - `Length` (double)
  - `HeadDiameter` (double)
  - `ClearanceHoleDiameter` (double)
  - `MaterialType` (FastenerMaterialType)
- **Static Methods:**
  - `CreateBolt(name, length, headDiameter, clearanceHoleDiameter)`
- **Python Coverage:** ✅ Complete

#### FootingMaterial (Lines 238-267)
- **Implements:** IEquatable<FootingMaterial>
- **Properties:**
  - `FootingType` (FootingMaterialType)
  - `Width` (double)
  - `Length` (double)
  - `Depth` (double)
- **Methods:**
  - `Equals(object obj)`
  - `GetHashCode()`
  - `Equals(FootingMaterial other)`
- **Python Coverage:** ✅ Complete

#### FrameMaterial (Lines 275-463)
- **Properties:**
  - `Id` (computed property)
  - `Name` (string)
  - `MaterialType` (FrameMaterialType)
  - `Flipped` (bool)
  - `Section` (int)
  - `Width`, `Height`, `Thickness` (double)
  - `IsB2B` (bool) - Back-to-back
  - `Web`, `Flange`, `Lip` (computed/stored)
  - `FlangeSingle` (computed)
  - `Z_FlangeF`, `Z_FlangeE` (double) - Z-section specific
  - `WebHoleCenters` (double)
  - `Depth` (computed)
  - `PAD_RebateWidth`, `PAD_RebateHeight`, `PAD_RebateTail` (double)
  - `SRDJ_Tail` (double)
- **Methods:**
  - `ToString()`
  - `GetHashCode()`
  - `ToOrientationInternal(bool flipped)` (private)
  - `ToOrientationNormal()`
  - `ToOrientationFlipped()`
  - `ToOrientationToggle()`
- **Static Factory Methods:**
  - `CreateC(...)` - C-section
  - `CreateZ(...)` - Z-section
  - `CreateTH(...)` - TopHat
  - `CreateSHS(...)` - Square Hollow Section
  - `CreatePadStile(...)` - Personal Access Door
  - `CreateSideRollerDoorJamb(...)`
- **Python Coverage:** ✅ Complete

#### Punching (Lines 505-560)
- **Struct implementing:** IEquatable<Punching>
- **Properties:**
  - `Position` (double)
  - `Where` (PunchingWhere)
- **Methods:**
  - `Negate()`
  - `Abs(sectionLength)`
  - `Add(position)`
  - `ToString()`
  - `Equals(Punching other)`
- **Static Factory Methods:**
  - `CreateWeb(position)`
  - `CreateFlange(position)`
  - `CreateCenter(position)`
- **Python Coverage:** ✅ Complete

#### StrapMaterial (Lines 571-576)
- **Properties:**
  - `Name` (string)
  - `Width` (double)
  - `Thickness` (double)
- **Python Coverage:** ✅ Complete

#### LiningMaterial (Lines 578-587)
- **Properties:**
  - `Name` (string)
  - `ProductCode` (string)
  - `Thickness` (double)
  - `WidthPerRoll` (double)
  - `LengthPerRoll` (double)
  - `Overlap` (double)
  - `AreaSqmPerRoll` (double)
- **Python Coverage:** ✅ Complete

### 2. Enums

#### FastenerMaterialType (Lines 225-236)
- Unknown
- Bolt
- **Python Coverage:** ✅ Complete

#### FootingMaterialType (Lines 269-273)
- Block
- Bored
- **Python Coverage:** ✅ Complete

#### FrameMaterialType (Lines 465-503)
- Unknown
- C
- TH (TopHat/RB)
- Z
- SHS
- PAD (Personal Access Door)
- SRDJ (Side Roller Door Jamb)
- **Python Coverage:** ✅ Complete

#### PunchingWhere (Lines 562-569)
- Web
- Flange
- Center
- WebLeft
- WebRight
- **Python Coverage:** ✅ Complete

## Helper Files Analysis

### MaterialSegmentHelper.cs (Lines 1-687)
- **Classes:**
  - `MaterialSegment` (internal)
  - `MaterialSegmentData` (abstract)
- **Methods:**
  - `GetCladdingSegments`
  - `GetNearestFullSegmentFromSkylight`
  - `GetRoofCladdingSegments`
  - `GetLiningSegments`
  - `GetRoofLiningOutlines`
  - `GetWallCladdingSegments` (multiple overloads)
  - Various private helper methods
- **Python Coverage:** ✅ Partial (core functionality implemented)

### BracketMaterialHelper.cs (Lines 1-245)
- **Static Methods:**
  - `LoadAllBrackets()`
  - `CreateAllBrackets()`
  - `TryGetBracketMesh(meshName)`
  - `GetBracketMesh(meshName)`
  - `ReadBracketAttachmentsFile` (multiple overloads)
  - `GetMesh(name)`
  - `TryGetBracketAttachments(name)`
  - `GetBracketMaterial(meshName, productCode)`
  - `Recenter(mesh, attachments)`
- **Python Coverage:** ✅ Partial (interface defined, implementation pending)

## Python Implementation Coverage Summary

### Fully Implemented (100% Coverage):
1. **Base Material Classes:**
   - ✅ BracketMaterial
   - ✅ BracketMesh
   - ✅ BracketAttachment
   - ✅ CladdingMaterial
   - ✅ ColorMaterial
   - ✅ FlashingMaterial
   - ✅ DownpipeMaterial
   - ✅ FastenerMaterial
   - ✅ FootingMaterial
   - ✅ FrameMaterial (all factory methods)
   - ✅ Punching
   - ✅ StrapMaterial
   - ✅ LiningMaterial

2. **Enums:**
   - ✅ FastenerMaterialType
   - ✅ FootingMaterialType
   - ✅ FrameMaterialType
   - ✅ PunchingWhere

3. **Additional Python Features:**
   - ✅ Material helpers (frame, cladding, fastener catalogs)
   - ✅ Visual properties system
   - ✅ Profile management
   - ✅ Segment data structures

### Partially Implemented:
1. **Helper Classes:**
   - MaterialSegmentHelper (core methods implemented)
   - BracketMaterialHelper (interface defined)

### Implementation Notes:
1. All core material classes from Materials.cs are fully implemented
2. Python implementation uses dataclasses for cleaner code
3. Property decorators used for computed properties
4. Static factory methods preserved as classmethod/staticmethod
5. Equality methods implemented using Python's __eq__ and __hash__
6. Additional visualization and profile features added beyond C# version

## Conclusion
The Python implementation has **100% coverage** of all classes, enums, and methods defined in the main Materials.cs file (lines 1-588). The helper files have partial implementation focusing on core functionality needed for the material system.