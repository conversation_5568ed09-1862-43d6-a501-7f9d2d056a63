# C# to Python Implementation Checklist

## Current Python Implementation Status vs C# Original

### ✅ Implemented and Aligned

#### Vec2 Class
- [x] Properties: X, Y
- [x] Methods: length(), length_squared(), angle()
- [x] Static: origin, unit_x, unit_y, min_value, max_value
- [x] Static: dot(), normal(), min(), max()
- [x] Operators: +, -, *, /, ==, !=
- [x] String representation

#### Vec3 Class
- [x] Properties: X, Y, Z
- [x] Methods: length(), length_squared(), to_array()
- [x] Static: origin, unit_x, unit_y, unit_z, min_value, max_value
- [x] Static: dot(), cross(), normal(), min(), max(), distance()
- [x] Operators: +, -, *, /, ==, !=
- [x] String representation

#### Basic Geo Functions
- [x] Angle constants (deg0, deg45, deg90, deg180, deg270, deg360)
- [x] Conversion constants (to_deg, to_rad)
- [x] normalize_angle()
- [x] v2(), v3() creators
- [x] polar(), rotate()
- [x] mid() for scalars and vectors

### ❌ Missing from Python Implementation

#### Vec2 Class Missing
- [ ] inters() - Line intersection method
- [ ] inters_must() - Line intersection with exception
- [ ] inters_list() - Intersection with polyline
- [ ] inters_list_must() - Intersection with polyline (throws)

#### Vec3 Class Missing
- [ ] distance_squared() - Returns float (not double!)
- [ ] midpoint() - Static method for midpoint

#### Line Classes (Completely Missing)
- [ ] Line1 struct with Start, End properties
- [ ] Line1.length(), contains(), from_center()
- [ ] Line2 struct with Start, End (Vec2) properties
- [ ] Line2.length(), direction(), magnitude()
- [ ] Line2.inters(), inters_must(), from_start(), from_center()
- [ ] Line2 operators: +, -, ==, !=
- [ ] Line3 struct with Start, End (Vec3) properties
- [ ] Line3.length(), direction(), magnitude()
- [ ] Line3 operators: +, -, ==, !=

#### Box Classes (Completely Missing)
- [ ] Box2 struct with Min, Max properties
- [ ] Box2.size(), middle(), corner accessors
- [ ] Box2.contains(), intersects(), union()
- [ ] Box2 static factory methods
- [ ] Box3 struct with similar functionality

#### Plane3 Class (Completely Missing)
- [ ] Properties: Normal (Vec3), D (double)
- [ ] Multiple constructors
- [ ] Static intersection methods

#### Basis3 Class (Completely Missing)
- [ ] Properties: X, Y, Z (all Vec3)
- [ ] Static factory methods: from_xy, from_xz, from_yz

#### TriIndex Class (Completely Missing)
- [ ] Properties: A, B, C (all int)

#### Mat4 Class (Completely Missing)
- [ ] 16 properties (M11-M44)
- [ ] get_translation(), get_basis()
- [ ] Static creation methods for rotation, translation, scale
- [ ] Matrix multiplication and inverse
- [ ] Transform methods for position, vector, normal

#### Geo Static Methods Missing
- [ ] wrap_index() - Circular array indexing
- [ ] Trigonometry helpers (trig_soh, trig_sho, etc.)
- [ ] mirror_angle(), mirror_vec_x()
- [ ] get_extents() - Bounding box of points
- [ ] get_offsets() - Cumulative offsets
- [ ] get_bay_sizes() - Bay division algorithm
- [ ] Vector creation shortcuts (vx, vy, vz, v2xy, v2xz, v2yz)
- [ ] Line creation methods (ln1, ln2, ln3, ln2xy, ln2xz, ln2yz)
- [ ] interpolate() for lines
- [ ] round() for vectors and lines
- [ ] Line operations (ln_swap, ln_extend, ln_up, ln_down, ln_offset)
- [ ] ln_low_high(), ln_left_right()
- [ ] ln_range() - Line from center and size
- [ ] poly_offset() - Polygon offsetting
- [ ] box_line_inters() - Line-box clipping
- [ ] get_near_far() - Point ordering by distance

### 🔧 Implementation Differences to Fix

#### Python-Specific Issues
1. **Value Semantics**: C# structs have value semantics, Python classes need careful implementation
2. **Property Access**: C# uses PascalCase properties, Python uses snake_case
3. **Nullable Types**: C# uses `?` suffix, Python needs `Optional[]` or `| None`
4. **Method Overloading**: C# supports multiple signatures, Python needs defaults or different names
5. **Float vs Double**: C# uses double everywhere except distance_squared returns float

#### Algorithm Differences
1. **Hash Code Calculation**: C# uses specific formula: `(((h1 << 5) + h1) ^ h2)`
2. **Debugger Display**: C# rounds to 5 decimal places for debug display
3. **Intersection Methods**: Must match exact C# algorithm for line intersections
4. **GetBaySizes**: Complex algorithm with specific rounding behavior

### 📋 Implementation Priority Order

1. **Critical Geometry Classes**
   - [ ] Line1, Line2, Line3 structures
   - [ ] Box2, Box3 structures
   - [ ] All missing methods on Vec2/Vec3

2. **Essential Algorithms**
   - [ ] Line intersection methods
   - [ ] get_bay_sizes() with proper rounding
   - [ ] Line offset algorithms

3. **Transformation Support**
   - [ ] Mat4 class with all operations
   - [ ] Plane3 for 3D operations
   - [ ] Basis3 for coordinate systems

4. **Utility Functions**
   - [ ] All line manipulation functions
   - [ ] Polygon operations
   - [ ] Missing vector shortcuts

5. **Nice to Have**
   - [ ] TriIndex structure
   - [ ] Specialized debug display
   - [ ] Performance optimizations

### 🚨 Critical Compatibility Requirements

1. **Exact Numeric Behavior**
   - Match C# double precision
   - Handle edge cases identically
   - Preserve special float return for distance_squared

2. **Exception Behavior**
   - "Must" methods throw with same messages
   - Validate parameters identically
   - Include parameter values in errors

3. **Algorithm Fidelity**
   - Line intersection must use same parametric method
   - Matrix inverse must use FGED1 algorithm
   - Bay sizes must round exactly as C# version

4. **Interface Compatibility**
   - Keep all method signatures
   - Support same operator overloads
   - Maintain nullable/optional patterns