# Quick Conversion Reference for Tests

## C# to Python Test Conversion

### Test Framework Mapping
| C# | Python | Usage |
|----|---------|--------|
| `[TestClass]` | `class TestXxx:` | Test class definition |
| `[TestMethod]` | `def test_xxx():` | Test method |
| `[DataTestMethod]` | `@pytest.mark.parametrize` | Parameterized tests |
| `[DataRow(...)]` | Tuple in parametrize | Test data |
| `Assert.AreEqual(a, b)` | `assert a == b` | Basic assertion |
| `Assert.IsNull(x)` | `assert x is None` | Null check |
| `Assert.Throws<T>()` | `pytest.raises(T)` | Exception testing |

### Common Test Patterns

#### 1. Parameterized Tests
```csharp
// C#
[DataTestMethod]
[DataRow(3, 4, 5)]
[DataRow(5, 12, 13)]
public void TestLength(int x, int y, int expected)
{
    var v = new Vec2(x, y);
    Assert.AreEqual(expected, v.Length());
}
```

```python
# Python
@pytest.mark.parametrize("x,y,expected", [
    (3, 4, 5),
    (5, 12, 13),
])
def test_length(x, y, expected):
    v = Vec2(x, y)
    assert v.length() == expected
```

#### 2. Floating Point Comparisons
```python
# Use pytest.approx for float comparisons
assert result == pytest.approx(expected, rel=1e-9)

# Or numpy for arrays
np.testing.assert_allclose(actual_array, expected_array, rtol=1e-9)
```

#### 3. Exception Testing
```csharp
// C#
Assert.ThrowsException<InvalidOperationException>(() => {
    Vec2.IntersMust(a1, a2, b1, b2);
});
```

```python
# Python
with pytest.raises(ValueError):
    Vec2.inters_must(a1, a2, b1, b2)
```

## Test File Naming

| C# Test File | Python Test File | Tests |
|--------------|------------------|--------|
| `GeoTests.cs` | `test_helpers.py` | Geo static methods |
| `Mat4_*_Tests.cs` | `test_mat4.py` | Matrix operations |
| `*Tests.cs` | `test_*.py` | General pattern |

## Quick Assertion Reference

```python
# Equality
assert a == b
assert a != b

# Approximate equality (floats)
assert abs(a - b) < 1e-10
assert math.isclose(a, b, rel_tol=1e-9)

# Collections
assert list1 == list2
assert set1 == set2
assert len(collection) == expected

# None/Not None
assert result is None
assert result is not None

# Boolean
assert condition
assert not condition

# Contains
assert item in collection
assert substring in string

# Type checking
assert isinstance(obj, Vec2)
```

## Test Organization Best Practices

1. **Group related tests in classes**
   ```python
   class TestVec2Operations:
       def test_addition(self): ...
       def test_subtraction(self): ...
   ```

2. **Use descriptive test names**
   ```python
   def test_intersection_parallel_lines_returns_none(self): ...
   ```

3. **Add C# reference in docstrings**
   ```python
   def test_get_bay_sizes():
       """Test bay size calculation.
       
       C# Ref: GeoTests.cs TestGetBaySizes (lines 15-32)
       """
   ```

4. **Use fixtures for common setup**
   ```python
   @pytest.fixture
   def unit_square():
       return Box2(Vec2(0, 0), Vec2(1, 1))
   ```

## Running Tests

```bash
# Run all tests
pytest

# Run specific file
pytest tests/geometry/test_vec2.py

# Run specific test
pytest tests/geometry/test_vec2.py::test_length

# Run with coverage
pytest --cov=src --cov-report=html

# Run with verbose output
pytest -v

# Run only marked tests
pytest -m "slow"
```

## Common Conversions

| C# | Python |
|----|---------|
| `new Vec2(x, y)` | `Vec2(x, y)` |
| `default(Vec2?)` | `None` |
| `double.MaxValue` | `float('inf')` |
| `Math.PI` | `math.pi` |
| `Math.Sqrt()` | `math.sqrt()` |
| `List<Vec2>` | `list[Vec2]` |
| `.Count()` | `len()` |
| `.ElementAt(i)` | `[i]` |

Remember: The goal is 100% behavioral compatibility with C# tests!