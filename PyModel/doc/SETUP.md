# BIM Backend Python Implementation Setup Guide

## Prerequisites

- Python 3.10 or higher
- pip package manager
- Virtual environment support

## Setting Up the Development Environment

### 1. Create a Virtual Environment

```bash
# On Windows
python -m venv venv
venv\Scripts\activate

# On Linux/Mac
python3 -m venv venv
source venv/bin/activate
```

### 2. Install Dependencies

```bash
pip install -r requirements.txt
```

### 3. Install Development Dependencies

```bash
pip install -e .
```

## Project Structure

```
PyModel/
├── src/
│   ├── geometry/       # Mathematical primitives (Vec2, Vec3, Mat4, etc.)
│   ├── materials/      # Material system definitions
│   └── models/         # Core BIM data models
├── tests/              # Comprehensive test suite
│   ├── geometry/       # Geometry tests
│   ├── materials/      # Material tests
│   └── integration/    # Integration tests
├── docs/               # Documentation
├── requirements.txt    # Project dependencies
└── setup.py           # Package setup file
```

## Running Tests

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=src --cov-report=html

# Run specific test file
pytest tests/geometry/test_vec2.py

# Run with verbose output
pytest -v
```

## Code Style

This project follows PEP 8 guidelines with the following tools:

- **black**: Code formatting
- **isort**: Import sorting
- **flake8**: Linting
- **mypy**: Type checking

Run code quality checks:

```bash
# Format code
black src/ tests/

# Sort imports
isort src/ tests/

# Lint code
flake8 src/ tests/

# Type checking
mypy src/
```

## Development Workflow

1. Always work within the virtual environment
2. Write tests first (TDD approach)
3. Ensure all tests pass before committing
4. Run code quality checks before pushing
5. Document all public APIs

## Mathematical Foundation Implementation Progress

- [ ] Vec2 class with all methods
- [ ] Vec3 class with all methods
- [ ] Mat4 class with all methods
- [ ] Line1, Line2, Line3 classes
- [ ] Box2, Box3 classes
- [ ] Plane3 class
- [ ] Basis3 class
- [ ] TriIndex class
- [ ] Comprehensive test coverage