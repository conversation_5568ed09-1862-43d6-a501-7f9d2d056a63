# Extending for New Building Types - Adding Support Beyond Carports and Sheds

This guide explains how to extend PyModel to support new building types, using a greenhouse example.

## Table of Contents
1. [Extension Architecture](#extension-architecture)
2. [Planning Your Building Type](#planning-your-building-type)
3. [Creating the Builder Class](#creating-the-builder-class)
4. [Defining Custom Components](#defining-custom-components)
5. [Business Logic Implementation](#business-logic-implementation)
6. [API Integration](#api-integration)
7. [Testing Your Extension](#testing-your-extension)
8. [Complete Example: Greenhouse](#complete-example-greenhouse)

## Extension Architecture

### Extension Points

PyModel is designed for extensibility at multiple levels:

```
Building Type Extension Points
├── Builder Class (IStructureBuilder)
├── Custom Components (BIMComponent)
├── Business Rules (ValidationRules)
├── Material Definitions (MaterialLibrary)
├── API Endpoints (FastAPI routes)
└── Output Customization (IOutputGenerator)
```

### Required Interfaces

```python
from abc import ABC, abstractmethod

class IStructureBuilder(ABC):
    """Interface for all building type builders"""
    
    @abstractmethod
    def validate_input(self, building_input):
        """Validate building parameters"""
        pass
    
    @abstractmethod
    def create_structure(self, building_input):
        """Create the building structure"""
        pass
    
    @abstractmethod
    def add_cladding(self, structure, cladding_options):
        """Add cladding to structure"""
        pass
    
    @abstractmethod
    def add_openings(self, structure, openings):
        """Add doors, windows, vents"""
        pass
    
    @abstractmethod
    def add_accessories(self, structure, accessories):
        """Add gutters, flashings, etc."""
        pass
    
    @abstractmethod
    def get_validation_rules(self):
        """Return building-specific validation rules"""
        pass
```

## Planning Your Building Type

### Step 1: Define Requirements

Before coding, document your building type requirements:

```python
# Example: Greenhouse Requirements
greenhouse_requirements = {
    "structural": {
        "frame_types": ["gable", "gothic_arch", "quonset"],
        "materials": ["aluminum", "galvanized_steel", "timber"],
        "foundation": ["concrete_pad", "ground_posts", "raised_beds"]
    },
    "envelope": {
        "glazing": ["polycarbonate", "glass", "polyethylene"],
        "ventilation": ["roof_vents", "side_vents", "exhaust_fans"],
        "shading": ["external_shade", "internal_shade", "none"]
    },
    "systems": {
        "heating": ["none", "gas", "electric", "hydronic"],
        "cooling": ["natural", "evaporative", "mechanical"],
        "irrigation": ["none", "drip", "overhead", "hydroponic"]
    },
    "dimensions": {
        "min_width": 3000,
        "max_width": 30000,
        "min_length": 6000,
        "max_length": 100000,
        "min_height": 2400,
        "max_height": 8000
    }
}
```

### Step 2: Identify Unique Components

List components specific to your building type:

```python
# Greenhouse-specific components
greenhouse_components = {
    "structural": [
        "ArchwayFrame",
        "TrussFrame", 
        "VentilationFrame"
    ],
    "glazing": [
        "PolycarbonatePanel",
        "GlassPanel",
        "PolyethyleneSheet"
    ],
    "systems": [
        "RoofVent",
        "SideVent",
        "ShadeScreen",
        "HeatingPipe",
        "IrrigationLine"
    ],
    "accessories": [
        "GutterWithHeating",
        "CondensationChannel",
        "InsectScreen"
    ]
}
```

## Creating the Builder Class

### Basic Builder Structure

```python
# src/business/greenhouse_builder.py
from src.business.structure_builder import IStructureBuilder
from src.bim import Building, Assembly
from src.geometry import Vec3, Mat4
from src.materials import FrameMaterial

class GreenhouseBuilder(IStructureBuilder):
    """Builder for greenhouse structures"""
    
    def __init__(self):
        self.building_type = "greenhouse"
        self.supported_profiles = ["gable", "gothic_arch", "quonset"]
        
    def validate_input(self, building_input):
        """Validate greenhouse-specific parameters"""
        errors = []
        
        # Check dimensions
        if building_input.width < 3000:
            errors.append("Greenhouse width must be at least 3m")
        
        if building_input.width > 30000:
            errors.append("Greenhouse width cannot exceed 30m")
            
        # Check bay spacing for structural integrity
        if building_input.bay_spacing > 3000:
            errors.append("Bay spacing cannot exceed 3m for greenhouses")
            
        # Validate glazing type
        valid_glazing = ["polycarbonate", "glass", "polyethylene"]
        if building_input.glazing_type not in valid_glazing:
            errors.append(f"Invalid glazing type. Must be one of: {valid_glazing}")
            
        # Check ventilation requirements
        if building_input.width > 9000 and not building_input.roof_vents:
            errors.append("Greenhouses over 9m wide require roof ventilation")
            
        return errors
    
    def create_structure(self, building_input):
        """Create greenhouse frame structure"""
        building = Building(f"Greenhouse_{building_input.id}")
        
        # Create based on profile type
        if building_input.profile == "gable":
            self._create_gable_greenhouse(building, building_input)
        elif building_input.profile == "gothic_arch":
            self._create_gothic_arch_greenhouse(building, building_input)
        elif building_input.profile == "quonset":
            self._create_quonset_greenhouse(building, building_input)
            
        return building
    
    def _create_gable_greenhouse(self, building, input_params):
        """Create gable-style greenhouse frame"""
        # Calculate key dimensions
        ridge_height = input_params.eave_height + (
            input_params.width / 2 * math.tan(math.radians(input_params.roof_pitch))
        )
        
        # Create frames
        num_frames = int(input_params.length / input_params.bay_spacing) + 1
        
        for i in range(num_frames):
            y_pos = i * input_params.bay_spacing
            
            # Create frame assembly
            frame = self._create_gable_frame(
                position=Vec3(0, y_pos, 0),
                width=input_params.width,
                eave_height=input_params.eave_height,
                ridge_height=ridge_height,
                material=self._get_frame_material(input_params)
            )
            
            building.add_assembly(frame)
    
    def _create_gable_frame(self, position, width, eave_height, ridge_height, material):
        """Create single gable frame"""
        frame = Assembly(f"Frame_at_{position.y}")
        
        # Left column
        left_column = Column(
            position=position,
            height=eave_height,
            material=material,
            name=f"LC_{position.y}"
        )
        
        # Right column  
        right_column = Column(
            position=position + Vec3(width, 0, 0),
            height=eave_height,
            material=material,
            name=f"RC_{position.y}"
        )
        
        # Left rafter
        left_rafter = Beam(
            start=Vec3(position.x, position.y, eave_height),
            end=Vec3(position.x + width/2, position.y, ridge_height),
            material=material,
            name=f"LR_{position.y}"
        )
        
        # Right rafter
        right_rafter = Beam(
            start=Vec3(position.x + width, position.y, eave_height),
            end=Vec3(position.x + width/2, position.y, ridge_height),
            material=material,
            name=f"RR_{position.y}"
        )
        
        # Add to assembly
        frame.add_components([left_column, right_column, left_rafter, right_rafter])
        
        return frame
```

### Advanced Frame Types

```python
def _create_gothic_arch_greenhouse(self, building, input_params):
    """Create gothic arch greenhouse with curved rafters"""
    
    # Gothic arch uses radius = width for the arch
    arch_radius = input_params.width / 2
    arch_center_height = input_params.eave_height
    
    num_frames = int(input_params.length / input_params.bay_spacing) + 1
    
    for i in range(num_frames):
        y_pos = i * input_params.bay_spacing
        
        # Create curved frame members
        frame = self._create_gothic_frame(
            position=Vec3(0, y_pos, 0),
            width=input_params.width,
            base_height=input_params.base_height,
            arch_radius=arch_radius,
            segments=16  # Number of segments for curve
        )
        
        building.add_assembly(frame)

def _create_gothic_frame(self, position, width, base_height, arch_radius, segments):
    """Create gothic arch frame with curved top"""
    frame = Assembly(f"GothicFrame_at_{position.y}")
    
    # Create straight columns up to spring point
    spring_height = base_height
    
    left_column = Column(
        position=position,
        height=spring_height,
        material=self._get_frame_material(),
        name=f"GLC_{position.y}"
    )
    
    right_column = Column(
        position=position + Vec3(width, 0, 0),
        height=spring_height,
        material=self._get_frame_material(),
        name=f"GRC_{position.y}"
    )
    
    # Create curved arch segments
    arch_segments = []
    
    # Left arch
    for i in range(segments // 2):
        angle_start = math.pi - (i * math.pi / segments)
        angle_end = math.pi - ((i + 1) * math.pi / segments)
        
        start_x = position.x + arch_radius + arch_radius * math.cos(angle_start)
        start_z = spring_height + arch_radius * math.sin(angle_start)
        end_x = position.x + arch_radius + arch_radius * math.cos(angle_end)
        end_z = spring_height + arch_radius * math.sin(angle_end)
        
        segment = Beam(
            start=Vec3(start_x, position.y, start_z),
            end=Vec3(end_x, position.y, end_z),
            material=self._get_frame_material(),
            name=f"GA_L{i}_{position.y}"
        )
        
        arch_segments.append(segment)
    
    frame.add_components([left_column, right_column] + arch_segments)
    
    return frame
```

## Defining Custom Components

### Greenhouse-Specific Components

```python
# src/bim/greenhouse_components.py
from src.bim.components import BIMComponent
from src.geometry import Vec3, Box3
from src.materials import TransparentMaterial

class PolycarbonatePanel(BIMComponent):
    """Multi-wall polycarbonate panel for greenhouse glazing"""
    
    def __init__(self, origin, width, height, thickness=8, walls=2, uv_coating=True):
        super().__init__(name="PolycarbonatePanel")
        self.origin = origin
        self.width = width
        self.height = height
        self.thickness = thickness  # mm
        self.wall_count = walls     # 2-wall, 3-wall, etc.
        self.uv_coating = uv_coating
        
        # Material properties
        self.material = TransparentMaterial(
            name=f"Polycarbonate_{walls}wall_{thickness}mm",
            transparency=0.82 if walls == 2 else 0.74,
            u_value=3.3 if walls == 2 else 2.5,  # W/m²K
            light_diffusion=0.53,
            impact_resistance="high",
            uv_protection=uv_coating
        )
    
    def get_thermal_properties(self):
        """Return thermal properties for energy calculations"""
        return {
            "u_value": self.material.u_value,
            "solar_heat_gain": 0.76 if self.wall_count == 2 else 0.68,
            "light_transmission": self.material.transparency,
            "r_value": 1 / self.material.u_value
        }
    
    def get_geometry(self):
        """Generate panel geometry"""
        vertices = [
            self.origin,
            self.origin + Vec3(self.width, 0, 0),
            self.origin + Vec3(self.width, 0, self.height),
            self.origin + Vec3(0, 0, self.height)
        ]
        
        # Add thickness
        back_vertices = [v + Vec3(0, self.thickness, 0) for v in vertices]
        
        return vertices + back_vertices

class RoofVent(BIMComponent):
    """Automatic roof ventilation window"""
    
    def __init__(self, position, width, height, opening_angle=45, automatic=True):
        super().__init__(name="RoofVent")
        self.position = position
        self.width = width
        self.height = height
        self.max_opening_angle = opening_angle
        self.automatic = automatic
        
        # Control system
        if automatic:
            self.control_system = {
                "type": "temperature_controlled",
                "sensor_type": "thermostatic",
                "opening_temp": 24,  # °C
                "closing_temp": 20,  # °C
                "wind_sensor": True,
                "rain_sensor": True
            }
    
    def calculate_ventilation_area(self, opening_percentage=100):
        """Calculate effective ventilation area"""
        # Effective area depends on opening angle
        angle_rad = math.radians(self.max_opening_angle * opening_percentage / 100)
        effective_height = self.height * math.sin(angle_rad)
        
        return self.width * effective_height / 1_000_000  # m²
    
    def get_geometry_at_opening(self, opening_percentage):
        """Get vent geometry at specific opening percentage"""
        angle = self.max_opening_angle * opening_percentage / 100
        rotation = Mat4.create_rotation_x(math.radians(angle))
        
        # Vent panel vertices
        base_vertices = [
            self.position,
            self.position + Vec3(self.width, 0, 0),
            self.position + Vec3(self.width, self.height, 0),
            self.position + Vec3(0, self.height, 0)
        ]
        
        # Apply rotation around bottom edge
        rotated_vertices = []
        pivot = self.position + Vec3(self.width / 2, 0, 0)
        
        for vertex in base_vertices:
            relative = vertex - pivot
            rotated = rotation.transform_position(relative)
            rotated_vertices.append(pivot + rotated)
        
        return rotated_vertices

class ShadeScreen(BIMComponent):
    """Retractable shade screen system"""
    
    def __init__(self, bay_start, bay_end, shade_factor=50, motorized=True):
        super().__init__(name="ShadeScreen")
        self.start_position = bay_start
        self.end_position = bay_end
        self.shade_factor = shade_factor  # Percentage of light blocked
        self.motorized = motorized
        
        # Screen material
        self.screen_material = {
            "type": "aluminum_strip",
            "openness_factor": 100 - shade_factor,
            "energy_saving": shade_factor * 0.75,  # Approximate
            "uv_protection": 0.95
        }
    
    def calculate_coverage_area(self):
        """Calculate area covered by shade screen"""
        width = abs(self.end_position.x - self.start_position.x)
        length = abs(self.end_position.y - self.start_position.y)
        return width * length / 1_000_000  # m²
    
    def get_energy_savings(self, solar_radiation):
        """Calculate energy savings from shading"""
        blocked_radiation = solar_radiation * (self.shade_factor / 100)
        return blocked_radiation * self.calculate_coverage_area()
```

### System Components

```python
class HeatingSystem(BIMComponent):
    """Greenhouse heating system"""
    
    def __init__(self, greenhouse_volume, heating_type="hydronic", capacity_kw=None):
        super().__init__(name="HeatingSystem")
        self.volume = greenhouse_volume
        self.heating_type = heating_type
        self.capacity = capacity_kw or self._calculate_required_capacity()
        
        # Define heating components based on type
        if heating_type == "hydronic":
            self.components = self._create_hydronic_system()
        elif heating_type == "forced_air":
            self.components = self._create_forced_air_system()
        elif heating_type == "radiant":
            self.components = self._create_radiant_system()
    
    def _calculate_required_capacity(self):
        """Calculate heating capacity based on volume and location"""
        # Simplified calculation - should use proper heat loss calculations
        # Assume 35 W/m³ for moderate climate
        return self.volume * 35 / 1000  # kW
    
    def _create_hydronic_system(self):
        """Create hydronic heating pipes and boiler"""
        components = []
        
        # Boiler
        boiler = {
            "type": "condensing_boiler",
            "capacity": self.capacity,
            "efficiency": 0.92,
            "fuel": "natural_gas"
        }
        
        # Heating pipes - typically along walls and under benches
        pipe_loops = []
        perimeter = 2 * (self.width + self.length)
        
        # Wall pipes
        pipe_loops.append({
            "location": "perimeter_wall",
            "length": perimeter,
            "diameter": 32,  # mm
            "material": "steel",
            "insulated": False
        })
        
        # Under-bench pipes if applicable
        if self.has_benches:
            pipe_loops.append({
                "location": "under_bench",
                "length": self.bench_length,
                "diameter": 25,
                "material": "pex",
                "insulated": True
            })
        
        components.append(boiler)
        components.extend(pipe_loops)
        
        return components
```

## Business Logic Implementation

### Greenhouse-Specific Rules

```python
# src/business/greenhouse_rules.py
class GreenhouseBusinessRules:
    """Business rules and calculations for greenhouses"""
    
    @staticmethod
    def calculate_ventilation_requirements(greenhouse):
        """Calculate required ventilation area"""
        # ASHRAE recommends 0.75-1.0 air changes per minute
        volume = greenhouse.get_volume()  # m³
        
        # Natural ventilation rule: vent area = 15-25% of floor area
        floor_area = greenhouse.get_floor_area()
        min_vent_area = floor_area * 0.15
        max_vent_area = floor_area * 0.25
        
        # Check if roof vents provide enough area
        total_vent_area = sum(
            vent.calculate_ventilation_area() 
            for vent in greenhouse.get_roof_vents()
        )
        
        if total_vent_area < min_vent_area:
            return {
                "sufficient": False,
                "required": min_vent_area,
                "provided": total_vent_area,
                "additional_needed": min_vent_area - total_vent_area
            }
        
        return {
            "sufficient": True,
            "required": min_vent_area,
            "provided": total_vent_area,
            "air_changes_per_hour": (total_vent_area * 3600) / volume
        }
    
    @staticmethod
    def calculate_heating_load(greenhouse, design_conditions):
        """Calculate heating requirements"""
        # Get surface areas
        glazing_area = greenhouse.get_glazing_area()
        
        # Get U-values
        glazing_u_value = greenhouse.glazing_material.u_value
        
        # Temperature difference
        delta_t = design_conditions["inside_temp"] - design_conditions["outside_temp"]
        
        # Basic heat loss calculation
        glazing_loss = glazing_area * glazing_u_value * delta_t
        
        # Add infiltration losses (simplified)
        volume = greenhouse.get_volume()
        air_changes = 0.5  # per hour
        infiltration_loss = (
            volume * air_changes * 1.2 * 1.006 * delta_t / 3600
        )  # kW
        
        # Total heating load
        total_load = (glazing_loss + infiltration_loss) * 1.2  # 20% safety factor
        
        return {
            "total_load_kw": total_load,
            "glazing_loss_kw": glazing_loss,
            "infiltration_loss_kw": infiltration_loss,
            "load_per_m2": total_load / greenhouse.get_floor_area()
        }
    
    @staticmethod
    def optimize_orientation(site_latitude, greenhouse_type):
        """Determine optimal greenhouse orientation"""
        if greenhouse_type == "production":
            # For year-round production, east-west is usually better
            if abs(site_latitude) > 40:
                return {
                    "orientation": "east_west",
                    "reason": "Better winter light distribution at high latitude"
                }
            else:
                return {
                    "orientation": "north_south",
                    "reason": "Better summer cooling at low latitude"
                }
        else:
            # For seasonal use
            return {
                "orientation": "north_south",
                "reason": "More uniform daily light"
            }
```

### Structural Calculations

```python
class GreenhouseEngineering:
    """Structural engineering for greenhouses"""
    
    @staticmethod
    def calculate_snow_load(greenhouse, ground_snow_load):
        """Calculate snow load on greenhouse roof"""
        # Greenhouse-specific factors
        thermal_factor = 1.2  # Heated greenhouse
        
        # Shape factors for different roof types
        shape_factors = {
            "gable": 0.8,
            "gothic_arch": 0.7,
            "quonset": 0.7,
            "sawtooth": 0.9
        }
        
        shape_factor = shape_factors.get(greenhouse.roof_type, 1.0)
        
        # Importance factor for agricultural buildings
        importance_factor = 0.8
        
        # Calculate roof snow load
        roof_snow_load = (
            ground_snow_load * 
            thermal_factor * 
            shape_factor * 
            importance_factor
        )
        
        return {
            "design_snow_load": roof_snow_load,
            "ground_snow_load": ground_snow_load,
            "thermal_factor": thermal_factor,
            "shape_factor": shape_factor
        }
    
    @staticmethod
    def calculate_wind_load(greenhouse, basic_wind_speed):
        """Calculate wind load on greenhouse"""
        # Greenhouse-specific pressure coefficients
        if greenhouse.roof_type == "gable":
            cp_windward = 0.8
            cp_leeward = -0.5
            cp_roof_windward = -0.7
            cp_roof_leeward = -0.3
        elif greenhouse.roof_type == "gothic_arch":
            cp_windward = 0.7
            cp_leeward = -0.4
            cp_roof = -0.6
        
        # Calculate pressure
        velocity_pressure = 0.613 * (basic_wind_speed ** 2) / 1000  # kPa
        
        # Apply factors
        exposure_factor = 1.0  # Open terrain typical for greenhouses
        gust_factor = 0.85
        
        design_pressure = velocity_pressure * exposure_factor * gust_factor
        
        return {
            "design_wind_pressure": design_pressure,
            "uplift_pressure": design_pressure * abs(cp_roof_windward),
            "lateral_pressure": design_pressure * cp_windward
        }
```

## API Integration

### Adding Greenhouse Endpoints

```python
# src/api/greenhouse_endpoints.py
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, validator
from typing import List, Optional

router = APIRouter(prefix="/api/greenhouse", tags=["greenhouse"])

class GreenhouseRequest(BaseModel):
    """Greenhouse generation request model"""
    
    # Dimensions
    width: float
    length: float
    eave_height: float
    
    # Structure
    profile_type: str = "gable"
    bay_spacing: float = 3000
    frame_material: str = "aluminum"
    
    # Glazing
    glazing_type: str = "polycarbonate"
    glazing_thickness: int = 8
    wall_count: int = 2
    
    # Ventilation
    roof_vents: bool = True
    vent_spacing: float = 6000
    side_vents: bool = False
    
    # Systems
    heating_type: Optional[str] = None
    cooling_type: str = "natural"
    shading: bool = False
    
    # Growing systems
    bench_layout: Optional[str] = None
    irrigation_type: Optional[str] = None
    
    @validator('width')
    def validate_width(cls, v):
        if v < 3000 or v > 30000:
            raise ValueError('Width must be between 3000 and 30000 mm')
        return v
    
    @validator('profile_type')
    def validate_profile(cls, v):
        valid_profiles = ['gable', 'gothic_arch', 'quonset', 'sawtooth']
        if v not in valid_profiles:
            raise ValueError(f'Profile must be one of: {valid_profiles}')
        return v

@router.post("/generate")
async def generate_greenhouse(request: GreenhouseRequest):
    """Generate a greenhouse model"""
    try:
        # Create builder
        builder = GreenhouseBuilder()
        
        # Validate input
        errors = builder.validate_input(request)
        if errors:
            raise HTTPException(status_code=400, detail={"errors": errors})
        
        # Create structure
        greenhouse = builder.create_structure(request)
        
        # Add glazing
        builder.add_glazing(greenhouse, request)
        
        # Add ventilation
        builder.add_ventilation(greenhouse, request)
        
        # Add systems
        if request.heating_type:
            builder.add_heating(greenhouse, request)
        
        if request.shading:
            builder.add_shading(greenhouse, request)
        
        # Generate outputs
        output_service = OutputService()
        
        results = {
            "model_id": greenhouse.id,
            "outputs": {
                "ifc": output_service.generate_ifc(greenhouse),
                "glb": output_service.generate_glb(greenhouse),
                "dxf": output_service.generate_dxf(greenhouse)
            },
            "calculations": {
                "floor_area": greenhouse.get_floor_area(),
                "volume": greenhouse.get_volume(),
                "glazing_area": greenhouse.get_glazing_area(),
                "ventilation": GreenhouseBusinessRules.calculate_ventilation_requirements(greenhouse)
            }
        }
        
        return results
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/calculate/heating")
async def calculate_heating(
    model_id: str,
    inside_temp: float = 18,
    outside_temp: float = -5
):
    """Calculate heating requirements for a greenhouse"""
    
    # Load greenhouse model
    greenhouse = load_model(model_id)
    
    if not greenhouse:
        raise HTTPException(status_code=404, detail="Model not found")
    
    # Calculate heating load
    design_conditions = {
        "inside_temp": inside_temp,
        "outside_temp": outside_temp
    }
    
    heating_calc = GreenhouseBusinessRules.calculate_heating_load(
        greenhouse, 
        design_conditions
    )
    
    return heating_calc

@router.post("/optimize/layout")
async def optimize_layout(
    width: float,
    length: float,
    bench_type: str = "rolling",
    aisle_width: float = 800
):
    """Optimize bench layout for growing area"""
    
    optimizer = GreenhouseLayoutOptimizer()
    
    layout = optimizer.optimize_bench_layout(
        width=width,
        length=length,
        bench_type=bench_type,
        aisle_width=aisle_width
    )
    
    return {
        "layout": layout,
        "growing_area": layout['total_bench_area'],
        "efficiency": layout['space_efficiency'],
        "bench_count": layout['bench_count']
    }
```

### Registering the New Building Type

```python
# src/api/main.py
from src.api.greenhouse_endpoints import router as greenhouse_router

# Register greenhouse endpoints
app.include_router(greenhouse_router)

# Register builder in factory
from src.business.building_factory import BuildingFactory
from src.business.greenhouse_builder import GreenhouseBuilder

BuildingFactory.register_builder("greenhouse", GreenhouseBuilder)
```

## Testing Your Extension

### Unit Tests

```python
# tests/greenhouse/test_greenhouse_builder.py
import pytest
from src.business.greenhouse_builder import GreenhouseBuilder
from src.business.building_input import BuildingInput

class TestGreenhouseBuilder:
    
    def test_validate_input_valid(self):
        """Test validation with valid input"""
        builder = GreenhouseBuilder()
        
        input_data = BuildingInput(
            width=9000,
            length=30000,
            eave_height=3000,
            profile="gable",
            bay_spacing=3000,
            glazing_type="polycarbonate"
        )
        
        errors = builder.validate_input(input_data)
        assert len(errors) == 0
    
    def test_validate_input_invalid_width(self):
        """Test validation with invalid width"""
        builder = GreenhouseBuilder()
        
        input_data = BuildingInput(
            width=2000,  # Too small
            length=30000,
            eave_height=3000
        )
        
        errors = builder.validate_input(input_data)
        assert len(errors) > 0
        assert "width must be at least 3m" in errors[0].lower()
    
    def test_create_gable_structure(self):
        """Test gable greenhouse creation"""
        builder = GreenhouseBuilder()
        
        input_data = BuildingInput(
            width=9000,
            length=30000,
            eave_height=3000,
            profile="gable",
            roof_pitch=26,
            bay_spacing=3000
        )
        
        greenhouse = builder.create_structure(input_data)
        
        # Check frame count
        frames = greenhouse.get_assemblies_by_type("frame")
        expected_frames = 11  # 30m / 3m + 1
        assert len(frames) == expected_frames
        
        # Check dimensions
        assert greenhouse.get_width() == 9000
        assert greenhouse.get_length() == 30000
    
    def test_ventilation_calculation(self):
        """Test ventilation requirements calculation"""
        builder = GreenhouseBuilder()
        
        # Create greenhouse
        greenhouse = builder.create_structure(test_input)
        builder.add_ventilation(greenhouse, test_input)
        
        # Calculate ventilation
        vent_calc = GreenhouseBusinessRules.calculate_ventilation_requirements(greenhouse)
        
        assert vent_calc["sufficient"] == True
        assert vent_calc["air_changes_per_hour"] > 30  # Should be 30-60 for natural ventilation
```

### Integration Tests

```python
# tests/greenhouse/test_greenhouse_integration.py
class TestGreenhouseIntegration:
    
    def test_full_greenhouse_generation(self, client):
        """Test complete greenhouse generation via API"""
        
        request_data = {
            "width": 12000,
            "length": 50000,
            "eave_height": 4000,
            "profile_type": "gothic_arch",
            "glazing_type": "polycarbonate",
            "glazing_thickness": 16,
            "wall_count": 3,
            "roof_vents": True,
            "vent_spacing": 6000,
            "heating_type": "hydronic",
            "shading": True
        }
        
        response = client.post("/api/greenhouse/generate", json=request_data)
        
        assert response.status_code == 200
        result = response.json()
        
        # Check outputs
        assert "ifc" in result["outputs"]
        assert "glb" in result["outputs"]
        
        # Check calculations
        assert result["calculations"]["floor_area"] == 600  # m²
        assert result["calculations"]["ventilation"]["sufficient"] == True
    
    def test_heating_calculation_endpoint(self, client):
        """Test heating calculation for greenhouse"""
        
        # First generate a greenhouse
        generate_response = client.post("/api/greenhouse/generate", json=test_greenhouse_data)
        model_id = generate_response.json()["model_id"]
        
        # Calculate heating
        heating_response = client.post(
            "/api/greenhouse/calculate/heating",
            json={
                "model_id": model_id,
                "inside_temp": 20,
                "outside_temp": -10
            }
        )
        
        assert heating_response.status_code == 200
        heating_data = heating_response.json()
        
        assert "total_load_kw" in heating_data
        assert heating_data["total_load_kw"] > 0
```

## Complete Example: Greenhouse

Here's a complete working example putting it all together:

```python
# Complete greenhouse implementation example
from src.business.greenhouse_builder import GreenhouseBuilder
from src.api.endpoints import app
import uvicorn

# 1. Create a greenhouse via API
import requests

greenhouse_data = {
    "width": 9600,       # 9.6m wide (standard)
    "length": 30000,     # 30m long
    "eave_height": 3500, # 3.5m to eave
    "profile_type": "gothic_arch",
    "bay_spacing": 3000,
    "frame_material": "galvanized_steel",
    
    # Glazing
    "glazing_type": "polycarbonate", 
    "glazing_thickness": 16,
    "wall_count": 3,  # Triple-wall for insulation
    
    # Ventilation
    "roof_vents": True,
    "vent_spacing": 6000,  # Every other bay
    "side_vents": True,
    
    # Systems
    "heating_type": "hydronic",
    "cooling_type": "evaporative",
    "shading": True,
    
    # Growing
    "bench_layout": "peninsula",
    "irrigation_type": "drip"
}

# Generate via API
response = requests.post(
    "http://localhost:8000/api/greenhouse/generate",
    json=greenhouse_data
)

result = response.json()
print(f"Greenhouse created: {result['model_id']}")
print(f"Floor area: {result['calculations']['floor_area']} m²")
print(f"Volume: {result['calculations']['volume']} m³")

# 2. Calculate heating requirements
heating_calc = requests.post(
    "http://localhost:8000/api/greenhouse/calculate/heating",
    json={
        "model_id": result['model_id'],
        "inside_temp": 18,
        "outside_temp": -15
    }
)

heating = heating_calc.json()
print(f"Heating required: {heating['total_load_kw']} kW")

# 3. Download outputs
# IFC file for BIM
ifc_url = result['outputs']['ifc']
ifc_response = requests.get(f"http://localhost:8000{ifc_url}")
with open("greenhouse.ifc", "wb") as f:
    f.write(ifc_response.content)

# GLB for 3D viewing
glb_url = result['outputs']['glb']
glb_response = requests.get(f"http://localhost:8000{glb_url}")
with open("greenhouse.glb", "wb") as f:
    f.write(glb_response.content)

# 4. Generate specialized reports
# Growing area optimization
layout = requests.post(
    "http://localhost:8000/api/greenhouse/optimize/layout",
    json={
        "width": 9600,
        "length": 30000,
        "bench_type": "rolling",
        "aisle_width": 800
    }
)

layout_result = layout.json()
print(f"Growing area: {layout_result['growing_area']} m²")
print(f"Space efficiency: {layout_result['efficiency']}%")
```

## Best Practices

1. **Follow Existing Patterns**
   ```python
   # Use same structure as existing builders
   class YourBuilder(IStructureBuilder):
       # Implement all required methods
   ```

2. **Validate Thoroughly**
   ```python
   # Add building-specific validation
   def validate_input(self, input_data):
       errors = super().validate_input(input_data)
       # Add your validations
       return errors
   ```

3. **Document Components**
   ```python
   class CustomComponent(BIMComponent):
       """
       Document purpose and usage.
       
       Args:
           param1: Description
           param2: Description
       """
   ```

4. **Test Comprehensively**
   ```python
   # Test validation, generation, and edge cases
   def test_edge_cases(self):
       # Minimum size
       # Maximum size
       # Invalid combinations
   ```

## Summary

To extend PyModel for new building types:

1. **Plan** - Define requirements and components
2. **Implement Builder** - Create builder class implementing IStructureBuilder
3. **Create Components** - Define building-specific BIM components
4. **Add Business Logic** - Implement rules and calculations
5. **Integrate API** - Add endpoints and models
6. **Test Thoroughly** - Unit and integration tests
7. **Document** - Add guides and examples

The greenhouse example shows how to:
- Handle curved geometry
- Add specialized systems
- Implement building-specific calculations
- Create custom components
- Integrate with the API

This same pattern can be applied to any building type: warehouses, barns, industrial buildings, or residential structures.