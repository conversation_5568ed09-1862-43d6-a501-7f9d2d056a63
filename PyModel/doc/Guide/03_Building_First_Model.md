# Building Your First Model - Step-by-Step Tutorial

This tutorial will walk you through creating your first building model using PyModel, from simple carport to complex shed with custom features.

## Table of Contents
1. [Prerequisites](#prerequisites)
2. [Simple Carport Model](#simple-carport-model)
3. [Adding Materials and Colors](#adding-materials-and-colors)
4. [Custom Dimensions](#custom-dimensions)
5. [Adding Openings](#adding-openings)
6. [Complex Shed Example](#complex-shed-example)
7. [Viewing Your Model](#viewing-your-model)
8. [Troubleshooting](#troubleshooting)

## Prerequisites

Before starting, ensure:
1. PyModel API server is running (`python run_api.py`)
2. You have a tool to make API calls (Python, Postman, cURL)
3. You have a 3D viewer for GLB files (Windows 3D Viewer, online viewers)
4. You have an IFC viewer (optional - BIM Vision, IFC++, etc.)

## Simple Carport Model

Let's start with the simplest possible carport:

### Step 1: Basic Structure

```python
import requests
import json

# API endpoint
BASE_URL = "http://localhost:8000"

# Minimal carport configuration
simple_carport = {
    "width": 6000,    # 6 meters wide
    "length": 6000,   # 6 meters long
    "height": 2400    # 2.4 meters high
}

# Generate the model
response = requests.post(
    f"{BASE_URL}/api/carport/generate",
    json=simple_carport
)

# Check response
if response.status_code == 200:
    result = response.json()
    print("Success! Model ID:", result['model_id'])
    print("Download IFC:", BASE_URL + result['outputs']['ifc_url'])
    print("Download GLB:", BASE_URL + result['outputs']['glb_url'])
else:
    print("Error:", response.text)
```

### Step 2: Understanding the Response

The API returns:
```json
{
    "success": true,
    "model_id": "a7f3b2c1-5d4e-4321-b123-456789abcdef",
    "message": "Carport model generated successfully",
    "outputs": {
        "ifc_url": "/api/models/a7f3b2c1-5d4e-4321-b123-456789abcdef/download/ifc",
        "glb_url": "/api/models/a7f3b2c1-5d4e-4321-b123-456789abcdef/download/glb"
    }
}
```

### Step 3: Download the Files

```python
# Download GLB file for 3D viewing
model_id = result['model_id']
glb_response = requests.get(f"{BASE_URL}/api/models/{model_id}/download/glb")

with open("my_first_carport.glb", "wb") as f:
    f.write(glb_response.content)
    
print("GLB file saved as 'my_first_carport.glb'")
```

## Adding Materials and Colors

Now let's create a more detailed carport with specific materials:

```python
# Carport with materials
detailed_carport = {
    "width": 6000,
    "length": 9000,    # Longer carport
    "height": 2700,    # Taller
    "roof_type": "gable",  # Peaked roof
    "roof_pitch": 15,      # 15-degree angle
    "materials": {
        "frame": {
            "type": "steel",
            "finish": "galvanized"
        },
        "cladding": {
            "type": "colorbond",
            "profile": "corrugated",
            "color": "monument"  # Dark grey color
        },
        "gutters": {
            "type": "colorbond",
            "profile": "quad",
            "color": "monument"
        }
    },
    "accessories": {
        "gutters": True,
        "downpipes": True,
        "ridge_capping": True
    }
}

response = requests.post(
    f"{BASE_URL}/api/carport/generate",
    json=detailed_carport
)

if response.status_code == 200:
    result = response.json()
    print("Detailed carport created:", result['model_id'])
```

### Material Options

Available materials and colors:

**Frame Materials:**
- `steel` - Standard steel frame
- `aluminum` - Lightweight option
- `timber` - Wood frame (if supported)

**Cladding Profiles:**
- `corrugated` - Traditional corrugated sheets
- `trimdek` - Modern profile
- `spandek` - Concealed fix profile
- `monoclad` - Industrial profile

**Colorbond Colors:**
- `monument` - Dark grey
- `woodland_grey` - Medium grey
- `surfmist` - Light grey
- `manor_red` - Dark red
- `cottage_green` - Dark green
- `deep_ocean` - Dark blue
- `paperbark` - Beige
- `classic_cream` - Cream

## Custom Dimensions

Let's create a carport with specific dimensions for a real use case:

```python
# Double carport with specific requirements
double_carport = {
    "width": 7200,     # 7.2m - fits 2 cars side by side
    "length": 6000,    # 6m - standard car length + buffer
    "height": 2400,    # 2.4m - minimum for most vehicles
    "roof_type": "skillion",  # Single slope roof
    "roof_pitch": 5,          # 5-degree slope for drainage
    "bay_spacing": 3000,      # 3m between support posts
    "end_bay_type": "open",   # Open ends (no walls)
    "materials": {
        "frame": {
            "type": "steel",
            "post_size": "100x100x3.0",  # 100mm square posts
            "rafter_size": "C15015"       # C-section rafters
        },
        "cladding": {
            "type": "colorbond",
            "profile": "trimdek",
            "color": "surfmist"
        }
    },
    "engineering": {
        "wind_region": "B",        # Medium wind area
        "terrain_category": 2.5,   # Suburban
        "importance_level": 2      # Normal importance
    }
}

response = requests.post(
    f"{BASE_URL}/api/carport/generate",
    json=double_carport
)
```

### Dimension Guidelines

**Width:**
- Single carport: 3000-4000mm
- Double carport: 6000-7500mm
- Triple carport: 9000-11000mm

**Length:**
- Standard car: 5000-6000mm
- Large car/4WD: 6000-7000mm
- With storage: 7000-9000mm

**Height:**
- Minimum clearance: 2100mm
- Standard cars: 2400mm
- High vehicles: 2700-3000mm
- Caravans/boats: 3000-3600mm

## Adding Openings

For enclosed carports or sheds, you can add doors and windows:

```python
# Partially enclosed carport
enclosed_carport = {
    "width": 6000,
    "length": 9000,
    "height": 2700,
    "roof_type": "gable",
    "roof_pitch": 20,
    "walls": {
        "left": "cladded",    # Left wall enclosed
        "right": "open",      # Right wall open
        "front": "open",      # Front open for access
        "back": "cladded"     # Back wall enclosed
    },
    "openings": [
        {
            "type": "window",
            "wall": "left",
            "width": 1200,
            "height": 900,
            "position": {
                "x": 3000,    # 3m from front
                "z": 1200     # 1.2m from ground
            }
        },
        {
            "type": "pa_door",    # Personnel access door
            "wall": "back",
            "width": 900,
            "height": 2100,
            "position": {
                "x": 1000,    # 1m from left edge
                "z": 0        # Ground level
            }
        }
    ],
    "materials": {
        "frame": "steel",
        "walls": {
            "cladding": "colorbond",
            "color": "woodland_grey"
        },
        "roof": {
            "cladding": "colorbond",
            "color": "surfmist"
        }
    }
}
```

## Complex Shed Example

Now let's build a complete shed with all features:

```python
# Workshop shed with mezzanine
workshop_shed = {
    # Basic dimensions
    "width": 12000,     # 12m wide
    "length": 18000,    # 18m long
    "height": 4200,     # 4.2m to eave
    "wall_height": 3600,  # 3.6m walls
    
    # Roof configuration
    "roof_type": "gable",
    "roof_pitch": 15,
    "ridge_direction": "lengthwise",
    
    # Frame details
    "bay_spacing": 3000,  # 3m bays
    "frame_type": "portal",
    "column_size": "250UB31",
    "rafter_size": "250UB31",
    
    # Walls
    "walls": {
        "all": "cladded"
    },
    
    # Openings
    "openings": [
        {
            "type": "roller_door",
            "wall": "front",
            "width": 4000,
            "height": 4000,
            "position": {"x": 4000, "z": 0},
            "motor": True
        },
        {
            "type": "roller_door",
            "wall": "front",
            "width": 3000,
            "height": 3000,
            "position": {"x": 9000, "z": 0}
        },
        {
            "type": "pa_door",
            "wall": "left",
            "width": 900,
            "height": 2100,
            "position": {"x": 3000, "z": 0}
        },
        {
            "type": "window",
            "wall": "left",
            "width": 1800,
            "height": 1200,
            "position": {"x": 6000, "z": 1200},
            "glazing": "clear",
            "operation": "sliding"
        },
        {
            "type": "window",
            "wall": "right",
            "width": 1800,
            "height": 1200,
            "position": {"x": 6000, "z": 1200}
        },
        {
            "type": "whirlybird",
            "position": {"x": 6000, "y": 9000},
            "diameter": 300,
            "color": "colorbond_match"
        }
    ],
    
    # Mezzanine floor
    "mezzanine": {
        "enabled": True,
        "area": 72,  # 72 m²
        "position": {
            "x": 0,      # Start from left wall
            "y": 12000   # Start 12m from front
        },
        "width": 12000,  # Full width
        "depth": 6000,   # 6m deep
        "height": 2400,  # Floor level at 2.4m
        "load_capacity": 3.0,  # 3.0 kPa
        "access": "internal_stair",
        "handrails": True
    },
    
    # Materials
    "materials": {
        "frame": {
            "type": "steel",
            "finish": "primer_painted",
            "color": "grey"
        },
        "roof": {
            "cladding": "colorbond",
            "profile": "trimdek",
            "color": "surfmist",
            "insulation": {
                "type": "blanket",
                "r_value": 1.5
            }
        },
        "walls": {
            "cladding": "colorbond",
            "profile": "trimdek",
            "color": "monument"
        },
        "gutters": {
            "type": "colorbond",
            "profile": "quad",
            "color": "monument"
        }
    },
    
    # Accessories
    "accessories": {
        "gutters": True,
        "downpipes": 6,  # Number of downpipes
        "ridge_capping": True,
        "barge_capping": True,
        "corner_flashing": True,
        "concrete_slab": {
            "thickness": 100,
            "mpa": 25,
            "reinforcement": "SL82"
        }
    },
    
    # Engineering
    "engineering": {
        "wind_region": "C",
        "terrain_category": 2,
        "importance_level": 2,
        "snow_load": False
    }
}

# Generate the workshop
response = requests.post(
    f"{BASE_URL}/api/shed/generate",
    json=workshop_shed
)

if response.status_code == 200:
    result = response.json()
    print("Workshop shed created!")
    print(f"Model ID: {result['model_id']}")
    print(f"Generation time: {result['metadata']['generation_time']}s")
    
    # Download all outputs
    model_id = result['model_id']
    
    # IFC file
    ifc = requests.get(f"{BASE_URL}/api/models/{model_id}/download/ifc")
    with open("workshop.ifc", "wb") as f:
        f.write(ifc.content)
    
    # GLB file
    glb = requests.get(f"{BASE_URL}/api/models/{model_id}/download/glb")
    with open("workshop.glb", "wb") as f:
        f.write(glb.content)
    
    # DXF file
    dxf = requests.get(f"{BASE_URL}/api/models/{model_id}/download/dxf")
    with open("workshop.dxf", "wb") as f:
        f.write(dxf.content)
    
    print("Files downloaded: workshop.ifc, workshop.glb, workshop.dxf")
```

## Viewing Your Model

### GLB Files (3D Models)

**Windows:**
- Double-click the .glb file to open in 3D Viewer
- Or drag into Paint 3D

**Online Viewers:**
- https://gltf-viewer.donmccurdy.com/
- https://sandbox.babylonjs.com/
- https://threejs.org/editor/

**In Your Application:**
```html
<!-- Using model-viewer web component -->
<script type="module" src="https://unpkg.com/@google/model-viewer/dist/model-viewer.min.js"></script>

<model-viewer 
    src="workshop.glb" 
    alt="Workshop 3D Model"
    auto-rotate 
    camera-controls
    style="width: 100%; height: 500px;">
</model-viewer>
```

### IFC Files (BIM Models)

**Free IFC Viewers:**
- BIM Vision (Windows)
- IFC++
- FreeCAD
- Blender with IFC plugin

**Web-based:**
- IFC.js viewer
- BIMserver.org

## Troubleshooting

### Common Issues

**1. Model Generation Fails**
```python
# Check validation first
validation = requests.post(
    f"{BASE_URL}/api/validate/dimensions",
    json=your_config
)
print(validation.json())
```

**2. Missing Materials**
```python
# Get available materials
materials = requests.get(f"{BASE_URL}/api/materials/available")
print(json.dumps(materials.json(), indent=2))
```

**3. Opening Placement Issues**
```python
# Ensure opening fits within wall
wall_length = 18000  # 18m wall
door_width = 3000    # 3m door
door_position = 7500 # 7.5m from origin

# Check: position + width/2 < wall_length
if door_position + door_width/2 > wall_length:
    print("Door extends beyond wall!")
```

**4. Performance Issues**
```python
# For large models, use async generation
response = requests.post(
    f"{BASE_URL}/api/shed/generate",
    json=complex_shed,
    params={"async": True}
)

job_id = response.json()["job_id"]

# Poll for completion
while True:
    status = requests.get(f"{BASE_URL}/api/jobs/{job_id}")
    if status.json()["status"] == "completed":
        break
    time.sleep(2)
```

### Validation Rules

**Dimensions:**
- Width: 3000mm - 30000mm
- Length: 3000mm - 60000mm
- Height: 2100mm - 8000mm
- Wall height ≥ 0.8 × eave height

**Openings:**
- Doors cannot overlap
- Must fit within wall dimensions
- Minimum 300mm from corners
- Roller doors max 5000mm wide

**Mezzanine:**
- Minimum headroom: 2100mm
- Maximum 50% of floor area
- Requires minimum 3600mm building height

## Next Steps

Now that you've built your first models:

1. Explore [Working with Geometry](04_Working_With_Geometry.md) to understand coordinates
2. Learn about [Understanding Materials](05_Understanding_Materials.md) for custom finishes
3. Study [Creating BIM Components](06_Creating_BIM_Components.md) for advanced features
4. See [Extending Building Types](08_Extending_Building_Types.md) to add new structures

## Summary

You've learned to:
- Create simple carport models
- Add materials and colors
- Configure custom dimensions
- Add doors and windows
- Build complex sheds with mezzanines
- Download and view generated files
- Troubleshoot common issues

The PyModel API makes it easy to generate building models programmatically. Start simple and gradually add complexity as needed!