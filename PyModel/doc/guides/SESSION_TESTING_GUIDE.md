# Session-by-Session Testing Guide

## Overview

This guide provides step-by-step testing procedures for each component of the BIM Backend system. Each session focuses on a specific layer or functionality.

## Prerequisites

```bash
# 1. Create virtual environment
python3 -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# 2. Install dependencies
pip install -r requirements.txt

# 3. Install in development mode
pip install -e .
```

## Testing Sessions

### Session 1: Geometry Testing (30 mins)

#### Objective
Verify all geometric operations work correctly.

#### Test Files
- `tests/geometry/test_vec2.py`
- `tests/geometry/test_vec3.py`
- `tests/geometry/test_mat4.py`
- `tests/geometry/test_lines.py`

#### Commands
```bash
# Run all geometry tests
pytest tests/geometry/ -v

# Run with coverage
pytest tests/geometry/ -v --cov=src/geometry

# Run specific test
pytest tests/geometry/test_vec2.py::TestVec2::test_length -v
```

#### Manual Testing Script
```python
# test_geometry_manual.py
from geometry.primitives import Vec2, Vec3
from geometry.matrix import Mat4
from geometry.helpers import Geo

# Test Vec2 operations
v1 = Vec2(3.0, 4.0)
print(f"Vec2 length: {v1.length()}")  # Should be 5.0
print(f"Vec2 normalized: {v1.normalized()}")

# Test Vec3 operations
v3d = Vec3(1.0, 2.0, 3.0)
cross = Vec3.cross(Vec3.unit_x(), Vec3.unit_y())
print(f"Cross product: {cross}")  # Should be (0, 0, 1)

# Test matrix operations
rot = Mat4.create_rotation_z(Geo.DEG45)
trans = Mat4.create_translation(10, 20, 30)
combined = trans * rot
print(f"Combined transform: {combined}")
```

#### Expected Results
- All vector operations return correct mathematical results
- Matrix multiplication follows proper order
- No floating-point precision errors beyond 1e-6

### Session 2: Materials Testing (20 mins)

#### Objective
Verify material definitions and validation.

#### Test Files
- `tests/materials/test_frame_material.py`
- `tests/materials/test_color_material.py`
- `tests/materials/test_material_library.py`

#### Commands
```bash
# Run materials tests
pytest tests/materials/ -v

# Test material library
python -c "from materials.helpers import MaterialLibrary; lib = MaterialLibrary.get_default(); print(f'Frame materials: {len(lib.get_frame_materials())}')"
```

#### Manual Testing Script
```python
# test_materials_manual.py
from materials.base import FrameMaterial, FrameMaterialType
from materials.visual import ColorMaterial
from materials.helpers import MaterialLibrary

# Test frame material
frame = FrameMaterial(
    name="C15024",
    material_type=FrameMaterialType.C,
    width=150.0,
    height=24.0,
    thickness=1.5
)
print(f"Frame web: {frame.web}, flange: {frame.flange}")

# Test color material
color = ColorMaterial(
    name="Monument",
    rgb_hex="#5A5956",
    finish="Colorbond"
)
print(f"Color RGB: ({color.r}, {color.g}, {color.b})")

# Test material library
lib = MaterialLibrary.get_default()
print(f"Available colors: {[c.name for c in lib.get_color_materials()][:5]}")
```

#### Expected Results
- Materials validate their properties correctly
- RGB conversion from hex works properly
- Material library contains default materials

### Session 3: BIM Model Testing (30 mins)

#### Objective
Verify BIM data structure creation and relationships.

#### Test Files
- `tests/bim/test_shed_bim.py`
- `tests/bim/test_components.py`

#### Commands
```bash
# Run BIM tests
pytest tests/bim/ -v

# Run integration test
pytest tests/integration/test_bim_structure.py -v
```

#### Manual Testing Script
```python
# test_bim_manual.py
from bim.shed_bim import ShedBim, ShedBimPartMain
from bim.wall_roof import ShedBimSide, ShedBimRoof
from bim.components import ShedBimColumn, ShedBimRafter
from geometry.primitives import Vec3

# Create basic structure
main = ShedBimPartMain(
    roof_type="Flat",
    roof_left=ShedBimRoof(
        cladding_material="Colorbond Monument",
        cladding_reversed=False
    ),
    side_left=ShedBimSide(),
    side_right=ShedBimSide()
)

# Add columns
for x in [0, 3000, 6000]:
    column = ShedBimColumn(
        base=Vec3(x, 0, 0),
        top=Vec3(x, 0, 2400),
        frame_material="C15024"
    )
    main.side_left.columns.append(column)

# Create full BIM
shed = ShedBim(main=main)

print(f"Created shed with {len(main.side_left.columns)} columns")
print(f"Roof type: {main.roof_type}")
print(f"Sides: {len(main.get_sides())}")
```

#### Expected Results
- BIM structure creates without errors
- Component relationships maintained
- Helper methods return correct counts

### Session 4: Business Logic Testing (45 mins)

#### Objective
Test building input validation and carport generation.

#### Test Files
- `tests/business/test_building_input.py`
- `tests/business/test_carport_builder.py`
- `tests/business/test_engineering.py`

#### Commands
```bash
# Run business logic tests
pytest tests/business/ -v

# Run with specific markers
pytest tests/business/ -v -m "not slow"
```

#### Manual Testing Script
```python
# test_business_manual.py
from business.building_input import BuildingInput, BuildingType, CarportRoofType
from business.structure_builder import CarportBuilder
from business.engineering import MockEngineeringService
import asyncio

# Test building input
building_input = BuildingInput(
    building_type=BuildingType.CARPORT,
    name="Test Carport",
    roof_type=CarportRoofType.FLAT,
    bays=2,
    span=6000.0,
    length=6000.0,
    height=2400.0,
    wind_speed=32,
    slab=True,
    slab_thickness=100.0,
    soil="SAND"
)

# Test overhang validation
building_input.overhang = 1000.0  # Should be clamped
print(f"Overhang after validation: {building_input.overhang}")

# Test engineering service
async def test_engineering():
    service = MockEngineeringService()
    eng_data = await service.validate_design(building_input)
    print(f"Engineering rafter: {eng_data.ENG_RAFTER}")
    return eng_data

# Run async test
eng_data = asyncio.run(test_engineering())

# Test carport builder
carport = CarportBuilder.create_carport(building_input, eng_data)
print(f"Generated carport with main structure: {carport.main is not None}")
```

#### Expected Results
- Building input validates constraints
- Engineering service returns appropriate data
- CarportBuilder creates valid structure

### Session 5: API Testing (45 mins)

#### Objective
Test API endpoints and encryption.

#### Test Files
- `tests/api/test_endpoints.py`
- `tests/api/test_models.py`
- `tests/services/test_encryption.py`

#### Commands
```bash
# Start API server
python run_api.py --reload

# In another terminal, run tests
python test_api.py

# Test with curl
curl http://localhost:8000/api/health
```

#### Manual Testing Script
```python
# test_api_manual.py
import requests
import json
from services.encryption import AESEncryption

# Test encryption
aes = AESEncryption("test-key")
test_data = {"building_type": "CARPORT", "span": 6000.0}
encrypted = aes.encrypt_json(test_data)
decrypted = aes.decrypt_json(encrypted)
print(f"Encryption works: {test_data == decrypted}")

# Test API health
response = requests.get("http://localhost:8000/api/health")
print(f"Health check: {response.json()}")

# Test sample request
response = requests.get("http://localhost:8000/api/carport/sample-request")
sample = response.json()
print(f"Sample request received: {sample['description']}")

# Test carport creation
request_data = {"encrypted_data": sample["encrypted_request"]["encrypted_data"]}
response = requests.post(
    "http://localhost:8000/api/carport/create",
    json=request_data
)
print(f"Carport creation: {response.json()}")
```

#### Expected Results
- Encryption/decryption works correctly
- API endpoints respond with correct status codes
- File generation and download works

### Session 6: Integration Testing (60 mins)

#### Objective
Test full system integration from input to output.

#### Commands
```bash
# Run integration tests
python test_integration_fixed.py

# Run full test suite
pytest -v

# Run with all markers
pytest -v -m "integration"
```

#### Manual Integration Test
```python
# test_integration_manual.py
from business.building_input import BuildingInput, BuildingType, CarportRoofType
from business.structure_builder import CarportBuilder
from services.encryption import AESEncryption
from api.endpoints import create_carport
import json

# Full pipeline test
def test_full_pipeline():
    # 1. Create input
    building_input = BuildingInput(
        building_type=BuildingType.CARPORT,
        name="Integration Test",
        roof_type=CarportRoofType.GABLE,
        bays=3,
        span=9000.0,
        length=9000.0,
        height=3000.0,
        wind_speed=41,
        pitch=15.0,
        slab=True,
        slab_thickness=120.0,
        soil="ROCK"
    )
    
    # 2. Generate structure
    carport = CarportBuilder.create_carport(building_input)
    
    # 3. Verify structure
    print(f"Main structure: {carport.main.roof_type}")
    print(f"Columns: {len(carport.main.side_left.columns)}")
    print(f"Rafters: {len(carport.main.roof_left.rafters) if carport.main.roof_left else 0}")
    
    # 4. Test encryption flow
    aes = AESEncryption("integration-test-key")
    input_dict = building_input.__dict__
    encrypted = aes.encrypt_json(input_dict)
    
    print(f"\nEncrypted data length: {len(encrypted)}")
    print("Full pipeline test completed successfully!")

test_full_pipeline()
```

### Session 7: Performance Testing (30 mins)

#### Objective
Measure performance and identify bottlenecks.

#### Commands
```bash
# Run performance benchmarks
pytest tests/performance/ -v --benchmark-only

# Profile specific function
python -m cProfile -s cumulative test_performance.py
```

#### Performance Test Script
```python
# test_performance.py
import time
from business.building_input import BuildingInput, BuildingType, CarportRoofType
from business.structure_builder import CarportBuilder

def benchmark_carport_generation():
    """Benchmark carport generation time"""
    times = []
    
    for i in range(10):
        start = time.time()
        
        building_input = BuildingInput(
            building_type=BuildingType.CARPORT,
            span=6000.0,
            length=6000.0,
            height=2400.0
        )
        
        carport = CarportBuilder.create_carport(building_input)
        
        end = time.time()
        times.append(end - start)
    
    avg_time = sum(times) / len(times)
    print(f"Average generation time: {avg_time:.3f} seconds")
    print(f"Min: {min(times):.3f}s, Max: {max(times):.3f}s")

benchmark_carport_generation()
```

### Session 8: Error Handling Testing (30 mins)

#### Objective
Verify error handling and edge cases.

#### Test Scenarios
```python
# test_error_handling.py
from business.building_input import BuildingInput, BuildingType
import pytest

def test_invalid_dimensions():
    """Test dimension validation"""
    # Test negative span
    with pytest.raises(ValueError):
        BuildingInput(span=-1000)
    
    # Test excessive span
    with pytest.raises(ValueError):
        BuildingInput(span=20000)
    
    # Test invalid roof type for overhang
    input = BuildingInput(roof_type="GABLE")
    input.overhang = 600  # Should be 0 for gable
    assert input.overhang == 0

def test_api_error_responses():
    """Test API error handling"""
    import requests
    
    # Test invalid encryption
    response = requests.post(
        "http://localhost:8000/api/carport/create",
        json={"encrypted_data": "invalid_base64!!!"}
    )
    assert response.status_code == 400
    assert "error" in response.json()
    
    # Test missing file
    response = requests.get(
        "http://localhost:8000/api/download/nonexistent"
    )
    assert response.status_code == 404
```

## Test Data Sets

### Minimal Carport
```json
{
    "building_type": "CARPORT",
    "roof_type": "FLAT",
    "span": 3000.0,
    "length": 3000.0,
    "height": 2100.0,
    "bays": 1
}
```

### Standard Carport
```json
{
    "building_type": "CARPORT",
    "roof_type": "FLAT",
    "span": 6000.0,
    "length": 6000.0,
    "height": 2400.0,
    "bays": 2,
    "wind_speed": 32,
    "slab": true,
    "slab_thickness": 100.0
}
```

### Complex Shed
```json
{
    "building_type": "SHED",
    "roof_type": "GABLE",
    "span": 12000.0,
    "length": 18000.0,
    "height": 4000.0,
    "bays": 6,
    "pitch": 22.5,
    "validate_engineering": true
}
```

## Continuous Testing

### Watch Mode
```bash
# Auto-run tests on file change
pytest-watch tests/ -v
```

### Coverage Report
```bash
# Generate HTML coverage report
pytest --cov=src --cov-report=html
open htmlcov/index.html
```

### Type Checking
```bash
# Run mypy type checker
mypy src/ --strict
```

### Code Quality
```bash
# Check code style
black src/ tests/ --check
isort src/ tests/ --check
flake8 src/ tests/
```

## Test Results Documentation

### Test Report Template
```markdown
## Test Session Report

**Date**: YYYY-MM-DD
**Tester**: Name
**Session**: Session Name

### Environment
- Python Version: 3.10.x
- OS: Windows/Linux/macOS
- Dependencies: Installed/Missing

### Tests Run
- [ ] Unit Tests
- [ ] Integration Tests
- [ ] API Tests
- [ ] Performance Tests

### Results
- Total Tests: XX
- Passed: XX
- Failed: XX
- Skipped: XX

### Issues Found
1. Issue description
   - Severity: High/Medium/Low
   - Steps to reproduce
   - Expected vs Actual

### Performance Metrics
- Average response time: XXms
- Memory usage: XXMB
- CPU usage: XX%

### Recommendations
- Action items
- Improvements needed
```

This comprehensive testing guide ensures thorough validation of all system components.