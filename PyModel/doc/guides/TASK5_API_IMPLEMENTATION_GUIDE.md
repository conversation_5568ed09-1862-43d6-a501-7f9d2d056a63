# Task 5: API Layer Implementation Guide

## Overview

Task 5 implements the API layer for the BIM Backend system using FastAPI. This provides a REST API interface for creating carports and sheds with encrypted communication and engineering validation support.

## Implementation Status ✅

### Completed Components

1. **API Structure** ✅
   - FastAPI application setup (`src/api/main.py`)
   - Modular endpoint organization (`src/api/endpoints.py`)
   - Pydantic models for requests/responses (`src/api/models.py`)

2. **Encryption Service** ✅
   - AES-256-CBC encryption implementation (`src/services/encryption.py`)
   - Compatible with .NET implementation
   - Base64 encoding for transport

3. **API Endpoints** ✅
   - `/api/health` - Health check endpoint
   - `/api/carport/create` - Create carport with encrypted payload
   - `/api/download/{file_id}` - Download generated files
   - `/api/carport/sample-request` - Get sample encrypted request

4. **Error Handling** ✅
   - Global exception handler
   - Structured error responses
   - HTTP status code mapping

5. **File Management** ✅
   - Temporary file storage
   - Automatic cleanup with retention policy
   - Secure file download endpoints

## Architecture

```
src/
├── api/
│   ├── __init__.py      # API module exports
│   ├── main.py          # FastAPI application setup
│   ├── models.py        # Request/Response models
│   └── endpoints.py     # API endpoint implementations
├── services/
│   ├── __init__.py      # Services module exports
│   └── encryption.py    # AES encryption service
```

## Key Features

### 1. Encrypted Communication

All carport creation requests use AES-256-CBC encryption:

```python
# Client encrypts the request
aes = AESEncryption("your-secret-key")
encrypted_data = aes.encrypt(json.dumps(building_input))

# Send encrypted request
response = requests.post("/api/carport/create", json={
    "encrypted_data": encrypted_data
})
```

### 2. Engineering Validation

Optional engineering validation through external service:

```python
if building_input.validate_engineering:
    async with EngineeringService(...) as eng_service:
        eng_data = await eng_service.validate_design(building_input)
```

### 3. Async Operations

Full async/await support for I/O operations:
- External API calls
- File operations
- Database queries (future)

### 4. CORS Support

Configurable CORS for frontend integration:

```python
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"]
)
```

## API Endpoints

### POST /api/carport/create

Create a new carport design.

**Request:**
```json
{
    "encrypted_data": "base64_encoded_aes_encrypted_json"
}
```

**Decrypted Payload Structure:**
```json
{
    "building_type": "CARPORT",
    "name": "My Carport",
    "roof_type": "FLAT",
    "validate_engineering": false,
    "bays": 2,
    "span": 6000.0,
    "length": 6000.0,
    "height": 2400.0,
    "wind_speed": 32,
    "pitch": 0.0,
    "slab": true,
    "slab_thickness": 100.0,
    "soil": "SAND",
    "overhang": 600.0
}
```

**Response:**
```json
{
    "success": true,
    "status": "success",
    "message": "Carport generated successfully",
    "file_url": "/api/download/abc123",
    "error_details": null
}
```

### GET /api/health

Check API health status.

**Response:**
```json
{
    "status": "healthy",
    "version": "1.0.0",
    "timestamp": "2024-01-15T10:30:00Z"
}
```

### GET /api/download/{file_id}

Download a generated file.

**Response:** Binary file data with appropriate content-type.

### GET /api/carport/sample-request

Get a sample encrypted request for testing.

**Response:**
```json
{
    "description": "Sample carport creation request",
    "decrypted_payload": { ... },
    "encrypted_request": {
        "encrypted_data": "..."
    }
}
```

## Running the API

### Development Mode

```bash
# Install dependencies
pip install -r requirements.txt

# Run with auto-reload
python run_api.py --reload

# Or with custom settings
python run_api.py --host 0.0.0.0 --port 8080 --reload
```

### Production Mode

```bash
# Run with multiple workers
python run_api.py --workers 4

# Or use uvicorn directly
uvicorn api.main:app --host 0.0.0.0 --port 8000 --workers 4
```

### Environment Variables

```bash
# API Configuration
export API_ENCRYPTION_KEY="your-secret-encryption-key"
export ENGINEERING_API_URL="https://engineering-api.example.com"
export ENGINEERING_API_KEY="your-engineering-api-key"
export CORS_ORIGINS="https://frontend.example.com,https://app.example.com"
export PORT=8000
```

## Testing

### Run API Tests

```bash
# Start the API server
python run_api.py

# In another terminal, run tests
python test_api.py
```

### Manual Testing with curl

```bash
# Health check
curl http://localhost:8000/api/health

# Get sample request
curl http://localhost:8000/api/carport/sample-request

# Create carport (use encrypted_data from sample request)
curl -X POST http://localhost:8000/api/carport/create \
  -H "Content-Type: application/json" \
  -d '{"encrypted_data": "..."}'
```

## Security Considerations

1. **Encryption Key Management**
   - Never commit encryption keys to version control
   - Use environment variables or secret management services
   - Rotate keys regularly

2. **API Authentication**
   - Current implementation uses encrypted payloads
   - Consider adding JWT or OAuth2 for additional security

3. **Input Validation**
   - All inputs validated through Pydantic models
   - Engineering validation for structural safety

4. **File Security**
   - Temporary files cleaned up automatically
   - File IDs are UUIDs to prevent enumeration
   - Consider adding user authentication for downloads

## Integration with Other Components

### Business Logic Layer
- Uses `CarportBuilder` from `src/business/structure_builder.py`
- Validates input through `BuildingInput` model

### Engineering Service
- Async integration with external validation API
- Fallback to mock service for development

### Output Generation
- Currently generates JSON representation
- Ready for GLTF/DXF/IFC generators when implemented

## Next Steps

1. **Authentication & Authorization**
   - Implement JWT-based authentication
   - Add role-based access control

2. **Database Integration**
   - Store generated models
   - Track user projects
   - Audit logging

3. **Output Formats**
   - Integrate GLTF generator
   - Add DXF export
   - Implement IFC support

4. **Performance Optimization**
   - Add Redis caching
   - Implement request queuing
   - Background job processing

5. **Monitoring & Logging**
   - Structured logging
   - APM integration
   - Health metrics

## Troubleshooting

### Common Issues

1. **Import Errors**
   ```bash
   # Ensure you're in the project root
   cd /path/to/PyModel
   
   # Install in development mode
   pip install -e .
   ```

2. **Encryption Failures**
   - Check encryption key is set correctly
   - Verify base64 encoding
   - Ensure padding is correct

3. **Engineering Service Timeout**
   - Check ENGINEERING_API_URL is accessible
   - Verify API key is valid
   - Increase timeout if needed

## C# to Python Mapping

| C# Component | Python Implementation | Notes |
|--------------|----------------------|-------|
| CarportController.cs | endpoints.py | FastAPI router |
| AESEncryption.cs | encryption.py | cryptography library |
| CarportRequest.cs | models.py | Pydantic models |
| Startup.cs | main.py | FastAPI app setup |
| IHttpClientFactory | httpx.AsyncClient | Async HTTP client |

## Performance Considerations

1. **Async Operations**
   - All I/O operations are async
   - Use connection pooling for external APIs
   - Consider background tasks for heavy processing

2. **File Handling**
   - Stream large files instead of loading to memory
   - Implement chunked uploads/downloads
   - Use CDN for static file delivery

3. **Caching**
   - Cache engineering validation results
   - Cache generated models for identical inputs
   - Use Redis for distributed caching

This completes the Task 5 API Layer implementation with a fully functional REST API for the BIM Backend system.