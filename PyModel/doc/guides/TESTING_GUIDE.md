# BIM Backend Python Testing Guide

## Overview

This guide provides comprehensive testing instructions for the Python port of the BIM Backend system, ensuring alignment with the original C# test suite.

## C# Test Suite Reference

The original C# test suite is located in:
- `/BimBackend-master/Shedkit.Tests/` - Core geometry tests
- `/BimBackend-master/Shedkit.TestRunner.Tests/` - Integration tests
- `/BimBackend-master/BimCoreLibrary.Tests/` - Business logic tests

### Key C# Test Files

1. **GeoTests.cs** (Shedkit.Tests)
   - Tests for `GetBaySizes()` method
   - Plane-line intersection tests
   - Geometric operation validations

2. **Mat4_*_Tests.cs** (Shedkit.Tests/FsCheckTests)
   - Mat4_NetDxf_Tests.cs - DXF compatibility tests
   - Mat4_Numerics_Tests.cs - Numerical accuracy tests
   - Mat4_Spec_Tests.cs - Specification compliance tests

3. **ShedBimWalkerTests.cs** (Shedkit.Tests)
   - Tree traversal and data structure tests

## Python Test Structure

```
tests/
├── TESTING_GUIDE.md          # This file
├── geometry/
│   ├── test_vec2.py         # Aligns with Vec2 tests from Geo.cs
│   ├── test_vec3.py         # Aligns with Vec3 tests from Geo.cs
│   ├── test_mat4.py         # Aligns with Mat4_*_Tests.cs
│   ├── test_lines.py        # Line1/2/3 tests
│   ├── test_boxes.py        # Box2/3 tests
│   ├── test_plane.py        # Plane3 tests
│   ├── test_basis.py        # Basis3 tests
│   └── test_helpers.py      # Geo helper function tests
└── integration/
    └── test_geometric_ops.py # Integration tests
```

## Test Alignment with C#

### 1. GetBaySizes Tests (GeoTests.cs lines 15-32)

The C# test uses DataTestMethod with specific test cases:
```csharp
[DataRow(6000, 1, 1, new[] { 6000 })]
[DataRow(6000, 2, 1, new[] { 3000, 3000 })]
[DataRow(10000, 3, 1, new[] { 3334, 3333, 3333 })]
[DataRow(10000, 3, 10, new[] { 3340, 3330, 3330 })]
[DataRow(10000, 3, 100, new[] { 3400, 3300, 3300 })]
[DataRow(10000, 3, 1000, new[] { 4000, 3000, 3000 })]
```

Python equivalent in `test_helpers.py`:
```python
class TestGetBaySizes:
    @pytest.mark.parametrize("length,num_bays,precision,expected", [
        (6000, 1, 1, [6000]),
        (6000, 2, 1, [3000, 3000]),
        (10000, 3, 1, [3334, 3333, 3333]),
        (10000, 3, 10, [3340, 3330, 3330]),
        (10000, 3, 100, [3400, 3300, 3300]),
        (10000, 3, 1000, [4000, 3000, 3000]),
    ])
    def test_get_bay_sizes(self, length, num_bays, precision, expected):
        result = Geo.get_bay_sizes(length, num_bays, precision)
        assert result == expected
```

### 2. Plane Intersection Tests (GeoTests.cs lines 34-62)

C# tests both finite and infinite line-plane intersections:
```csharp
[TestMethod]
public void TestInters_LineSegment_Plane_Finite_NoIntersection()
{
    var plane = new Plane3(0, 0, 1, 0);
    var line = Ln3(0, 0, 1, 0, 0, 2);
    var expected = default(Vec3?);
    var actual = Plane3.Inters(line, plane, infinite: false);
    Assert.AreEqual(expected, actual);
}
```

Python equivalent:
```python
class TestPlaneIntersection:
    def test_line_segment_plane_finite_no_intersection(self):
        plane = Plane3(0, 0, 1, 0)
        line = Line3(Vec3(0, 0, 1), Vec3(0, 0, 2))
        result = Plane3.inters(line, plane, infinite=False)
        assert result is None
    
    def test_line_segment_plane_infinite_intersection(self):
        plane = Plane3(0, 0, 1, 0)
        line = Line3(Vec3(0, 0, 1), Vec3(0, 0, 2))
        result = Plane3.inters(line, plane, infinite=True)
        assert result == Vec3.origin()
```

### 3. Matrix Tests (Mat4 tests)

C# uses property-based testing with FsCheck. Python should use hypothesis or manual property tests:

```python
class TestMat4Properties:
    def test_identity_multiplication(self):
        """M * I = M for any matrix M"""
        m = Mat4.create_rotation_z(math.pi / 4)
        result = m * Mat4.identity()
        assert_matrices_equal(result, m)
    
    def test_inverse_multiplication(self):
        """M * M^-1 = I for invertible matrix M"""
        m = Mat4.create_translation(1, 2, 3)
        inv = Mat4.get_inverse(m)
        result = m * inv
        assert_matrices_equal(result, Mat4.identity(), tolerance=1e-10)
```

## Testing Best Practices

### 1. Numerical Tolerance

C# uses exact equality for many tests. Python should use appropriate tolerances:

```python
def assert_vec3_equal(actual: Vec3, expected: Vec3, tolerance: float = 1e-10):
    assert abs(actual.x - expected.x) < tolerance
    assert abs(actual.y - expected.y) < tolerance
    assert abs(actual.z - expected.z) < tolerance
```

### 2. Edge Cases

Test the same edge cases as C#:
- Zero vectors
- Unit vectors
- Parallel/perpendicular vectors
- Degenerate geometries
- Boundary conditions

### 3. Performance Benchmarks

While C# uses BenchmarkDotNet, Python can use pytest-benchmark:

```python
def test_matrix_multiplication_performance(benchmark):
    m1 = Mat4.create_rotation_x(0.5)
    m2 = Mat4.create_translation(1, 2, 3)
    result = benchmark(lambda: m1 * m2)
    assert result is not None
```

## Running Tests

### Basic Test Run
```bash
python run_tests.py
```

### With Coverage
```bash
python run_tests.py --coverage
```

### Specific Test File
```bash
python run_tests.py -f tests/geometry/test_vec2.py
```

### Specific Test Pattern
```bash
python run_tests.py -k "test_intersection"
```

## Test Categories

### 1. Unit Tests
- Individual method functionality
- Edge case handling
- Error conditions

### 2. Integration Tests
- Complex geometric operations
- Multiple component interactions
- Real-world scenarios

### 3. Property Tests
- Mathematical properties (associativity, commutativity)
- Invariants (length preservation, orthogonality)
- Inverse operations

### 4. Regression Tests
- Specific bug fixes from C# codebase
- Known problematic inputs
- Historical edge cases

## C# to Python Test Conversion Checklist

When converting a C# test:

1. ✅ Identify test method and its purpose
2. ✅ Map C# assertions to Python assertions
3. ✅ Convert test data (accounting for type differences)
4. ✅ Add appropriate numerical tolerances
5. ✅ Include C# reference in docstring
6. ✅ Verify test passes with same inputs
7. ✅ Add edge cases if missing

## Example Test Conversion

C# Test:
```csharp
[TestMethod]
public void TestVec2Length()
{
    var v = new Vec2(3, 4);
    Assert.AreEqual(5.0, v.Length());
}
```

Python Test:
```python
def test_vec2_length():
    """Test Vec2 length calculation.
    
    C# Reference: GeoTests.TestVec2Length
    """
    v = Vec2(3, 4)
    assert v.length() == 5.0
```

## Continuous Integration

Ensure all tests pass before merging:
1. Unit tests: 100% pass rate
2. Code coverage: Minimum 90%
3. No linting errors
4. Type checking passes

## Future Testing Enhancements

1. **Hypothesis Integration**: Property-based testing for geometric operations
2. **Performance Benchmarks**: Track performance relative to C# implementation
3. **Visual Testing**: Generate test visualizations for complex geometric operations
4. **Mutation Testing**: Ensure test suite quality