# C# to Python Alignment Documentation

## Overview
This document provides a complete method-by-method alignment between the .NET Geo.cs/Mat4.cs and Python geometry module implementations. Each section includes:
- C# method signature
- Python equivalent location
- Implementation status
- Reference to specific line numbers

## Geo.cs Constants (Lines 13-21)
| C# Constant | Python Location | Status | Notes |
|-------------|-----------------|---------|--------|
| `Deg0 = 0` | `helpers.py:18` | ✅ | Renamed to DEG0 |
| `Deg45 = Math.PI / 4` | `helpers.py:19` | ✅ | Renamed to DEG45 |
| `Deg90 = Math.PI / 2` | `helpers.py:20` | ✅ | Renamed to DEG90 |
| `Deg180 = Math.PI` | `helpers.py:21` | ✅ | Renamed to DEG180 |
| `Deg270 = Math.PI + Math.PI / 2` | `helpers.py:22` | ✅ | Renamed to DEG270 |
| `Deg360 = Math.PI * 2` | `helpers.py:23` | ✅ | Renamed to DEG360 |
| `ToDeg = 180 / Math.PI` | `helpers.py:25` | ✅ | Renamed to TO_DEG |
| `ToRad = Math.PI / 180` | `helpers.py:26` | ✅ | Renamed to TO_RAD |

## Geo Static Methods (Lines 23-581)

### Angle Operations
| C# Method | Line | Python Location | Status |
|-----------|------|-----------------|---------|
| `NormalizeAngle(double angle)` | 23-36 | `helpers.py:29-41` | ✅ |
| `MirrorAngle(double angle)` | 53-56 | `helpers.py:44-50` | ✅ |

### Index Operations
| C# Method | Line | Python Location | Status |
|-----------|------|-----------------|---------|
| `WrapIndex<T>(ICollection<T> list, int index)` | 38-44 | `helpers.py:53-63` | ✅ |

### Trigonometry Helpers (SOH-CAH-TOA)
| C# Method | Line | Python Location | Status |
|-----------|------|-----------------|---------|
| `TrigSoh(double angle, double o)` | 46 | `helpers.py:66-69` | ✅ |
| `TrigSho(double angle, double h)` | 47 | `helpers.py:72-75` | ✅ |
| `TrigCah(double angle, double a)` | 48 | `helpers.py:78-81` | ✅ |
| `TrigCha(double angle, double h)` | 49 | `helpers.py:84-87` | ✅ |
| `TrigToa(double angle, double o)` | 50 | `helpers.py:90-93` | ✅ |
| `TrigTao(double angle, double a)` | 51 | `helpers.py:96-99` | ✅ |

### Mirror Operations
| C# Method | Line | Python Location | Status |
|-----------|------|-----------------|---------|
| `MirrorVecX(double baseX, Vec2 v)` | 58-61 | `helpers.py:102-105` | ✅ |

### Extents and Bounds
| C# Method | Line | Python Location | Status |
|-----------|------|-----------------|---------|
| `GetExtents(IEnumerable<Vec2> verts, double extend = 0.0)` | 63-84 | `helpers.py:108-133` | ✅ |

### Bay and Offset Calculations
| C# Method | Line | Python Location | Status |
|-----------|------|-----------------|---------|
| `GetOffsets(IEnumerable<double> bays, double startPos = 0.0, int dir = +1)` | 86-101 | `helpers.py:136-154` | ✅ |
| `GetBaySizes(double length, int numBays, double precision = 10)` | 103-146 | `helpers.py:157-215` | ✅ |

### Vector Creation Helpers
| C# Method | Line | Python Location | Status |
|-----------|------|-----------------|---------|
| `V2(double x, double y)` | 148-151 | `helpers.py:218-221` | ✅ |
| `V2xy(Vec3 a)` | 153-156 | `helpers.py:224-227` | ✅ |
| `V2xz(Vec3 a)` | 158-161 | `helpers.py:230-233` | ✅ |
| `V2yz(Vec3 a)` | 163-166 | `helpers.py:236-239` | ✅ |
| `V3(double x, double y, double z)` | 168-171 | `helpers.py:242-245` | ✅ |
| `Vx(double x)` | 173-176 | `helpers.py:248-251` | ✅ |
| `Vy(double y)` | 178-181 | `helpers.py:254-257` | ✅ |
| `Vz(double z)` | 183-186 | `helpers.py:260-263` | ✅ |

### Line Creation Helpers
| C# Method | Line | Python Location | Status |
|-----------|------|-----------------|---------|
| `Ln2xy(Line3 line)` | 188-191 | `helpers.py:266-269` | ✅ |
| `Ln2xz(Line3 line)` | 193-196 | `helpers.py:272-275` | ✅ |
| `Ln2yz(Line3 line)` | 198-201 | `helpers.py:278-281` | ✅ |
| `Ln1(double start, double end)` | 203-206 | `helpers.py:284-287` | ✅ |
| `Ln2(double startX, double startY, double endX, double endY)` | 208-215 | `helpers.py:290-293` | ✅ |
| `Ln2(Vec2 start, Vec2 end)` | 217-224 | `helpers.py:296-299` | ✅ |
| `Ln3(double x1, double y1, double z1, double x2, double y2, double z2)` | 226-229 | `helpers.py:302-305` | ✅ |
| `Ln3(Vec3 start, Vec3 end)` | 231-234 | `helpers.py:308-311` | ✅ |

### Polar and Rotation
| C# Method | Line | Python Location | Status |
|-----------|------|-----------------|---------|
| `Polar(double ang, double dist)` | 236-239 | `helpers.py:314-321` | ✅ |
| `Rotate(double ang, Vec2 v)` | 241-244 | `helpers.py:324-331` | ✅ |

### Midpoint Calculations
| C# Method | Line | Python Location | Status |
|-----------|------|-----------------|---------|
| `Mid(double a, double b)` | 246-249 | `helpers.py:334-337` | ✅ |
| `Mid(Vec2 a, Vec2 b)` | 251-254 | `helpers.py:340-343` | ✅ |
| `Mid(Vec3 a, Vec3 b)` | 256-259 | `helpers.py:346-349` | ✅ |
| `Mid(Line1 line)` | 261-264 | `helpers.py:352-355` | ✅ |
| `Mid(Line2 line)` | 266-269 | `helpers.py:358-361` | ✅ |
| `Mid(Line3 line)` | 271-274 | `helpers.py:364-367` | ✅ |

### Interpolation
| C# Method | Line | Python Location | Status |
|-----------|------|-----------------|---------|
| `Interpolate(Line1 line, double scale)` | 276-279 | `helpers.py:370-373` | ✅ |
| `Interpolate(Line2 line, double scale)` | 281-284 | `helpers.py:376-379` | ✅ |
| `Interpolate(Line3 line, double scale)` | 286-289 | `helpers.py:382-385` | ✅ |

### Rounding Operations
| C# Method | Line | Python Location | Status |
|-----------|------|-----------------|---------|
| `Round(Vec2 a, int decimals = 0)` | 291-294 | `helpers.py:388-391` | ✅ |
| `Round(Vec3 a, int decimals = 0)` | 296-299 | `helpers.py:394-397` | ✅ |
| `Round(Line2 a, int decimals = 0)` | 301-304 | `helpers.py:400-403` | ✅ |
| `Round(Line3 a, int decimals = 0)` | 306-309 | `helpers.py:406-409` | ✅ |

### Line Swap Operations
| C# Method | Line | Python Location | Status |
|-----------|------|-----------------|---------|
| `LnSwap(Line1 a)` | 311-314 | `helpers.py:412-415` | ✅ |
| `LnSwap(Line2 a)` | 316-319 | `helpers.py:418-421` | ✅ |
| `LnSwap(Line3 a)` | 321-324 | `helpers.py:424-427` | ✅ |

### Line Extension Operations
| C# Method | Line | Python Location | Status |
|-----------|------|-----------------|---------|
| `LnExtend(Line2 line, Line2 test)` | 326-344 | `helpers.py:430-453` | ✅ |
| `LnExtend(Vec2 start, Vec2 end, double startExtend, double endExtend)` | 346-352 | `helpers.py:456-465` | ✅ |
| `LnExtend(Vec3 start, Vec3 end, double startExtend, double endExtend)` | 354-360 | `helpers.py:468-477` | ✅ |
| `LnExtend(Line2 line, double startExtend, double endExtend)` | 362-365 | `helpers.py:480-483` | ✅ |
| `LnExtend(Line3 line, double startExtend, double endExtend)` | 367-370 | `helpers.py:486-489` | ✅ |

### Direction-Aware Line Offset
| C# Method | Line | Python Location | Status |
|-----------|------|-----------------|---------|
| `LnUp(Vec2 start, Vec2 end, double distance)` | 372-380 | `helpers.py:492-507` | ✅ |
| `LnUp(Line2 line, double distance)` | 382-385 | `helpers.py:510-513` | ✅ |
| `LnDown(Vec2 start, Vec2 end, double distance)` | 387-395 | `helpers.py:516-531` | ✅ |
| `LnDown(Line2 line, double distance)` | 397-400 | `helpers.py:534-537` | ✅ |

### Line Offset Operations
| C# Method | Line | Python Location | Status |
|-----------|------|-----------------|---------|
| `LnOffset(Vec2 start, Vec2 end, double distance)` | 402-407 | `helpers.py:540-549` | ✅ |
| `LnOffset(Line2 line, double distance)` | 409-412 | `helpers.py:552-555` | ✅ |
| `LnOffset(IList<Vec2> line, double distance)` | 414-433 | `helpers.py:558-585` | ✅ |

### Polygon Offset
| C# Method | Line | Python Location | Status |
|-----------|------|-----------------|---------|
| `PolyOffset(List<Vec2> poly, double distance)` | 435-459 | `helpers.py:588-620` | ✅ |

### Line Ordering
| C# Method | Line | Python Location | Status |
|-----------|------|-----------------|---------|
| `LnLowHigh(Line2 line)` | 461-480 | `helpers.py:623-644` | ✅ |
| `LnLeftRight(Line2 line)` | 488-501 | `helpers.py:647-668` | ✅ |

### Line Range
| C# Method | Line | Python Location | Status |
|-----------|------|-----------------|---------|
| `LnRange(double middle, double size)` | 503-506 | `helpers.py:671-678` | ✅ |

### Box-Line Intersection
| C# Method | Line | Python Location | Status |
|-----------|------|-----------------|---------|
| `BoxLineInters(Box2 box, Line2 line)` | 508-569 | `helpers.py:681-770` | ✅ |

### Point Distance
| C# Method | Line | Python Location | Status |
|-----------|------|-----------------|---------|
| `GetNearFar(Vec3 basePt, Vec3 pt1, Vec3 pt2)` | 571-580 | `helpers.py:773-785` | ✅ |

## Vec2 Structure (Lines 583-765)

### Properties and Constants
| C# Member | Line | Python Location | Status |
|-----------|------|-----------------|---------|
| `X, Y` properties | 586-587 | `primitives.py:17-18` | ✅ |
| `Length()` | 595 | `primitives.py:32-34` | ✅ |
| `LengthSquared()` | 596 | `primitives.py:36-38` | ✅ |
| `Angle()` | 597 | `primitives.py:40-42` | ✅ |
| `Origin` | 599 | `primitives.py:25` | ✅ |
| `UnitX` | 601 | `primitives.py:28` | ✅ |
| `UnitY` | 602 | `primitives.py:31` | ✅ |
| `MinValue` | 604 | `primitives.py:134` | ✅ |
| `MaxValue` | 605 | `primitives.py:137` | ✅ |

### Static Methods
| C# Method | Line | Python Location | Status |
|-----------|------|-----------------|---------|
| `Dot(Vec2 a, Vec2 b)` | 607-610 | `primitives.py:45-52` | ✅ |
| `Normal(Vec2 a)` | 612-615 | `primitives.py:55-64` | ✅ |
| `Inters(Vec2 a1, Vec2 a2, Vec2 b1, Vec2 b2, bool infinite)` | 617-641 | `primitives.py:67-98` | ✅ |
| `IntersMust(Vec2 a1, Vec2 a2, Vec2 b1, Vec2 b2, bool infinite)` | 643-651 | `primitives.py:101-116` | ✅ |
| `IntersList(Vec2 a1, Vec2 a2, List<Vec2> bx, bool infinite)` | 653-665 | ❌ Not implemented |
| `IntersListMust(Vec2 a1, Vec2 a2, List<Vec2> bx, bool infinite)` | 667-675 | ❌ Not implemented |
| `Min(Vec2 a, Vec2 b)` | 677-683 | `primitives.py:119-125` | ✅ |
| `Max(Vec2 a, Vec2 b)` | 685-691 | `primitives.py:128-131` | ✅ |

### Operators
| C# Operator | Line | Python Location | Status |
|-------------|------|-----------------|---------|
| `operator +(Vec2 a)` | 693-696 | `primitives.py:140` | ✅ |
| `operator +(Vec2 a, Vec2 b)` | 698-701 | `primitives.py:143-145` | ✅ |
| `operator -(Vec2 a)` | 703-706 | `primitives.py:148-150` | ✅ |
| `operator -(Vec2 a, Vec2 b)` | 708-711 | `primitives.py:153-155` | ✅ |
| `operator *(Vec2 a, double b)` | 713-716 | `primitives.py:158-160` | ✅ |
| `operator *(double a, Vec2 b)` | 718-721 | `primitives.py:163-165` | ✅ |
| `operator /(Vec2 a, double b)` | 723-726 | `primitives.py:168-170` | ✅ |
| `operator ==` | 756-759 | `primitives.py:179-181` | ✅ |
| `operator !=` | 761-764 | `primitives.py:184-186` | ✅ |

## Vec3 Structure (Lines 767-916)

### Properties and Constants
| C# Member | Line | Python Location | Status |
|-----------|------|-----------------|---------|
| `X, Y, Z` properties | 770-772 | `primitives.py:193-195` | ✅ |
| `DistanceSquared(Vec3 other)` | 780 | `primitives.py:230-232` | ✅ |
| `ToArray()` | 781 | `primitives.py:234-236` | ✅ |
| `Midpoint(Vec3 a, Vec3 b)` | 783-786 | `primitives.py:239-242` | ✅ |
| `Length()` | 789 | `primitives.py:209-211` | ✅ |
| `LengthSquared()` | 790 | `primitives.py:213-215` | ✅ |
| `Origin` | 792 | `primitives.py:201` | ✅ |
| `UnitX` | 794 | `primitives.py:204` | ✅ |
| `UnitY` | 795 | `primitives.py:207` | ✅ |
| `UnitZ` | 796 | `primitives.py:210` | ✅ |
| `MinValue` | 798 | `primitives.py:317` | ✅ |
| `MaxValue` | 799 | `primitives.py:320` | ✅ |

### Static Methods
| C# Method | Line | Python Location | Status |
|-----------|------|-----------------|---------|
| `Normal(Vec3 a)` | 801-804 | `primitives.py:245-254` | ✅ |
| `Dot(Vec3 a, Vec3 b)` | 806-809 | `primitives.py:257-264` | ✅ |
| `Cross(Vec3 a, Vec3 b)` | 811-818 | `primitives.py:267-279` | ✅ |
| `Min(Vec3 a, Vec3 b)` | 820-827 | `primitives.py:302-309` | ✅ |
| `Max(Vec3 a, Vec3 b)` | 829-836 | `primitives.py:312-314` | ✅ |
| `Distance(Vec3 a, Vec3 b)` | 838-841 | `primitives.py:217-220` | ✅ |

### Operators
| C# Operator | Line | Python Location | Status |
|-------------|------|-----------------|---------|
| All arithmetic operators | 843-876 | `primitives.py:323-365` | ✅ |

## Line1 Structure (Lines 918-982)

| C# Member | Line | Python Location | Status |
|-----------|------|-----------------|---------|
| Constructor | 927-931 | `lines.py:17-20` | ✅ |
| `Length()` | 933 | `lines.py:22-24` | ✅ |
| `Contains(double value)` | 935-938 | `lines.py:26-28` | ✅ |
| `FromCenter(double center, double size)` | 978-981 | `lines.py:31-34` | ✅ |

## Line2 Structure (Lines 984-1070)

| C# Member | Line | Python Location | Status |
|-----------|------|-----------------|---------|
| Constructor | 993-997 | `lines.py:44-47` | ✅ |
| `Length()` | 999 | `lines.py:49-51` | ✅ |
| `Direction()` | 1000 | `lines.py:53-55` | ✅ |
| `Magnitude()` | 1001 | `lines.py:57-59` | ✅ |
| `Inters(Line2 a, Line2 b, bool infinite)` | 1051-1054 | `lines.py:77-80` | ✅ |
| `IntersMust(Line2 a, Line2 b, bool infinite)` | 1056-1059 | `lines.py:83-86` | ✅ |
| `FromStart(Vec2 position, Vec2 direction)` | 1061-1064 | `lines.py:89-92` | ✅ |
| `FromCenter(Vec2 center, Vec2 length)` | 1066-1069 | `lines.py:95-98` | ✅ |

## Line3 Structure (Lines 1072-1135)

| C# Member | Line | Python Location | Status |
|-----------|------|-----------------|---------|
| Constructor | 1080-1084 | `lines.py:108-111` | ✅ |
| `Length()` | 1086 | `lines.py:113-115` | ✅ |
| `Direction()` | 1087 | `lines.py:117-119` | ✅ |
| `Magnitude()` | 1088 | `lines.py:121-123` | ✅ |

## Box2 Structure (Lines 1137-1261)

| C# Member | Line | Python Location | Status |
|-----------|------|-----------------|---------|
| Constructor | 1145-1149 | `boxes.py:16-20` | ✅ |
| `Size()` | 1151 | `boxes.py:22-24` | ✅ |
| `Middle()` | 1153 | `boxes.py:26-28` | ✅ |
| `BottomLeft()` | 1155 | `boxes.py:30-32` | ✅ |
| `BottomRight()` | 1156 | `boxes.py:34-36` | ✅ |
| `TopLeft()` | 1157 | `boxes.py:38-40` | ✅ |
| `TopRight()` | 1158 | `boxes.py:42-44` | ✅ |
| `Bottom()` | 1160 | `boxes.py:46-48` | ✅ |
| `Top()` | 1161 | `boxes.py:50-52` | ✅ |
| `Left()` | 1162 | `boxes.py:54-56` | ✅ |
| `Right()` | 1163 | `boxes.py:58-60` | ✅ |
| `Contains(Vec2 position)` | 1171-1174 | `boxes.py:62-68` | ✅ |
| `Intersects(Box2 box)` | 1176-1180 | `boxes.py:70-79` | ✅ |
| `Union(Box2 box)` | 1182-1185 | `boxes.py:81-84` | ✅ |
| `FromPositionAndSize(Vec2 position, Vec2 size)` | 1187-1190 | `boxes.py:87-90` | ✅ |
| `FromCenter(Vec2 center, Vec2 size)` | 1192-1195 | `boxes.py:93-96` | ✅ |
| `FromBottomCenter(Vec2 bottomCenter, Vec2 size)` | 1197-1200 | `boxes.py:99-105` | ✅ |
| `FromCorners` methods | 1202-1210 | `boxes.py:108-117` | ✅ |
| `FromList(IEnumerable<Vec2> vecs)` | 1212-1224 | `boxes.py:120-131` | ✅ |

## Box3 Structure (Lines 1263-1357)

| C# Member | Line | Python Location | Status |
|-----------|------|-----------------|---------|
| Constructor | 1271-1275 | `boxes.py:141-145` | ✅ |
| `Size()` | 1277 | `boxes.py:147-149` | ✅ |
| `Middle()` | 1278 | `boxes.py:151-153` | ✅ |
| `Contains(Vec3 position)` | 1286-1289 | `boxes.py:155-164` | ✅ |
| `Intersects(Box3 box)` | 1291-1296 | `boxes.py:166-178` | ✅ |
| `Union(Box3 box)` | 1298-1301 | `boxes.py:180-183` | ✅ |
| `FromList(IEnumerable<Vec3> vecs)` | 1303-1315 | `boxes.py:186-197` | ✅ |
| `FromCenter(Vec3 center, Vec3 size)` | 1317-1320 | `boxes.py:200-203` | ✅ |

## Plane3 Structure (Lines 1359-1487)

| C# Member | Line | Python Location | Status |
|-----------|------|-----------------|---------|
| Constructors | 1367-1397 | `plane.py:17-52` | ✅ |
| `DotV(Plane3 f, Vec3 v)` | 1430-1433 | `plane.py:94-101` | ✅ |
| `DotP(Plane3 f, Vec3 p)` | 1435-1438 | `plane.py:104-111` | ✅ |
| `Inters(Line3 l, Plane3 f, bool infinite)` | 1443-1446 | `plane.py:114-118` | ✅ |
| `Inters(Vec3 p, Vec3 v, Plane3 f, bool infinite)` | 1451-1466 | `plane.py:121-144` | ✅ |
| `Inters(Plane3 f1, Plane3 f2)` | 1471-1486 | `plane.py:147-170` | ✅ |
| Distance/projection methods | - | `plane.py:55-91` | ✅ Added extras |

## Basis3 Structure (Lines 1489-1558)

| C# Member | Line | Python Location | Status |
|-----------|------|-----------------|---------|
| Constructor | 1495-1500 | `basis.py:16-20` | ✅ |
| `UnitXYZ` | 1502 | `basis.py:23` | ✅ |
| `FromXY(Vec3 basisX, Vec3 basisY)` | 1504-1508 | `basis.py:26-32` | ✅ |
| `FromXZ(Vec3 basisX, Vec3 basisZ)` | 1510-1514 | `basis.py:35-41` | ✅ |
| `FromYZ(Vec3 basisY, Vec3 basisZ)` | 1516-1520 | `basis.py:44-50` | ✅ |

## TriIndex Structure (Lines 1560-1604)

| C# Member | Line | Python Location | Status |
|-----------|------|-----------------|---------|
| Constructor | 1566-1571 | `triangle.py:16-20` | ✅ |
| Properties A, B, C | 1562-1564 | `triangle.py:13-15` | ✅ |

## Mat4 Structure (Mat4.cs Lines 1-305)

### Properties and Constants
| C# Member | Line | Python Location | Status |
|-----------|------|-----------------|---------|
| M11-M44 properties | 15-30 | `matrix.py:21-36` | ✅ |
| Constructor | 32-54 | `matrix.py:38-57` | ✅ |
| `GetTranslation()` | 56-59 | `matrix.py:59-61` | ✅ |
| `GetBasis()` | 61-67 | `matrix.py:63-69` | ✅ |
| `Identity` | 69-73 | `matrix.py:72-79` | ✅ |

### Creation Methods
| C# Method | Line | Python Location | Status |
|-----------|------|-----------------|---------|
| `CreateRotationX(double ang)` | 77-87 | `matrix.py:82-92` | ✅ |
| `CreateRotationY(double ang)` | 89-99 | `matrix.py:95-105` | ✅ |
| `CreateRotationZ(double ang)` | 101-111 | `matrix.py:108-118` | ✅ |
| `CreateTranslation` methods | 113-125 | `matrix.py:121-139` | ✅ |
| `CreateScale` methods | 127-144 | `matrix.py:142-165` | ✅ |
| `CreateBasis` methods | 146-158 | `matrix.py:168-180` | ✅ |
| `CreateTransform` methods | 160-172 | `matrix.py:183-198` | ✅ |

### Operations
| C# Method | Line | Python Location | Status |
|-----------|------|-----------------|---------|
| `operator *(Mat4 a, Mat4 b)` | 174-193 | `matrix.py:200-220` | ✅ |
| `operator *(Mat4 m, Vec3 v)` | 195-198 | `matrix.py:222-224` | ✅ |
| `GetInverse(Mat4 m)` | 201-234 | `matrix.py:227-259` | ✅ |
| `TransformPosition(Mat4 m, Vec3 p)` | 236-242 | `matrix.py:261-268` | ✅ |
| `TransformVector(Mat4 m, Vec3 v)` | 244-250 | `matrix.py:270-277` | ✅ |
| `TransformNormal(Vec3 n, Mat4 m)` | 256-262 | `matrix.py:279-289` | ✅ |

## Missing Implementations

The following methods from the C# code are not yet implemented in Python:
1. `Vec2.IntersList()` - Line 653
2. `Vec2.IntersListMust()` - Line 667

These methods handle intersection of a line with a list of connected line segments.