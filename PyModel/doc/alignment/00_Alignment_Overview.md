# C# to Python Alignment Overview

This document provides a comprehensive overview of how PyModel aligns with the original C# BimBackend implementation.

## Table of Contents
1. [Alignment Philosophy](#alignment-philosophy)
2. [Project Structure Mapping](#project-structure-mapping)
3. [Key Differences](#key-differences)
4. [Alignment Verification](#alignment-verification)
5. [Reference Guidelines](#reference-guidelines)

## Alignment Philosophy

PyModel maintains strict alignment with the C# BimBackend while adapting to Python idioms:

### Core Principles
1. **Behavioral Equivalence** - Same inputs produce same outputs
2. **Structural Similarity** - Similar class hierarchies and relationships
3. **API Compatibility** - Compatible data formats and interfaces
4. **Pythonic Implementation** - Following Python best practices

### What We Preserve
- Mathematical calculations and algorithms
- Business logic and rules
- Data structures and relationships
- API contracts and formats
- Component hierarchies

### What We Adapt
- Language-specific syntax
- Naming conventions (camelCase → snake_case)
- Type systems (static → dynamic with hints)
- Collection types (List<T> → List[T])
- Properties → methods or @property

## Project Structure Mapping

### C# BimBackend Structure
```
BimBackend-master/
├── BimAPI/                    → PyModel: src/api/
├── BimCoreLibrary/           → PyModel: src/
│   ├── Builders/             → src/business/
│   ├── Classes/              → src/business/
│   └── Helpers/              → src/bim/
├── Shedkit.Bim.Shed/         → PyModel: src/
│   ├── Geo.cs               → src/geometry/
│   ├── Materials.cs          → src/materials/
│   └── ShedBim.cs           → src/bim/
├── Shedkit.Output.*/         → PyModel: src/output/
└── Tests/                    → PyModel: tests/
```

### Detailed Module Mapping

| C# Module | Python Module | Purpose |
|-----------|---------------|---------|
| Geo.cs | geometry/primitives.py | Vec2, Vec3 types |
| Mat4.cs | geometry/matrix.py | Matrix operations |
| Materials.cs | materials/base.py | Material definitions |
| ShedBim.cs | bim/shed_bim.py | Shed BIM logic |
| CarportBuilder.cs | business/structure_builder.py | Building generation |
| IFC/GLB/DXF generators | output/*_generator.py | File generation |

### Namespace to Module Conversion
```csharp
// C#
namespace Shedkit.Bim.Shed.Geometry {
    public struct Vec3 { }
}

# Python
# src/geometry/primitives.py
@dataclass
class Vec3:
    pass
```

## Key Differences

### 1. Type System

**C# Static Typing:**
```csharp
public double CalculateArea(double width, double height) {
    return width * height;
}
```

**Python Type Hints:**
```python
def calculate_area(width: float, height: float) -> float:
    return width * height
```

### 2. Properties

**C# Properties:**
```csharp
public class Building {
    public double Width { get; set; }
    public double Height { get; private set; }
}
```

**Python Properties:**
```python
class Building:
    def __init__(self):
        self._width = 0
        self._height = 0
    
    @property
    def width(self) -> float:
        return self._width
    
    @width.setter
    def width(self, value: float):
        self._width = value
    
    @property
    def height(self) -> float:
        return self._height
```

### 3. Collections

**C# Collections:**
```csharp
List<Component> components = new List<Component>();
Dictionary<string, Material> materials = new Dictionary<string, Material>();
```

**Python Collections:**
```python
components: List[Component] = []
materials: Dict[str, Material] = {}
```

### 4. Enums

**C# Enums:**
```csharp
public enum RoofType {
    Gable,
    Skillion,
    Flat
}
```

**Python Enums:**
```python
from enum import Enum

class RoofType(Enum):
    GABLE = "gable"
    SKILLION = "skillion"
    FLAT = "flat"
```

### 5. Interfaces

**C# Interfaces:**
```csharp
public interface IStructureBuilder {
    Building CreateStructure(BuildingInput input);
}
```

**Python Abstract Base Classes:**
```python
from abc import ABC, abstractmethod

class IStructureBuilder(ABC):
    @abstractmethod
    def create_structure(self, building_input: BuildingInput) -> Building:
        pass
```

## Alignment Verification

### Mathematical Accuracy
All mathematical operations maintain exact alignment:
- Vector operations match to 15 decimal places
- Matrix calculations use same algorithms
- Geometric intersections produce identical results

### Test Coverage
```python
# accuracy_tests/test_geometry_accuracy.py
def test_vec3_operations_match_csharp():
    """Verify Vec3 operations match C# exactly"""
    # Test data generated from C# reference
    with open("reference_data/vec3_operations.json") as f:
        test_cases = json.load(f)
    
    for case in test_cases:
        v1 = Vec3(**case["v1"])
        v2 = Vec3(**case["v2"])
        
        # Test operations
        assert v1 + v2 == Vec3(**case["add_result"])
        assert v1.dot(v2) == case["dot_result"]
        assert v1.cross(v2) == Vec3(**case["cross_result"])
```

### Business Logic Validation
```python
# Ensure same building outputs
def test_carport_generation_matches_csharp():
    input_params = {
        "width": 6000,
        "length": 9000,
        "height": 3000,
        "roof_type": "gable"
    }
    
    # Generate with Python
    py_building = CarportBuilder().create_structure(input_params)
    
    # Load C# reference
    cs_reference = load_csharp_reference("carport_6x9_gable.json")
    
    # Compare structures
    assert_buildings_match(py_building, cs_reference)
```

## Reference Guidelines

### Code Comments
Every Python file includes C# reference comments:
```python
"""
Module: geometry.primitives
C# Reference: Shedkit.Bim.Shed/Geo.cs
C# Namespace: Shedkit.Bim.Shed.Geometry
"""

@dataclass
class Vec3:
    """
    3D vector representation.
    
    C# Reference: Geo.cs lines 915-1241
    C# Definition: public struct Vec3 : IEquatable<Vec3>
    """
    
    # C# Ref: Lines 918-920 - public double X/Y/Z { get; set; }
    x: float
    y: float
    z: float
    
    def length(self) -> float:
        """
        Calculate vector magnitude.
        
        C# Reference: Geo.cs line 967
        C# Signature: public double Length()
        """
        return math.sqrt(self.x**2 + self.y**2 + self.z**2)
```

### Method Mapping
Document C# to Python method conversions:
```python
# C# Method: public static Vec3 Cross(Vec3 a, Vec3 b)
# Python Method: Vec3.cross(a: Vec3, b: Vec3) -> Vec3
@staticmethod
def cross(a: 'Vec3', b: 'Vec3') -> 'Vec3':
    """Cross product of two vectors."""
    return Vec3(
        a.y * b.z - a.z * b.y,
        a.z * b.x - a.x * b.z,
        a.x * b.y - a.y * b.x
    )
```

### Constant Alignment
```python
# C# Constants from Geo.cs
class Geo:
    # C# Ref: public const double Epsilon = 1e-10;
    EPSILON = 1e-10
    
    # C# Ref: public const double DefaultTolerance = 0.01;
    DEFAULT_TOLERANCE = 0.01
    
    # C# Ref: public const double Pi = Math.PI;
    PI = math.pi
    
    # C# Ref: public const double Deg2Rad = Pi / 180.0;
    DEG2RAD = math.pi / 180.0
```

### Testing Alignment
Each module has corresponding alignment tests:
```python
# tests/alignment/test_geometry_alignment.py
class TestGeometryAlignment:
    """Test geometry calculations match C# exactly"""
    
    def test_matrix_multiplication_matches(self):
        """Verify matrix operations align with C#"""
        # Test cases from C# unit tests
        test_matrices = load_csharp_test_data("matrix_tests.json")
        
        for test in test_matrices:
            m1 = Mat4.from_list(test["m1"])
            m2 = Mat4.from_list(test["m2"])
            expected = Mat4.from_list(test["result"])
            
            result = m1 * m2
            assert matrices_equal(result, expected, tolerance=1e-10)
```

## Verification Process

### 1. Automated Testing
```bash
# Run alignment tests
python -m pytest tests/alignment/ -v

# Run accuracy tests
python -m pytest accuracy_tests/ -v
```

### 2. Reference Data Generation
```bash
# Generate C# reference data
cd BimBackend-master
dotnet run --project ReferenceDataGenerator

# Copy to Python project
cp reference_output/*.json ../PyModel/accuracy_tests/reference_data/
```

### 3. Visual Comparison
```python
# Compare generated models visually
def compare_models_visual(py_model_path, cs_model_path):
    py_glb = load_glb(py_model_path)
    cs_glb = load_glb(cs_model_path)
    
    # Compare vertex positions
    assert_vertices_match(py_glb.vertices, cs_glb.vertices)
    
    # Compare face indices
    assert_faces_match(py_glb.faces, cs_glb.faces)
    
    # Visual diff tool
    create_visual_diff(py_glb, cs_glb, "diff_output.html")
```

## Maintenance Guidelines

### When C# Changes
1. Update Python implementation
2. Update reference comments
3. Regenerate test data
4. Run alignment tests
5. Update documentation

### Adding New Features
1. Implement in C# first (if maintaining alignment)
2. Generate reference test data
3. Implement Python version
4. Add alignment tests
5. Document mapping

### Version Tracking
```python
# src/__init__.py
__version__ = "1.0.0"
__csharp_version__ = "1.0.0"  # Aligned C# version
__last_sync__ = "2024-01-23"   # Last synchronization date
```

## Summary

The PyModel project maintains strict alignment with C# BimBackend through:
- Exact mathematical equivalence
- Consistent business logic
- Compatible data structures
- Comprehensive testing
- Detailed documentation

This alignment ensures that:
- Models generated by either system are interchangeable
- Business rules produce identical results
- API contracts remain compatible
- Future updates can be synchronized

For specific alignment details, see:
- [Geometry Alignment](01_Geometry_Alignment.md)
- [Materials Alignment](02_Materials_Alignment.md)
- [BIM Components Alignment](03_BIM_Components_Alignment.md)
- [Business Logic Alignment](04_Business_Logic_Alignment.md)