# Material System Complete Documentation

## Overview

The Material System is a comprehensive framework for defining and managing various building materials in the BIM Backend. This Python implementation corresponds to the C# implementation across multiple files:

### Core Files Implemented
1. **Materials.cs** (588 lines) - Base material definitions
2. **MeshProfileHelper.cs** (779 lines) - Profile generation for 3D visualization  
3. **CladdingProfileHelper.cs** (162 lines) - Cladding profile shapes
4. **MaterialSegmentHelper.cs** (687 lines) - Material segmentation logic
5. **FrameMaterialHelper.cs** (300+ lines) - Frame material catalog
6. **BracketMaterialHelper.cs** - Bracket loading and management
7. **FlashingMaterialHelper.cs** (670 lines) - Flashing material definitions

## Python Module Structure

```
src/materials/
├── __init__.py         # Module exports
├── base.py            # Core material classes (Materials.cs)
├── mesh.py            # 3D mesh structures
├── visual.py          # Visual properties and appearance
├── helpers.py         # Material catalogs and helpers
├── profiles.py        # Mesh profile generation (MeshProfileHelper.cs)
└── segments.py        # Material segmentation (MaterialSegmentHelper.cs)
```

## Material Types

### 1. Bracket Materials
```python
@dataclass
class BracketMaterial:
    name: str
    mesh_name: str
    _mesh: Optional[BracketMesh] = field(default=None, init=False)
```

### 2. Cladding Materials
```python
@dataclass
class CladdingMaterial:
    name: str
    design: str
    cover_width: float
    rib_height: float
    overlap: float
    bmt: float
    tct: float
    profile: List[Vec2]
    is_profile_rotated: bool
```

### 3. Frame Materials
```python
@dataclass
class FrameMaterial:
    name: str
    material_type: FrameMaterialType
    width: float
    height: float
    thickness: float
    flipped: bool = False
    section: int = 0
    is_b2b: bool = False
    # ... additional properties
```

### 4. Flashing Materials
```python
@dataclass
class FlashingMaterial:
    name: str
    description: str = ""
    thickness: float = 0.55
    profile: List[Vec2] = field(default_factory=list)
    # ... additional properties
```

## Helper Classes

### FrameMaterialHelper
Provides a comprehensive catalog of frame materials:
- **C-sections**: C10010, C15024, C20019, etc.
- **Z-sections**: Z10010, Z15024, Z20019, etc.
- **TopHat sections**: TH064075, TH096100, etc.
- **SHS sections**: SHS07507525, SHS10010040, etc.
- **PAD sections**: PADJ06427, PADJ10027, etc.
- **Floor joists**: J11510, J18210, J23512, etc.

Example usage:
```python
# Get a specific frame material
c_section = FrameMaterialHelper.get_frame_material("C15024")

# Access predefined sections
c100 = FrameMaterialHelper.C_SECTIONS["C10010"]()
z150 = FrameMaterialHelper.Z_SECTIONS["Z15024"]()
```

### CladdingProfileHelper
Generates profile shapes for various cladding types:
```python
# Generate corrugated profile
corrugated = CladdingProfileHelper.create_corrugated(profile_height=19)

# Generate monoclad profile
monoclad = CladdingProfileHelper.create_monoclad()

# Other profiles: monopanel, k_panel, sharpline, t_rib, metrib, metclad
```

### MeshProfileHelper
Creates 2D profiles for 3D mesh generation:
```python
# Create mesh profile for a frame material
material = FrameMaterialHelper.get_frame_material("C15024")
profile = MeshProfileHelper.create_mesh_profile(material, draw_profile_arcs=True)

# Profile contains:
# - items: List of profile items (outer/inner shapes)
# - punching_map: List of punching locations
```

### MaterialSegmentHelper
Handles segmentation of cladding sheets for material optimization:
```python
# Get cladding segments for a roof
segments = MaterialSegmentHelper.get_cladding_segments(shed_bim_cladding)

# Each segment contains:
# - full_outline: Complete sheet outline
# - effective_outline: Outline minus overlaps
# - sheet_width: Width of the sheet
# - full_length: Length of the sheet
# - holes: Any cutouts in the sheet
```

## Material Validation

### Frame Material Validation
- Width must be positive
- Height must be positive  
- Thickness must be positive
- Lip must be non-negative
- Web hole centers validated based on section size

### Cladding Material Validation
- Cover width must be positive
- Overlap must be positive and less than cover width
- BMT (base metal thickness) must be positive
- Profile must have at least 2 points

### Color Material Validation
- RGB values must be 0-255
- Alpha must be 0-255
- CMYK percentages must be 0-100

## Factory Methods

### Frame Materials
```python
# Create C-section
c_section = FrameMaterial.create_c(
    name="C15024",
    section=150,
    is_b2b=False,
    width=152,
    height=64,
    lip=18.5,
    thickness=2.4,
    web_hole_centers=60
)

# Create Z-section
z_section = FrameMaterial.create_z(
    name="Z15024",
    section=150,
    width=152,
    z_flange_f=66,
    z_flange_e=61,
    lip=18.5,
    thickness=2.4,
    web_hole_centers=60
)
```

### Footing Materials
```python
# Create block footing
block = FootingMaterial.create_block(
    width=600,
    length=600,
    depth=900
)

# Create bored footing
bored = FootingMaterial.create_bored(
    diameter=450,
    depth=1200
)
```

## Visual Properties

### Material Appearance
```python
appearance = MaterialAppearance(
    base_color=ColorMaterial(r=200, g=200, b=200),
    finish=MaterialFinish.GALVANIZED,
    texture_mapping=TextureMapping.PLANAR,
    reflectivity=0.3,
    roughness=0.7
)
```

### Color Libraries
```python
# Access predefined colors
colorbond_colors = ColorLibrary.get_colorbond_colors()
surfmist = colorbond_colors["Surfmist"]

# RAL colors
ral_colors = ColorLibrary.get_ral_colors()
ral_9002 = ral_colors["RAL9002"]
```

## Material Segmentation

The material segmentation system optimizes sheet layout to minimize waste:

1. **Roof Segmentation**: Calculates optimal sheet placement considering overlaps
2. **Wall Segmentation**: Handles vertical cladding with different overlap requirements
3. **Lining Segmentation**: Manages interior lining materials

Example:
```python
# Segment roof cladding
segments = MaterialSegmentHelper.get_roof_cladding_segments(roof_cladding)

# Each segment represents a physical sheet with:
# - Position on the roof
# - Actual dimensions
# - Effective coverage area
# - Any required cuts or holes
```

## Integration with BIM System

Materials integrate with the broader BIM system through:

1. **Structure Builder**: Materials assigned during construction
2. **Quantity Takeoff**: Material properties used for BOM generation
3. **3D Visualization**: Visual properties and profiles for rendering
4. **Engineering Validation**: Material properties checked against loads
5. **Cost Estimation**: Material costs calculated from quantities

## Performance Considerations

1. **Material Caching**: Frame materials cached after first lookup
2. **Profile Generation**: Profiles generated once and reused
3. **Segmentation Optimization**: Efficient algorithms for sheet layout
4. **Memory Management**: Lazy loading of bracket meshes

## Testing

Comprehensive test coverage includes:
- Material property validation
- Factory method correctness
- Profile generation accuracy
- Segmentation algorithm verification
- Visual property calculations

See `tests/test_materials/` for complete test suite.

## Future Enhancements

1. **Full STL Support**: Complete bracket mesh loading from STL files
2. **Advanced Profiles**: Support for custom cladding profiles
3. **Material Database**: SQLAlchemy integration for material persistence
4. **Cost Integration**: Direct material cost calculations
5. **Sustainability Metrics**: Embodied carbon calculations

## C# to Python Migration Notes

### Key Differences
1. **Structs vs Dataclasses**: C# structs converted to Python dataclasses
2. **Static Classes**: Converted to class methods or module-level functions
3. **Enums**: Direct enum conversion with string values
4. **Properties**: Using @property decorators for computed values
5. **Null Handling**: Optional types for nullable references

### Performance Optimizations
1. **NumPy Integration**: Vector operations for profile calculations
2. **Caching**: LRU cache for expensive computations
3. **Batch Operations**: Process multiple materials together
4. **Generator Patterns**: For large material lists

This completes the material system implementation, providing full parity with the C# version while leveraging Python's strengths.