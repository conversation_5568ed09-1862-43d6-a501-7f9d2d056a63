# BIM Backend Architecture Documentation

## System Overview

The BIM Backend is a Python-based system for generating 3D Building Information Models (BIM) for carports and sheds. It provides a complete pipeline from user input to 3D file generation with engineering validation.

## Architecture Diagram

```mermaid
graph TB
    subgraph "Client Layer"
        WEB[Web Client]
        API_CLIENT[API Client]
    end
    
    subgraph "API Layer"
        FASTAPI[FastAPI Server]
        AUTH[Authentication]
        ENCRYPT[AES Encryption]
    end
    
    subgraph "Business Layer"
        INPUT[Building Input Validation]
        BUILDER[Carport Builder]
        ENG_SERVICE[Engineering Service]
    end
    
    subgraph "Data Layer"
        BIM[BIM Data Model]
        MAT[Materials System]
        GEOM[Geometry Engine]
    end
    
    subgraph "Output Layer"
        GLTF[GLTF Generator]
        DXF[DXF Generator]
        IFC[IFC Generator]
    end
    
    subgraph "External Services"
        ENG_API[Engineering API]
        STORAGE[File Storage]
    end
    
    WEB --> FASTAPI
    API_CLIENT --> FASTAPI
    FASTAPI --> AUTH
    FASTAPI --> ENCRYPT
    ENCRYPT --> INPUT
    INPUT --> BUILDER
    BUILDER --> ENG_SERVICE
    ENG_SERVICE --> ENG_API
    BUILDER --> BIM
    BIM --> MAT
    BIM --> GEOM
    BUILDER --> GLTF
    BUILDER --> DXF
    BUILDER --> IFC
    GLTF --> STORAGE
```

## Layer Architecture

### 1. Geometry Layer (Foundation)
```
src/geometry/
├── primitives.py      # Vec2, Vec3 - Basic vector operations
├── matrix.py          # Mat4 - 4x4 transformation matrices
├── lines.py           # Line1, Line2, Line3 - Line segments
├── boxes.py           # Box2, Box3 - Bounding boxes
├── plane.py           # Plane3 - 3D planes
├── basis.py           # Basis3 - Coordinate systems
├── triangle.py        # TriIndex - Triangle indexing
├── quaternion.py      # Quaternion - Rotations
├── angle.py           # Angle - Angle utilities
├── shapes.py          # Triangle, Triangle3D
└── helpers.py         # Geo - Static helper functions
```

**Key Responsibilities:**
- Vector mathematics (2D/3D)
- Matrix transformations
- Geometric intersections
- Coordinate transformations
- Spatial calculations

### 2. Materials Layer
```
src/materials/
├── base.py           # Base material classes
├── visual.py         # ColorMaterial, CladdingMaterial
├── structural.py     # FrameMaterial, FootingMaterial
├── accessories.py    # FlashingMaterial, GutterMaterial
├── mesh.py          # BracketMesh definitions
├── profiles.py      # Frame profile generation
├── segments.py      # Path segments for profiles
└── helpers.py       # MaterialLibrary
```

**Key Responsibilities:**
- Material definitions
- Physical properties
- Visual properties
- Material validation
- Library management

### 3. BIM Data Model Layer
```
src/bim/
├── shed_bim.py      # Root ShedBim structure
├── wall_roof.py     # Walls and roof structures
├── components.py    # Columns, rafters, purlins
├── accessories.py   # Gutters, downpipes, straps
├── openings.py      # Doors, windows, skylights
├── cladding.py      # Cladding and trim
└── mezzanine.py     # Mezzanine structures
```

**Key Responsibilities:**
- 3D model representation
- Component relationships
- Spatial hierarchy
- Material assignments

### 4. Business Logic Layer
```
src/business/
├── building_input.py    # Input validation and constraints
├── structure_builder.py # CarportBuilder - 14-step pipeline
├── engineering.py       # Engineering validation service
└── helpers.py          # Business rule helpers
```

**Key Responsibilities:**
- Input validation
- Construction logic
- Engineering rules
- Business constraints

### 5. API Layer
```
src/api/
├── main.py         # FastAPI application
├── models.py       # Request/Response models
└── endpoints.py    # API endpoints
```

**Key Responsibilities:**
- HTTP endpoints
- Request handling
- Response formatting
- Error handling

### 6. Services Layer
```
src/services/
├── encryption.py   # AES encryption service
└── __init__.py    # Service exports
```

**Key Responsibilities:**
- Encryption/Decryption
- External integrations
- Shared utilities

## Data Flow

### 1. API Request Flow
```
Client Request
    ↓
Encrypted JSON (AES-256-CBC)
    ↓
FastAPI Endpoint
    ↓
Decrypt & Validate
    ↓
BuildingInput Model
    ↓
CarportBuilder
    ↓
ShedBim Structure
    ↓
Output Generation
    ↓
File Storage
    ↓
Download URL Response
```

### 2. Construction Pipeline (14 Steps)
```python
1. _initialize_bim()           # Create base structure
2. _find_slab_structure()      # Foundation slab
3. _find_brackets_structure()   # Connection brackets
4. _find_columns_and_footings() # Vertical supports
5. _find_rafter_structure()     # Roof framing
6. _find_roof_claddings()       # Roof covering
7. _find_purlins_structure()    # Roof purlins
8. _find_eave_purlin()          # Eave details
9. _find_attached_awning()      # Awning structures
10. _find_flashings()           # Weatherproofing
11. _find_gutters_downpipes()   # Water management
12. _find_braces()              # Structural bracing
13. _find_punchings()           # Connection details
14. _find_fasteners()           # Screws and bolts
```

## Component Interactions

### Geometry Usage
```python
# Materials use geometry
class FrameMaterial:
    sections: List[Vec2]  # Profile points
    
# BIM uses geometry
class ShedBimColumn:
    base: Vec3           # Bottom position
    top: Vec3            # Top position
    
# Business uses geometry
def calculate_positions() -> List[Vec3]:
    transform = Mat4.create_rotation_z(angle)
```

### Material References
```python
# BIM references materials
class ShedBimColumn:
    frame_material: str  # "C15024"
    
class ShedBimRoof:
    cladding_material: str  # "Colorbond"
    
# Builder assigns materials
column.frame_material = self.default_column_material
roof.cladding_material = self.default_roof_material
```

## API Endpoints

### Core Endpoints
```
POST /api/carport/create
    Request:  { encrypted_data: "..." }
    Response: { success: bool, file_url: str }
    
GET /api/download/{file_id}
    Response: Binary file data
    
GET /api/health
    Response: { status: "healthy", version: "1.0.0" }
    
GET /api/carport/sample-request
    Response: Sample encrypted request for testing
```

### Request Encryption
```python
# Client side
aes = AESEncryption(key)
encrypted = aes.encrypt_json({
    "building_type": "CARPORT",
    "span": 6000.0,
    "length": 6000.0
})

# Server side
decrypted = aes.decrypt_json(request.encrypted_data)
building_input = BuildingInput(**decrypted)
```

## Design Patterns

### 1. Factory Pattern
```python
class CarportBuilder:
    @staticmethod
    def create_carport(input: BuildingInput) -> ShedBim:
        builder = CarportBuilder(input)
        return builder._prepare_carport()
```

### 2. Builder Pattern
```python
class StructureBuilderBase:
    def _prepare_structure(self):
        self._step1()
        self._step2()
        # ... 14 steps total
```

### 3. Strategy Pattern
```python
# Different strategies for engineering validation
if building_input.validate_engineering:
    eng_service = RealEngineeringService()
else:
    eng_service = MockEngineeringService()
```

### 4. Repository Pattern
```python
class MaterialLibrary:
    @classmethod
    def get_default(cls) -> 'MaterialLibrary':
        # Return singleton instance
```

## Configuration

### Environment Variables
```bash
# API Configuration
API_ENCRYPTION_KEY=your-secret-key
API_PORT=8000

# Engineering Service
ENGINEERING_API_URL=https://eng-api.example.com
ENGINEERING_API_KEY=your-api-key

# CORS Settings
CORS_ORIGINS=https://app.example.com

# File Storage
FILE_RETENTION_HOURS=24
TEMP_FILE_DIR=/tmp/bim-files
```

### Material Configuration
```python
# Default materials defined in code
DEFAULT_FRAME_MATERIALS = [
    FrameMaterial("C10010", FrameMaterialType.C, 100, 10, 1.0),
    FrameMaterial("C15024", FrameMaterialType.C, 150, 24, 1.5),
    # ...
]
```

## Error Handling

### API Error Responses
```python
# Structured error response
{
    "error": "validation_error",
    "message": "Invalid building parameters",
    "details": {
        "field": "span",
        "reason": "Must be between 3000 and 12000"
    }
}
```

### Exception Hierarchy
```
Exception
├── BIMException
│   ├── ValidationError
│   ├── GeometryError
│   └── MaterialError
├── APIException
│   ├── AuthenticationError
│   └── EncryptionError
└── BusinessException
    ├── EngineeringError
    └── ConstraintError
```

## Performance Considerations

### Optimization Strategies
1. **Vectorized Operations**: Use NumPy for batch calculations
2. **Lazy Loading**: Load materials/meshes on demand
3. **Caching**: Cache expensive calculations
4. **Async I/O**: Use async for external calls

### Memory Management
```python
# Use generators for large datasets
def generate_positions() -> Iterator[Vec3]:
    for i in range(count):
        yield Vec3(x, y, z)
        
# Clear large objects
del large_bim_structure
```

## Security Architecture

### Encryption Layer
- AES-256-CBC for API requests
- SHA-256 key hashing
- Random IV generation
- Base64 encoding

### Input Validation
- Pydantic models for type checking
- Range validation for dimensions
- Enum validation for types
- SQL injection prevention

### API Security
- CORS configuration
- Request size limits
- Rate limiting (planned)
- Authentication (planned)

## Deployment Architecture

### Docker Deployment
```dockerfile
FROM python:3.10-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY src/ src/
CMD ["uvicorn", "api.main:app", "--host", "0.0.0.0"]
```

### Kubernetes Deployment
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: bim-backend
spec:
  replicas: 3
  template:
    spec:
      containers:
      - name: api
        image: bim-backend:latest
        ports:
        - containerPort: 8000
```

## Monitoring & Logging

### Logging Strategy
```python
import logging

logger = logging.getLogger(__name__)

# Structured logging
logger.info("Carport created", extra={
    "user_id": user_id,
    "building_type": "CARPORT",
    "span": 6000.0
})
```

### Metrics (Planned)
- Request count
- Response time
- Error rate
- CPU/Memory usage
- Active connections

## Future Architecture Enhancements

### Microservices Split
```
bim-api-service/        # API Gateway
bim-builder-service/    # Construction logic
bim-engineering-service/# Engineering validation
bim-output-service/     # File generation
```

### Event-Driven Architecture
```
Request → Queue → Worker → Storage → Notification
```

### Caching Layer
```
Client → CDN → API → Redis → Database
```

This architecture provides a solid foundation for a scalable, maintainable BIM generation system while allowing for future enhancements and optimizations.