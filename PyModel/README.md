# PyModel - Python BIM Backend API

A Python implementation of Building Information Modeling (BIM) backend system for generating IFC and GLB files for construction projects. This API serves as the Python equivalent of the C# BimBackend system, specifically designed for carport and shed building types with extensibility for future building types.

## 📍 Latest Updates (2025-07-04)

### GLB Generation Enhancement
- **New GLB Module**: Created dedicated `src/glb_generation/` module aligned with C# ShedGltf
- **Binary GLB Format**: Implemented proper GLB binary format with JSON and BIN chunks
- **Material System**: PBR materials matching C# implementation (steel, concrete, etc.)
- **Mesh Generation**: Accurate geometry for all component types (SHS, C-sections, TopHat)
- **API Integration**: Enhanced API v2 endpoints for direct GLB generation
- **Comprehensive Testing**: New GLB-focused test facility with validation

### Previous Updates (2025-07-03)

### Major Project Review
- **Complete C# Alignment Verification**: All geometry calculations verified against C# reference
- **Detailed Documentation**: Added comprehensive geometry calculation references with line numbers
- **Project Structure**: Cleaned and organized with proper archiving
- **API Enhancement**: Updated Swagger API for IFC and GLB generation
- **Testing Facility**: Enhanced comprehensive testing scripts

### IFC Generation Fixes (2025-07-01)
- **Slab BREP**: Fixed horizontal positioning at ground level (Z=0 to Z=-thickness)
- **Eave Purlin C-sections**: Fixed BREP generation for proper Y-axis orientation
- **Rotation Consistency**: All structural members now use correct axis rotations matching C# implementation
- **Codebase Cleanup**: Archived legacy files and organized project structure

## 🎯 Overview

PyModel provides a complete API for:
- 3D geometry calculations and transformations
- Material system management
- BIM data modeling for construction elements
- IFC (Industry Foundation Classes) file generation
- GLB (3D model) file generation
- Engineering validation and business logic
- Complete C# BimCoreLibrary compatibility

## 🚀 Quick Start

### Prerequisites

- Python 3.10 or higher
- pip package manager
- Virtual environment (recommended)

### Installation

1. Clone the repository:
```bash
git clone [repository-url]
cd PyModel
```

2. Create and activate virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install dependencies:
```bash
pip install -r requirements.txt
```

4. Install in development mode:
```bash
pip install -e .
```

### Running the API

Start the FastAPI server:
```bash
python run_api.py
```

The API will be available at `http://localhost:8000`

### API Documentation

Once the server is running, you can access:
- **Swagger UI**: `http://localhost:8000/docs`
- **ReDoc**: `http://localhost:8000/redoc`

## 📐 Geometry Calculations Reference

### Coordinate System
- **X-axis**: Width direction (span)
- **Y-axis**: Length direction (along building)  
- **Z-axis**: Height direction (vertical)
- **Origin**: (0,0,0) with footings at ground level

### Component Geometry Calculations

#### 1. **Columns and Footings**

**C# Reference**: `StructureBuilderBase.cs` Lines 218-393 (`FindColumnsAndFootingsStructureBuilder`)  
**Python Implementation**: `src/business/structure_builder.py` Lines 555-656 (`find_columns_and_footings_structure`)

**Key Calculations**:
- **X Offset**:
  - C# Lines 228-248: `leftXOffset = Overhang + Column.Width/2 + (RoofType==GABLE ? EavePurlin.Flange : Rafter.Flange)`
  - Python Lines 568-577: Same calculation with roof type logic
- **Y Offset**:
  - C# Lines 230-270: First frame: `0.5 * Column.Width`, Last frame: `-0.5 * Column.Width`
  - Python Lines 580-596: Identical frame-based positioning
- **Heights**:
  - C# Lines 232-244: `Height + Rafter.Flange * tan(pitch)`
  - Python Lines 599-606: Same height calculation with pitch adjustment

#### 2. **Rafters**

**Flat/Awning/Attached Rafters**:
- **C# Reference**: Lines 715-762 (`FindFlatRafters`)
- **Python Implementation**: Lines 668-711 (`_find_flat_rafters`)

**Gable Rafters**:
- **C# Reference**: Lines 647-714 (`FindGableRafters`)
- **Python Implementation**: Lines 713-776 (`_find_gable_rafters`)

**Key Calculations**:
- **Flat Rafter Span**: 
  - C# Line 721-722: `Start=0, End=Span+2*Overhang`
  - Python Lines 674-675: Identical span calculation
- **Gable Rafter Positioning**:
  - C# Lines 654-655: Complex calculation with material dimensions
  - Python Lines 728-730: Matching calculation with sin/cos adjustments

#### 3. **Purlins**

**Flat/Awning Purlins** (Segmented between rafters):
- **C# Reference**: Lines 965-1085 (`FindPurlinsFlat`)
- **Python Implementation**: Lines 822-890 (`_find_flat_purlins`)

**Gable Purlins** (Overlapping rafters):
- **C# Reference**: Lines 1087-1239 (`FindPurlinsGable`)
- **Python Implementation**: Lines 892-1023 (`_find_gable_purlins`)

**Key Calculations**:
- **X Interval (Flat)**:
  - C# Line 976: `(Span - EavePurlin.Flange) / (PurlinRows - 1)`
  - Python Line 827: Same interval calculation
- **Y Segmentation**:
  - C# Lines 1017-1040: Different logic for first/middle/last bays
  - Python Lines 844-860: Matching bay-based segmentation
- **Gable Overlap**:
  - C# Lines 1146-1175: Purlins extend beyond rafters
  - Python Lines 958-975: Same overlap logic

#### 4. **Eave Purlins**

**C# Reference**: Lines 1241-1440 (`FindEavePurlinStructure`)  
**Python Implementation**: Lines 1025-1124 (`find_eave_purlin_structure`)

**Key Calculations**:
- **Gable Eave Positions**:
  - C# Lines 1258-1267: Simple positioning at roof edges
  - Python Lines 1043-1046: Matching edge placement
- **Flat/Awning Eave Positions**:
  - C# Lines 1332-1357: Complex height adjustments with pitch
  - Python Lines 1077-1085: Identical pitch-based positioning

### Material Dimension Adjustments

All calculations account for material dimensions:
- **Column.Width**: Used in positioning and offsets
- **Rafter.Flange, Rafter.Web, Rafter.Width**: Affects rafter and purlin placement
- **Purlin.Web, Purlin.Flange**: Influences purlin positioning
- **EavePurlin.Flange, EavePurlin.Web, EavePurlin.Width**: Critical for eave positioning

### Roof Type Specific Behaviors

| Roof Type | Overhang | Right Columns | Purlin Behavior | Special Notes |
|-----------|----------|---------------|-----------------|---------------|
| FLAT | Yes | Yes | Segmented | Standard carport |
| GABLE | No (forced to 0) | Yes | Overlapping | Peak roof design |
| AWNING | No (forced to 0) | Yes | Segmented | Single slope |
| ATTACHED_AWNING | No | No | Segmented | Building attachment |

## 🏗️ IFC Generation

### Generate Single Carport
```python
from src.ifc_generation import CarportIFCGenerator

gen = CarportIFCGenerator()
path = gen.generate_carport(
    span=6000,      # 6 meters
    length=9000,    # 9 meters
    height=2700,    # 2.7 meters
    roof_type=CarportRoofType.GABLE,
    pitch=15,       # 15 degrees
    bays=3,         # 3 bays
    slab=True       # Include slab
)
print(f"Generated IFC file: {path}")
```

### Test All Roof Types
```bash
python test_all_roof_types_comprehensive.py
```

This generates IFC files covering all roof types with proper configurations.

## 📁 Project Structure

```
PyModel/
├── src/
│   ├── api/                     # FastAPI endpoints and models
│   ├── bim/                     # BIM data models and components
│   ├── business/                # Core business logic
│   │   ├── structure_builder.py     # Main structure calculations (Lines 555-1386)
│   │   ├── building_input.py        # Input models and enums
│   │   └── helpers.py               # Utility functions
│   ├── geometry/                # 3D geometry calculations
│   │   ├── matrix.py               # Matrix transformations
│   │   ├── primitives.py           # Basic 3D primitives
│   │   └── lines.py                # Line and vector operations
│   ├── ifc_generation/          # IFC file generation
│   │   ├── ifc_brep_generator.py    # BREP-based IFC generator
│   │   └── carport_generator.py     # High-level carport API
│   ├── materials/               # Material definitions
│   │   ├── profiles.py             # C-sections, SHS, TopHat profiles
│   │   └── segments.py             # Material segment definitions
│   └── output/                  # Output generators
│       ├── ifc/                    # Multiple IFC implementations
│       ├── gltf/                   # GLB/GLTF generation
│       └── dxf/                    # DXF file generation
├── tests/                       # Comprehensive test suite
├── manual_testing/              # Manual verification scripts
├── test_output/                 # Generated test files
├── archive/                     # Archived implementations
└── requirements.txt            # Python dependencies
```

## 🛠️ API Endpoints

### Building Creation
```http
POST /buildings
Content-Type: application/json

{
  "type": "carport",
  "span": 6000,
  "length": 9000,
  "height": 2700,
  "roofType": "GABLE",
  "pitch": 15,
  "bays": 3,
  "overhang": 300,
  "slab": true
}
```

### File Generation
```http
POST /buildings/{id}/generate-ifc
POST /buildings/{id}/generate-glb
```

### Engineering Data Override
```http
POST /buildings/with-engineering
Content-Type: application/json

{
  "buildingInput": { ... },
  "engData": {
    "ENG_COLUMN": "SHS10010030",
    "ENG_RAFTER": "C20319",
    "ENG_PURLINSIZE": "TH075125",
    "ENG_PURLINROW": 4,
    "ENG_FOOTINGTYPE": "bored",
    "ENG_FOOTINGSIZE": 450,
    "ENG_FOOTINGDEPTH": 600
  }
}
```

#### Python Usage
```python
from src.business.engineering import EngData

# All fields are required for EngData
eng_data = EngData(
    ENG_COLUMN="SHS10010040",   # Must exist in materials catalog
    ENG_RAFTER="C20019",        # Available C-sections: C10010-C40030
    ENG_PURLINSIZE="C15019",    # See src/materials/helpers.py
    ENG_PURLINROW=7,
    ENG_FOOTINGTYPE="block",    # "bored" or "block"
    ENG_FOOTINGDIA="400",       # String value in mm
    ENG_FOOTINGDEPTH="600"      # String value in mm
)
```

## 📋 Material Specifications

Default materials (matching C# implementation):
- **Column**: SHS07507525 (75x75x2.5mm square hollow section)
- **Rafter**: C15015 (C-section 152x64x1.5mm)
- **Purlin (Gable)**: TH064100 (TopHat 64x100x1.5mm)
- **Purlin (Flat)**: C15015 (C-section 152x64x1.5mm)
- **Eave Purlin**: C15015 (C-section 150x64x1.5mm)
- **Footing**: Bored 300x300 (300mm diameter x 300mm depth)

## 🔬 BREP Implementation Details

### Profile Types
1. **SHS (Square Hollow Section)**: `ifc_brep_generator.py` Lines 59-131
   - Hollow rectangular profile with wall thickness
   - 10 faces total (outer and inner shells)

2. **C-Section**: `ifc_brep_generator.py` Lines 133-234
   - 12 vertices for complete profile with lips
   - Web facing direction controlled by rotation

3. **TopHat Section**: `ifc_brep_generator.py` Lines 236-318
   - 8 vertices for trapezoidal profile
   - Used exclusively for gable purlins

4. **Footings**: `ifc_brep_generator.py` Lines 320-419
   - Cylindrical (16 segments) for bored
   - Rectangular block for pad footings

## 🧪 Testing

### Run All Tests
```bash
# Complete test suite
python test_all_roof_types_comprehensive.py

# Rotation verification
python test_rotation_fixes.py

# Unit tests
pytest tests/

# With coverage
pytest --cov=src tests/
```

### Test Output Location
Generated IFC files are saved to: `test_output/`

## 🔧 Development

### Code Style
```bash
# Format code
black src/ tests/

# Check linting
flake8 src/ tests/

# Type checking
mypy src/
```

### C# Reference Locations
- **Main Implementation**: `C:\Users\<USER>\Desktop\Project\Model\BimBackend-master\BimCoreLibrary`
- **Structure Builder**: `Classes\StructureBuilderBase.cs`
- **Carport Builder**: `Builders\CarportBuilder.cs`
- **Input Models**: `Classes\BuildingInput.cs`

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## 📞 Support

For issues and questions:
- Create an issue in the GitHub repository
- Refer to CLAUDE.md for AI assistant instructions
- Check C# reference implementation for calculation details

## 🙏 Acknowledgments

- Based on the C# BimBackend system
- Uses IfcOpenShell for IFC file handling
- Built with FastAPI for modern API development
- BREP approach ensures universal IFC viewer compatibility