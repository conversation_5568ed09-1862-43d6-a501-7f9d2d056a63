# Virtual Environment
venv/
.venv/
env/
ENV/

# Python cache
__pycache__/
*.py[cod]
*.pyo
*.pyd
.Python

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# PyInstaller
*.manifest
*.spec

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Database
*.db
*.sqlite3

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Temporary files
*.tmp
*.temp

# Node modules (if any)
node_modules/ 

# Generated output files
test_output/**/*.ifc
test_output/**/*.glb
output/*.ifc
output/*.glb
*.bak

# Archive directories
archive/cleanup_*
