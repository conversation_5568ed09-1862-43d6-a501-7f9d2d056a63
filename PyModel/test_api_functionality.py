#!/usr/bin/env python3
"""
API Functionality Test Script

Tests all API endpoints including IFC and GLB generation.
Run the API server first: python run_api.py
"""

import requests
import json
import time
import os
from pathlib import Path
from datetime import datetime
import sys

# Add project to path
sys.path.insert(0, str(Path(__file__).parent))

from src.services.encryption import AESEncryption

# Configuration
API_BASE_URL = "http://localhost:8000"
OUTPUT_DIR = Path("test_output/api_tests")
OUTPUT_DIR.mkdir(parents=True, exist_ok=True)

# Test results
test_results = {
    "timestamp": datetime.now().isoformat(),
    "tests": [],
    "passed": 0,
    "failed": 0
}


def log_test(name: str, passed: bool, details: str = ""):
    """Log test result."""
    status = "✅ PASS" if passed else "❌ FAIL"
    print(f"{status} - {name}")
    if details:
        print(f"       {details}")
    
    test_results["tests"].append({
        "name": name,
        "passed": passed,
        "details": details
    })
    
    if passed:
        test_results["passed"] += 1
    else:
        test_results["failed"] += 1


def test_health_check():
    """Test health check endpoint."""
    print("\n1. Testing Health Check Endpoint")
    print("-" * 40)
    
    try:
        response = requests.get(f"{API_BASE_URL}/api/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            log_test("Health Check", True, f"Status: {data.get('status', 'unknown')}")
        else:
            log_test("Health Check", False, f"Status code: {response.status_code}")
    except Exception as e:
        log_test("Health Check", False, f"Error: {str(e)}")


def test_v2_info_endpoints():
    """Test V2 information endpoints."""
    print("\n2. Testing V2 Information Endpoints")
    print("-" * 40)
    
    endpoints = [
        ("/api/v2/materials", "Materials List"),
        ("/api/v2/roof-types", "Roof Types"),
        ("/api/v2/examples", "Example Configurations")
    ]
    
    for endpoint, name in endpoints:
        try:
            response = requests.get(f"{API_BASE_URL}{endpoint}", timeout=5)
            if response.status_code == 200:
                data = response.json()
                log_test(f"GET {endpoint}", True, f"Returned {len(data)} items")
            else:
                log_test(f"GET {endpoint}", False, f"Status code: {response.status_code}")
        except Exception as e:
            log_test(f"GET {endpoint}", False, f"Error: {str(e)}")


def test_v1_encrypted_api():
    """Test V1 encrypted API endpoints."""
    print("\n3. Testing V1 Encrypted API")
    print("-" * 40)
    
    # Get sample request
    try:
        response = requests.get(f"{API_BASE_URL}/api/carport/sample-request", timeout=5)
        if response.status_code == 200:
            sample_data = response.json()
            log_test("Sample Request", True, "Retrieved sample encrypted request")
            
            # Test carport creation with sample
            encrypted_request = sample_data["encrypted_request"]
            
            create_response = requests.post(
                f"{API_BASE_URL}/api/carport/create",
                json=encrypted_request,
                timeout=30
            )
            
            if create_response.status_code == 200:
                result = create_response.json()
                if result.get("success"):
                    log_test("Encrypted Carport Create", True, 
                            f"File URL: {result.get('file_url', 'N/A')}")
                else:
                    log_test("Encrypted Carport Create", False,
                            result.get("message", "Unknown error"))
            else:
                log_test("Encrypted Carport Create", False,
                        f"Status code: {create_response.status_code}")
        else:
            log_test("Sample Request", False, f"Status code: {response.status_code}")
    except Exception as e:
        log_test("V1 Encrypted API", False, f"Error: {str(e)}")


def test_v2_direct_generation():
    """Test V2 direct generation endpoints."""
    print("\n4. Testing V2 Direct Generation")
    print("-" * 40)
    
    # Test configurations
    test_configs = [
        {
            "name": "Small Flat Carport",
            "data": {
                "name": "API Test Flat",
                "roof_type": "FLAT",
                "span": 3000,
                "length": 6000,
                "height": 2400,
                "bays": 2,
                "pitch": 2,
                "overhang": 300,
                "slab": True
            }
        },
        {
            "name": "Gable Carport",
            "data": {
                "name": "API Test Gable",
                "roof_type": "GABLE",
                "span": 6000,
                "length": 9000,
                "height": 2700,
                "bays": 3,
                "pitch": 15,
                "overhang": 0,
                "slab": True
            }
        }
    ]
    
    for config in test_configs:
        print(f"\nTesting: {config['name']}")
        
        # Test IFC generation
        try:
            response = requests.post(
                f"{API_BASE_URL}/api/v2/generate/ifc",
                json=config["data"],
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    log_test(f"IFC Generation - {config['name']}", True,
                            f"File: {result.get('filename', 'N/A')}, "
                            f"Size: {result.get('file_size', 0)/1024:.1f}KB")
                    
                    # Try to download
                    download_url = result.get("download_url")
                    if download_url:
                        download_response = requests.get(
                            f"{API_BASE_URL}{download_url}",
                            timeout=10
                        )
                        if download_response.status_code == 200:
                            output_file = OUTPUT_DIR / result.get("filename", "test.ifc")
                            with open(output_file, "wb") as f:
                                f.write(download_response.content)
                            log_test(f"IFC Download - {config['name']}", True,
                                    f"Saved to: {output_file}")
                        else:
                            log_test(f"IFC Download - {config['name']}", False,
                                    f"Download failed: {download_response.status_code}")
                else:
                    log_test(f"IFC Generation - {config['name']}", False,
                            "Generation failed")
            else:
                log_test(f"IFC Generation - {config['name']}", False,
                        f"Status code: {response.status_code}")
        except Exception as e:
            log_test(f"IFC Generation - {config['name']}", False, f"Error: {str(e)}")
        
        # Test GLB generation
        try:
            response = requests.post(
                f"{API_BASE_URL}/api/v2/generate/glb",
                json=config["data"],
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    log_test(f"GLB Generation - {config['name']}", True,
                            f"File: {result.get('filename', 'N/A')}, "
                            f"Size: {result.get('file_size', 0)/1024:.1f}KB")
                    
                    # Try to download
                    download_url = result.get("download_url")
                    if download_url:
                        download_response = requests.get(
                            f"{API_BASE_URL}{download_url}",
                            timeout=10
                        )
                        if download_response.status_code == 200:
                            output_file = OUTPUT_DIR / result.get("filename", "test.glb")
                            with open(output_file, "wb") as f:
                                f.write(download_response.content)
                            log_test(f"GLB Download - {config['name']}", True,
                                    f"Saved to: {output_file}")
                        else:
                            log_test(f"GLB Download - {config['name']}", False,
                                    f"Download failed: {download_response.status_code}")
                else:
                    log_test(f"GLB Generation - {config['name']}", False,
                            "Generation failed")
            else:
                log_test(f"GLB Generation - {config['name']}", False,
                        f"Status code: {response.status_code}")
        except Exception as e:
            log_test(f"GLB Generation - {config['name']}", False, f"Error: {str(e)}")


def test_batch_generation():
    """Test batch generation endpoint."""
    print("\n5. Testing Batch Generation")
    print("-" * 40)
    
    request_data = {
        "name": "Batch Test",
        "roof_type": "AWNING",
        "span": 4000,
        "length": 8000,
        "height": 2500,
        "bays": 3,
        "pitch": 10,
        "overhang": 0,
        "slab": True
    }
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/api/v2/generate/batch",
            json=request_data,
            params={"formats": ["ifc", "glb"]},
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            formats_generated = result.get("results", {})
            success_count = sum(1 for fmt, data in formats_generated.items() 
                              if isinstance(data, dict) and data.get("success"))
            
            log_test("Batch Generation", success_count == 2,
                    f"Generated {success_count}/2 formats successfully")
        else:
            log_test("Batch Generation", False, 
                    f"Status code: {response.status_code}")
    except Exception as e:
        log_test("Batch Generation", False, f"Error: {str(e)}")


def generate_report():
    """Generate test report."""
    report_path = OUTPUT_DIR / f"api_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    
    with open(report_path, "w") as f:
        json.dump(test_results, f, indent=2)
    
    print("\n" + "=" * 60)
    print("API TEST SUMMARY")
    print("=" * 60)
    print(f"Total Tests: {test_results['passed'] + test_results['failed']}")
    print(f"Passed: {test_results['passed']}")
    print(f"Failed: {test_results['failed']}")
    print(f"Success Rate: {test_results['passed'] / (test_results['passed'] + test_results['failed']) * 100:.1f}%")
    print(f"\nDetailed report saved to: {report_path}")
    print(f"Generated files saved to: {OUTPUT_DIR}")


def main():
    """Run all API tests."""
    print("=" * 60)
    print("API FUNCTIONALITY TEST")
    print("=" * 60)
    print(f"Testing API at: {API_BASE_URL}")
    print(f"Output directory: {OUTPUT_DIR}")
    
    # Check if API is running
    print("\nChecking API availability...")
    try:
        response = requests.get(f"{API_BASE_URL}/", timeout=2)
        if response.status_code != 200:
            print("❌ API is not responding properly")
            print("Please start the API server: python run_api.py")
            return
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to API")
        print("Please start the API server: python run_api.py")
        return
    
    print("✅ API is running")
    
    # Run tests
    test_health_check()
    test_v2_info_endpoints()
    test_v1_encrypted_api()
    test_v2_direct_generation()
    test_batch_generation()
    
    # Generate report
    generate_report()


if __name__ == "__main__":
    main()