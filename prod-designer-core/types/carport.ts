export const roofTypes = ['Skillion Overhang', 'Skillion Attached', 'Gable', 'Skillion'] as const;
export type RoofType = (typeof roofTypes)[number];
export type DimensionType = 'length' | 'height' | 'span' | 'pitch' | 'overhang';

export interface CarportState {
  length: number;
  height: number;
  span: number;
  pitch: number;
  overhang: number;
  roofType: RoofType;
  
  // Temporary properties used during model loading from shared links
  // These are not part of the core state but used during initialization
  _sceneLeanProperties?: {
    leftLeanTo?: boolean;
    leftLeanToSpan?: number;
    leftLeanToDropHeight?: number;
    rightLeanTo?: boolean;
    rightLeanToSpan?: number;
    rightLeanToDropHeight?: number;
    rightLeanToAttached?: boolean;
  };
  
  // Additional scene settings for initialization
  _sceneSettings?: any;
}
