{"Size": "尺寸", "Roof": "屋顶", "Span": "跨度", "Length": "长度", "Height": "高度", "Overhang": "檐伸", "Pitch": "坡度", "Slab": "地板", "LeanTo": "侧棚", "LeftLeanTo": "左侧棚", "RightLeanTo": "右侧棚", "Drop Height": "落差高度", "Walls": "墙体", "Wall Configuration": "墙体配置（{{count}} 段）", "Back": "后侧", "Front": "前侧", "Extras": "附加项", "Roof Profile": "屋顶样式", "Wall Profile": "墙体样式", "Colours": "颜色", "Trim": "边框", "Quote": "报价", "Customer Information": "客户信息", "Request a Quote": "请求报价", "Full Name": "全名", "Enter your full name": "请输入您的全名", "Name": "姓名", "Enter your name": "请输入您的姓名", "Email": "电子邮件", "Enter your email": "请输入您的电子邮件", "Phone": "电话", "Enter your phone number": "请输入您的电话号码", "Cancel": "取消", "Request Quote": "请求报价", "Sending...": "发送中...", "Please enter your name": "请输入您的姓名", "Please enter a valid email address": "请输入有效的电子邮件地址", "Please enter your phone number": "请输入您的电话号码", "Thankyou": "感谢您的报价请求！", "WillContact": "您的报价请求已成功发送，我们的团队成员将尽快与您联系。", "Plan": "平面图", "Persp": "透视图", "Ortho": "正交图", "New": "新建", "Select a Template": "选择模板", "Skillion Roof Carport": "单坡屋顶车棚", "Standard skillion roof carport with no lean-to.": "标准单坡屋顶车棚，无侧棚", "Gable Roof Carport": "双坡屋顶车棚", "Classic gable roof design with pitched roof.": "经典双坡屋顶设计，带倾斜屋面", "Skillion Roof Carport with Lean-to": "单坡屋顶车棚（右侧带棚）", "Modern skillion roof with Right Lean-to.": "现代风格单坡屋顶，右侧带棚", "Gable Roof with Lean-to": "双坡屋顶加侧棚", "Gable roof carport with right lean-to extension.": "右侧扩展侧棚的双坡屋顶车棚", "Gable roof carport with left and right lean-to extension.": "左右两侧均有扩展侧棚的双坡屋顶车棚", "Skillion Attached": "附着式单坡屋顶", "Skillion roof attached to an existing structure.": "连接至现有建筑物的单坡屋顶", "Load": "加载", "Load Saved Model": "加载已保存模型", "Loading saved models...": "正在加载已保存的模型...", "No Saved Model found": "未找到保存的模型", "Save": "保存", "Model Name": "模型名称", "Enter model name(optional)": "输入模型名称（可选）", "Note": "注意：模型仅保存在本地浏览器缓存中，清除缓存将删除所有保存的模型。", "Share": "分享", "Share Design": "分享设计", "ShareNote": "分享您的车棚设计，其他人打开链接即可查看相同配置。", "Copy Link": "复制链接", "Link Copied!": "链接已复制！", "Share via Email": "通过电子邮件分享", "Email Sent!": "邮件已发送！", "Close": "关闭", "Hide Diemnsions": "隐藏尺寸", "Show Diemnsions": "显示尺寸", "Views": "视图", "Normal": "正常", "Transparent": "透明", "Frame only": "仅结构框架", "Export 3D View": "导出 3D 视图", "Export Plan View": "导出平面图"}