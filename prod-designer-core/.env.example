# Production Designer Core Environment Configuration

# Security Configuration
NODE_ENV=production
SECURITY_ENABLED=true

# Allowed IPs (comma-separated)
# Add your production server IPs here
ALLOWED_IPS=127.0.0.1,::1

# Allowed Domains (comma-separated)
# Add your production domains here
ALLOWED_DOMAINS=localhost,designmycarport.firebaseapp.com,designmycarport.web.app,website-463606.firebaseapp.com,website-463606.web.app

# Allowed Origins (comma-separated)
# Add your production origins here
ALLOWED_ORIGINS=http://localhost:8080,http://localhost:3000,https://designmycarport.firebaseapp.com,https://designmycarport.web.app,https://website-463606.firebaseapp.com,https://website-463606.web.app

# Admin Configuration
ADMIN_KEY=your-secure-admin-key-here

# Firebase Configuration (if needed for additional services)
FIREBASE_PROJECT_ID=designmycarport
FIREBASE_REGION=us-central1

# Logging Configuration
LOG_LEVEL=info
ENABLE_ACCESS_LOGS=true

# Rate Limiting (requests per minute)
RATE_LIMIT_WINDOW=60000
RATE_LIMIT_MAX_REQUESTS=100

# Development Settings (set to false in production)
DEVELOPMENT_MODE=false
ALLOW_LOCALHOST=true
