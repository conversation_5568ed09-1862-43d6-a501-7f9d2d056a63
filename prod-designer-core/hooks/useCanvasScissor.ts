import { useMemo } from 'react';
import { THREE } from 'expo-three';
import { useFrame, useThree } from '@react-three/fiber/native';
import { Colors } from '@/constants/colors/Colors';

const origin = new THREE.Vector3(0, 0, 0);

function useCanvasScissor(active: boolean = true) {
  const { size, scene } = useThree();

  const camera1 = useMemo(() => new THREE.OrthographicCamera(-10, 10, 10, -10, 0.1, 100), []);
  camera1.position.set(10, 0, 0);
  camera1.lookAt(origin);
  const camera2 = useMemo(() => new THREE.OrthographicCamera(-10, 10, 10, -10, 0.1, 100), []);
  camera2.position.set(-10, 0, 0);
  camera2.lookAt(origin);
  const camera3 = useMemo(() => new THREE.OrthographicCamera(-10, 10, 10, -10, 0.1, 100), []);
  camera3.position.set(0, 10, 0);
  camera3.lookAt(origin);
  const camera4 = useMemo(() => new THREE.OrthographicCamera(-10, 10, 10, -10, 0.1, 100), []);
  camera4.position.set(0, 0, -10);
  camera4.lookAt(origin);

  useFrame((state) => {
    const renderer = state.gl;
    const left = size.left;
    const bottom = 0;
    const width = size.width / 2;
    const height = size.height / 2;
    const autoClear = renderer.autoClear;

    if (!active) {
      renderer.setViewport(0, 0, size.width, size.height);
      renderer.setScissor(0, 0, size.width, size.height);
      renderer.setScissorTest(false);
      return;
    }

    renderer.setViewport(left, bottom, width, height);
    renderer.setScissor(left, bottom, width, height);
    renderer.setScissorTest(true);
    renderer.render(scene, camera1);

    renderer.setViewport(left + width, bottom + height, width, height);
    renderer.setScissor(left + width, bottom + height, width, height);
    renderer.setScissorTest(true);
    renderer.render(scene, camera2);

    renderer.setViewport(left + width, bottom, width, height);
    renderer.setScissor(left + width, bottom, width, height);
    renderer.setScissorTest(true);
    renderer.render(scene, camera3);

    renderer.setViewport(left, bottom + height, width, height);
    renderer.setScissor(left, bottom + height, width, height);
    renderer.setScissorTest(true);
    renderer.render(scene, camera4);

    renderer.setViewport(0, 0, 0, 0);
    renderer.setScissor(0, 0, 0, 0);
    renderer.setScissorTest(true);

    renderer.autoClear = autoClear;
    renderer.setClearColor(Colors.dark.background);
  });
}

export { useCanvasScissor };
