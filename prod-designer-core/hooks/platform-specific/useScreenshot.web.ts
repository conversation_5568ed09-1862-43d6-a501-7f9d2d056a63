import { useEffect, useState } from 'react';

export function useScreenshot(ref: React.MutableRefObject<HTMLCanvasElement>) {
  const [objectUrl, setObjectUrl] = useState<string>();

  async function takeScreenShot(): Promise<string | undefined> {
    return new Promise((resolve) => {
      ref.current.toBlob((blob) => {
        if (blob) {
          const url = URL.createObjectURL(blob);
          setObjectUrl(url);
          resolve(url);
        } else {
          resolve(undefined);
        }
      });
    });
  }

  useEffect(() => {
    return () => {
      objectUrl && URL.revokeObjectURL(objectUrl);
    };
  }, [objectUrl]);

  return {
    takeScreenShot,
    objectUrl,
  };
}
