/**
 * Security Middleware for iframe embedding protection
 * Validates IP addresses and domain names for authorized embedding
 */

import { Request, Response } from 'firebase-functions';
import { corsMiddleware, createCORSConfig } from './cors-config';

// Configuration for allowed IPs and domains
interface SecurityConfig {
  allowedIPs: string[];
  allowedDomains: string[];
  allowedOrigins: string[];
  developmentMode: boolean;
}

// Default security configuration
const DEFAULT_CONFIG: SecurityConfig = {
  allowedIPs: [
    '127.0.0.1',
    '::1',
    // Add your production server IPs here
    // Example: '***********', '************'
  ],
  allowedDomains: [
    'localhost',
    'designmycarport.firebaseapp.com',
    'designmycarport.web.app',
    'website-463606.firebaseapp.com',
    'website-463606.web.app',
    // Add your custom domains here
    // Example: 'yourdomain.com', 'app.yourdomain.com'
  ],
  allowedOrigins: [
    'http://localhost:8080',
    'http://localhost:3000',
    'https://designmycarport.firebaseapp.com',
    'https://designmycarport.web.app',
    'https://website-463606.firebaseapp.com',
    'https://website-463606.web.app',
    // Add your production origins here
  ],
  developmentMode: process.env.NODE_ENV !== 'production'
};

/**
 * Extract client IP address from request
 */
function getClientIP(req: Request): string {
  const forwarded = req.headers['x-forwarded-for'] as string;
  const realIP = req.headers['x-real-ip'] as string;
  const clientIP = req.headers['x-client-ip'] as string;
  
  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }
  
  if (realIP) {
    return realIP;
  }
  
  if (clientIP) {
    return clientIP;
  }
  
  return req.connection?.remoteAddress || req.socket?.remoteAddress || 'unknown';
}

/**
 * Extract domain from origin or referer
 */
function extractDomain(url: string): string | null {
  try {
    const urlObj = new URL(url);
    return urlObj.hostname;
  } catch {
    return null;
  }
}

/**
 * Check if IP is in allowed list (supports CIDR notation)
 */
function isIPAllowed(clientIP: string, allowedIPs: string[]): boolean {
  // Simple IP matching (can be extended for CIDR notation)
  return allowedIPs.includes(clientIP) || 
         allowedIPs.some(ip => ip.includes('*') && clientIP.startsWith(ip.replace('*', '')));
}

/**
 * Check if domain is in allowed list (supports wildcards)
 */
function isDomainAllowed(domain: string, allowedDomains: string[]): boolean {
  return allowedDomains.includes(domain) ||
         allowedDomains.some(allowed => {
           if (allowed.startsWith('*.')) {
             const baseDomain = allowed.substring(2);
             return domain.endsWith(baseDomain);
           }
           return false;
         });
}

/**
 * Main security validation function
 */
export function validateAccess(req: Request, config: SecurityConfig = DEFAULT_CONFIG): {
  allowed: boolean;
  reason?: string;
  clientIP: string;
  origin?: string;
  referer?: string;
} {
  const clientIP = getClientIP(req);
  const origin = req.headers.origin as string;
  const referer = req.headers.referer as string;
  
  // In development mode, allow all requests
  if (config.developmentMode) {
    return {
      allowed: true,
      clientIP,
      origin,
      referer
    };
  }
  
  // Check IP whitelist
  if (!isIPAllowed(clientIP, config.allowedIPs)) {
    return {
      allowed: false,
      reason: `IP address ${clientIP} not in whitelist`,
      clientIP,
      origin,
      referer
    };
  }
  
  // Check origin if present
  if (origin) {
    if (!config.allowedOrigins.includes(origin)) {
      const domain = extractDomain(origin);
      if (!domain || !isDomainAllowed(domain, config.allowedDomains)) {
        return {
          allowed: false,
          reason: `Origin ${origin} not in whitelist`,
          clientIP,
          origin,
          referer
        };
      }
    }
  }
  
  // Check referer if present and no origin
  if (!origin && referer) {
    const domain = extractDomain(referer);
    if (!domain || !isDomainAllowed(domain, config.allowedDomains)) {
      return {
        allowed: false,
        reason: `Referer domain ${domain} not in whitelist`,
        clientIP,
        origin,
        referer
      };
    }
  }
  
  return {
    allowed: true,
    clientIP,
    origin,
    referer
  };
}

/**
 * Express middleware for security validation with CORS
 */
export function securityMiddleware(config?: Partial<SecurityConfig>) {
  const finalConfig = { ...DEFAULT_CONFIG, ...config };
  const environment = finalConfig.developmentMode ? 'development' : 'production';
  const corsConfig = createCORSConfig(environment);
  const cors = corsMiddleware(corsConfig);

  return (req: Request, res: Response, next: Function) => {
    // Apply CORS first
    cors(req, res, (corsError?: any) => {
      if (corsError) {
        return; // CORS middleware already sent response
      }

      // Then apply security validation
      const validation = validateAccess(req, finalConfig);

      if (!validation.allowed) {
        console.warn('Access denied:', validation);
        res.status(403).json({
          error: 'Access denied',
          message: 'Your IP address or domain is not authorized to access this resource',
          code: 'FORBIDDEN_ACCESS'
        });
        return;
      }

      // Log successful access
      console.log('Access granted:', {
        ip: validation.clientIP,
        origin: validation.origin,
        referer: validation.referer
      });

      // Add additional security headers
      res.set({
        'X-Frame-Options': 'ALLOWALL', // Allow iframe embedding from whitelisted domains
        'X-Content-Type-Options': 'nosniff',
        'Referrer-Policy': 'strict-origin-when-cross-origin',
        'X-XSS-Protection': '1; mode=block'
      });

      next();
    });
  };
}

/**
 * Update security configuration at runtime
 */
export function updateSecurityConfig(newConfig: Partial<SecurityConfig>): SecurityConfig {
  Object.assign(DEFAULT_CONFIG, newConfig);
  return DEFAULT_CONFIG;
}

export { SecurityConfig, DEFAULT_CONFIG };
