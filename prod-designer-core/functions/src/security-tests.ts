/**
 * Security Testing Suite
 * Tests for iframe embedding security measures
 */

import { validateAccess, SecurityConfig } from './security-middleware';
import { applyCORSHeaders, CORSConfig } from './cors-config';

// Mock request object for testing
interface MockRequest {
  method: string;
  headers: Record<string, string>;
  connection?: { remoteAddress?: string };
  socket?: { remoteAddress?: string };
}

// Mock response object for testing
class MockResponse {
  private headers: Record<string, string> = {};
  private statusCode: number = 200;
  private body: any = null;

  set(header: string, value: string): void {
    this.headers[header] = value;
  }

  status(code: number): MockResponse {
    this.statusCode = code;
    return this;
  }

  json(data: any): void {
    this.body = data;
  }

  send(data: any): void {
    this.body = data;
  }

  getHeaders(): Record<string, string> {
    return this.headers;
  }

  getStatus(): number {
    return this.statusCode;
  }

  getBody(): any {
    return this.body;
  }
}

/**
 * Test suite for security validation
 */
export class SecurityTestSuite {
  private testResults: Array<{ name: string; passed: boolean; message: string }> = [];

  private addResult(name: string, passed: boolean, message: string): void {
    this.testResults.push({ name, passed, message });
    console.log(`${passed ? '✅' : '❌'} ${name}: ${message}`);
  }

  /**
   * Test IP whitelist validation
   */
  testIPWhitelist(): void {
    const config: SecurityConfig = {
      allowedIPs: ['127.0.0.1', '*************'],
      allowedDomains: ['localhost'],
      allowedOrigins: ['http://localhost:3000'],
      developmentMode: false
    };

    // Test allowed IP
    const allowedReq: MockRequest = {
      method: 'GET',
      headers: { origin: 'http://localhost:3000' },
      connection: { remoteAddress: '127.0.0.1' }
    };

    const allowedResult = validateAccess(allowedReq as any, config);
    this.addResult(
      'IP Whitelist - Allowed IP',
      allowedResult.allowed,
      allowedResult.allowed ? 'Allowed IP passed' : allowedResult.reason || 'Failed'
    );

    // Test blocked IP
    const blockedReq: MockRequest = {
      method: 'GET',
      headers: { origin: 'http://localhost:3000' },
      connection: { remoteAddress: '***********' }
    };

    const blockedResult = validateAccess(blockedReq as any, config);
    this.addResult(
      'IP Whitelist - Blocked IP',
      !blockedResult.allowed,
      !blockedResult.allowed ? 'Blocked IP correctly denied' : 'Should have been blocked'
    );
  }

  /**
   * Test domain whitelist validation
   */
  testDomainWhitelist(): void {
    const config: SecurityConfig = {
      allowedIPs: ['127.0.0.1'],
      allowedDomains: ['localhost', 'example.com'],
      allowedOrigins: ['http://localhost:3000', 'https://example.com'],
      developmentMode: false
    };

    // Test allowed domain
    const allowedReq: MockRequest = {
      method: 'GET',
      headers: { origin: 'https://example.com' },
      connection: { remoteAddress: '127.0.0.1' }
    };

    const allowedResult = validateAccess(allowedReq as any, config);
    this.addResult(
      'Domain Whitelist - Allowed Domain',
      allowedResult.allowed,
      allowedResult.allowed ? 'Allowed domain passed' : allowedResult.reason || 'Failed'
    );

    // Test blocked domain
    const blockedReq: MockRequest = {
      method: 'GET',
      headers: { origin: 'https://malicious.com' },
      connection: { remoteAddress: '127.0.0.1' }
    };

    const blockedResult = validateAccess(blockedReq as any, config);
    this.addResult(
      'Domain Whitelist - Blocked Domain',
      !blockedResult.allowed,
      !blockedResult.allowed ? 'Blocked domain correctly denied' : 'Should have been blocked'
    );
  }

  /**
   * Test CORS headers
   */
  testCORSHeaders(): void {
    const config: CORSConfig = {
      allowedOrigins: ['https://example.com', 'http://localhost:3000'],
      allowedMethods: ['GET', 'POST'],
      allowedHeaders: ['Content-Type', 'Authorization'],
      exposedHeaders: [],
      maxAge: 86400,
      credentials: true,
      preflightContinue: false,
      optionsSuccessStatus: 204
    };

    // Test allowed origin
    const allowedReq: MockRequest = {
      method: 'GET',
      headers: { origin: 'https://example.com' }
    };

    const allowedRes = new MockResponse();
    const allowedResult = applyCORSHeaders(allowedReq as any, allowedRes as any, config);
    
    this.addResult(
      'CORS - Allowed Origin',
      allowedResult && allowedRes.getHeaders()['Access-Control-Allow-Origin'] === 'https://example.com',
      allowedResult ? 'CORS headers set correctly' : 'CORS validation failed'
    );

    // Test blocked origin
    const blockedReq: MockRequest = {
      method: 'GET',
      headers: { origin: 'https://malicious.com' }
    };

    const blockedRes = new MockResponse();
    const blockedResult = applyCORSHeaders(blockedReq as any, blockedRes as any, config);
    
    this.addResult(
      'CORS - Blocked Origin',
      !blockedResult,
      !blockedResult ? 'Blocked origin correctly denied' : 'Should have been blocked'
    );
  }

  /**
   * Test development mode bypass
   */
  testDevelopmentMode(): void {
    const config: SecurityConfig = {
      allowedIPs: ['127.0.0.1'],
      allowedDomains: ['localhost'],
      allowedOrigins: ['http://localhost:3000'],
      developmentMode: true
    };

    const req: MockRequest = {
      method: 'GET',
      headers: { origin: 'https://any-domain.com' },
      connection: { remoteAddress: '***********' }
    };

    const result = validateAccess(req as any, config);
    this.addResult(
      'Development Mode - Bypass Security',
      result.allowed,
      result.allowed ? 'Development mode correctly bypassed security' : 'Development mode should bypass security'
    );
  }

  /**
   * Test iframe embedding headers
   */
  testIframeHeaders(): void {
    const res = new MockResponse();
    
    // Simulate setting iframe-friendly headers
    res.set('X-Frame-Options', 'ALLOWALL');
    res.set('Content-Security-Policy', 'frame-ancestors \'self\' https://example.com;');
    
    const headers = res.getHeaders();
    const hasFrameOptions = headers['X-Frame-Options'] === 'ALLOWALL';
    const hasCSP = headers['Content-Security-Policy']?.includes('frame-ancestors');
    
    this.addResult(
      'Iframe Headers - X-Frame-Options',
      hasFrameOptions,
      hasFrameOptions ? 'X-Frame-Options set correctly' : 'X-Frame-Options not set'
    );
    
    this.addResult(
      'Iframe Headers - CSP frame-ancestors',
      hasCSP,
      hasCSP ? 'CSP frame-ancestors set correctly' : 'CSP frame-ancestors not set'
    );
  }

  /**
   * Run all security tests
   */
  runAllTests(): { passed: number; failed: number; total: number; results: Array<{ name: string; passed: boolean; message: string }> } {
    console.log('🔒 Running Security Test Suite...\n');
    
    this.testResults = [];
    
    this.testIPWhitelist();
    this.testDomainWhitelist();
    this.testCORSHeaders();
    this.testDevelopmentMode();
    this.testIframeHeaders();
    
    const passed = this.testResults.filter(r => r.passed).length;
    const failed = this.testResults.filter(r => !r.passed).length;
    const total = this.testResults.length;
    
    console.log(`\n📊 Test Results: ${passed}/${total} passed, ${failed} failed`);
    
    if (failed > 0) {
      console.log('\n❌ Failed Tests:');
      this.testResults.filter(r => !r.passed).forEach(r => {
        console.log(`  - ${r.name}: ${r.message}`);
      });
    }
    
    return { passed, failed, total, results: this.testResults };
  }
}

/**
 * Manual security test function for Cloud Functions
 */
export const runSecurityTests = async (): Promise<any> => {
  const testSuite = new SecurityTestSuite();
  return testSuite.runAllTests();
};

export default SecurityTestSuite;
