/**
 * CORS Configuration for secure cross-origin embedding
 * Manages Cross-Origin Resource Sharing policies for iframe embedding
 */

import { Request, Response } from 'firebase-functions';

export interface CORSConfig {
  allowedOrigins: string[];
  allowedMethods: string[];
  allowedHeaders: string[];
  exposedHeaders: string[];
  maxAge: number;
  credentials: boolean;
  preflightContinue: boolean;
  optionsSuccessStatus: number;
}

// Default CORS configuration for production
export const DEFAULT_CORS_CONFIG: CORSConfig = {
  allowedOrigins: [
    'https://designmycarport.firebaseapp.com',
    'https://designmycarport.web.app',
    'https://website-463606.firebaseapp.com',
    'https://website-463606.web.app',
    // Development origins
    'http://localhost:8080',
    'http://localhost:3000',
    'http://localhost:8084',
    // Add your production domains here
    // 'https://yourdomain.com',
    // 'https://app.yourdomain.com'
  ],
  allowedMethods: [
    'GET',
    'POST',
    'PUT',
    'DELETE',
    'OPTIONS',
    'HEAD'
  ],
  allowedHeaders: [
    'Origin',
    'X-Requested-With',
    'Content-Type',
    'Accept',
    'Authorization',
    'Cache-Control',
    'X-Admin-Key'
  ],
  exposedHeaders: [
    'Content-Length',
    'X-Request-ID',
    'X-Response-Time'
  ],
  maxAge: 86400, // 24 hours
  credentials: true,
  preflightContinue: false,
  optionsSuccessStatus: 204
};

/**
 * Check if origin is allowed
 */
function isOriginAllowed(origin: string, allowedOrigins: string[]): boolean {
  if (!origin) return false;
  
  return allowedOrigins.some(allowed => {
    // Exact match
    if (allowed === origin) return true;
    
    // Wildcard subdomain match (e.g., *.example.com)
    if (allowed.startsWith('*.')) {
      const domain = allowed.substring(2);
      return origin.endsWith(domain);
    }
    
    // Development localhost with any port
    if (allowed.includes('localhost:*') && origin.includes('localhost:')) {
      return true;
    }
    
    return false;
  });
}

/**
 * Apply CORS headers to response
 */
export function applyCORSHeaders(
  req: Request, 
  res: Response, 
  config: CORSConfig = DEFAULT_CORS_CONFIG
): boolean {
  const origin = req.headers.origin as string;
  const requestMethod = req.headers['access-control-request-method'] as string;
  const requestHeaders = req.headers['access-control-request-headers'] as string;
  
  // Check if origin is allowed
  if (origin && isOriginAllowed(origin, config.allowedOrigins)) {
    res.set('Access-Control-Allow-Origin', origin);
  } else if (!origin) {
    // Allow requests without origin (e.g., direct API calls)
    res.set('Access-Control-Allow-Origin', '*');
  } else {
    // Origin not allowed
    return false;
  }
  
  // Set credentials header
  if (config.credentials) {
    res.set('Access-Control-Allow-Credentials', 'true');
  }
  
  // Handle preflight request
  if (req.method === 'OPTIONS') {
    // Check if method is allowed
    if (requestMethod && !config.allowedMethods.includes(requestMethod)) {
      return false;
    }
    
    // Set allowed methods
    res.set('Access-Control-Allow-Methods', config.allowedMethods.join(', '));
    
    // Set allowed headers
    if (requestHeaders) {
      const headers = requestHeaders.split(',').map(h => h.trim());
      const allowedHeaders = headers.filter(h => 
        config.allowedHeaders.some(allowed => 
          allowed.toLowerCase() === h.toLowerCase()
        )
      );
      
      if (allowedHeaders.length === headers.length) {
        res.set('Access-Control-Allow-Headers', config.allowedHeaders.join(', '));
      } else {
        return false;
      }
    } else {
      res.set('Access-Control-Allow-Headers', config.allowedHeaders.join(', '));
    }
    
    // Set max age for preflight cache
    res.set('Access-Control-Max-Age', config.maxAge.toString());
    
    // Set success status for OPTIONS
    res.status(config.optionsSuccessStatus);
    return true;
  }
  
  // Set exposed headers for actual requests
  if (config.exposedHeaders.length > 0) {
    res.set('Access-Control-Expose-Headers', config.exposedHeaders.join(', '));
  }
  
  return true;
}

/**
 * CORS middleware function
 */
export function corsMiddleware(config: Partial<CORSConfig> = {}) {
  const finalConfig = { ...DEFAULT_CORS_CONFIG, ...config };
  
  return (req: Request, res: Response, next: Function) => {
    const allowed = applyCORSHeaders(req, res, finalConfig);
    
    if (!allowed) {
      res.status(403).json({
        error: 'CORS policy violation',
        message: 'Origin not allowed by CORS policy',
        code: 'CORS_VIOLATION'
      });
      return;
    }
    
    // Handle preflight request
    if (req.method === 'OPTIONS') {
      res.send('');
      return;
    }
    
    next();
  };
}

/**
 * Create CORS configuration for specific environments
 */
export function createCORSConfig(environment: 'development' | 'staging' | 'production'): CORSConfig {
  const baseConfig = { ...DEFAULT_CORS_CONFIG };
  
  switch (environment) {
    case 'development':
      return {
        ...baseConfig,
        allowedOrigins: [
          ...baseConfig.allowedOrigins,
          'http://localhost:*',
          'https://localhost:*',
          'http://127.0.0.1:*',
          'https://127.0.0.1:*'
        ]
      };
      
    case 'staging':
      return {
        ...baseConfig,
        allowedOrigins: [
          ...baseConfig.allowedOrigins,
          'https://staging.yourdomain.com',
          'https://staging-website-463606.firebaseapp.com'
        ]
      };
      
    case 'production':
      return {
        ...baseConfig,
        // Remove development origins in production
        allowedOrigins: baseConfig.allowedOrigins.filter(origin => 
          !origin.includes('localhost') && !origin.includes('127.0.0.1')
        )
      };
      
    default:
      return baseConfig;
  }
}

export { CORSConfig };
