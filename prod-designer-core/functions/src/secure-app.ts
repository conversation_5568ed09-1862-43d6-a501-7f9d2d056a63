/**
 * Secure App Cloud Function
 * Serves the application with IP/domain validation for iframe embedding
 */

import * as functions from 'firebase-functions';
import { securityMiddleware, SecurityConfig } from './security-middleware';
import { runSecurityTests } from './security-tests';

// Production security configuration
const PRODUCTION_CONFIG: Partial<SecurityConfig> = {
  allowedIPs: [
    '127.0.0.1',
    '::1',
    // Add your production server IPs here
    // Example: '***********'
  ],
  allowedDomains: [
    'localhost',
    'designmycarport.firebaseapp.com',
    'designmycarport.web.app',
    'website-463606.firebaseapp.com',
    'website-463606.web.app',
    // Add your production domains here
    // Example: 'yourdomain.com'
  ],
  allowedOrigins: [
    'http://localhost:8080',
    'http://localhost:3000',
    'https://designmycarport.firebaseapp.com',
    'https://designmycarport.web.app',
    'https://website-463606.firebaseapp.com',
    'https://website-463606.web.app',
    // Add your production origins here
  ],
  developmentMode: false
};

/**
 * Secure application endpoint with IP/domain validation
 */
export const secureApp = functions.https.onRequest(async (req, res) => {
  // Apply security middleware
  const middleware = securityMiddleware(PRODUCTION_CONFIG);
  
  // Wrap middleware in promise for async handling
  await new Promise<void>((resolve, reject) => {
    middleware(req, res, (error?: any) => {
      if (error) {
        reject(error);
      } else {
        resolve();
      }
    });
  });
  
  // If we reach here, access is allowed
  // Set additional security headers for iframe embedding
  res.set({
    'Content-Security-Policy': `
      frame-ancestors 'self' 
      https://*.firebaseapp.com 
      https://*.web.app 
      https://localhost:* 
      http://localhost:*;
    `.replace(/\s+/g, ' ').trim(),
    'X-Frame-Options': 'ALLOWALL',
    'Access-Control-Allow-Origin': req.headers.origin || '*',
    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Credentials': 'true'
  });
  
  // Handle preflight OPTIONS request
  if (req.method === 'OPTIONS') {
    res.status(204).send('');
    return;
  }
  
  // For GET requests, serve a redirect to the main app
  if (req.method === 'GET') {
    // In a real implementation, you might serve the actual app content here
    // For now, we'll redirect to the main hosting URL
    res.redirect(302, 'https://designmycarport.firebaseapp.com/');
    return;
  }
  
  // For other requests, return success
  res.status(200).json({
    message: 'Access granted',
    timestamp: new Date().toISOString(),
    clientInfo: {
      ip: req.headers['x-forwarded-for'] || req.connection?.remoteAddress,
      origin: req.headers.origin,
      userAgent: req.headers['user-agent']
    }
  });
});

/**
 * Health check endpoint for monitoring
 */
export const healthCheck = functions.https.onRequest(async (req, res) => {
  res.set({
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type'
  });
  
  if (req.method === 'OPTIONS') {
    res.status(204).send('');
    return;
  }
  
  res.status(200).json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: 'prod-designer-core-security'
  });
});

/**
 * Configuration endpoint for updating security settings (admin only)
 */
export const updateConfig = functions.https.onRequest(async (req, res) => {
  // Basic admin authentication (in production, use proper auth)
  const adminKey = req.headers['x-admin-key'];
  const expectedKey = functions.config().admin?.key || 'dev-admin-key';
  
  if (adminKey !== expectedKey) {
    res.status(401).json({ error: 'Unauthorized' });
    return;
  }
  
  if (req.method !== 'POST') {
    res.status(405).json({ error: 'Method not allowed' });
    return;
  }
  
  try {
    const newConfig = req.body;
    // In a real implementation, you'd validate and persist the config
    res.status(200).json({
      message: 'Configuration updated',
      config: newConfig
    });
  } catch (error) {
    res.status(400).json({ error: 'Invalid configuration' });
  }
});

/**
 * Security testing endpoint (admin only)
 */
export const testSecurity = functions.https.onRequest(async (req, res) => {
  // Basic admin authentication
  const adminKey = req.headers['x-admin-key'];
  const expectedKey = functions.config().admin?.key || 'dev-admin-key';

  if (adminKey !== expectedKey) {
    res.status(401).json({ error: 'Unauthorized' });
    return;
  }

  if (req.method !== 'GET') {
    res.status(405).json({ error: 'Method not allowed' });
    return;
  }

  try {
    const testResults = await runSecurityTests();
    res.status(200).json({
      message: 'Security tests completed',
      results: testResults,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      error: 'Security test failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});
