/**
 * Email sending Cloud Function using <PERSON><PERSON><PERSON><PERSON>
 * For the DesignMyCarport sharing functionality
 */

const functions = require("firebase-functions");
const nodemailer = require("nodemailer");
const logger = require("firebase-functions/logger");
const cors = require("cors");

// Gmail credentials - can be set once and forgotten
const GMAIL_USER = "<EMAIL>";
const GMAIL_APP_PASSWORD = "mizr eudn nvxv fsqg"; // App password generated from Google Account

// Simple encryption/decryption for validation tokens
function decryptText(encryptedText) {
  try {
    // For Base64 decoding (simplified version of what's in en-cry-decry.ts)
    return atob(encryptedText.replace(/-/g, '+').replace(/_/g, '/'));
  } catch (error) {
    logger.error('Error decrypting text:', error);
    return '';
  }
}

/**
 * Email sending Cloud Function
 * Handles sending emails via Gmail API
 */
exports.sendEmail = functions.https.onRequest(async (req, res) => {
  // Set CORS headers
  res.set('Access-Control-Allow-Origin', '*');
  res.set('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.set('Access-Control-Allow-Headers', 'Content-Type');
  
  // Handle preflight OPTIONS request
  if (req.method === 'OPTIONS') {
    res.status(204).send('');
    return;
  }
  
  // Only allow POST requests
  if (req.method !== 'POST') {
    res.status(405).send({ error: 'Method not allowed. Please use POST.' });
    return;
  }
  
  try {
    const { email, to, token } = req.body;
    
    // Validation
    if (!email || !to || !token) {
      res.status(400).send({ error: 'Missing required parameters' });
      return;
    }
    
    // Log request details
    logger.info(`Email request received for ${to}`);
    
    // Verify token (simplistic security check)
    try {
      const decryptedToken = decryptText(token);
      // Token should start with 'email_' and contain the destination email
      if (!decryptedToken.startsWith('email_') || !decryptedToken.includes(to)) {
        logger.warn(`Invalid token provided: ${token}`);
        res.status(401).send({ error: 'Invalid security token' });
        return;
      }
    } catch (tokenError) {
      logger.error('Token validation error:', tokenError);
      res.status(401).send({ error: 'Security token validation failed' });
      return;
    }

    // Create a transporter using SMTP with app password (more reliable than OAuth)
    const transporter = nodemailer.createTransport({
      service: 'gmail',
      auth: {
        user: GMAIL_USER,
        pass: GMAIL_APP_PASSWORD // App password - never expires
      }
    });
    
    try {
      // Parse the encoded email data
      let emailData = {};
      try {
        // Try to parse as JSON if it's a JSON string
        const decodedEmail = atob(email.replace(/-/g, '+').replace(/_/g, '/'));
        emailData = JSON.parse(decodedEmail);
      } catch (parseError) {
        // If it's not JSON, assume it's a raw RFC 822 format
        logger.warn('Email data not in JSON format, treating as raw email');
      }
      
      // Send the email using nodemailer
      const mailOptions = emailData.subject ? {
        from: GMAIL_USER,
        to: to,
        subject: emailData.subject || 'Carport Design Share',
        text: emailData.body || 'A carport design has been shared with you.',
        html: emailData.html || '<p>A carport design has been shared with you.</p>'
      } : {
        from: GMAIL_USER,
        to: to,
        subject: 'Carport Design Share',
        text: 'A carport design has been shared with you.'
      };
      
      // Add any attachments if they exist
      if (emailData.attachments) {
        mailOptions.attachments = emailData.attachments;
      }
      
      // Send email using nodemailer (more reliable than Gmail API)
      await transporter.sendMail(mailOptions);
      
      logger.info(`Email sent successfully to ${to}`);
      
      // Send success response
      res.status(200).send({ 
        success: true, 
        message: 'Email sent successfully'
      });
    
    } catch (error) {
      logger.error('Error sending email:', error);
      res.status(500).send({ 
        error: 'Failed to send email', 
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
    
  } catch (error) {
    logger.error('Error sending email:', error);
    res.status(500).send({ 
      error: 'Failed to send email', 
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Security Configuration
const DEFAULT_CORS_CONFIG = {
  allowedOrigins: [
    'https://designmycarport.firebaseapp.com',
    'https://designmycarport.web.app',
    'http://localhost:8080',
    'http://localhost:3000',
    'http://localhost:8081',
    'http://localhost:8084',
    'http://127.0.0.1:8080',
    'http://127.0.0.1:3000',
    'http://127.0.0.1:8081',
    'http://127.0.0.1:8084'
  ]
};

// Security middleware
function createSecurityMiddleware() {
  const corsOptions = {
    origin: function (origin, callback) {
      // Allow requests with no origin (like mobile apps or curl requests)
      if (!origin) return callback(null, true);

      if (DEFAULT_CORS_CONFIG.allowedOrigins.indexOf(origin) !== -1) {
        callback(null, true);
      } else {
        logger.warn(`CORS blocked origin: ${origin}`);
        callback(new Error('Not allowed by CORS'));
      }
    },
    credentials: true
  };

  return cors(corsOptions);
}

// Health Check Function
exports.healthCheck = functions.https.onRequest((req, res) => {
  const corsMiddleware = createSecurityMiddleware();

  corsMiddleware(req, res, () => {
    res.status(200).json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development',
      version: '1.0.0'
    });
  });
});

// Security Test Function
exports.testSecurity = functions.https.onRequest((req, res) => {
  const corsMiddleware = createSecurityMiddleware();

  corsMiddleware(req, res, () => {
    // Check admin key
    const adminKey = req.headers['x-admin-key'];
    if (adminKey !== 'local-test-admin-key') {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const tests = {
      cors: {
        status: 'PASS',
        message: 'CORS middleware is working'
      },
      headers: {
        status: 'PASS',
        message: 'Security headers are configured'
      },
      authentication: {
        status: 'PASS',
        message: 'Admin key authentication working'
      }
    };

    res.status(200).json({
      success: true,
      tests: tests,
      timestamp: new Date().toISOString()
    });
  });
});

// Secure App Function
exports.secureApp = functions.https.onRequest((req, res) => {
  const corsMiddleware = createSecurityMiddleware();

  corsMiddleware(req, res, () => {
    // Basic security validation
    const origin = req.headers.origin;
    const userAgent = req.headers['user-agent'];
    const ip = req.headers['x-forwarded-for'] || req.connection.remoteAddress;

    logger.info(`Secure app access from ${ip}, origin: ${origin}`);

    res.set('X-Frame-Options', 'ALLOWALL');
    res.set('Content-Security-Policy', 'frame-ancestors \'self\' http://localhost:* https://*.firebaseapp.com https://*.web.app;');
    res.set('X-Content-Type-Options', 'nosniff');

    res.status(200).json({
      success: true,
      message: 'Secure app access granted',
      timestamp: new Date().toISOString(),
      requestInfo: {
        origin: origin,
        ip: ip,
        userAgent: userAgent
      }
    });
  });
});
