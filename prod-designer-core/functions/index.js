/**
 * Email sending Cloud Function using <PERSON>demail<PERSON>
 * For the DesignMyCarport sharing functionality
 */

const functions = require("firebase-functions");
const nodemailer = require("nodemailer");
const logger = require("firebase-functions/logger");

// Gmail credentials - can be set once and forgotten
const GMAIL_USER = "<EMAIL>";
const GMAIL_APP_PASSWORD = "mizr eudn nvxv fsqg"; // App password generated from Google Account

// Simple encryption/decryption for validation tokens
function decryptText(encryptedText) {
  try {
    // For Base64 decoding (simplified version of what's in en-cry-decry.ts)
    return atob(encryptedText.replace(/-/g, '+').replace(/_/g, '/'));
  } catch (error) {
    logger.error('Error decrypting text:', error);
    return '';
  }
}

/**
 * Email sending Cloud Function
 * Handles sending emails via Gmail API
 */
exports.sendEmail = functions.https.onRequest(async (req, res) => {
  // Set CORS headers
  res.set('Access-Control-Allow-Origin', '*');
  res.set('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.set('Access-Control-Allow-Headers', 'Content-Type');
  
  // Handle preflight OPTIONS request
  if (req.method === 'OPTIONS') {
    res.status(204).send('');
    return;
  }
  
  // Only allow POST requests
  if (req.method !== 'POST') {
    res.status(405).send({ error: 'Method not allowed. Please use POST.' });
    return;
  }
  
  try {
    const { email, to, token } = req.body;
    
    // Validation
    if (!email || !to || !token) {
      res.status(400).send({ error: 'Missing required parameters' });
      return;
    }
    
    // Log request details
    logger.info(`Email request received for ${to}`);
    
    // Verify token (simplistic security check)
    try {
      const decryptedToken = decryptText(token);
      // Token should start with 'email_' and contain the destination email
      if (!decryptedToken.startsWith('email_') || !decryptedToken.includes(to)) {
        logger.warn(`Invalid token provided: ${token}`);
        res.status(401).send({ error: 'Invalid security token' });
        return;
      }
    } catch (tokenError) {
      logger.error('Token validation error:', tokenError);
      res.status(401).send({ error: 'Security token validation failed' });
      return;
    }

    // Create a transporter using SMTP with app password (more reliable than OAuth)
    const transporter = nodemailer.createTransport({
      service: 'gmail',
      auth: {
        user: GMAIL_USER,
        pass: GMAIL_APP_PASSWORD // App password - never expires
      }
    });
    
    try {
      // Parse the encoded email data
      let emailData = {};
      try {
        // Try to parse as JSON if it's a JSON string
        const decodedEmail = atob(email.replace(/-/g, '+').replace(/_/g, '/'));
        emailData = JSON.parse(decodedEmail);
      } catch (parseError) {
        // If it's not JSON, assume it's a raw RFC 822 format
        logger.warn('Email data not in JSON format, treating as raw email');
      }
      
      // Send the email using nodemailer
      const mailOptions = emailData.subject ? {
        from: GMAIL_USER,
        to: to,
        subject: emailData.subject || 'Carport Design Share',
        text: emailData.body || 'A carport design has been shared with you.',
        html: emailData.html || '<p>A carport design has been shared with you.</p>'
      } : {
        from: GMAIL_USER,
        to: to,
        subject: 'Carport Design Share',
        text: 'A carport design has been shared with you.'
      };
      
      // Add any attachments if they exist
      if (emailData.attachments) {
        mailOptions.attachments = emailData.attachments;
      }
      
      // Send email using nodemailer (more reliable than Gmail API)
      await transporter.sendMail(mailOptions);
      
      logger.info(`Email sent successfully to ${to}`);
      
      // Send success response
      res.status(200).send({ 
        success: true, 
        message: 'Email sent successfully'
      });
    
    } catch (error) {
      logger.error('Error sending email:', error);
      res.status(500).send({ 
        error: 'Failed to send email', 
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
    
  } catch (error) {
    logger.error('Error sending email:', error);
    res.status(500).send({ 
      error: 'Failed to send email', 
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});
