const { google } = require('googleapis');
const fs = require('fs');
const readline = require('readline');

// Gmail API credentials from the secret directory
const credentials = {
  client_id: "683633751065-26d4g02k3uvsb8v5cgcqc4i1m46edv03.apps.googleusercontent.com",
  client_secret: "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",
  redirect_uris: ["http://localhost:8081"]
};

const oauth2Client = new google.auth.OAuth2(
  credentials.client_id,
  credentials.client_secret,
  credentials.redirect_uris[0]
);

// Generate a URL to request access
const scopes = ['https://www.googleapis.com/auth/gmail.send'];
const authUrl = oauth2Client.generateAuthUrl({
  access_type: 'offline',
  scope: scopes,
});

console.log('Authorize this app by visiting this URL:', authUrl);

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

rl.question('Enter the code from that page here: ', (code) => {
  rl.close();
  
  oauth2Client.getToken(code, (err, token) => {
    if (err) return console.error('Error retrieving access token', err);
    
    // Store the token
    fs.writeFileSync('gmail-token.json', JSON.stringify(token, null, 2));
    console.log('Token stored to gmail-token.json');
  });
});