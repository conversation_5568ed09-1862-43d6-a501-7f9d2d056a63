[debug] [2025-07-06T06:19:50.901Z] ----------------------------------------------------------------------
[debug] [2025-07-06T06:19:50.902Z] Command:       /usr/local/bin/node /Users/<USER>/.npm-global/bin/firebase emulators:start --only functions
[debug] [2025-07-06T06:19:50.902Z] CLI Version:   14.9.0
[debug] [2025-07-06T06:19:50.902Z] Platform:      darwin
[debug] [2025-07-06T06:19:50.902Z] Node Version:  v22.17.0
[debug] [2025-07-06T06:19:50.902Z] Time:          Sun Jul 06 2025 16:19:50 GMT+1000 (Australian Eastern Standard Time)
[debug] [2025-07-06T06:19:50.902Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-07-06T06:19:50.903Z] >>> [apiv2][query] GET https://firebase-public.firebaseio.com/cli.json [none]
[debug] [2025-07-06T06:19:50.991Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] Failed to authenticate, have you run firebase login?
[warn] ⚠  emulators: You are not currently authenticated so some features may not work correctly. Please run firebase login to authenticate the CLI. 
[info] i  emulators: Starting emulators: functions {"metadata":{"emulator":{"name":"hub"},"message":"Starting emulators: functions"}}
[debug] [2025-07-06T06:19:51.291Z] [logging] Logging Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-06T06:19:51.291Z] assigned listening specs for emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4000},{"address":"::1","family":"IPv6","port":4000}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}]},"metadata":{"message":"assigned listening specs for emulators"}}
[debug] [2025-07-06T06:19:51.292Z] [hub] writing locator at /var/folders/1c/zc9yz6vj6_d48n9ftlxkylf00000gn/T/hub-designmycarport.json
[debug] [2025-07-06T06:19:51.581Z] [functions] Functions Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-06T06:19:51.582Z] [eventarc] Eventarc Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-06T06:19:51.582Z] [tasks] Cloud Tasks Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-06T06:19:51.582Z] late-assigned ports for functions and eventarc emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4000},{"address":"::1","family":"IPv6","port":4000}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}],"functions":[{"address":"127.0.0.1","family":"IPv4","port":5001}],"eventarc":[{"address":"127.0.0.1","family":"IPv4","port":9299}],"tasks":[{"address":"127.0.0.1","family":"IPv4","port":9499}]},"metadata":{"message":"late-assigned ports for functions and eventarc emulators"}}
[warn] ⚠  functions: The following emulators are not running, calls to these services from the Functions emulator will affect production: apphosting, auth, firestore, database, hosting, pubsub, storage, dataconnect {"metadata":{"emulator":{"name":"functions"},"message":"The following emulators are not running, calls to these services from the Functions emulator will affect production: \u001b[1mapphosting, auth, firestore, database, hosting, pubsub, storage, dataconnect\u001b[22m"}}
[debug] [2025-07-06T06:19:51.583Z] No OAuth tokens found
[debug] [2025-07-06T06:19:51.583Z] No OAuth tokens found
[debug] [2025-07-06T06:19:51.583Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-06T06:19:51.583Z] Failed to get Admin SDK config for designmycarport, falling back to cache Failed to get Admin SDK for Firebase project designmycarport. Please make sure the project exists and your account has permission to access it. {"name":"FirebaseError","children":[],"exit":2,"message":"Failed to get Admin SDK for Firebase project designmycarport. Please make sure the project exists and your account has permission to access it.","original":{"name":"FirebaseError","children":[],"exit":1,"message":"Failed to authenticate, have you run \u001b[1mfirebase login\u001b[22m?","original":{},"status":500},"status":500}
[warn] ⚠  functions: Unable to fetch project Admin SDK configuration, Admin SDK behavior in Cloud Functions emulator may be incorrect. {"metadata":{"emulator":{"name":"functions"},"message":"Unable to fetch project Admin SDK configuration, Admin SDK behavior in Cloud Functions emulator may be incorrect."}}
[info] i  ui: downloading ui-v1.15.0.zip... {"metadata":{"emulator":{"name":"ui"},"message":"downloading ui-v1.15.0.zip..."}}
[debug] [2025-07-06T06:19:51.587Z] >>> [apiv2][query] GET https://storage.googleapis.com/firebase-preview-drop/emulator/ui-v1.15.0.zip 
[debug] [2025-07-06T06:19:52.094Z] <<< [apiv2][status] GET https://storage.googleapis.com/firebase-preview-drop/emulator/ui-v1.15.0.zip 200
[debug] [2025-07-06T06:19:52.094Z] <<< [apiv2][body] GET https://storage.googleapis.com/firebase-preview-drop/emulator/ui-v1.15.0.zip [stream]
[debug] [2025-07-06T06:19:52.180Z] <<< [apiv2][status] GET https://firebase-public.firebaseio.com/cli.json 200
[debug] [2025-07-06T06:19:52.180Z] <<< [apiv2][body] GET https://firebase-public.firebaseio.com/cli.json {"cloudBuildErrorAfter":1594252800000,"cloudBuildWarnAfter":1590019200000,"defaultNode10After":1594252800000,"minVersion":"3.0.5","node8DeploysDisabledAfter":1613390400000,"node8RuntimeDisabledAfter":1615809600000,"node8WarnAfter":1600128000000}
[debug] [2025-07-06T06:19:52.581Z] Data is 3538469
[debug] [2025-07-06T06:19:52.581Z] [unzip] Entry: client/ (compressed_size=0 bytes, uncompressed_size=0 bytes)
[debug] [2025-07-06T06:19:52.581Z] [unzip] Processing entry: client/
[debug] [2025-07-06T06:19:52.581Z] [unzip] mkdir: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client/
[debug] [2025-07-06T06:19:52.581Z] [unzip] Entry: client/favicon-16x16.png (compressed_size=293 bytes, uncompressed_size=293 bytes)
[debug] [2025-07-06T06:19:52.581Z] [unzip] Processing entry: client/favicon-16x16.png
[debug] [2025-07-06T06:19:52.581Z] [unzip] else mkdir: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client
[debug] [2025-07-06T06:19:52.582Z] [unzip] Writing file: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client/favicon-16x16.png
[debug] [2025-07-06T06:19:52.582Z] [unzip] Entry: client/safari-pinned-tab.svg (compressed_size=1433 bytes, uncompressed_size=2611 bytes)
[debug] [2025-07-06T06:19:52.583Z] [unzip] Processing entry: client/safari-pinned-tab.svg
[debug] [2025-07-06T06:19:52.583Z] [unzip] else mkdir: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client
[debug] [2025-07-06T06:19:52.583Z] [unzip] deflating: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client/safari-pinned-tab.svg
[debug] [2025-07-06T06:19:52.585Z] [unzip] Entry: client/favicon.ico (compressed_size=2933 bytes, uncompressed_size=13294 bytes)
[debug] [2025-07-06T06:19:52.585Z] [unzip] Processing entry: client/favicon.ico
[debug] [2025-07-06T06:19:52.585Z] [unzip] else mkdir: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client
[debug] [2025-07-06T06:19:52.585Z] [unzip] deflating: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client/favicon.ico
[debug] [2025-07-06T06:19:52.586Z] [unzip] Entry: client/index.html (compressed_size=1364 bytes, uncompressed_size=3071 bytes)
[debug] [2025-07-06T06:19:52.586Z] [unzip] Processing entry: client/index.html
[debug] [2025-07-06T06:19:52.586Z] [unzip] else mkdir: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client
[debug] [2025-07-06T06:19:52.586Z] [unzip] deflating: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client/index.html
[debug] [2025-07-06T06:19:52.587Z] [unzip] Entry: client/android-chrome-192x192.png (compressed_size=2684 bytes, uncompressed_size=2684 bytes)
[debug] [2025-07-06T06:19:52.587Z] [unzip] Processing entry: client/android-chrome-192x192.png
[debug] [2025-07-06T06:19:52.587Z] [unzip] else mkdir: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client
[debug] [2025-07-06T06:19:52.587Z] [unzip] Writing file: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client/android-chrome-192x192.png
[debug] [2025-07-06T06:19:52.587Z] [unzip] Entry: client/apple-touch-icon.png (compressed_size=2123 bytes, uncompressed_size=2152 bytes)
[debug] [2025-07-06T06:19:52.587Z] [unzip] Processing entry: client/apple-touch-icon.png
[debug] [2025-07-06T06:19:52.587Z] [unzip] else mkdir: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client
[debug] [2025-07-06T06:19:52.587Z] [unzip] deflating: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client/apple-touch-icon.png
[debug] [2025-07-06T06:19:52.588Z] [unzip] Entry: client/android-chrome-512x512.png (compressed_size=5369 bytes, uncompressed_size=5411 bytes)
[debug] [2025-07-06T06:19:52.588Z] [unzip] Processing entry: client/android-chrome-512x512.png
[debug] [2025-07-06T06:19:52.588Z] [unzip] else mkdir: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client
[debug] [2025-07-06T06:19:52.588Z] [unzip] deflating: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client/android-chrome-512x512.png
[debug] [2025-07-06T06:19:52.588Z] [unzip] Entry: client/manifest.json (compressed_size=245 bytes, uncompressed_size=551 bytes)
[debug] [2025-07-06T06:19:52.588Z] [unzip] Processing entry: client/manifest.json
[debug] [2025-07-06T06:19:52.588Z] [unzip] else mkdir: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client
[debug] [2025-07-06T06:19:52.589Z] [unzip] deflating: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client/manifest.json
[debug] [2025-07-06T06:19:52.589Z] [unzip] Entry: client/robots.txt (compressed_size=26 bytes, uncompressed_size=26 bytes)
[debug] [2025-07-06T06:19:52.589Z] [unzip] Processing entry: client/robots.txt
[debug] [2025-07-06T06:19:52.589Z] [unzip] else mkdir: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client
[debug] [2025-07-06T06:19:52.589Z] [unzip] Writing file: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client/robots.txt
[debug] [2025-07-06T06:19:52.589Z] [unzip] Entry: client/mstile-150x150.png (compressed_size=2034 bytes, uncompressed_size=2034 bytes)
[debug] [2025-07-06T06:19:52.589Z] [unzip] Processing entry: client/mstile-150x150.png
[debug] [2025-07-06T06:19:52.589Z] [unzip] else mkdir: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client
[debug] [2025-07-06T06:19:52.590Z] [unzip] Writing file: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client/mstile-150x150.png
[debug] [2025-07-06T06:19:52.590Z] [unzip] Entry: client/assets/ (compressed_size=0 bytes, uncompressed_size=0 bytes)
[debug] [2025-07-06T06:19:52.590Z] [unzip] Processing entry: client/assets/
[debug] [2025-07-06T06:19:52.590Z] [unzip] mkdir: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client/assets/
[debug] [2025-07-06T06:19:52.590Z] [unzip] Entry: client/assets/index-BX-A56oj.js (compressed_size=582089 bytes, uncompressed_size=2092775 bytes)
[debug] [2025-07-06T06:19:52.590Z] [unzip] Processing entry: client/assets/index-BX-A56oj.js
[debug] [2025-07-06T06:19:52.590Z] [unzip] else mkdir: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client/assets
[debug] [2025-07-06T06:19:52.590Z] [unzip] deflating: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client/assets/index-BX-A56oj.js
[debug] [2025-07-06T06:19:52.597Z] [unzip] Entry: client/assets/index-CjB9C900.css (compressed_size=35979 bytes, uncompressed_size=298654 bytes)
[debug] [2025-07-06T06:19:52.597Z] [unzip] Processing entry: client/assets/index-CjB9C900.css
[debug] [2025-07-06T06:19:52.597Z] [unzip] else mkdir: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client/assets
[debug] [2025-07-06T06:19:52.597Z] [unzip] deflating: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client/assets/index-CjB9C900.css
[debug] [2025-07-06T06:19:52.598Z] [unzip] Entry: client/assets/index-BX-A56oj.js.map (compressed_size=2362482 bytes, uncompressed_size=10036679 bytes)
[debug] [2025-07-06T06:19:52.598Z] [unzip] Processing entry: client/assets/index-BX-A56oj.js.map
[debug] [2025-07-06T06:19:52.598Z] [unzip] else mkdir: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client/assets
[debug] [2025-07-06T06:19:52.598Z] [unzip] deflating: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client/assets/index-BX-A56oj.js.map
[debug] [2025-07-06T06:19:52.623Z] [unzip] Entry: client/assets/extensions/ (compressed_size=0 bytes, uncompressed_size=0 bytes)
[debug] [2025-07-06T06:19:52.623Z] [unzip] Processing entry: client/assets/extensions/
[debug] [2025-07-06T06:19:52.623Z] [unzip] mkdir: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client/assets/extensions/
[debug] [2025-07-06T06:19:52.623Z] [unzip] Entry: client/assets/extensions/default-extension.png (compressed_size=1646 bytes, uncompressed_size=1657 bytes)
[debug] [2025-07-06T06:19:52.623Z] [unzip] Processing entry: client/assets/extensions/default-extension.png
[debug] [2025-07-06T06:19:52.623Z] [unzip] else mkdir: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client/assets/extensions
[debug] [2025-07-06T06:19:52.623Z] [unzip] deflating: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client/assets/extensions/default-extension.png
[debug] [2025-07-06T06:19:52.623Z] [unzip] Entry: client/assets/img/ (compressed_size=0 bytes, uncompressed_size=0 bytes)
[debug] [2025-07-06T06:19:52.623Z] [unzip] Processing entry: client/assets/img/
[debug] [2025-07-06T06:19:52.623Z] [unzip] mkdir: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client/assets/img/
[debug] [2025-07-06T06:19:52.624Z] [unzip] Entry: client/assets/img/database.png (compressed_size=29449 bytes, uncompressed_size=29988 bytes)
[debug] [2025-07-06T06:19:52.624Z] [unzip] Processing entry: client/assets/img/database.png
[debug] [2025-07-06T06:19:52.624Z] [unzip] else mkdir: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client/assets/img
[debug] [2025-07-06T06:19:52.624Z] [unzip] deflating: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client/assets/img/database.png
[debug] [2025-07-06T06:19:52.624Z] [unzip] Entry: client/assets/provider-icons/ (compressed_size=0 bytes, uncompressed_size=0 bytes)
[debug] [2025-07-06T06:19:52.624Z] [unzip] Processing entry: client/assets/provider-icons/
[debug] [2025-07-06T06:19:52.624Z] [unzip] mkdir: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client/assets/provider-icons/
[debug] [2025-07-06T06:19:52.624Z] [unzip] Entry: client/assets/provider-icons/auth_service_saml.svg (compressed_size=575 bytes, uncompressed_size=1226 bytes)
[debug] [2025-07-06T06:19:52.624Z] [unzip] Processing entry: client/assets/provider-icons/auth_service_saml.svg
[debug] [2025-07-06T06:19:52.624Z] [unzip] else mkdir: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client/assets/provider-icons
[debug] [2025-07-06T06:19:52.624Z] [unzip] deflating: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client/assets/provider-icons/auth_service_saml.svg
[debug] [2025-07-06T06:19:52.624Z] [unzip] Entry: client/assets/provider-icons/auth_service_phone.svg (compressed_size=261 bytes, uncompressed_size=414 bytes)
[debug] [2025-07-06T06:19:52.624Z] [unzip] Processing entry: client/assets/provider-icons/auth_service_phone.svg
[debug] [2025-07-06T06:19:52.624Z] [unzip] else mkdir: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client/assets/provider-icons
[debug] [2025-07-06T06:19:52.624Z] [unzip] deflating: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client/assets/provider-icons/auth_service_phone.svg
[debug] [2025-07-06T06:19:52.625Z] [unzip] Entry: client/assets/provider-icons/auth_service_facebook.svg (compressed_size=289 bytes, uncompressed_size=457 bytes)
[debug] [2025-07-06T06:19:52.625Z] [unzip] Processing entry: client/assets/provider-icons/auth_service_facebook.svg
[debug] [2025-07-06T06:19:52.625Z] [unzip] else mkdir: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client/assets/provider-icons
[debug] [2025-07-06T06:19:52.625Z] [unzip] deflating: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client/assets/provider-icons/auth_service_facebook.svg
[debug] [2025-07-06T06:19:52.625Z] [unzip] Entry: client/assets/provider-icons/auth_service_game_center.svg (compressed_size=991 bytes, uncompressed_size=3921 bytes)
[debug] [2025-07-06T06:19:52.625Z] [unzip] Processing entry: client/assets/provider-icons/auth_service_game_center.svg
[debug] [2025-07-06T06:19:52.625Z] [unzip] else mkdir: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client/assets/provider-icons
[debug] [2025-07-06T06:19:52.625Z] [unzip] deflating: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client/assets/provider-icons/auth_service_game_center.svg
[debug] [2025-07-06T06:19:52.625Z] [unzip] Entry: client/assets/provider-icons/auth_service_apple.svg (compressed_size=230 bytes, uncompressed_size=334 bytes)
[debug] [2025-07-06T06:19:52.625Z] [unzip] Processing entry: client/assets/provider-icons/auth_service_apple.svg
[debug] [2025-07-06T06:19:52.625Z] [unzip] else mkdir: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client/assets/provider-icons
[debug] [2025-07-06T06:19:52.625Z] [unzip] deflating: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client/assets/provider-icons/auth_service_apple.svg
[debug] [2025-07-06T06:19:52.625Z] [unzip] Entry: client/assets/provider-icons/auth_service_github.svg (compressed_size=466 bytes, uncompressed_size=838 bytes)
[debug] [2025-07-06T06:19:52.625Z] [unzip] Processing entry: client/assets/provider-icons/auth_service_github.svg
[debug] [2025-07-06T06:19:52.625Z] [unzip] else mkdir: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client/assets/provider-icons
[debug] [2025-07-06T06:19:52.625Z] [unzip] deflating: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client/assets/provider-icons/auth_service_github.svg
[debug] [2025-07-06T06:19:52.626Z] [unzip] Entry: client/assets/provider-icons/auth_service_mslive.svg (compressed_size=203 bytes, uncompressed_size=378 bytes)
[debug] [2025-07-06T06:19:52.626Z] [unzip] Processing entry: client/assets/provider-icons/auth_service_mslive.svg
[debug] [2025-07-06T06:19:52.626Z] [unzip] else mkdir: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client/assets/provider-icons
[debug] [2025-07-06T06:19:52.626Z] [unzip] deflating: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client/assets/provider-icons/auth_service_mslive.svg
[debug] [2025-07-06T06:19:52.626Z] [unzip] Entry: client/assets/provider-icons/auth_service_yahoo.svg (compressed_size=577 bytes, uncompressed_size=1182 bytes)
[debug] [2025-07-06T06:19:52.626Z] [unzip] Processing entry: client/assets/provider-icons/auth_service_yahoo.svg
[debug] [2025-07-06T06:19:52.626Z] [unzip] else mkdir: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client/assets/provider-icons
[debug] [2025-07-06T06:19:52.626Z] [unzip] deflating: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client/assets/provider-icons/auth_service_yahoo.svg
[debug] [2025-07-06T06:19:52.626Z] [unzip] Entry: client/assets/provider-icons/auth_service_twitter.svg (compressed_size=444 bytes, uncompressed_size=751 bytes)
[debug] [2025-07-06T06:19:52.626Z] [unzip] Processing entry: client/assets/provider-icons/auth_service_twitter.svg
[debug] [2025-07-06T06:19:52.626Z] [unzip] else mkdir: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client/assets/provider-icons
[debug] [2025-07-06T06:19:52.626Z] [unzip] deflating: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client/assets/provider-icons/auth_service_twitter.svg
[debug] [2025-07-06T06:19:52.626Z] [unzip] Entry: client/assets/provider-icons/auth_service_play_games.svg (compressed_size=565 bytes, uncompressed_size=1173 bytes)
[debug] [2025-07-06T06:19:52.627Z] [unzip] Processing entry: client/assets/provider-icons/auth_service_play_games.svg
[debug] [2025-07-06T06:19:52.627Z] [unzip] else mkdir: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client/assets/provider-icons
[debug] [2025-07-06T06:19:52.627Z] [unzip] deflating: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client/assets/provider-icons/auth_service_play_games.svg
[debug] [2025-07-06T06:19:52.628Z] [unzip] Entry: client/assets/provider-icons/auth_service_email.svg (compressed_size=228 bytes, uncompressed_size=326 bytes)
[debug] [2025-07-06T06:19:52.628Z] [unzip] Processing entry: client/assets/provider-icons/auth_service_email.svg
[debug] [2025-07-06T06:19:52.628Z] [unzip] else mkdir: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client/assets/provider-icons
[debug] [2025-07-06T06:19:52.628Z] [unzip] deflating: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client/assets/provider-icons/auth_service_email.svg
[debug] [2025-07-06T06:19:52.629Z] [unzip] Entry: client/assets/provider-icons/auth_service_google.svg (compressed_size=409 bytes, uncompressed_size=720 bytes)
[debug] [2025-07-06T06:19:52.629Z] [unzip] Processing entry: client/assets/provider-icons/auth_service_google.svg
[debug] [2025-07-06T06:19:52.629Z] [unzip] else mkdir: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client/assets/provider-icons
[debug] [2025-07-06T06:19:52.629Z] [unzip] deflating: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client/assets/provider-icons/auth_service_google.svg
[debug] [2025-07-06T06:19:52.629Z] [unzip] Entry: client/assets/provider-icons/auth_service_oidc.svg (compressed_size=414 bytes, uncompressed_size=858 bytes)
[debug] [2025-07-06T06:19:52.629Z] [unzip] Processing entry: client/assets/provider-icons/auth_service_oidc.svg
[debug] [2025-07-06T06:19:52.629Z] [unzip] else mkdir: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client/assets/provider-icons
[debug] [2025-07-06T06:19:52.629Z] [unzip] deflating: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client/assets/provider-icons/auth_service_oidc.svg
[debug] [2025-07-06T06:19:52.629Z] [unzip] Entry: client/browserconfig.xml (compressed_size=491 bytes, uncompressed_size=822 bytes)
[debug] [2025-07-06T06:19:52.629Z] [unzip] Processing entry: client/browserconfig.xml
[debug] [2025-07-06T06:19:52.629Z] [unzip] else mkdir: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client
[debug] [2025-07-06T06:19:52.629Z] [unzip] deflating: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client/browserconfig.xml
[debug] [2025-07-06T06:19:52.629Z] [unzip] Entry: client/favicon-32x32.png (compressed_size=475 bytes, uncompressed_size=475 bytes)
[debug] [2025-07-06T06:19:52.629Z] [unzip] Processing entry: client/favicon-32x32.png
[debug] [2025-07-06T06:19:52.629Z] [unzip] else mkdir: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client
[debug] [2025-07-06T06:19:52.629Z] [unzip] Writing file: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/client/favicon-32x32.png
[debug] [2025-07-06T06:19:52.630Z] [unzip] Entry: server/ (compressed_size=0 bytes, uncompressed_size=0 bytes)
[debug] [2025-07-06T06:19:52.630Z] [unzip] Processing entry: server/
[debug] [2025-07-06T06:19:52.630Z] [unzip] mkdir: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/server/
[debug] [2025-07-06T06:19:52.630Z] [unzip] Entry: server/server.mjs.map (compressed_size=217845 bytes, uncompressed_size=944978 bytes)
[debug] [2025-07-06T06:19:52.630Z] [unzip] Processing entry: server/server.mjs.map
[debug] [2025-07-06T06:19:52.630Z] [unzip] else mkdir: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/server
[debug] [2025-07-06T06:19:52.630Z] [unzip] deflating: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/server/server.mjs.map
[debug] [2025-07-06T06:19:52.631Z] [unzip] Entry: server/server.mjs (compressed_size=276407 bytes, uncompressed_size=933701 bytes)
[debug] [2025-07-06T06:19:52.632Z] [unzip] Processing entry: server/server.mjs
[debug] [2025-07-06T06:19:52.632Z] [unzip] else mkdir: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/server
[debug] [2025-07-06T06:19:52.632Z] [unzip] deflating: /Users/<USER>/.cache/firebase/emulators/ui-v1.15.0/server/server.mjs
[info] i  functions: Watching "/Users/<USER>/Documents/xproject/prod-designer-core/functions" for Cloud Functions... {"metadata":{"emulator":{"name":"functions"},"message":"Watching \"/Users/<USER>/Documents/xproject/prod-designer-core/functions\" for Cloud Functions..."}}
[debug] [2025-07-06T06:19:52.638Z] Validating nodejs source
[debug] [2025-07-06T06:19:52.872Z] > [functions] package.json contents: {
  "name": "functions",
  "description": "Cloud Functions for Firebase",
  "scripts": {
    "serve": "firebase emulators:start --only functions",
    "shell": "firebase functions:shell",
    "start": "npm run shell",
    "deploy": "firebase deploy --only functions",
    "logs": "firebase functions:log"
  },
  "engines": {
    "node": "22"
  },
  "main": "index.js",
  "dependencies": {
    "firebase-admin": "^12.6.0",
    "firebase-functions": "^6.0.1",
    "googleapis": "^148.0.0",
    "nodemailer": "^6.10.1"
  },
  "devDependencies": {
    "firebase-functions-test": "^3.1.0",
    "typescript": "^4.9.5",
    "@types/node": "^18.15.0"
  },
  "private": true
}
[debug] [2025-07-06T06:19:52.872Z] Building nodejs source
[debug] [2025-07-06T06:19:52.873Z] Failed to find version of module node: reached end of search path /Users/<USER>/Documents/xproject/prod-designer-core/functions/node_modules
[info] ✔  functions: Using node@22 from host. 
[debug] [2025-07-06T06:19:52.873Z] Could not find functions.yaml. Must use http discovery
[debug] [2025-07-06T06:19:52.876Z] Found firebase-functions binary at '/Users/<USER>/Documents/xproject/prod-designer-core/functions/node_modules/.bin/firebase-functions'
[info] Serving at port 8082

[debug] [2025-07-06T06:19:54.148Z] Got response from /__/functions.yaml {"endpoints":{"sendEmail":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"httpsTrigger":{},"entryPoint":"sendEmail"}},"specVersion":"v1alpha1","requiredAPIs":[],"extensions":{}}
[info] ✔  functions: Loaded functions definitions from source: sendEmail. {"metadata":{"emulator":{"name":"functions"},"message":"Loaded functions definitions from source: sendEmail."}}
[info] ✔  functions[us-central1-sendEmail]: http function initialized (http://127.0.0.1:5001/designmycarport/us-central1/sendEmail). {"metadata":{"emulator":{"name":"functions"},"message":"\u001b[1mhttp\u001b[22m function initialized (http://127.0.0.1:5001/designmycarport/us-central1/sendEmail)."}}
[debug] [2025-07-06T06:19:58.164Z] Could not find VSCode notification endpoint: FetchError: request to http://localhost:40001/vscode/notify failed, reason: . If you are not running the Firebase Data Connect VSCode extension, this is expected and not an issue.
[info] 
┌─────────────────────────────────────────────────────────────┐
│ ✔  All emulators ready! It is now safe to connect your app. │
│ i  View Emulator UI at http://127.0.0.1:4000/               │
└─────────────────────────────────────────────────────────────┘

┌───────────┬────────────────┬─────────────────────────────────┐
│ Emulator  │ Host:Port      │ View in Emulator UI             │
├───────────┼────────────────┼─────────────────────────────────┤
│ Functions │ 127.0.0.1:5001 │ http://127.0.0.1:4000/functions │
└───────────┴────────────────┴─────────────────────────────────┘
  Emulator Hub host: 127.0.0.1 port: 4400
  Other reserved ports: 4500

Issues? Report them at https://github.com/firebase/firebase-tools/issues and attach the *-debug.log files.
 
[debug] [2025-07-06T06:20:57.516Z] File /Users/<USER>/Documents/xproject/prod-designer-core/functions/index.js changed, reloading triggers {"metadata":{"emulator":{"name":"functions"},"message":"File /Users/<USER>/Documents/xproject/prod-designer-core/functions/index.js changed, reloading triggers"}}
[debug] [2025-07-06T06:20:58.518Z] Validating nodejs source
[debug] [2025-07-06T06:20:59.006Z] > [functions] package.json contents: {
  "name": "functions",
  "description": "Cloud Functions for Firebase",
  "scripts": {
    "serve": "firebase emulators:start --only functions",
    "shell": "firebase functions:shell",
    "start": "npm run shell",
    "deploy": "firebase deploy --only functions",
    "logs": "firebase functions:log"
  },
  "engines": {
    "node": "22"
  },
  "main": "index.js",
  "dependencies": {
    "firebase-admin": "^12.6.0",
    "firebase-functions": "^6.0.1",
    "googleapis": "^148.0.0",
    "nodemailer": "^6.10.1"
  },
  "devDependencies": {
    "firebase-functions-test": "^3.1.0",
    "typescript": "^4.9.5",
    "@types/node": "^18.15.0"
  },
  "private": true
}
[debug] [2025-07-06T06:20:59.006Z] Building nodejs source
[debug] [2025-07-06T06:20:59.006Z] Failed to find version of module node: reached end of search path /Users/<USER>/Documents/xproject/prod-designer-core/functions/node_modules
[info] ✔  functions: Using node@22 from host. 
[debug] [2025-07-06T06:20:59.006Z] Could not find functions.yaml. Must use http discovery
[debug] [2025-07-06T06:20:59.007Z] Found firebase-functions binary at '/Users/<USER>/Documents/xproject/prod-designer-core/functions/node_modules/.bin/firebase-functions'
[info] Serving at port 8336

[debug] [2025-07-06T06:20:59.137Z] Got response from /__/functions.yaml {"endpoints":{"sendEmail":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"httpsTrigger":{},"entryPoint":"sendEmail"}},"specVersion":"v1alpha1","requiredAPIs":[],"extensions":{}}
[info] ✔  functions: Loaded functions definitions from source: sendEmail. {"metadata":{"emulator":{"name":"functions"},"message":"Loaded functions definitions from source: sendEmail."}}
[debug] [2025-07-06T06:21:22.215Z] File /Users/<USER>/Documents/xproject/prod-designer-core/functions/index.js changed, reloading triggers {"metadata":{"emulator":{"name":"functions"},"message":"File /Users/<USER>/Documents/xproject/prod-designer-core/functions/index.js changed, reloading triggers"}}
[debug] [2025-07-06T06:21:23.215Z] Validating nodejs source
[debug] [2025-07-06T06:21:23.656Z] > [functions] package.json contents: {
  "name": "functions",
  "description": "Cloud Functions for Firebase",
  "scripts": {
    "serve": "firebase emulators:start --only functions",
    "shell": "firebase functions:shell",
    "start": "npm run shell",
    "deploy": "firebase deploy --only functions",
    "logs": "firebase functions:log"
  },
  "engines": {
    "node": "22"
  },
  "main": "index.js",
  "dependencies": {
    "firebase-admin": "^12.6.0",
    "firebase-functions": "^6.0.1",
    "googleapis": "^148.0.0",
    "nodemailer": "^6.10.1"
  },
  "devDependencies": {
    "firebase-functions-test": "^3.1.0",
    "typescript": "^4.9.5",
    "@types/node": "^18.15.0"
  },
  "private": true
}
[debug] [2025-07-06T06:21:23.656Z] Building nodejs source
[debug] [2025-07-06T06:21:23.656Z] Failed to find version of module node: reached end of search path /Users/<USER>/Documents/xproject/prod-designer-core/functions/node_modules
[info] ✔  functions: Using node@22 from host. 
[debug] [2025-07-06T06:21:23.657Z] Could not find functions.yaml. Must use http discovery
[debug] [2025-07-06T06:21:23.658Z] Found firebase-functions binary at '/Users/<USER>/Documents/xproject/prod-designer-core/functions/node_modules/.bin/firebase-functions'
[info] Serving at port 8822

[debug] [2025-07-06T06:21:23.790Z] Got response from /__/functions.yaml {"endpoints":{"sendEmail":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"httpsTrigger":{},"entryPoint":"sendEmail"},"healthCheck":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"httpsTrigger":{},"entryPoint":"healthCheck"},"testSecurity":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"httpsTrigger":{},"entryPoint":"testSecurity"},"secureApp":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"httpsTrigger":{},"entryPoint":"secureApp"}},"specVersion":"v1alpha1","requiredAPIs":[],"extensions":{}}
[info] ✔  functions: Loaded functions definitions from source: sendEmail, healthCheck, testSecurity, secureApp. {"metadata":{"emulator":{"name":"functions"},"message":"Loaded functions definitions from source: sendEmail, healthCheck, testSecurity, secureApp."}}
[info] ✔  functions[us-central1-healthCheck]: http function initialized (http://127.0.0.1:5001/designmycarport/us-central1/healthCheck). {"metadata":{"emulator":{"name":"functions"},"message":"\u001b[1mhttp\u001b[22m function initialized (http://127.0.0.1:5001/designmycarport/us-central1/healthCheck)."}}
[info] ✔  functions[us-central1-testSecurity]: http function initialized (http://127.0.0.1:5001/designmycarport/us-central1/testSecurity). {"metadata":{"emulator":{"name":"functions"},"message":"\u001b[1mhttp\u001b[22m function initialized (http://127.0.0.1:5001/designmycarport/us-central1/testSecurity)."}}
[info] ✔  functions[us-central1-secureApp]: http function initialized (http://127.0.0.1:5001/designmycarport/us-central1/secureApp). {"metadata":{"emulator":{"name":"functions"},"message":"\u001b[1mhttp\u001b[22m function initialized (http://127.0.0.1:5001/designmycarport/us-central1/secureApp)."}}
[debug] [2025-07-06T06:21:33.550Z] File /Users/<USER>/Documents/xproject/prod-designer-core/functions/package.json changed, reloading triggers {"metadata":{"emulator":{"name":"functions"},"message":"File /Users/<USER>/Documents/xproject/prod-designer-core/functions/package.json changed, reloading triggers"}}
[debug] [2025-07-06T06:21:33.651Z] File /Users/<USER>/Documents/xproject/prod-designer-core/functions/package-lock.json changed, reloading triggers {"metadata":{"emulator":{"name":"functions"},"message":"File /Users/<USER>/Documents/xproject/prod-designer-core/functions/package-lock.json changed, reloading triggers"}}
[debug] [2025-07-06T06:21:34.653Z] Validating nodejs source
[debug] [2025-07-06T06:21:35.052Z] > [functions] package.json contents: {
  "name": "functions",
  "description": "Cloud Functions for Firebase",
  "scripts": {
    "serve": "firebase emulators:start --only functions",
    "shell": "firebase functions:shell",
    "start": "npm run shell",
    "deploy": "firebase deploy --only functions",
    "logs": "firebase functions:log"
  },
  "engines": {
    "node": "22"
  },
  "main": "index.js",
  "dependencies": {
    "cors": "^2.8.5",
    "firebase-admin": "^12.6.0",
    "firebase-functions": "^6.0.1",
    "googleapis": "^148.0.0",
    "nodemailer": "^6.10.1"
  },
  "devDependencies": {
    "@types/node": "^18.15.0",
    "firebase-functions-test": "^3.1.0",
    "typescript": "^4.9.5"
  },
  "private": true
}
[debug] [2025-07-06T06:21:35.052Z] Building nodejs source
[debug] [2025-07-06T06:21:35.052Z] Failed to find version of module node: reached end of search path /Users/<USER>/Documents/xproject/prod-designer-core/functions/node_modules
[info] ✔  functions: Using node@22 from host. 
[debug] [2025-07-06T06:21:35.053Z] Could not find functions.yaml. Must use http discovery
[debug] [2025-07-06T06:21:35.053Z] Found firebase-functions binary at '/Users/<USER>/Documents/xproject/prod-designer-core/functions/node_modules/.bin/firebase-functions'
[info] Serving at port 8422

[debug] [2025-07-06T06:21:35.182Z] Got response from /__/functions.yaml {"endpoints":{"sendEmail":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"httpsTrigger":{},"entryPoint":"sendEmail"},"healthCheck":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"httpsTrigger":{},"entryPoint":"healthCheck"},"testSecurity":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"httpsTrigger":{},"entryPoint":"testSecurity"},"secureApp":{"availableMemoryMb":null,"timeoutSeconds":null,"minInstances":null,"maxInstances":null,"ingressSettings":null,"concurrency":null,"serviceAccountEmail":null,"vpc":null,"platform":"gcfv2","labels":{},"httpsTrigger":{},"entryPoint":"secureApp"}},"specVersion":"v1alpha1","requiredAPIs":[],"extensions":{}}
[info] ✔  functions: Loaded functions definitions from source: sendEmail, healthCheck, testSecurity, secureApp. {"metadata":{"emulator":{"name":"functions"},"message":"Loaded functions definitions from source: sendEmail, healthCheck, testSecurity, secureApp."}}
[debug] [2025-07-06T06:21:39.580Z] Received signal SIGHUP 1
[info]  
[info] i  emulators: Received SIGHUP for the first time. Starting a clean shutdown. 
[info] i  emulators: Please wait for a clean shutdown or send the SIGHUP signal again to stop right now. 
[debug] [2025-07-06T06:21:39.582Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.583Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.583Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.583Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.583Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.584Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.584Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.584Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.584Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.585Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.585Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.585Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.585Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.585Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.585Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.586Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.586Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.586Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.586Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.586Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.586Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.587Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.587Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.587Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.587Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.587Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.587Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.587Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.588Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.588Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.588Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.588Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.588Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.588Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.588Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.589Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.589Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.589Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.589Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.589Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.589Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.589Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.589Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.590Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.590Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.590Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.590Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.590Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.590Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.590Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.591Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.591Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.591Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.591Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.591Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.591Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.591Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.591Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.592Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.592Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.592Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.592Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.592Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.592Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.592Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.592Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.592Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.593Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.593Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.593Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.593Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.593Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.593Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.593Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.594Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.594Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.594Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.594Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.594Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.594Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.594Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.594Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.595Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.595Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.596Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.596Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.596Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.596Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.596Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.596Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.596Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.596Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.597Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.597Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.597Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.597Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.597Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.597Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.597Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.597Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.597Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.598Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.598Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.598Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.598Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.598Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.598Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[info] i  emulators: Shutting down emulators. {"metadata":{"emulator":{"name":"hub"},"message":"Shutting down emulators."}}
[info] i  ui: Stopping Emulator UI {"metadata":{"emulator":{"name":"ui"},"message":"Stopping Emulator UI"}}
[info] i  functions: Stopping Functions Emulator {"metadata":{"emulator":{"name":"functions"},"message":"Stopping Functions Emulator"}}
[info] i  eventarc: Stopping Eventarc Emulator {"metadata":{"emulator":{"name":"eventarc"},"message":"Stopping Eventarc Emulator"}}
[info] i  tasks: Stopping Cloud Tasks Emulator {"metadata":{"emulator":{"name":"tasks"},"message":"Stopping Cloud Tasks Emulator"}}
[info] i  hub: Stopping emulator hub {"metadata":{"emulator":{"name":"hub"},"message":"Stopping emulator hub"}}
[debug] [2025-07-06T06:21:39.600Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.600Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.600Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.601Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.601Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.601Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.601Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.601Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.601Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.601Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.601Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.601Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.602Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.602Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.602Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.602Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.602Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.602Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.602Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.602Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.603Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.603Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.603Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.603Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.603Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.603Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.603Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.603Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.603Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.604Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.604Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.604Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.604Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.604Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.604Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.604Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.604Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.605Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.605Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.605Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.605Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.605Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.605Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.605Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.605Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.606Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.606Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.606Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.606Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.606Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.606Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.606Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.606Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.606Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.606Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.607Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.607Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.607Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.607Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.607Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.607Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.607Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.607Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.607Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.607Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.607Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.608Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[info] i  logging: Stopping Logging Emulator {"metadata":{"emulator":{"name":"logging"},"message":"Stopping Logging Emulator"}}
[debug] [2025-07-06T06:21:39.609Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.609Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.609Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.609Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.609Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.609Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.609Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.609Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.609Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.609Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.609Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.610Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.610Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.610Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.610Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.610Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.610Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.610Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.610Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.610Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.610Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.611Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.611Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.611Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.611Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.611Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.611Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.611Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.611Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.611Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.611Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.611Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.611Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.611Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.611Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.612Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.612Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.612Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.612Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.612Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.612Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.612Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.612Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.612Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.612Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.612Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.612Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.612Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.612Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.613Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.613Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.613Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.613Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.613Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.613Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.613Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.613Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.613Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.613Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.613Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.613Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.613Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.613Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.613Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.614Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.614Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.614Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.614Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.614Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.614Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.615Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.615Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.615Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.615Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.615Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.615Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.615Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.615Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.615Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.615Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.615Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.615Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.615Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.616Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.616Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.616Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.616Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.616Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.616Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.616Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.616Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.616Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.616Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.616Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.616Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.616Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
[error] 
[error] Error: An unexpected error has occurred.
[debug] [2025-07-06T06:21:39.616Z] Error: write EIO
    at afterWriteDispatched (node:internal/stream_base_commons:159:15)
    at writeGeneric (node:internal/stream_base_commons:150:3)
    at Socket._writeGeneric (node:net:966:11)
    at Socket._write (node:net:978:8)
    at writeOrBuffer (node:internal/streams/writable:572:12)
    at _write (node:internal/streams/writable:501:10)
    at Writable.write (node:internal/streams/writable:510:10)
    at Console.log (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston/lib/winston/transports/console.js:87:23)
    at Console._write (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/modern.js:103:17)
    at doWrite (/Users/<USER>/.npm-global/lib/node_modules/firebase-tools/node_modules/winston-transport/node_modules/readable-stream/lib/_stream_writable.js:390:139)
