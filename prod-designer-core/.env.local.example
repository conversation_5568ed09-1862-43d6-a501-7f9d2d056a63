# Local Development Environment Configuration
# Copy this file to .env.local and modify as needed

# Environment
NODE_ENV=development
SECURITY_ENABLED=true
DEVELOPMENT_MODE=true

# Local Network Configuration
# Add your machine's IP address for testing from other devices
# Find your IP with: ipconfig (Windows) or ifconfig (Mac/Linux)
ALLOWED_IPS=127.0.0.1,::1,localhost,***********/24,10.0.0.0/24

# Local Domains
ALLOWED_DOMAINS=localhost,127.0.0.1

# Local Origins - Add ports for all your local development servers
ALLOWED_ORIGINS=http://localhost:8080,http://localhost:3000,http://localhost:8084,http://127.0.0.1:8080,http://127.0.0.1:3000,http://127.0.0.1:8084

# Admin Configuration for Testing
ADMIN_KEY=local-test-admin-key

# Firebase Configuration
FIREBASE_PROJECT_ID=designmycarport
FIREBASE_REGION=us-central1

# Logging Configuration
LOG_LEVEL=debug
ENABLE_ACCESS_LOGS=true

# Rate Limiting (relaxed for development)
RATE_LIMIT_WINDOW=60000
RATE_LIMIT_MAX_REQUESTS=1000

# Development Settings
ALLOW_LOCALHOST=true

# Testing Configuration
ENABLE_SECURITY_TESTS=true
TEST_MODE=true
