export interface TemplateOption {
  id: string;
  name: string;
  description: string;
  imageSrc: string | any;
  settings: {
    dimensions: {
      length?: number;
      span?: number;
      height?: number;
      pitch?: number;
      overhang?: number;
      roofType?: 'Gable' | 'Skillion' | 'Skillion Attached' | 'Skillion Overhang';
    };
    sceneSettings: {
      leftLeanTo?: boolean;
      leftLeanToSpan?: number;
      leftLeanToDropHeight?: number;
      rightLeanTo?: boolean;
      rightLeanToSpan?: number;
      rightLeanToDropHeight?: number;
      rightLeanToAttached?: boolean;
      roofColor?: string;
      wallColor?: string;
      trimColor?: string;
    };
  };
}

export const templateOptions: TemplateOption[] = [
  {
    id: 'flat-carport',
    name: 'Skillion Roof Carport',
    description: 'Standard skillion roof carport with no lean-to.',
    imageSrc: require('../../assets/LoadingImage/SkillionOverhangNoleanto.png'),
    settings: {
      dimensions: {
        length: 5500,
        span: 5400,
        height: 2700,
        pitch: 2,
        overhang: 600,
        roofType: 'Skillion',
      },
      sceneSettings: {
        leftLeanTo: false,
        rightLeanTo: false,
        roofColor: 'NightSky',
        wallColor: 'Classic Cream',
        trimColor: 'Surfmist',
      },
    },
  },
  {
    id: 'gable-carport',
    name: 'Gable Roof Carport',
    description: 'Classic gable roof design with pitched roof.',
    imageSrc: require('../../assets/LoadingImage/Gable5.5x5.4.jpg'),
    settings: {
      dimensions: {
        length: 5500,
        span: 5400,
        height: 2400,
        pitch: 15,
        overhang: 0,
        roofType: 'Gable',
      },
      sceneSettings: {
        leftLeanTo: false,
        rightLeanTo: false,
        roofColor: 'Nigth Sky',
        wallColor: 'Classic Cream',
        trimColor: 'Surfmist',
      },
    },
  },
  {
    id: 'skillion-carport',
    name: 'Skillion Roof Carport with Lean-to',
    description: 'Modern skillion roof with Right Lean-to.',
    imageSrc: require('../../assets/LoadingImage/SkillionOHRightleanto.jpg'),
    settings: {
      dimensions: {
        length: 6000,
        span: 6000,
        height: 2700,
        pitch: 5,
        overhang: 600,
        roofType: 'Skillion',
      },
      sceneSettings: {
        leftLeanTo: false,
        rightLeanTo: true,
        rightLeanToSpan: 2500,
        rightLeanToDropHeight: 500,
        rightLeanToAttached: false,
        roofColor: 'NightSky',
        wallColor: 'Classic Cream',
        trimColor: 'Surfmist',
      },
    },
  },
  {
    id: 'gable-leanto',
    name: 'Gable Roof with Lean-to',
    description: 'Gable roof carport with right lean-to extension.',
    imageSrc: require('../../assets/LoadingImage/GableRightleanto.jpg'),
    settings: {
      dimensions: {
        length: 5500,
        span: 5400,
        height: 2400,
        pitch: 15,
        overhang: 0,
        roofType: 'Gable',
      },
      sceneSettings: {
        leftLeanTo: false,
        rightLeanTo: true,
        rightLeanToSpan: 1500,
        rightLeanToDropHeight: 500,
        rightLeanToAttached: false,
        roofColor: 'NightSky',
        wallColor: 'Classic Cream',
        trimColor: 'Surfmist',
      },
    },
  },
  {
    id: 'gable-leanto2',
    name: 'Gable Roof with Lean-to',
    description: 'Gable roof carport with left and right lean-to extension.',
    imageSrc: require('../../assets/LoadingImage/Gablebothleanto.jpg'),
    settings: {
      dimensions: {
        length: 6000,
        span: 6000,
        height: 2400,
        pitch: 15,
        overhang: 0,
        roofType: 'Gable',
      },
      sceneSettings: {
        leftLeanTo: true,
        leftLeanToSpan: 1500,
        leftLeanToDropHeight: 500,
        rightLeanTo: true,
        rightLeanToSpan: 1500,
        rightLeanToDropHeight: 500,
        rightLeanToAttached: false,
        roofColor: 'NightSky',
        wallColor: 'Classic Cream',
        trimColor: 'Surfmist',
      },
    },
  },
  {
    id: 'skillion-attached',
    name: 'Skillion Attached',
    description: 'Skillion roof attached to an existing structure.',
    imageSrc: require('../../assets/LoadingImage/AttachedSkillion.jpg'),
    settings: {
      dimensions: {
        length: 6000,
        span: 6000,
        height: 2400,
        pitch: 2,
        overhang: 0,
        roofType: 'Skillion Attached',
      },
      sceneSettings: {
        leftLeanTo: false,
        rightLeanTo: true,
        rightLeanToSpan: 1500,
        rightLeanToDropHeight: 500,
        rightLeanToAttached: true,
        roofColor: 'NightSky',
        wallColor: 'Classic Cream',
        trimColor: 'Surfmist',
      },
    },
  },
];
