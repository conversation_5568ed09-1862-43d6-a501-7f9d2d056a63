export const post_size = 75;
export const EP_web = 152;
export const EP_flange = 64;
export const Rafter_web = 152;
export const Rafter_flange = 64;
export const Footing_size = 300;
export const Footing_depth = 600;
export const Roof_sheet_width = 762;
export const Gutter_offset = 20;
export const Roof_monoclad_height = 29;
export const Roof_monoclad_thickness = 5.5;
export const Purlin_row_n = 6;
export const Gable_purlin_row_n = 4;
export const knee_web = 50;
export const knee_flange = 102;
export const apex_web = 50;
export const apex_flange = 102;
// Z-purlins/girts typically 100-150mm deep, but need to scale for the scene
export const Girt_web = 75; // Overall width/depth in mm (scaled down for scene)
export const Girt_flangeE = 25;  // Overall height in mm (scaled down for scene)
export const Girt_flangeF = 24;  // Overall height in mm (scaled down for scene)
export const Girt_lip = 10;  // Overall height in mm (scaled down for scene)
export const Girt_thickness = 1.0; // Material thickness in mm

