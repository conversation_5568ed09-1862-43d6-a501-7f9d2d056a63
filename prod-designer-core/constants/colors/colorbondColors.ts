/**
 * Colorbond steel colors with their hex values
 * These are approximations of the actual Colorbond colors
 */

export interface ColorbondColor {
  name: string;
  value: string;
  hex: string;
}

export const colorbondColors: ColorbondColor[] = [
  { name: 'Surfmist', value: 'Surfmist', hex: '#E6E8E3' },
  { name: 'Classic Cream', value: 'ClassicCream', hex: '#E8DDB5' },
  { name: 'Paperbark', value: 'Paperbark', hex: '#D5CCBA' },
  { name: 'Evening Haze', value: 'EveningHaze', hex: '#BDBEB7' },
  { name: '<PERSON><PERSON> <PERSON>', value: 'Shale<PERSON><PERSON>', hex: '#B2B4B3' },
  { name: 'Dune', value: 'Dune', hex: '#C2BEB2' },
  { name: 'Cove', value: 'Cove', hex: '#A2A9A6' },
  { name: 'Windspray', value: 'Windspray', hex: '#8A9091' },
  { name: 'Pale Eucalypt', value: 'PaleEucalypt', hex: '#7F8B7A' },
  { name: 'Wilderness', value: 'Wilderness', hex: '#616A58' },
  { name: 'Cottage Green', value: 'CottageGreen', hex: '#3E5141' },
  { name: 'Mangrove', value: 'Mangrove', hex: '#5B6259' },
  { name: 'Woodland Grey', value: 'WoodlandGrey', hex: '#5E6059' },
  { name: 'Deep Ocean', value: 'DeepOcean', hex: '#1F3346' },
  { name: 'Ironstone', value: 'Ironstone', hex: '#6E4C4B' },
  { name: 'Night Sky', value: 'NightSky', hex: '#2B3443' },
  { name: 'Monument', value: 'Monument', hex: '#3C3C3C' },
  { name: 'Basalt', value: 'Basalt', hex: '#3F4444' },
  { name: 'Manor Red', value: 'ManorRed', hex: '#722A2D' },
  { name: 'Terrain', value: 'Terrain', hex: '#7C4D3A' },
  { name: 'Jasper', value: 'Jasper', hex: '#A35453' },
];
