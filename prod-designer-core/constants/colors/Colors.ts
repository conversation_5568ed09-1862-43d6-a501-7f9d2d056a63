/**
 * Below are the colors that are used in the app. The colors are defined in the light and dark mode.
 * There are many other ways to style your app. For example, [Nativewind](https://www.nativewind.dev/), [Tamagui](https://tamagui.dev/), [unistyles](https://reactnativeunistyles.vercel.app), etc.
 */

const tintColorLight = '#0a7ea4';
const tintColorDark = '#fff';

const accentColorLight1 = '#3b82f6';
const accentColorDark1 = '#3b82f6';

const accentColorLight2 = '#8ea38c';
const accentColorDark2 = '#8ea38c';

export const Colors = {
  light: {
    text: '#11181C',
    background: '#fff',
    tint: tintColorLight,
    icon: '#687076',
    tabIconDefault: '#687076',
    tabIconSelected: tintColorLight,
    accentColor: accentColorLight1,
  },
  dark: {
    text: '#ECEDEE',
    background: '#303035',
    tint: tintColorDark,
    icon: '#9BA1A6',
    tabIconDefault: '#9BA1A6',
    tabIconSelected: tintColorDark,
    accentColor: accentColorDark1,
  },
};
