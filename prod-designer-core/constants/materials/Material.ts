import { THREE } from 'expo-three';

export enum CarportMaterialType {
    Post = 'post',
    Rafter = 'rafter',
    Footing = 'footing',
    Slab = 'slab',
    Roof = 'roof',
    BrickWall = 'brickWall',
    ZincFinish = 'zincFinish',
    Cap = 'cap', // New material type for ridge caps and barge caps
}  
    
export type CarportMaterial = {
    color: number;
    metalness?: number;
    roughness?: number;
};

export const MaterialsList : { [key in CarportMaterialType] : CarportMaterial} = {
    [CarportMaterialType.Post]: {
        color: 0xc0c0c0,
        metalness: 1,
        roughness: 0.2,
    },
    [CarportMaterialType.Footing]: {
        color: 0x909090, // Darker gray for footings
        metalness: 0.05, // Almost no metalness for concrete
        roughness: 0.98, // Very rough concrete
    },
    [CarportMaterialType.Rafter]: {
        color: 0xb0c4de,
        metalness: 0.8,
        roughness: 0.4,
    },
    [CarportMaterialType.Roof]: {
        color: 0x808080
    },
    [CarportMaterialType.Slab]: {
        color: 0xb0b0b0, // Medium gray for concrete slab
        metalness: 0.03, // Very slight metalness
        roughness: 0.92, // High roughness for realistic concrete
    },
    [CarportMaterialType.BrickWall]: {
        color: 0xc84a2e, // Rich terracotta color (more vibrant orange-red)
        metalness: 0.0,
        roughness: 0.9, // Slightly reduced roughness for better color visibility
    },
    [CarportMaterialType.ZincFinish]: {
        color: 0xd2d7dc, // Brighter zinc/galvanized steel color (silvery with slight blue tint)
        metalness: 0.9,
        roughness: 0.3, // Slightly smoother for a more polished look
    },
    [CarportMaterialType.Cap]: {
        color: 0xd0d0d0, // Default silver color for caps (ridge caps and barge caps)
        metalness: 0.8,
        roughness: 0.3, // Smoother finish for caps
    },
}

export function createMaterial(materialType: CarportMaterialType): THREE.MeshStandardMaterial {
  return new THREE.MeshStandardMaterial({ ...MaterialsList[materialType] });
}
