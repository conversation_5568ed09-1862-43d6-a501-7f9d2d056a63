import { ObjectOption } from '../components/ui/ObjectPicker';

// Object options with placeholder image URLs
// In a real application, these would be actual image URLs or require statements
export const objectOptions: ObjectOption[] = [
  {
    id: 'car',
    name: '5 Seater Car',
    imageUrl: 'https://cdn-icons-png.flaticon.com/512/3774/3774278.png',
  },
  {
    id: 'motorbike',
    name: 'Motorbike',
    imageUrl: 'https://cdn-icons-png.flaticon.com/512/2972/2972185.png',
  },
  {
    id: 'ute',
    name: 'Ute',
    imageUrl: 'https://cdn-icons-png.flaticon.com/512/2076/2076528.png',
  },
  {
    id: 'trailer',
    name: 'Trailer',
    imageUrl: 'https://cdn-icons-png.flaticon.com/512/5052/5052130.png',
  },
  {
    id: 'mobileHome',
    name: 'Mobile Home',
    imageUrl: 'https://cdn-icons-png.flaticon.com/512/1867/1867956.png',
  },
  {
    id: 'forklift',
    name: 'Forklift',
    imageUrl: 'https://cdn-icons-png.flaticon.com/512/2382/2382461.png',
  },
  {
    id: 'lawnMower',
    name: 'Lawn Mower',
    imageUrl: 'https://cdn-icons-png.flaticon.com/512/1886/1886097.png',
  },
  {
    id: 'scissorLift',
    name: 'Scissor Lift',
    imageUrl: 'https://cdn-icons-png.flaticon.com/512/2906/2906179.png',
  },
  {
    id: 'boat',
    name: 'Boat',
    imageUrl: 'https://cdn-icons-png.flaticon.com/512/2065/2065007.png',
  },
];
