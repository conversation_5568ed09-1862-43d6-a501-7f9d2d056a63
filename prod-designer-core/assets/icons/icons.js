// This file exports all the icon images for direct use in the application
// Using PNG format for maximum compatibility with all platforms

export default {
  // Control icons
  'chevron-left': require('./png/chevron-left.png'),
  'chevron-right': require('./png/chevron-right.png'),
  'straighten': require('./png/straighten.png'),
  'view-quilt': require('./png/view-quilt.png'),
  'grid-on': require('./png/grid-on.png'),
  'settings': require('./png/settings.png'),
  'palette': require('./png/palette.png'),
  'description': require('./png/description.png'),
  
  // Utility icons
  'add': require('./png/add.png'),
  'save': require('./png/save.png'),
  'folder-open': require('./png/folder-open.png'),
  'share': require('./png/share.png'),
  'download': require('./png/download.png'),
};
