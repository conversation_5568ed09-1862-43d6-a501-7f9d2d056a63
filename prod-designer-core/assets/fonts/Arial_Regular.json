{"glyphs": {"0": {"ha": 772, "x_min": 58, "x_max": 706, "o": "m 58 490 q 94 774 58 667 q 202 940 130 882 q 382 998 273 998 q 522 966 462 998 q 622 873 583 934 q 684 725 661 812 q 706 490 706 638 q 670 208 706 315 q 563 42 634 100 q 382 -17 491 -17 q 156 86 238 -17 q 58 490 58 210 m 183 490 q 240 164 183 245 q 382 83 298 83 q 523 165 466 83 q 581 490 581 246 q 523 817 581 736 q 380 897 466 897 q 246 826 296 897 q 183 490 183 735 z "}, "1": {"ha": 772, "x_min": 151, "x_max": 517, "o": "m 517 0 l 395 0 l 395 778 q 280 694 351 736 q 151 631 208 652 l 151 749 q 330 865 254 797 q 439 998 407 934 l 517 998 l 517 0 z "}, "2": {"ha": 772, "x_min": 41, "x_max": 699, "o": "m 699 117 l 699 0 l 42 0 q 56 85 41 44 q 137 217 81 152 q 296 368 192 282 q 515 578 458 500 q 572 725 572 656 q 520 847 572 798 q 385 897 469 897 q 244 844 297 897 q 191 698 191 791 l 65 711 q 162 925 78 851 q 388 998 246 998 q 614 919 531 998 q 698 722 698 840 q 673 605 698 663 q 592 484 649 547 q 404 309 536 420 q 263 183 294 216 q 212 117 232 151 l 699 117 z "}, "3": {"ha": 772, "x_min": 58, "x_max": 709, "o": "m 58 262 l 180 279 q 252 129 201 175 q 375 83 302 83 q 520 143 461 83 q 580 291 580 203 q 525 430 580 375 q 385 484 470 484 q 299 471 351 484 l 313 578 q 332 576 325 576 q 473 617 410 576 q 535 743 535 658 q 490 854 535 810 q 372 898 444 898 q 254 853 301 898 q 193 719 206 808 l 71 741 q 172 931 93 863 q 370 998 252 998 q 519 963 451 998 q 624 868 588 928 q 661 740 661 808 q 626 623 661 675 q 524 538 591 570 q 661 454 612 518 q 709 294 709 390 q 614 73 709 163 q 374 -18 519 -18 q 157 60 243 -18 q 58 262 71 138 z "}, "4": {"ha": 772, "x_min": 18, "x_max": 705, "o": "m 449 0 l 449 238 l 18 238 l 18 350 l 471 994 l 571 994 l 571 350 l 705 350 l 705 238 l 571 238 l 571 0 l 449 0 m 449 350 l 449 798 l 138 350 l 449 350 z "}, "5": {"ha": 772, "x_min": 58, "x_max": 717, "o": "m 58 260 l 186 271 q 252 131 200 178 q 377 83 304 83 q 526 150 465 83 q 587 326 587 216 q 529 491 587 431 q 375 551 470 551 q 269 525 316 551 q 194 455 221 498 l 79 470 l 176 981 l 670 981 l 670 864 l 273 864 l 220 597 q 408 659 309 659 q 627 569 538 659 q 717 337 717 479 q 638 104 717 202 q 377 -17 543 -17 q 156 59 241 -17 q 58 260 70 135 z "}, "6": {"ha": 772, "x_min": 52, "x_max": 709, "o": "m 691 751 l 570 741 q 524 846 553 813 q 401 898 474 898 q 299 865 343 898 q 208 743 241 823 q 174 513 175 662 q 281 612 218 580 q 415 645 345 645 q 623 555 537 645 q 709 323 709 465 q 668 149 709 229 q 557 26 628 68 q 397 -17 487 -17 q 149 95 245 -17 q 52 465 52 208 q 159 884 52 753 q 409 998 252 998 q 601 932 526 998 q 691 751 676 867 m 193 322 q 219 201 193 259 q 294 114 246 144 q 395 83 342 83 q 528 146 473 83 q 584 315 584 208 q 529 478 584 418 q 391 537 474 537 q 250 478 308 537 q 193 322 193 418 z "}, "7": {"ha": 772, "x_min": 66, "x_max": 709, "o": "m 66 864 l 66 981 l 709 981 l 709 886 q 521 618 614 785 q 377 273 428 450 q 330 0 340 149 l 205 0 q 251 283 207 117 q 377 604 295 450 q 553 864 460 758 l 66 864 z "}, "8": {"ha": 772, "x_min": 56, "x_max": 711, "o": "m 245 539 q 133 618 170 567 q 96 742 96 670 q 174 924 96 850 q 382 998 252 998 q 591 923 512 998 q 671 739 671 847 q 634 618 671 669 q 524 539 598 567 q 664 443 616 509 q 711 284 711 376 q 621 70 711 157 q 384 -17 531 -17 q 146 70 237 -17 q 56 288 56 157 q 105 450 56 385 q 245 539 155 515 m 221 746 q 267 631 221 675 q 385 586 312 586 q 500 630 455 586 q 545 739 545 675 q 499 852 545 806 q 383 898 452 898 q 267 853 313 898 q 221 746 221 808 m 182 287 q 207 186 182 235 q 280 110 231 137 q 385 83 329 83 q 530 140 473 83 q 587 283 587 196 q 528 429 587 371 q 381 486 469 486 q 238 429 295 486 q 182 287 182 372 z "}, "9": {"ha": 772, "x_min": 58, "x_max": 711, "o": "m 76 230 l 193 241 q 250 121 208 158 q 358 83 292 83 q 457 109 414 83 q 526 178 499 135 q 572 294 553 221 q 590 444 590 368 q 589 468 590 452 q 489 373 553 410 q 352 337 426 337 q 143 427 229 337 q 58 663 58 516 q 147 906 58 814 q 370 998 236 998 q 548 946 467 998 q 670 797 628 894 q 711 517 711 701 q 670 214 711 327 q 547 42 629 101 q 355 -17 465 -17 q 165 48 239 -17 q 76 230 91 113 m 576 669 q 520 836 576 774 q 385 897 464 897 q 243 831 304 897 q 183 659 183 764 q 240 504 183 564 q 382 445 298 445 q 521 504 467 445 q 576 669 576 564 z "}, " ": {"ha": 386, "x_min": 0, "x_max": 0, "o": ""}, "!": {"ha": 386, "x_min": 119, "x_max": 271, "o": "m 157 247 l 119 774 l 119 994 l 271 994 l 271 774 l 235 247 l 157 247 m 125 0 l 125 139 l 265 139 l 265 0 l 125 0 z "}, "\"": {"ha": 493, "x_min": 64, "x_max": 428, "o": "m 98 642 l 64 831 l 64 994 l 203 994 l 203 831 l 172 642 l 98 642 m 322 642 l 289 831 l 289 994 l 428 994 l 428 831 l 395 642 l 322 642 z "}, "#": {"ha": 772, "x_min": 14, "x_max": 755, "o": "m 70 -17 l 129 272 l 14 272 l 14 373 l 149 373 l 199 619 l 14 619 l 14 720 l 220 720 l 279 1011 l 380 1011 l 321 720 l 535 720 l 594 1011 l 696 1011 l 637 720 l 755 720 l 755 619 l 617 619 l 566 373 l 755 373 l 755 272 l 546 272 l 487 -17 l 385 -17 l 444 272 l 231 272 l 172 -17 l 70 -17 m 251 373 l 464 373 l 515 619 l 301 619 l 251 373 z "}, "$": {"ha": 772, "x_min": 50, "x_max": 707, "o": "m 346 -143 l 346 -21 q 197 20 254 -9 q 98 115 140 50 q 50 276 56 181 l 172 299 q 222 155 186 201 q 346 82 274 90 l 346 471 q 192 529 271 485 q 102 619 134 562 q 71 750 71 677 q 163 961 71 880 q 346 1027 224 1015 l 346 1086 l 418 1086 l 418 1027 q 587 965 524 1017 q 683 783 667 899 l 557 764 q 512 874 546 836 q 418 924 477 912 l 418 572 q 540 536 510 549 q 633 475 597 511 q 688 390 669 439 q 707 283 707 340 q 627 71 707 156 q 418 -20 546 -14 l 418 -143 l 346 -143 m 346 926 q 234 869 275 915 q 193 760 193 823 q 228 655 193 697 q 346 588 262 613 l 346 926 m 418 82 q 535 144 489 91 q 582 275 582 197 q 549 381 582 341 q 418 454 516 422 l 418 82 z "}, "%": {"ha": 1235, "x_min": 81, "x_max": 1149, "o": "m 81 755 q 134 937 81 862 q 290 1011 188 1011 q 445 944 383 1011 q 506 748 506 878 q 444 554 506 622 q 291 486 382 486 q 141 553 201 486 q 81 755 81 620 m 293 927 q 217 888 248 927 q 187 743 187 848 q 218 609 187 648 q 293 570 248 570 q 369 609 339 570 q 399 753 399 648 q 369 888 399 849 q 293 927 338 927 m 294 -37 l 838 1011 l 937 1011 l 395 -37 l 294 -37 m 724 233 q 777 415 724 340 q 933 489 831 489 q 1088 422 1027 489 q 1149 226 1149 355 q 1087 32 1149 100 q 934 -37 1025 -37 q 784 31 844 -37 q 724 233 724 98 m 937 405 q 860 366 890 405 q 830 221 830 326 q 861 87 830 126 q 936 47 891 47 q 1013 87 983 47 q 1043 231 1043 126 q 1013 366 1043 327 q 937 405 982 405 z "}, "&": {"ha": 926, "x_min": 60, "x_max": 895, "o": "m 660 117 q 528 17 600 50 q 374 -17 457 -17 q 132 86 222 -17 q 60 274 60 170 q 119 440 60 366 q 296 571 178 515 q 207 696 229 648 q 184 789 184 745 q 255 945 184 879 q 431 1011 325 1011 q 598 949 533 1011 q 663 799 663 886 q 475 557 663 657 l 653 330 q 701 468 684 389 l 827 441 q 739 227 795 311 q 895 74 808 136 l 812 -23 q 660 117 739 24 m 412 635 q 514 718 491 682 q 538 795 538 753 q 506 879 538 846 q 425 911 473 911 q 343 879 376 911 q 310 801 310 847 q 322 752 310 778 q 357 699 334 727 l 412 635 m 583 214 l 359 491 q 226 381 260 432 q 191 281 191 331 q 239 156 191 221 q 376 91 288 91 q 489 125 431 91 q 583 214 548 159 z "}, "'": {"ha": 265, "x_min": 61, "x_max": 200, "o": "m 92 642 l 61 828 l 61 994 l 200 994 l 200 828 l 168 642 l 92 642 z "}, "(": {"ha": 463, "x_min": 84, "x_max": 412, "o": "m 325 -292 q 154 6 224 -165 q 84 360 84 177 q 136 669 84 522 q 325 1011 197 841 l 412 1011 q 304 810 330 870 q 239 614 262 716 q 210 359 210 488 q 412 -292 210 33 l 325 -292 z "}, ")": {"ha": 463, "x_min": 84, "x_max": 412, "o": "m 172 -292 l 84 -292 q 287 359 287 33 q 258 612 287 487 q 193 808 235 714 q 84 1011 167 869 l 172 1011 q 360 669 299 841 q 412 360 412 522 q 342 6 412 177 q 172 -292 272 -165 z "}, "*": {"ha": 541, "x_min": 43, "x_max": 492, "o": "m 43 812 l 75 908 q 231 842 182 870 q 218 1011 218 965 l 316 1011 q 300 843 314 944 q 460 908 370 878 l 492 812 q 323 774 406 783 q 439 646 364 738 l 358 588 q 265 734 319 642 q 177 588 215 638 l 97 646 q 210 774 176 743 q 43 812 122 791 z "}, "+": {"ha": 811, "x_min": 77, "x_max": 734, "o": "m 348 161 l 348 433 l 77 433 l 77 547 l 348 547 l 348 818 l 463 818 l 463 547 l 734 547 l 734 433 l 463 433 l 463 161 l 348 161 z "}, ",": {"ha": 386, "x_min": 115, "x_max": 262, "o": "m 123 0 l 123 139 l 262 139 l 262 0 q 235 -124 262 -77 q 149 -197 208 -171 l 115 -144 q 172 -95 154 -127 q 193 0 191 -62 l 123 0 z "}, "-": {"ha": 463, "x_min": 44, "x_max": 419, "o": "m 44 298 l 44 421 l 419 421 l 419 298 l 44 298 z "}, ".": {"ha": 386, "x_min": 126, "x_max": 265, "o": "m 126 0 l 126 139 l 265 139 l 265 0 l 126 0 z "}, "/": {"ha": 386, "x_min": 0, "x_max": 386, "o": "m 0 -17 l 288 1011 l 386 1011 l 98 -17 l 0 -17 z "}, ":": {"ha": 386, "x_min": 125, "x_max": 264, "o": "m 125 581 l 125 720 l 264 720 l 264 581 l 125 581 m 125 0 l 125 139 l 264 139 l 264 0 l 125 0 z "}, ";": {"ha": 386, "x_min": 115, "x_max": 262, "o": "m 123 581 l 123 720 l 262 720 l 262 581 l 123 581 m 123 0 l 123 139 l 262 139 l 262 0 q 235 -124 262 -77 q 149 -197 208 -171 l 115 -144 q 172 -95 154 -127 q 193 0 191 -62 l 123 0 z "}, "<": {"ha": 811, "x_min": 76, "x_max": 734, "o": "m 76 435 l 76 549 l 734 827 l 734 705 l 212 491 l 734 275 l 734 153 l 76 435 z "}, "=": {"ha": 811, "x_min": 77, "x_max": 734, "o": "m 734 585 l 77 585 l 77 699 l 734 699 l 734 585 m 734 283 l 77 283 l 77 397 l 734 397 l 734 283 z "}, ">": {"ha": 811, "x_min": 76, "x_max": 734, "o": "m 734 435 l 76 153 l 76 275 l 597 491 l 76 705 l 76 827 l 734 549 l 734 435 z "}, "?": {"ha": 772, "x_min": 61, "x_max": 703, "o": "m 320 245 q 319 281 319 269 q 340 406 319 353 q 388 485 355 445 q 476 570 412 514 q 558 659 539 626 q 577 732 577 692 q 522 857 577 803 q 385 911 466 911 q 255 862 307 911 q 186 709 203 813 l 61 724 q 162 937 78 863 q 383 1011 245 1011 q 616 932 529 1011 q 703 740 703 852 q 672 620 703 675 q 553 486 642 565 q 475 408 493 433 q 448 351 456 383 q 437 245 439 318 l 320 245 m 313 0 l 313 139 l 452 139 l 452 0 l 313 0 z "}, "@": {"ha": 1410, "x_min": 75, "x_max": 1360, "o": "m 787 111 q 689 29 743 60 q 579 -2 634 -2 q 462 33 519 -2 q 369 142 405 68 q 334 302 334 215 q 389 518 334 410 q 526 681 444 627 q 686 735 608 735 q 798 704 745 735 q 890 610 852 673 l 913 715 l 1035 715 l 937 260 q 917 155 917 165 q 931 124 917 137 q 964 111 945 111 q 1059 152 1000 111 q 1181 297 1136 206 q 1226 486 1226 389 q 1168 697 1226 599 q 995 854 1110 795 q 741 913 880 913 q 451 839 583 913 q 248 626 320 765 q 176 329 176 488 q 248 43 176 163 q 457 -135 320 -77 q 760 -192 593 -192 q 1057 -132 937 -192 q 1237 13 1177 -73 l 1360 13 q 1241 -132 1325 -58 q 1041 -249 1157 -206 q 762 -292 925 -292 q 484 -254 611 -292 q 267 -137 357 -215 q 132 41 178 -60 q 75 318 75 170 q 143 633 75 484 q 378 915 226 817 q 747 1013 530 1013 q 1049 944 916 1013 q 1261 739 1183 875 q 1326 484 1326 621 q 1188 134 1326 287 q 918 -3 1065 -3 q 843 12 871 -3 q 800 52 814 26 q 787 111 791 69 m 459 294 q 503 150 459 201 q 604 98 547 98 q 684 121 642 98 q 765 189 726 144 q 827 302 803 233 q 852 440 852 371 q 806 583 852 532 q 694 634 760 634 q 613 612 651 634 q 538 541 574 590 q 481 422 503 492 q 459 294 459 353 z "}, "A": {"ha": 926, "x_min": -2, "x_max": 928, "o": "m -2 0 l 380 994 l 522 994 l 928 0 l 779 0 l 663 301 l 247 301 l 138 0 l -2 0 m 285 408 l 622 408 l 518 684 q 448 890 471 809 q 394 700 429 794 l 285 408 z "}, "B": {"ha": 926, "x_min": 102, "x_max": 852, "o": "m 102 0 l 102 994 l 475 994 q 657 964 589 994 q 765 871 726 934 q 804 740 804 808 q 770 620 804 676 q 665 529 735 564 q 804 439 755 503 q 852 288 852 375 q 823 158 852 218 q 750 66 793 98 q 641 17 707 33 q 481 0 576 0 l 102 0 m 233 576 l 448 576 q 574 588 536 576 q 649 637 624 603 q 675 724 675 672 q 651 811 675 774 q 583 863 627 849 q 432 877 539 877 l 233 877 l 233 576 m 233 117 l 481 117 q 570 122 545 117 q 646 149 616 130 q 696 204 677 168 q 716 288 716 241 q 688 385 716 344 q 609 443 659 426 q 463 459 558 459 l 233 459 l 233 117 z "}, "C": {"ha": 1003, "x_min": 69, "x_max": 948, "o": "m 817 349 l 948 315 q 799 68 907 153 q 536 -17 692 -17 q 275 48 376 -17 q 122 238 174 114 q 69 505 69 362 q 129 775 69 660 q 297 951 188 891 q 538 1011 407 1011 q 789 935 688 1011 q 931 722 891 859 l 802 691 q 701 849 767 800 q 536 899 635 899 q 344 844 421 899 q 236 696 267 789 q 205 505 205 604 q 242 284 205 378 q 357 142 279 189 q 526 96 435 96 q 713 159 636 96 q 817 349 789 223 z "}, "D": {"ha": 1003, "x_min": 107, "x_max": 929, "o": "m 107 0 l 107 994 l 450 994 q 627 980 566 994 q 772 909 712 960 q 890 739 851 842 q 929 503 929 635 q 903 302 929 389 q 835 157 876 214 q 744 67 793 100 q 626 17 695 34 q 466 0 556 0 l 107 0 m 239 117 l 451 117 q 605 136 549 117 q 694 187 661 154 q 767 313 741 234 q 793 505 793 392 q 742 744 793 661 q 618 857 691 828 q 448 877 565 877 l 239 877 l 239 117 z "}, "E": {"ha": 926, "x_min": 110, "x_max": 852, "o": "m 110 0 l 110 994 l 829 994 l 829 877 l 241 877 l 241 572 l 791 572 l 791 456 l 241 456 l 241 117 l 852 117 l 852 0 l 110 0 z "}, "F": {"ha": 848, "x_min": 114, "x_max": 785, "o": "m 114 0 l 114 994 l 785 994 l 785 877 l 245 877 l 245 569 l 712 569 l 712 452 l 245 452 l 245 0 l 114 0 z "}, "G": {"ha": 1080, "x_min": 74, "x_max": 994, "o": "m 572 390 l 572 507 l 994 507 l 994 138 q 793 22 897 61 q 582 -17 690 -17 q 316 46 435 -17 q 135 227 196 109 q 74 492 74 346 q 135 763 74 637 q 309 950 195 889 q 572 1011 423 1011 q 767 976 680 1011 q 903 879 854 941 q 979 716 953 817 l 860 684 q 804 803 838 760 q 709 872 771 846 q 572 899 648 899 q 416 871 482 899 q 310 799 351 844 q 248 701 270 754 q 210 500 210 608 q 255 278 210 368 q 389 145 301 189 q 574 102 476 102 q 741 135 660 102 q 865 205 823 168 l 865 390 l 572 390 z "}, "H": {"ha": 1003, "x_min": 111, "x_max": 891, "o": "m 111 0 l 111 994 l 243 994 l 243 586 l 760 586 l 760 994 l 891 994 l 891 0 l 760 0 l 760 469 l 243 469 l 243 0 l 111 0 z "}, "I": {"ha": 386, "x_min": 130, "x_max": 261, "o": "m 130 0 l 130 994 l 261 994 l 261 0 l 130 0 z "}, "J": {"ha": 694, "x_min": 37, "x_max": 587, "o": "m 40 282 l 159 298 q 201 142 163 184 q 307 100 239 100 q 392 123 356 100 q 441 185 428 146 q 455 309 455 224 l 455 994 l 587 994 l 587 317 q 556 123 587 192 q 461 19 526 55 q 307 -17 395 -17 q 107 58 176 -17 q 40 282 37 134 z "}, "K": {"ha": 926, "x_min": 102, "x_max": 924, "o": "m 102 0 l 102 994 l 233 994 l 233 501 l 727 994 l 905 994 l 488 591 l 924 0 l 750 0 l 396 503 l 233 345 l 233 0 l 102 0 z "}, "L": {"ha": 772, "x_min": 102, "x_max": 723, "o": "m 102 0 l 102 994 l 233 994 l 233 117 l 723 117 l 723 0 l 102 0 z "}, "M": {"ha": 1157, "x_min": 103, "x_max": 1052, "o": "m 103 0 l 103 994 l 301 994 l 536 290 q 584 143 569 192 q 637 302 601 197 l 875 994 l 1052 994 l 1052 0 l 925 0 l 925 832 l 636 0 l 517 0 l 230 846 l 230 0 l 103 0 z "}, "N": {"ha": 1003, "x_min": 106, "x_max": 889, "o": "m 106 0 l 106 994 l 241 994 l 763 214 l 763 994 l 889 994 l 889 0 l 754 0 l 232 781 l 232 0 l 106 0 z "}, "O": {"ha": 1080, "x_min": 67, "x_max": 1018, "o": "m 67 484 q 200 872 67 732 q 543 1012 333 1012 q 791 946 681 1012 q 960 763 902 880 q 1018 496 1018 645 q 957 225 1018 345 q 784 44 896 106 q 543 -17 672 -17 q 292 51 402 -17 q 124 236 181 119 q 67 484 67 353 m 203 482 q 299 199 203 302 q 542 96 396 96 q 786 200 690 96 q 882 496 882 304 q 841 708 882 618 q 721 849 800 799 q 544 899 642 899 q 303 803 404 899 q 203 482 203 707 z "}, "P": {"ha": 926, "x_min": 107, "x_max": 866, "o": "m 107 0 l 107 994 l 482 994 q 633 985 581 994 q 756 938 707 972 q 836 842 806 904 q 866 707 866 781 q 785 492 866 580 q 494 404 705 404 l 239 404 l 239 0 l 107 0 m 239 522 l 496 522 q 677 569 623 522 q 730 703 730 616 q 699 809 730 765 q 616 868 667 854 q 493 877 583 877 l 239 877 l 239 522 z "}, "Q": {"ha": 1080, "x_min": 60, "x_max": 1029, "o": "m 861 106 q 1029 14 952 43 l 991 -77 q 777 45 884 -39 q 533 -17 667 -17 q 288 48 398 -17 q 119 231 178 113 q 60 497 60 349 q 119 765 60 644 q 289 949 179 886 q 536 1012 399 1012 q 784 946 673 1012 q 952 763 895 881 q 1010 498 1010 646 q 973 277 1010 375 q 861 106 936 179 m 571 275 q 759 180 685 243 q 875 498 875 286 q 834 709 875 618 q 714 849 793 799 q 536 899 635 899 q 292 798 389 899 q 195 497 195 697 q 291 199 195 303 q 536 96 387 96 q 669 122 607 96 q 539 179 608 162 l 571 275 z "}, "R": {"ha": 1003, "x_min": 109, "x_max": 985, "o": "m 109 0 l 109 994 l 550 994 q 752 967 683 994 q 863 873 821 941 q 904 723 904 805 q 836 545 904 617 q 624 452 767 472 q 703 403 676 427 q 812 271 761 350 l 985 0 l 820 0 l 688 207 q 593 344 631 296 q 527 410 556 391 q 467 437 497 429 q 393 441 444 441 l 241 441 l 241 0 l 109 0 m 241 555 l 524 555 q 665 574 614 555 q 742 634 715 593 q 768 723 768 675 q 717 839 768 793 q 555 884 666 884 l 241 884 l 241 555 z "}, "S": {"ha": 926, "x_min": 62, "x_max": 854, "o": "m 62 319 l 186 330 q 228 208 195 256 q 328 131 260 160 q 480 101 395 101 q 613 123 555 101 q 699 185 671 146 q 727 270 727 224 q 700 352 727 317 q 610 410 673 387 q 433 459 570 426 q 241 521 296 492 q 135 613 170 558 q 100 737 100 669 q 143 878 100 812 q 268 977 186 943 q 450 1011 350 1011 q 645 976 561 1011 q 775 871 730 940 q 824 714 821 802 l 698 705 q 629 847 688 799 q 456 895 570 895 q 282 851 336 895 q 227 746 227 808 q 266 658 227 692 q 464 587 304 623 q 684 524 625 551 q 812 422 771 484 q 854 281 854 361 q 808 131 854 201 q 678 22 763 61 q 486 -17 593 -17 q 260 22 351 -17 q 117 141 169 62 q 62 319 65 220 z "}, "T": {"ha": 848, "x_min": 33, "x_max": 821, "o": "m 360 0 l 360 877 l 33 877 l 33 994 l 821 994 l 821 877 l 492 877 l 492 0 l 360 0 z "}, "U": {"ha": 1003, "x_min": 109, "x_max": 891, "o": "m 760 994 l 891 994 l 891 420 q 857 182 891 270 q 735 38 823 94 q 503 -17 646 -17 q 274 31 363 -17 q 147 171 185 79 q 109 420 109 262 l 109 994 l 241 994 l 241 420 q 265 230 241 291 q 348 135 289 168 q 491 102 406 102 q 698 168 636 102 q 760 420 760 233 l 760 994 z "}, "V": {"ha": 926, "x_min": 6, "x_max": 916, "o": "m 391 0 l 6 994 l 149 994 l 407 272 q 459 109 438 185 q 513 272 482 191 l 781 994 l 916 994 l 526 0 l 391 0 z "}, "W": {"ha": 1311, "x_min": 17, "x_max": 1295, "o": "m 281 0 l 17 994 l 152 994 l 303 342 q 345 139 328 240 q 390 323 383 298 l 579 994 l 738 994 l 880 491 q 958 139 934 304 q 1007 355 977 233 l 1163 994 l 1295 994 l 1023 0 l 896 0 l 686 758 q 655 874 660 852 q 626 758 640 806 l 415 0 l 281 0 z "}, "X": {"ha": 926, "x_min": 6, "x_max": 918, "o": "m 6 0 l 391 518 l 52 994 l 208 994 l 389 739 q 469 617 445 660 q 547 730 502 671 l 747 994 l 890 994 l 541 526 l 918 0 l 755 0 l 505 355 q 461 421 484 385 q 414 347 428 367 l 164 0 l 6 0 z "}, "Y": {"ha": 926, "x_min": 4, "x_max": 916, "o": "m 387 0 l 387 421 l 4 994 l 164 994 l 360 694 q 461 526 414 610 q 570 702 506 604 l 762 994 l 916 994 l 519 421 l 519 0 l 387 0 z "}, "Z": {"ha": 848, "x_min": 28, "x_max": 814, "o": "m 28 0 l 28 122 l 537 759 q 640 877 591 827 l 85 877 l 85 994 l 798 994 l 798 877 l 239 187 l 179 117 l 814 117 l 814 0 l 28 0 z "}, "[": {"ha": 386, "x_min": 94, "x_max": 363, "o": "m 94 -276 l 94 994 l 363 994 l 363 893 l 216 893 l 216 -175 l 363 -175 l 363 -276 l 94 -276 z "}, "\\": {"ha": 386, "x_min": 0, "x_max": 386, "o": "m 288 -17 l 0 1011 l 98 1011 l 386 -17 l 288 -17 z "}, "]": {"ha": 386, "x_min": 26, "x_max": 296, "o": "m 296 -276 l 26 -276 l 26 -175 l 174 -175 l 174 893 l 26 893 l 26 994 l 296 994 l 296 -276 z "}, "^": {"ha": 652, "x_min": 37, "x_max": 615, "o": "m 162 468 l 37 468 l 276 1011 l 374 1011 l 615 468 l 492 468 l 325 873 l 162 468 z "}, "_": {"ha": 772, "x_min": -21, "x_max": 788, "o": "m -21 -276 l -21 -188 l 788 -188 l 788 -276 l -21 -276 z "}, "`": {"ha": 463, "x_min": 60, "x_max": 315, "o": "m 315 810 l 217 810 l 60 1000 l 224 1000 l 315 810 z "}, "a": {"ha": 772, "x_min": 50, "x_max": 713, "o": "m 562 89 q 431 7 494 31 q 296 -16 368 -16 q 114 42 178 -16 q 50 190 50 100 q 74 287 50 243 q 137 357 98 330 q 225 397 176 383 q 334 415 261 406 q 551 457 481 433 q 552 489 552 482 q 517 594 552 564 q 378 635 471 635 q 251 605 292 635 q 191 498 210 575 l 71 515 q 125 638 87 591 q 233 711 162 686 q 396 736 303 736 q 546 715 488 736 q 631 660 604 693 q 669 577 658 627 q 675 465 675 546 l 675 302 q 683 86 675 132 q 713 0 690 41 l 586 0 q 562 89 567 38 m 551 361 q 352 315 485 334 q 245 291 277 304 q 197 251 214 277 q 180 193 180 225 q 217 112 180 144 q 326 79 254 79 q 451 110 396 79 q 532 195 506 141 q 551 317 551 236 l 551 361 z "}, "b": {"ha": 772, "x_min": 91, "x_max": 715, "o": "m 204 0 l 91 0 l 91 994 l 213 994 l 213 640 q 410 736 290 736 q 536 710 477 736 q 634 634 595 683 q 694 517 672 586 q 715 371 715 449 q 624 85 715 186 q 404 -16 532 -16 q 204 90 277 -16 l 204 0 m 203 366 q 238 178 203 236 q 394 84 296 84 q 532 154 474 84 q 591 361 591 223 q 535 569 591 502 q 399 636 479 636 q 261 567 319 636 q 203 366 203 497 z "}, "c": {"ha": 694, "x_min": 54, "x_max": 682, "o": "m 562 264 l 682 248 q 581 54 662 124 q 382 -16 500 -16 q 144 80 234 -16 q 54 357 54 177 q 93 562 54 474 q 211 693 132 649 q 382 736 290 736 q 574 677 500 736 q 670 509 649 618 l 551 490 q 491 600 534 563 q 387 636 448 636 q 237 570 295 636 q 180 361 180 504 q 235 150 180 216 q 380 84 291 84 q 500 128 452 84 q 562 264 549 172 z "}, "d": {"ha": 772, "x_min": 47, "x_max": 672, "o": "m 559 0 l 559 91 q 357 -16 490 -16 q 199 31 271 -16 q 87 164 127 79 q 47 359 47 249 q 83 555 47 467 q 191 690 119 643 q 352 736 263 736 q 468 709 417 736 q 551 637 519 682 l 551 994 l 672 994 l 672 0 l 559 0 m 173 359 q 231 153 173 221 q 369 84 290 84 q 505 150 449 84 q 561 349 561 215 q 504 566 561 497 q 363 635 447 635 q 228 569 282 635 q 173 359 173 503 z "}, "e": {"ha": 772, "x_min": 51, "x_max": 715, "o": "m 585 232 l 711 216 q 600 45 681 106 q 394 -16 519 -16 q 143 81 236 -16 q 51 354 51 178 q 144 636 51 536 q 387 736 238 736 q 623 638 532 736 q 715 361 715 540 q 714 329 715 351 l 177 329 q 244 147 184 210 q 395 84 304 84 q 509 119 462 84 q 585 232 557 155 m 184 429 l 586 429 q 540 566 578 520 q 389 636 481 636 q 247 580 304 636 q 184 429 190 524 z "}, "f": {"ha": 386, "x_min": 13, "x_max": 434, "o": "m 121 0 l 121 625 l 13 625 l 13 720 l 121 720 l 121 797 q 134 905 121 869 q 196 982 151 952 q 320 1011 240 1011 q 434 999 372 1011 l 416 892 q 344 899 378 899 q 265 876 288 899 q 242 787 242 852 l 242 720 l 382 720 l 382 625 l 242 625 l 242 0 l 121 0 z "}, "g": {"ha": 772, "x_min": 45, "x_max": 680, "o": "m 69 -60 l 188 -77 q 229 -157 195 -132 q 353 -191 275 -191 q 484 -157 438 -191 q 547 -62 530 -123 q 555 94 556 -25 q 356 0 475 0 q 126 107 208 0 q 45 364 45 214 q 82 554 45 467 q 190 689 119 642 q 357 736 261 736 q 567 633 484 736 l 567 720 l 680 720 l 680 98 q 645 -141 680 -71 q 537 -252 611 -211 q 354 -292 463 -292 q 146 -234 225 -292 q 69 -60 66 -176 m 170 373 q 227 166 170 231 q 368 101 283 101 q 509 166 452 101 q 566 369 566 231 q 507 568 566 501 q 366 635 448 635 q 227 569 284 635 q 170 373 170 503 z "}, "h": {"ha": 772, "x_min": 92, "x_max": 678, "o": "m 92 0 l 92 994 l 214 994 l 214 637 q 429 736 299 736 q 568 705 509 736 q 653 618 627 673 q 678 456 678 562 l 678 0 l 556 0 l 556 456 q 516 590 556 548 q 404 631 477 631 q 302 603 350 631 q 234 527 254 575 q 214 394 214 479 l 214 0 l 92 0 z "}, "i": {"ha": 309, "x_min": 92, "x_max": 214, "o": "m 92 854 l 92 994 l 214 994 l 214 854 l 92 854 m 92 0 l 92 720 l 214 720 l 214 0 l 92 0 z "}, "j": {"ha": 309, "x_min": -64, "x_max": 213, "o": "m 91 852 l 91 994 l 213 994 l 213 852 l 91 852 m -64 -279 l -41 -176 q 17 -185 -4 -185 q 73 -160 54 -185 q 91 -37 91 -136 l 91 720 l 213 720 l 213 -39 q 178 -224 213 -172 q 32 -292 134 -292 q -64 -279 -18 -292 z "}, "k": {"ha": 694, "x_min": 92, "x_max": 689, "o": "m 92 0 l 92 994 l 214 994 l 214 427 l 503 720 l 661 720 l 386 453 l 689 0 l 538 0 l 300 368 l 214 286 l 214 0 l 92 0 z "}, "l": {"ha": 309, "x_min": 89, "x_max": 211, "o": "m 89 0 l 89 994 l 211 994 l 211 0 l 89 0 z "}, "m": {"ha": 1157, "x_min": 92, "x_max": 1067, "o": "m 92 0 l 92 720 l 201 720 l 201 619 q 291 704 235 672 q 419 736 347 736 q 550 703 499 736 q 623 610 602 670 q 845 736 708 736 q 1010 677 952 736 q 1067 494 1067 618 l 1067 0 l 946 0 l 946 454 q 934 559 946 527 q 891 611 922 591 q 818 631 860 631 q 692 580 742 631 q 642 418 642 530 l 642 0 l 519 0 l 519 468 q 490 590 519 549 q 392 631 460 631 q 297 604 340 631 q 233 524 253 576 q 214 374 214 472 l 214 0 l 92 0 z "}, "n": {"ha": 772, "x_min": 92, "x_max": 677, "o": "m 92 0 l 92 720 l 201 720 l 201 618 q 431 736 281 736 q 550 713 496 736 q 632 652 605 690 q 670 562 659 614 q 677 443 677 528 l 677 0 l 555 0 l 555 438 q 541 550 555 513 q 490 609 526 587 q 405 631 454 631 q 270 581 327 631 q 214 393 214 532 l 214 0 l 92 0 z "}, "o": {"ha": 772, "x_min": 46, "x_max": 721, "o": "m 46 360 q 157 656 46 560 q 384 736 250 736 q 627 639 532 736 q 721 370 721 542 q 679 152 721 231 q 558 28 637 72 q 384 -16 478 -16 q 139 81 233 -16 q 46 360 46 178 m 172 360 q 232 153 172 222 q 384 84 292 84 q 535 153 475 84 q 595 364 595 222 q 535 567 595 498 q 384 635 474 635 q 232 567 292 635 q 172 360 172 498 z "}, "p": {"ha": 772, "x_min": 92, "x_max": 717, "o": "m 92 -276 l 92 720 l 203 720 l 203 627 q 292 709 242 682 q 412 736 341 736 q 574 689 504 736 q 681 555 645 642 q 717 366 717 469 q 677 166 717 255 q 562 31 637 78 q 403 -16 486 -16 q 293 9 342 -16 q 214 75 245 35 l 214 -276 l 92 -276 m 202 356 q 258 151 202 217 q 395 84 315 84 q 534 153 476 84 q 592 366 592 222 q 535 572 592 504 q 400 641 479 641 q 262 568 322 641 q 202 356 202 495 z "}, "q": {"ha": 772, "x_min": 49, "x_max": 673, "o": "m 551 -276 l 551 77 q 471 10 522 37 q 362 -16 420 -16 q 141 86 234 -16 q 49 367 49 189 q 86 562 49 475 q 196 692 124 648 q 353 736 267 736 q 563 624 486 736 l 563 720 l 673 720 l 673 -276 l 551 -276 m 174 362 q 233 154 174 223 q 372 84 291 84 q 507 150 450 84 q 563 351 563 216 q 504 568 563 495 q 364 640 444 640 q 230 573 285 640 q 174 362 174 505 z "}, "r": {"ha": 463, "x_min": 90, "x_max": 481, "o": "m 90 0 l 90 720 l 200 720 l 200 611 q 278 712 242 688 q 356 736 313 736 q 481 697 418 736 l 439 584 q 350 610 395 610 q 278 586 310 610 q 233 519 246 562 q 212 377 212 454 l 212 0 l 90 0 z "}, "s": {"ha": 694, "x_min": 43, "x_max": 641, "o": "m 43 215 l 163 234 q 220 123 174 161 q 350 84 267 84 q 475 118 434 84 q 515 199 515 153 q 479 264 515 240 q 355 305 454 280 q 169 364 220 339 q 90 432 117 389 q 63 529 63 476 q 85 618 63 577 q 145 686 107 659 q 223 722 174 707 q 328 736 272 736 q 477 712 413 736 q 572 646 541 688 q 614 534 602 604 l 494 518 q 447 605 486 574 q 337 636 408 636 q 217 608 253 636 q 181 543 181 581 q 196 500 181 519 q 243 468 211 481 q 351 437 261 461 q 531 380 480 402 q 612 316 583 358 q 641 212 641 274 q 605 97 641 151 q 503 13 570 43 q 351 -16 435 -16 q 137 42 210 -16 q 43 215 63 100 z "}, "t": {"ha": 386, "x_min": 24, "x_max": 376, "o": "m 358 109 l 376 1 q 283 -9 324 -9 q 180 12 217 -9 q 129 67 144 33 q 114 211 114 101 l 114 625 l 24 625 l 24 720 l 114 720 l 114 899 l 235 972 l 235 720 l 358 720 l 358 625 l 235 625 l 235 204 q 242 137 235 152 q 263 113 248 122 q 304 104 277 104 q 358 109 325 104 z "}, "u": {"ha": 772, "x_min": 89, "x_max": 673, "o": "m 564 0 l 564 106 q 335 -16 479 -16 q 216 8 271 -16 q 134 70 161 33 q 96 160 107 106 q 89 274 89 196 l 89 720 l 211 720 l 211 321 q 218 192 211 225 q 267 116 230 144 q 359 89 304 89 q 463 117 414 89 q 531 194 511 145 q 551 334 551 242 l 551 720 l 673 720 l 673 0 l 564 0 z "}, "v": {"ha": 694, "x_min": 18, "x_max": 678, "o": "m 292 0 l 18 720 l 146 720 l 301 289 q 347 144 326 219 q 393 281 363 201 l 553 720 l 678 720 l 406 0 l 292 0 z "}, "w": {"ha": 1003, "x_min": 4, "x_max": 992, "o": "m 224 0 l 4 720 l 130 720 l 245 304 l 288 150 q 325 298 290 161 l 439 720 l 565 720 l 673 302 l 709 165 l 750 304 l 873 720 l 992 720 l 767 0 l 640 0 l 526 431 l 498 554 l 352 0 l 224 0 z "}, "x": {"ha": 694, "x_min": 10, "x_max": 684, "o": "m 10 0 l 273 374 l 30 720 l 182 720 l 293 551 q 343 471 324 503 q 398 550 373 515 l 519 720 l 665 720 l 416 381 l 684 0 l 534 0 l 387 224 l 347 284 l 158 0 l 10 0 z "}, "y": {"ha": 694, "x_min": 22, "x_max": 682, "o": "m 86 -277 l 73 -163 q 142 -174 113 -174 q 208 -160 183 -174 q 248 -122 232 -146 q 285 -31 259 -104 q 296 -1 288 -21 l 22 720 l 154 720 l 304 303 q 356 136 333 224 q 406 300 377 220 l 560 720 l 682 720 l 408 -12 q 340 -176 364 -131 q 265 -264 307 -236 q 165 -292 223 -292 q 86 -277 130 -292 z "}, "z": {"ha": 694, "x_min": 27, "x_max": 665, "o": "m 27 0 l 27 99 l 486 625 q 348 621 408 621 l 54 621 l 54 720 l 643 720 l 643 640 l 253 182 l 178 99 q 332 105 260 105 l 665 105 l 665 0 l 27 0 z "}, "{": {"ha": 464, "x_min": 39, "x_max": 431, "o": "m 39 415 q 124 443 91 416 q 168 517 157 470 q 179 677 178 564 q 183 826 180 790 q 206 918 189 883 q 247 973 222 952 q 309 1004 271 994 q 393 1011 335 1011 l 431 1011 l 431 905 l 410 905 q 317 879 340 905 q 294 766 294 854 q 286 541 294 588 q 244 429 274 469 q 152 359 215 390 q 260 264 227 328 q 294 54 294 200 q 296 -103 294 -78 q 324 -168 302 -149 q 410 -186 346 -186 l 431 -186 l 431 -292 l 393 -292 q 297 -281 327 -292 q 225 -231 254 -266 q 188 -142 197 -196 q 179 33 180 -89 q 168 202 178 155 q 124 276 157 249 q 39 304 91 303 l 39 415 z "}, "|": {"ha": 361, "x_min": 127, "x_max": 234, "o": "m 127 -292 l 127 1011 l 234 1011 l 234 -292 l 127 -292 z "}, "}": {"ha": 464, "x_min": 32, "x_max": 425, "o": "m 425 415 l 425 304 q 339 276 372 303 q 296 202 307 249 q 284 43 285 156 q 280 -106 283 -71 q 257 -198 274 -164 q 216 -253 241 -233 q 154 -285 192 -274 q 70 -292 128 -292 l 32 -292 l 32 -186 l 53 -186 q 146 -160 123 -186 q 170 -46 170 -135 q 176 169 170 123 q 219 289 186 244 q 311 359 252 334 q 201 458 233 397 q 170 665 170 519 q 166 823 170 798 q 139 887 161 869 q 53 905 117 905 l 32 905 l 32 1011 l 70 1011 q 166 1000 136 1011 q 238 950 210 985 q 275 861 267 915 q 284 686 283 808 q 296 517 285 564 q 339 444 307 471 q 425 415 372 416 z "}, "~": {"ha": 811, "x_min": 59, "x_max": 753, "o": "m 59 378 l 59 517 q 248 598 131 598 q 333 586 288 598 q 460 539 378 574 q 531 513 507 519 q 578 507 554 507 q 669 534 622 507 q 753 600 716 560 l 753 456 q 665 397 709 416 q 565 379 621 379 q 487 389 524 379 q 370 434 450 398 q 236 470 290 470 q 154 451 193 470 q 59 378 116 433 z "}, "°": {"ha": 555, "x_min": 87, "x_max": 463, "o": "m 87 823 q 142 956 87 901 q 275 1011 197 1011 q 408 956 353 1011 q 463 823 463 901 q 408 690 463 745 q 275 635 353 635 q 142 690 197 635 q 87 823 87 745 m 161 823 q 194 742 161 776 q 275 709 228 709 q 356 742 322 709 q 389 823 389 776 q 356 904 389 871 q 275 938 322 938 q 194 904 228 938 q 161 823 161 871 z "}}, "familyName": "<PERSON><PERSON>", "ascender": 1257, "descender": -294, "underlinePosition": -147, "underlineThickness": 102, "boundingBox": {"yMin": -451, "xMin": -923, "yMax": 1444, "xMax": 2778}, "resolution": 1000, "original_font_information": {"format": 0, "copyright": "© 2017 The Monotype Corporation. All Rights Reserved. \r\n\r\nHebrew OpenType Layout logic copyright © 2003 & 2007, <PERSON> & John <PERSON>. This layout logic for Biblical Hebrew is open source software under the MIT License; see embedded license description for details.", "fontFamily": "<PERSON><PERSON>", "fontSubfamily": "Regular", "uniqueID": "Monotype:Arial Regular (Microsoft)", "fullName": "<PERSON><PERSON>", "version": "Version 7.01", "postScriptName": "ArialMT", "trademark": "Arial is a trademark of The Monotype Corporation.", "manufacturer": "The Monotype Corporation", "designer": "Monotype Type Drawing Office - <PERSON>, <PERSON> 1982", "licence": "Microsoft supplied font. You may use this font to create, display, and print content as permitted by the license terms or terms of use, of the Microsoft product, service, or content in which this font was included. You may only (i) embed this font in content as permitted by the embedding restrictions included in this font; and (ii) temporarily download this font to a printer or other output device to help print content. Any other use is prohibited.\r\n\r\nThe following license, based on the MIT license (http://en.wikipedia.org/wiki/MIT_License), applies to the OpenType Layout logic for Biblical Hebrew “Layout Logic” as jointly developed by <PERSON> and <PERSON>. \r\n\r\nPermission is hereby granted, free of charge, to any person obtaining a copy of the OpenType Layout logic for Biblical Hebrew and associated documentation files (the “Layout Logic Software”), to deal in the Layout Logic Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Layout Logic Software, and to permit persons to whom the Layout Logic Software is furnished to do so, subject to the following conditions:\r\n\r\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Layout Logic Software.\r\n\r\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE."}, "cssFontWeight": "normal", "cssFontStyle": "normal"}