{"glyphs": {"0": {"ha": 772, "x_min": 58, "x_max": 704, "o": "m 381 998 q 607 895 526 998 q 704 490 704 773 q 606 85 704 208 q 381 -17 526 -17 q 147 95 236 -17 q 58 492 58 206 q 156 897 58 773 q 381 998 237 998 m 381 840 q 319 818 347 840 q 277 739 292 796 q 258 490 258 665 q 275 250 258 315 q 320 163 293 184 q 381 141 347 141 q 443 163 416 141 q 485 242 470 185 q 505 490 505 315 q 487 731 505 665 q 443 818 469 796 q 381 840 416 840 z "}, "1": {"ha": 772, "x_min": 110, "x_max": 547, "o": "m 547 0 l 356 0 l 356 718 q 110 574 252 621 l 110 747 q 272 839 184 771 q 392 998 359 907 l 547 998 l 547 0 z "}, "2": {"ha": 772, "x_min": 35, "x_max": 703, "o": "m 703 177 l 703 0 l 35 0 q 100 190 45 100 q 314 429 154 280 q 472 591 443 549 q 511 708 511 650 q 477 806 511 772 q 382 840 443 840 q 288 804 323 840 q 247 685 252 768 l 57 704 q 163 930 74 861 q 387 998 253 998 q 618 919 534 998 q 703 722 703 840 q 679 594 703 654 q 602 467 654 533 q 477 340 568 422 q 363 230 387 257 q 324 177 339 203 l 703 177 z "}, "3": {"ha": 772, "x_min": 52, "x_max": 713, "o": "m 52 264 l 237 286 q 284 178 245 216 q 378 141 323 141 q 477 186 437 141 q 517 307 517 231 q 479 420 517 378 q 385 463 440 463 q 297 448 348 463 l 318 604 q 436 637 395 602 q 477 732 477 673 q 447 812 477 782 q 368 842 417 842 q 284 808 319 842 q 242 709 250 774 l 66 739 q 122 883 85 829 q 225 967 159 937 q 373 998 291 998 q 598 909 513 998 q 668 743 668 836 q 525 534 668 612 q 662 452 610 516 q 713 298 713 389 q 617 75 713 168 q 379 -17 522 -17 q 155 61 244 -17 q 52 264 66 138 z "}, "4": {"ha": 772, "x_min": 26, "x_max": 741, "o": "m 433 0 l 433 200 l 26 200 l 26 367 l 457 998 l 617 998 l 617 368 l 741 368 l 741 200 l 617 200 l 617 0 l 433 0 m 433 368 l 433 707 l 204 368 l 433 368 z "}, "5": {"ha": 772, "x_min": 62, "x_max": 730, "o": "m 62 256 l 252 275 q 300 173 260 211 q 392 136 340 136 q 493 184 452 136 q 534 330 534 233 q 493 468 534 422 q 387 513 452 513 q 239 441 304 513 l 85 463 l 182 981 l 686 981 l 686 802 l 327 802 l 297 633 q 427 665 361 665 q 642 573 554 665 q 730 334 730 481 q 659 115 730 211 q 390 -17 562 -17 q 165 57 252 -17 q 62 256 79 131 z "}, "6": {"ha": 772, "x_min": 59, "x_max": 723, "o": "m 705 751 l 520 730 q 485 814 513 787 q 411 842 456 842 q 309 787 351 842 q 256 562 267 733 q 433 646 328 646 q 638 555 553 646 q 723 320 723 464 q 633 75 723 168 q 404 -17 544 -17 q 156 100 253 -17 q 59 484 59 217 q 160 878 59 757 q 422 998 261 998 q 610 935 536 998 q 705 751 684 871 m 273 335 q 315 192 273 242 q 413 141 358 141 q 501 182 466 141 q 536 318 536 224 q 498 460 536 415 q 404 505 460 505 q 311 462 349 505 q 273 335 273 419 z "}, "7": {"ha": 772, "x_min": 59, "x_max": 711, "o": "m 59 804 l 59 981 l 711 981 l 711 842 q 547 614 630 763 q 419 299 463 466 q 376 0 376 132 l 193 0 q 278 420 197 206 q 492 804 358 635 l 59 804 z "}, "8": {"ha": 772, "x_min": 56, "x_max": 710, "o": "m 222 536 q 115 622 149 568 q 81 742 81 677 q 159 926 81 853 q 380 998 237 998 q 600 926 522 998 q 678 742 678 853 q 642 619 678 673 q 541 536 606 565 q 667 439 624 503 q 710 292 710 376 q 622 68 710 155 q 389 -18 534 -18 q 163 54 253 -18 q 56 284 56 138 q 96 432 56 365 q 222 536 136 500 m 262 728 q 294 640 262 671 q 380 608 326 608 q 467 640 434 608 q 499 729 499 672 q 467 815 499 783 q 382 847 435 847 q 294 814 327 847 q 262 728 262 782 m 244 302 q 284 180 244 224 q 385 136 325 136 q 483 178 444 136 q 522 300 522 220 q 482 413 522 370 q 382 455 443 455 q 278 407 313 455 q 244 302 244 359 z "}, "9": {"ha": 772, "x_min": 44, "x_max": 708, "o": "m 63 230 l 248 250 q 283 167 254 194 q 358 140 311 140 q 458 194 417 140 q 511 419 500 248 q 332 336 439 336 q 129 426 214 336 q 44 662 44 517 q 134 906 44 813 q 363 998 224 998 q 611 881 514 998 q 708 496 708 764 q 607 103 708 224 q 344 -18 506 -18 q 155 44 227 -18 q 63 230 83 106 m 494 646 q 452 789 494 739 q 354 840 410 840 q 266 799 301 840 q 231 662 231 757 q 269 520 231 566 q 364 475 307 475 q 457 519 419 475 q 494 646 494 562 z "}, " ": {"ha": 386, "x_min": 0, "x_max": 0, "o": ""}, "!": {"ha": 463, "x_min": 125, "x_max": 331, "o": "m 174 256 l 125 761 l 125 994 l 331 994 l 331 761 l 282 256 l 174 256 m 133 0 l 133 191 l 323 191 l 323 0 l 133 0 z "}, "\"": {"ha": 659, "x_min": 76, "x_max": 589, "o": "m 112 641 l 76 826 l 76 994 l 283 994 l 283 826 l 252 641 l 112 641 m 418 641 l 382 826 l 382 994 l 589 994 l 589 826 l 558 641 l 418 641 z "}, "#": {"ha": 772, "x_min": 12, "x_max": 756, "o": "m 104 248 l 12 248 l 12 397 l 134 397 l 175 597 l 12 597 l 12 747 l 205 747 l 259 1011 l 411 1011 l 357 747 l 507 747 l 559 1011 l 717 1011 l 663 747 l 756 747 l 756 597 l 632 597 l 591 397 l 756 397 l 756 248 l 561 248 l 507 -17 l 356 -17 l 408 248 l 258 248 l 204 -17 l 51 -17 l 104 248 m 328 597 l 288 397 l 438 397 l 477 597 l 328 597 z "}, "$": {"ha": 772, "x_min": 47, "x_max": 711, "o": "m 332 147 l 332 431 q 137 546 199 469 q 75 733 75 623 q 145 920 75 844 q 332 1007 215 996 l 332 1074 l 430 1074 l 430 1007 q 602 933 538 994 q 683 771 665 873 l 511 749 q 430 857 496 829 l 430 592 q 652 478 593 548 q 711 298 711 408 q 636 91 711 175 q 430 -12 562 7 l 430 -139 l 332 -139 l 332 -16 q 141 72 214 -1 q 47 279 68 145 l 224 298 q 265 204 235 243 q 332 147 295 165 m 332 859 q 268 813 292 846 q 244 741 244 781 q 266 674 244 705 q 332 625 288 644 l 332 859 m 430 138 q 513 185 481 147 q 545 273 545 222 q 518 352 545 319 q 430 402 491 385 l 430 138 z "}, "%": {"ha": 1235, "x_min": 60, "x_max": 1170, "o": "m 60 749 q 119 947 60 882 q 278 1011 178 1011 q 440 947 382 1011 q 499 749 499 883 q 440 551 499 615 q 281 486 382 486 q 119 550 178 486 q 60 749 60 614 m 209 750 q 231 620 209 653 q 278 595 248 595 q 326 620 309 595 q 347 750 347 653 q 326 880 347 847 q 278 905 309 905 q 231 880 248 905 q 209 750 209 847 m 423 -38 l 282 -38 l 812 1011 l 949 1011 l 423 -38 m 731 223 q 790 421 731 357 q 950 486 848 486 q 1111 421 1053 486 q 1170 223 1170 357 q 1111 24 1170 89 q 952 -40 1053 -40 q 790 24 848 -40 q 731 223 731 89 m 880 224 q 902 94 880 127 q 949 69 919 69 q 996 94 979 69 q 1018 224 1018 127 q 996 354 1018 321 q 949 378 979 378 q 902 354 918 378 q 880 224 880 321 z "}, "&": {"ha": 1003, "x_min": 61, "x_max": 981, "o": "m 981 121 l 865 -26 q 703 88 781 16 q 574 8 642 34 q 413 -18 507 -18 q 134 87 227 -18 q 61 273 61 168 q 118 445 61 368 q 288 577 175 521 q 212 692 237 637 q 186 795 186 747 q 258 948 186 886 q 464 1011 330 1011 q 665 945 593 1011 q 738 785 738 880 q 702 670 738 724 q 557 547 666 616 l 695 365 q 737 478 720 408 l 909 439 q 865 305 884 347 q 824 234 846 262 q 906 169 856 204 q 981 121 957 133 m 462 662 l 513 701 q 570 789 570 745 q 543 851 570 825 q 469 877 515 877 q 397 854 423 877 q 372 802 372 831 q 415 716 372 766 l 462 662 m 387 455 q 289 375 321 422 q 256 279 256 328 q 297 179 256 218 q 405 140 337 140 q 491 157 450 140 q 581 216 532 175 l 387 455 z "}, "'": {"ha": 330, "x_min": 62, "x_max": 270, "o": "m 98 641 l 62 826 l 62 994 l 270 994 l 270 826 l 239 641 l 98 641 z "}, "(": {"ha": 463, "x_min": 73, "x_max": 418, "o": "m 416 -292 l 285 -292 q 127 33 181 -136 q 73 360 73 202 q 140 731 73 556 q 288 1011 198 883 l 418 1011 q 290 662 325 806 q 255 356 255 517 q 276 128 255 245 q 332 -94 296 12 q 416 -292 356 -163 z "}, ")": {"ha": 463, "x_min": 45, "x_max": 391, "o": "m 47 -292 q 126 -107 103 -172 q 169 41 149 -43 q 198 201 189 125 q 208 356 208 277 q 173 662 208 517 q 45 1011 138 806 l 175 1011 q 334 702 277 865 q 391 370 391 538 q 346 66 391 229 q 178 -292 295 -115 l 47 -292 z "}, "*": {"ha": 541, "x_min": 19, "x_max": 511, "o": "m 166 537 l 64 616 q 165 722 113 671 q 192 747 186 742 q 95 770 175 750 q 19 791 37 784 l 59 910 q 218 831 148 874 q 201 1011 201 941 l 322 1011 q 304 829 322 962 q 362 856 317 835 q 475 903 423 884 l 511 781 q 336 747 435 764 l 418 656 q 456 610 442 628 l 353 542 l 261 693 q 166 537 220 620 z "}, "+": {"ha": 811, "x_min": 58, "x_max": 753, "o": "m 318 143 l 318 401 l 58 401 l 58 580 l 318 580 l 318 838 l 492 838 l 492 580 l 753 580 l 753 401 l 492 401 l 492 143 l 318 143 z "}, ",": {"ha": 386, "x_min": 79, "x_max": 286, "o": "m 95 191 l 286 191 l 286 54 q 271 -76 286 -28 q 217 -162 257 -124 q 117 -222 178 -200 l 79 -143 q 161 -91 137 -124 q 187 0 186 -58 l 95 0 l 95 191 z "}, "-": {"ha": 463, "x_min": 78, "x_max": 452, "o": "m 78 265 l 78 456 l 452 456 l 452 265 l 78 265 z "}, ".": {"ha": 386, "x_min": 100, "x_max": 290, "o": "m 100 0 l 100 191 l 290 191 l 290 0 l 100 0 z "}, "/": {"ha": 386, "x_min": -2, "x_max": 387, "o": "m -2 -17 l 244 1011 l 387 1011 l 138 -17 l -2 -17 z "}, ":": {"ha": 463, "x_min": 136, "x_max": 327, "o": "m 136 530 l 136 720 l 327 720 l 327 530 l 136 530 m 136 0 l 136 191 l 327 191 l 327 0 l 136 0 z "}, ";": {"ha": 463, "x_min": 115, "x_max": 321, "o": "m 131 530 l 131 720 l 321 720 l 321 530 l 131 530 m 131 191 l 321 191 l 321 54 q 307 -76 321 -28 q 253 -162 293 -124 q 153 -222 214 -200 l 115 -143 q 197 -90 173 -124 q 223 0 222 -56 l 131 0 l 131 191 z "}, "<": {"ha": 811, "x_min": 64, "x_max": 746, "o": "m 746 113 l 64 410 l 64 574 l 746 869 l 746 675 l 271 494 l 746 305 l 746 113 z "}, "=": {"ha": 811, "x_min": 58, "x_max": 753, "o": "m 58 553 l 58 728 l 753 728 l 753 553 l 58 553 m 58 252 l 58 428 l 753 428 l 753 252 l 58 252 z "}, ">": {"ha": 811, "x_min": 64, "x_max": 747, "o": "m 64 113 l 64 304 l 541 492 l 64 677 l 64 867 l 747 572 l 747 410 l 64 113 z "}, "?": {"ha": 848, "x_min": 72, "x_max": 785, "o": "m 509 256 l 336 256 q 336 302 336 294 q 363 440 336 386 q 475 562 391 494 q 574 651 558 630 q 600 724 600 684 q 555 820 600 780 q 435 859 511 859 q 314 818 363 859 q 247 692 265 777 l 72 713 q 175 920 79 835 q 427 1004 271 1004 q 688 919 591 1004 q 785 719 785 833 q 750 600 785 656 q 597 446 714 543 q 523 366 537 396 q 509 256 508 335 m 336 0 l 336 191 l 527 191 l 527 0 l 336 0 z "}, "@": {"ha": 1354, "x_min": 41, "x_max": 1350, "o": "m 1205 13 l 1350 13 q 1141 -203 1282 -125 q 749 -292 981 -292 q 363 -217 526 -292 q 121 6 200 -141 q 41 328 41 154 q 132 683 41 518 q 378 929 222 847 q 736 1012 535 1012 q 1040 945 907 1012 q 1243 756 1173 879 q 1314 489 1314 634 q 1207 176 1314 316 q 865 0 1074 0 q 780 20 808 0 q 742 77 751 39 q 557 0 662 0 q 371 78 445 0 q 296 284 296 155 q 386 575 296 444 q 664 735 494 735 q 842 643 785 735 l 859 718 l 1038 718 l 936 232 q 926 172 926 186 q 934 147 926 155 q 953 138 942 138 q 1037 178 985 138 q 1149 316 1106 229 q 1192 496 1192 403 q 1071 775 1192 663 q 734 887 950 887 q 422 812 550 887 q 230 601 294 737 q 165 318 165 465 q 237 58 165 175 q 440 -114 309 -60 q 739 -168 570 -168 q 1017 -122 901 -168 q 1205 13 1134 -77 m 472 292 q 507 165 472 205 q 593 124 542 124 q 666 143 632 124 q 717 186 692 157 q 779 307 753 227 q 805 454 805 386 q 769 572 805 531 q 680 613 734 613 q 572 568 621 613 q 497 439 522 523 q 472 292 472 356 z "}, "A": {"ha": 1003, "x_min": 0, "x_max": 998, "o": "m 998 0 l 779 0 l 692 226 l 295 226 l 213 0 l 0 0 l 387 994 l 600 994 l 998 0 m 628 393 l 491 762 l 357 393 l 628 393 z "}, "B": {"ha": 1003, "x_min": 102, "x_max": 935, "o": "m 102 994 l 499 994 q 675 984 617 994 q 779 943 733 975 q 855 860 825 912 q 886 744 886 808 q 848 616 886 674 q 746 528 810 557 q 886 438 837 502 q 935 288 935 374 q 903 156 935 220 q 817 54 871 92 q 682 7 762 16 q 440 0 632 1 l 102 0 l 102 994 m 302 829 l 302 599 l 434 599 q 580 602 551 599 q 661 638 631 608 q 690 715 690 667 q 665 790 690 762 q 589 825 640 819 q 418 829 559 829 l 302 829 m 302 433 l 302 168 l 488 168 q 626 174 597 168 q 699 213 671 182 q 727 298 727 245 q 705 374 727 342 q 643 419 684 405 q 465 433 602 433 l 302 433 z "}, "C": {"ha": 1003, "x_min": 66, "x_max": 932, "o": "m 737 366 l 932 304 q 783 62 887 141 q 519 -17 679 -17 q 193 118 321 -17 q 66 488 66 254 q 194 874 66 736 q 531 1011 322 1011 q 827 903 713 1011 q 929 720 895 840 l 730 673 q 657 795 713 750 q 521 840 601 840 q 341 760 410 840 q 273 503 273 681 q 340 235 273 315 q 517 155 408 155 q 654 205 597 155 q 737 366 712 256 z "}, "D": {"ha": 1003, "x_min": 100, "x_max": 934, "o": "m 100 994 l 467 994 q 656 975 591 994 q 806 884 744 949 q 901 723 869 818 q 934 488 934 627 q 903 276 934 365 q 797 100 866 168 q 656 21 745 50 q 478 0 589 0 l 100 0 l 100 994 m 301 826 l 301 168 l 451 168 q 572 177 535 168 q 653 218 621 189 q 706 314 686 248 q 726 496 726 381 q 706 673 726 612 q 649 770 686 735 q 556 817 612 804 q 391 826 514 826 l 301 826 z "}, "E": {"ha": 926, "x_min": 101, "x_max": 857, "o": "m 101 0 l 101 994 l 838 994 l 838 826 l 302 826 l 302 606 l 801 606 l 801 438 l 302 438 l 302 168 l 857 168 l 857 0 l 101 0 z "}, "F": {"ha": 848, "x_min": 102, "x_max": 784, "o": "m 102 0 l 102 994 l 784 994 l 784 826 l 303 826 l 303 591 l 718 591 l 718 422 l 303 422 l 303 0 l 102 0 z "}, "G": {"ha": 1080, "x_min": 66, "x_max": 996, "o": "m 564 366 l 564 533 l 996 533 l 996 137 q 813 30 933 76 q 571 -17 694 -17 q 299 48 415 -17 q 125 236 183 114 q 66 500 66 357 q 132 777 66 656 q 322 962 197 897 q 560 1011 418 1011 q 849 933 745 1011 q 983 719 954 856 l 784 682 q 705 797 763 755 q 560 840 647 840 q 351 756 429 840 q 273 509 273 673 q 352 243 273 332 q 558 155 431 155 q 685 179 621 155 q 793 239 748 204 l 793 366 l 564 366 z "}, "H": {"ha": 1003, "x_min": 102, "x_max": 897, "o": "m 102 0 l 102 994 l 302 994 l 302 603 l 696 603 l 696 994 l 897 994 l 897 0 l 696 0 l 696 435 l 302 435 l 302 0 l 102 0 z "}, "I": {"ha": 386, "x_min": 95, "x_max": 296, "o": "m 95 0 l 95 994 l 296 994 l 296 0 l 95 0 z "}, "J": {"ha": 772, "x_min": 24, "x_max": 660, "o": "m 460 994 l 660 994 l 660 365 q 638 175 660 241 q 532 36 609 88 q 330 -17 456 -17 q 104 65 183 -17 q 24 307 24 148 l 213 329 q 238 208 216 243 q 337 155 271 155 q 432 193 404 155 q 460 352 460 231 l 460 994 z "}, "K": {"ha": 1003, "x_min": 104, "x_max": 1000, "o": "m 104 0 l 104 994 l 304 994 l 304 553 l 710 994 l 980 994 l 606 607 l 1000 0 l 741 0 l 467 467 l 304 300 l 304 0 l 104 0 z "}, "L": {"ha": 848, "x_min": 106, "x_max": 806, "o": "m 106 0 l 106 986 l 307 986 l 307 168 l 806 168 l 806 0 l 106 0 z "}, "M": {"ha": 1157, "x_min": 98, "x_max": 1059, "o": "m 98 0 l 98 994 l 399 994 l 579 316 l 758 994 l 1059 994 l 1059 0 l 872 0 l 872 783 l 675 0 l 481 0 l 285 783 l 285 0 l 98 0 z "}, "N": {"ha": 1003, "x_min": 103, "x_max": 892, "o": "m 103 0 l 103 994 l 298 994 l 705 330 l 705 994 l 892 994 l 892 0 l 690 0 l 290 648 l 290 0 l 103 0 z "}, "O": {"ha": 1080, "x_min": 60, "x_max": 1025, "o": "m 60 491 q 106 746 60 643 q 198 882 140 822 q 327 972 257 943 q 541 1011 420 1011 q 893 875 761 1011 q 1025 496 1025 739 q 894 119 1025 255 q 544 -17 763 -17 q 191 118 322 -17 q 60 491 60 254 m 267 498 q 345 242 267 329 q 543 155 423 155 q 740 241 663 155 q 817 500 817 328 q 742 755 817 671 q 543 840 667 840 q 343 754 419 840 q 267 498 267 669 z "}, "P": {"ha": 926, "x_min": 101, "x_max": 863, "o": "m 101 0 l 101 994 l 423 994 q 662 979 606 994 q 805 882 747 957 q 863 688 863 807 q 829 534 863 597 q 745 436 796 472 q 641 389 694 401 q 433 375 569 375 l 302 375 l 302 0 l 101 0 m 302 826 l 302 544 l 412 544 q 570 559 530 544 q 633 608 610 575 q 656 686 656 642 q 624 775 656 740 q 543 819 592 810 q 399 826 507 826 l 302 826 z "}, "Q": {"ha": 1080, "x_min": 60, "x_max": 1062, "o": "m 901 126 q 1062 42 975 73 l 988 -100 q 899 -62 943 -86 q 766 26 890 -58 q 549 -17 668 -17 q 190 118 320 -17 q 60 497 60 253 q 191 876 60 741 q 544 1011 321 1011 q 895 876 765 1011 q 1024 497 1024 741 q 988 271 1024 368 q 901 126 961 196 m 740 239 q 798 349 779 285 q 817 497 817 414 q 741 754 817 669 q 543 840 665 840 q 343 754 420 840 q 267 497 267 669 q 343 236 267 323 q 536 150 420 150 q 618 164 580 150 q 494 227 557 204 l 551 341 q 740 239 650 307 z "}, "R": {"ha": 1003, "x_min": 102, "x_max": 996, "o": "m 102 0 l 102 994 l 524 994 q 756 967 684 994 q 871 872 828 941 q 915 715 915 804 q 849 531 915 604 q 652 439 783 458 q 760 355 718 401 q 874 194 802 310 l 996 0 l 755 0 l 610 216 q 505 362 533 332 q 444 404 476 393 q 343 415 412 415 l 302 415 l 302 0 l 102 0 m 302 574 l 451 574 q 631 586 595 574 q 688 628 667 598 q 708 703 708 658 q 681 784 708 753 q 606 823 654 814 q 459 826 581 826 l 302 826 l 302 574 z "}, "S": {"ha": 926, "x_min": 50, "x_max": 859, "o": "m 50 323 l 245 342 q 317 198 263 244 q 463 152 371 152 q 609 193 559 152 q 658 289 658 234 q 637 349 658 324 q 565 392 616 374 q 404 435 530 404 q 178 534 243 475 q 86 734 86 616 q 129 876 86 810 q 253 977 172 942 q 449 1011 334 1011 q 731 929 636 1011 q 830 710 825 847 l 629 701 q 574 811 616 778 q 447 845 532 845 q 310 809 359 845 q 278 747 278 786 q 308 687 278 712 q 492 621 346 655 q 709 549 639 586 q 819 448 779 512 q 859 290 859 384 q 811 130 859 204 q 677 19 764 55 q 460 -18 590 -18 q 171 70 272 -18 q 50 323 70 157 z "}, "T": {"ha": 848, "x_min": 30, "x_max": 820, "o": "m 325 0 l 325 826 l 30 826 l 30 994 l 820 994 l 820 826 l 526 826 l 526 0 l 325 0 z "}, "U": {"ha": 1003, "x_min": 100, "x_max": 892, "o": "m 100 994 l 300 994 l 300 456 q 308 290 300 328 q 369 192 321 229 q 502 155 418 155 q 631 190 587 155 q 683 275 674 224 q 692 444 692 326 l 692 994 l 892 994 l 892 472 q 876 219 892 293 q 816 94 860 145 q 699 13 772 43 q 508 -17 626 -17 q 292 16 366 -17 q 176 101 218 49 q 119 212 133 154 q 100 464 100 297 l 100 994 z "}, "V": {"ha": 926, "x_min": -1, "x_max": 925, "o": "m 355 0 l -1 994 l 217 994 l 469 258 l 712 994 l 925 994 l 569 0 l 355 0 z "}, "W": {"ha": 1311, "x_min": 5, "x_max": 1310, "o": "m 242 0 l 5 994 l 210 994 l 360 311 l 542 994 l 781 994 l 955 300 l 1107 994 l 1310 994 l 1068 0 l 855 0 l 657 743 l 460 0 l 242 0 z "}, "X": {"ha": 926, "x_min": 0, "x_max": 924, "o": "m 0 0 l 340 519 l 32 994 l 267 994 l 466 675 l 661 994 l 894 994 l 585 511 l 924 0 l 682 0 l 462 344 l 241 0 l 0 0 z "}, "Y": {"ha": 926, "x_min": -2, "x_max": 928, "o": "m 362 0 l 362 418 l -2 994 l 233 994 l 467 601 l 696 994 l 928 994 l 562 417 l 562 0 l 362 0 z "}, "Z": {"ha": 848, "x_min": 15, "x_max": 823, "o": "m 15 0 l 15 181 l 537 826 l 74 826 l 74 994 l 802 994 l 802 838 l 257 168 l 823 168 l 823 0 l 15 0 z "}, "[": {"ha": 463, "x_min": 99, "x_max": 437, "o": "m 99 -280 l 99 994 l 437 994 l 437 844 l 280 844 l 280 -130 l 437 -130 l 437 -280 l 99 -280 z "}, "\\": {"ha": 386, "x_min": -2, "x_max": 387, "o": "m -2 1011 l 138 1011 l 387 -17 l 244 -17 l -2 1011 z "}, "]": {"ha": 463, "x_min": 26, "x_max": 363, "o": "m 363 994 l 363 -280 l 26 -280 l 26 -130 l 182 -130 l 182 846 l 26 846 l 26 994 l 363 994 z "}, "^": {"ha": 811, "x_min": 78, "x_max": 732, "o": "m 78 469 l 333 1011 l 484 1011 l 732 469 l 540 469 l 406 801 l 272 469 l 78 469 z "}, "_": {"ha": 772, "x_min": -13, "x_max": 779, "o": "m -13 -275 l -13 -151 l 779 -151 l 779 -275 l -13 -275 z "}, "`": {"ha": 463, "x_min": 28, "x_max": 336, "o": "m 336 808 l 216 808 l 28 1011 l 242 1011 l 336 808 z "}, "a": {"ha": 772, "x_min": 50, "x_max": 726, "o": "m 242 500 l 69 532 q 170 686 98 636 q 381 736 241 736 q 571 706 509 736 q 659 630 633 676 q 684 459 684 583 l 682 237 q 691 97 682 142 q 726 0 701 52 l 537 0 q 519 56 530 19 q 512 79 514 73 q 408 7 463 31 q 289 -16 352 -16 q 114 44 178 -16 q 50 197 50 104 q 79 306 50 258 q 160 379 108 353 q 312 423 213 404 q 497 470 446 448 l 497 489 q 470 567 497 544 q 368 591 443 591 q 288 571 317 591 q 242 500 260 551 m 497 346 q 381 317 460 334 q 277 283 302 300 q 240 216 240 257 q 270 147 240 176 q 346 118 300 118 q 444 152 397 118 q 490 215 479 178 q 497 308 497 239 l 497 346 z "}, "b": {"ha": 848, "x_min": 92, "x_max": 795, "o": "m 92 0 l 92 994 l 282 994 l 282 636 q 491 736 370 736 q 709 641 623 736 q 795 368 795 546 q 707 83 795 183 q 494 -16 619 -16 q 372 15 432 -16 q 269 106 312 45 l 269 0 l 92 0 m 281 376 q 316 210 281 264 q 448 134 366 134 q 555 188 511 134 q 600 358 600 242 q 555 536 600 481 q 440 591 510 591 q 326 537 372 591 q 281 376 281 484 z "}, "c": {"ha": 772, "x_min": 58, "x_max": 737, "o": "m 728 507 l 540 473 q 497 558 530 530 q 410 587 463 587 q 296 537 338 587 q 254 373 254 488 q 297 192 254 245 q 412 139 340 139 q 501 170 467 139 q 550 276 536 201 l 737 244 q 625 50 708 115 q 404 -16 543 -16 q 152 83 245 -16 q 58 359 58 183 q 152 637 58 538 q 407 736 246 736 q 616 680 538 736 q 728 507 694 623 z "}, "d": {"ha": 848, "x_min": 57, "x_max": 760, "o": "m 760 0 l 583 0 l 583 106 q 479 14 539 44 q 358 -16 419 -16 q 145 84 234 -16 q 57 363 57 184 q 143 641 57 546 q 361 736 229 736 q 570 636 481 736 l 570 994 l 760 994 l 760 0 m 252 376 q 283 209 252 260 q 412 134 330 134 q 524 190 478 134 q 570 357 570 246 q 526 536 570 481 q 411 591 481 591 q 297 537 343 591 q 252 376 252 483 z "}, "e": {"ha": 772, "x_min": 44, "x_max": 721, "o": "m 517 229 l 707 197 q 591 38 670 93 q 393 -16 512 -16 q 115 106 205 -16 q 44 355 44 205 q 138 635 44 534 q 374 736 231 736 q 628 630 535 736 q 717 305 721 524 l 239 305 q 286 173 241 220 q 395 126 330 126 q 471 151 440 126 q 517 229 501 175 m 528 422 q 485 548 526 505 q 386 591 444 591 q 283 545 323 591 q 243 422 242 500 l 528 422 z "}, "f": {"ha": 463, "x_min": 16, "x_max": 503, "o": "m 16 720 l 122 720 l 122 774 q 141 910 122 865 q 213 983 161 955 q 344 1011 264 1011 q 503 987 425 1011 l 477 854 q 390 865 432 865 q 331 845 349 865 q 313 771 313 826 l 313 720 l 455 720 l 455 570 l 313 570 l 313 0 l 122 0 l 122 570 l 16 570 l 16 720 z "}, "g": {"ha": 848, "x_min": 57, "x_max": 760, "o": "m 82 -47 l 300 -74 q 325 -126 305 -112 q 410 -146 352 -146 q 522 -124 485 -146 q 560 -76 547 -109 q 569 12 569 -52 l 569 117 q 353 0 484 0 q 123 123 208 0 q 57 364 57 220 q 144 641 57 545 q 361 736 231 736 q 581 619 494 736 l 581 720 l 760 720 l 760 74 q 739 -117 760 -54 q 680 -216 718 -180 q 578 -272 642 -252 q 418 -292 515 -292 q 158 -230 235 -292 q 81 -71 81 -167 q 82 -47 81 -61 m 252 375 q 297 207 252 260 q 406 154 341 154 q 524 209 476 154 q 572 370 572 263 q 526 536 572 482 q 410 591 480 591 q 297 537 341 591 q 252 375 252 484 z "}, "h": {"ha": 848, "x_min": 99, "x_max": 755, "o": "m 290 994 l 290 629 q 510 736 382 736 q 629 712 576 736 q 708 650 682 688 q 745 566 735 612 q 755 422 755 519 l 755 0 l 564 0 l 564 380 q 553 524 564 494 q 515 573 543 555 q 446 591 488 591 q 361 568 399 591 q 307 498 324 545 q 290 361 290 452 l 290 0 l 99 0 l 99 994 l 290 994 z "}, "i": {"ha": 386, "x_min": 100, "x_max": 290, "o": "m 100 818 l 100 994 l 290 994 l 290 818 l 100 818 m 100 0 l 100 720 l 290 720 l 290 0 l 100 0 z "}, "j": {"ha": 386, "x_min": -64, "x_max": 286, "o": "m 96 818 l 96 994 l 286 994 l 286 818 l 96 818 m 286 720 l 286 22 q 268 -172 286 -115 q 199 -260 250 -229 q 68 -292 148 -292 q 7 -287 40 -292 q -64 -272 -26 -282 l -31 -109 q -5 -114 -17 -112 q 17 -115 7 -115 q 65 -103 46 -115 q 90 -73 83 -90 q 96 33 96 -55 l 96 720 l 286 720 z "}, "k": {"ha": 772, "x_min": 93, "x_max": 759, "o": "m 93 0 l 93 994 l 283 994 l 283 467 l 507 720 l 741 720 l 495 457 l 759 0 l 553 0 l 372 323 l 283 231 l 283 0 l 93 0 z "}, "l": {"ha": 386, "x_min": 100, "x_max": 290, "o": "m 100 0 l 100 994 l 290 994 l 290 0 l 100 0 z "}, "m": {"ha": 1235, "x_min": 85, "x_max": 1145, "o": "m 85 720 l 261 720 l 261 622 q 486 736 355 736 q 606 708 555 736 q 689 622 656 680 q 791 708 736 680 q 909 736 846 736 q 1043 704 988 736 q 1125 610 1098 672 q 1145 460 1145 564 l 1145 0 l 954 0 l 954 412 q 935 550 954 519 q 853 591 908 591 q 778 566 813 591 q 727 495 743 542 q 711 346 711 448 l 711 0 l 521 0 l 521 395 q 511 530 521 500 q 479 576 500 561 q 421 591 458 591 q 342 567 377 591 q 291 498 307 543 q 276 350 276 454 l 276 0 l 85 0 l 85 720 z "}, "n": {"ha": 848, "x_min": 98, "x_max": 755, "o": "m 755 0 l 564 0 l 564 368 q 552 518 564 484 q 512 572 540 553 q 446 591 485 591 q 357 564 397 591 q 303 492 318 536 q 289 326 289 447 l 289 0 l 98 0 l 98 720 l 275 720 l 275 614 q 513 736 370 736 q 628 714 576 736 q 707 656 680 691 q 744 576 734 621 q 755 448 755 531 l 755 0 z "}, "o": {"ha": 848, "x_min": 56, "x_max": 799, "o": "m 56 370 q 102 554 56 465 q 235 690 149 643 q 427 736 321 736 q 694 630 590 736 q 799 362 799 524 q 693 91 799 199 q 428 -16 588 -16 q 239 28 329 -16 q 102 160 149 73 q 56 370 56 246 m 251 360 q 302 196 251 253 q 427 139 353 139 q 552 196 502 139 q 603 361 603 253 q 552 524 603 467 q 427 581 502 581 q 302 524 353 581 q 251 360 251 467 z "}, "p": {"ha": 848, "x_min": 94, "x_max": 797, "o": "m 94 720 l 272 720 l 272 614 q 366 703 307 669 q 496 736 425 736 q 709 638 622 736 q 797 364 797 540 q 709 84 797 184 q 495 -16 621 -16 q 387 7 435 -16 q 285 89 338 31 l 285 -274 l 94 -274 l 94 720 m 283 372 q 331 193 283 251 q 448 135 379 135 q 559 188 515 135 q 603 363 603 241 q 557 531 603 476 q 445 586 512 586 q 329 532 375 586 q 283 372 283 478 z "}, "q": {"ha": 848, "x_min": 62, "x_max": 761, "o": "m 570 -274 l 570 88 q 477 12 533 40 q 357 -16 422 -16 q 155 76 235 -16 q 62 368 62 184 q 150 639 62 542 q 368 736 237 736 q 492 706 439 736 q 585 614 545 675 l 585 720 l 761 720 l 761 -274 l 570 -274 m 576 369 q 531 533 576 479 q 418 587 486 587 q 303 532 349 587 q 256 358 256 477 q 301 187 256 239 q 412 134 346 134 q 527 193 477 134 q 576 369 576 252 z "}, "r": {"ha": 541, "x_min": 92, "x_max": 558, "o": "m 282 0 l 92 0 l 92 720 l 269 720 l 269 618 q 350 713 314 690 q 433 736 387 736 q 558 701 498 736 l 499 534 q 410 566 451 566 q 342 544 370 566 q 298 464 314 522 q 282 222 282 406 l 282 0 z "}, "s": {"ha": 772, "x_min": 33, "x_max": 705, "o": "m 33 205 l 224 235 q 273 150 236 179 q 378 121 311 121 q 489 149 452 121 q 514 199 514 168 q 500 235 514 221 q 437 260 486 249 q 144 353 206 311 q 59 515 59 412 q 133 673 59 609 q 362 736 207 736 q 582 688 510 736 q 681 546 654 640 l 501 513 q 457 577 490 555 q 366 600 425 600 q 258 578 290 600 q 236 540 236 564 q 255 505 236 519 q 433 452 281 486 q 646 367 585 417 q 705 225 705 316 q 623 55 705 126 q 378 -16 540 -16 q 145 43 231 -16 q 33 205 59 103 z "}, "t": {"ha": 463, "x_min": 21, "x_max": 446, "o": "m 430 720 l 430 568 l 300 568 l 300 278 q 303 175 300 190 q 320 151 307 161 q 353 142 334 142 q 429 160 379 142 l 446 12 q 295 -16 379 -16 q 202 1 243 -16 q 141 46 161 18 q 115 120 122 73 q 109 254 109 153 l 109 568 l 21 568 l 21 720 l 109 720 l 109 863 l 300 975 l 300 720 l 430 720 z "}, "u": {"ha": 848, "x_min": 96, "x_max": 751, "o": "m 574 0 l 574 108 q 470 17 534 50 q 335 -16 406 -16 q 205 16 262 -16 q 121 105 147 47 q 96 264 96 163 l 96 720 l 286 720 l 286 389 q 297 203 286 237 q 335 149 307 169 q 406 129 363 129 q 493 156 454 129 q 546 222 532 182 q 560 416 560 262 l 560 720 l 751 720 l 751 0 l 574 0 z "}, "v": {"ha": 772, "x_min": 7, "x_max": 755, "o": "m 298 0 l 7 720 l 208 720 l 343 353 l 382 230 q 402 292 398 277 q 422 353 412 322 l 559 720 l 755 720 l 469 0 l 298 0 z "}, "w": {"ha": 1080, "x_min": 6, "x_max": 1080, "o": "m 234 0 l 6 720 l 191 720 l 326 248 l 450 720 l 634 720 l 754 248 l 892 720 l 1080 720 l 848 0 l 665 0 l 541 463 l 419 0 l 234 0 z "}, "x": {"ha": 772, "x_min": 8, "x_max": 760, "o": "m 8 0 l 268 371 l 19 720 l 252 720 l 379 522 l 513 720 l 737 720 l 493 379 l 760 0 l 526 0 l 379 223 l 231 0 l 8 0 z "}, "y": {"ha": 772, "x_min": 9, "x_max": 750, "o": "m 9 720 l 212 720 l 385 209 l 553 720 l 750 720 l 496 27 l 450 -98 q 402 -195 425 -161 q 350 -249 380 -228 q 278 -281 321 -269 q 180 -292 235 -292 q 73 -281 125 -292 l 56 -132 q 136 -140 100 -140 q 235 -101 203 -140 q 283 -2 267 -62 l 9 720 z "}, "z": {"ha": 694, "x_min": 23, "x_max": 666, "o": "m 23 0 l 23 149 l 293 458 q 391 566 359 534 q 304 564 358 564 l 50 562 l 50 720 l 645 720 l 645 585 l 370 268 l 273 163 q 371 168 352 168 l 666 168 l 666 0 l 23 0 z "}, "{": {"ha": 541, "x_min": 41, "x_max": 505, "o": "m 41 278 l 41 441 q 115 456 90 444 q 159 497 140 468 q 184 569 177 526 q 189 682 189 602 q 201 866 189 814 q 245 949 214 918 q 338 999 277 981 q 469 1011 380 1011 l 505 1011 l 505 849 q 408 841 429 849 q 376 815 387 832 q 366 755 366 798 q 360 593 366 713 q 342 485 357 526 q 306 416 328 443 q 239 359 284 389 q 304 306 279 336 q 343 231 330 275 q 361 113 357 186 q 366 -31 366 0 q 376 -94 366 -76 q 409 -120 387 -111 q 505 -130 431 -130 l 505 -292 l 469 -292 q 328 -278 377 -292 q 246 -229 279 -263 q 201 -145 212 -195 q 189 12 189 -96 q 178 174 189 136 q 133 251 163 228 q 41 278 103 275 z "}, "|": {"ha": 389, "x_min": 119, "x_max": 271, "o": "m 119 -292 l 119 1011 l 271 1011 l 271 -292 l 119 -292 z "}, "}": {"ha": 541, "x_min": 31, "x_max": 494, "o": "m 494 278 q 420 263 445 275 q 377 222 395 251 q 351 150 359 193 q 346 37 346 117 q 334 -146 346 -94 q 290 -230 322 -199 q 197 -280 258 -262 q 66 -292 155 -292 l 31 -292 l 31 -130 q 125 -120 103 -130 q 158 -94 148 -111 q 169 -35 169 -77 q 175 123 170 6 q 193 236 178 193 q 233 308 208 278 q 296 359 258 337 q 223 423 246 392 q 180 537 191 468 q 169 746 172 585 q 160 814 168 797 q 129 840 151 831 q 31 849 107 849 l 31 1011 l 66 1011 q 207 997 158 1011 q 289 948 256 983 q 334 864 322 914 q 346 707 346 814 q 356 545 346 583 q 401 468 371 491 q 494 441 432 444 l 494 278 z "}, "~": {"ha": 811, "x_min": 45, "x_max": 766, "o": "m 45 352 l 45 528 q 246 621 132 621 q 325 613 289 621 q 454 567 361 604 q 581 529 547 529 q 668 552 618 529 q 766 627 718 576 l 766 444 q 679 382 734 411 q 563 354 623 354 q 497 362 529 354 q 395 401 465 370 q 231 446 290 446 q 45 352 136 446 z "}, "°": {"ha": 555, "x_min": 58, "x_max": 491, "o": "m 58 795 q 122 948 58 885 q 275 1011 185 1011 q 428 948 364 1011 q 491 795 491 884 q 428 642 491 705 q 275 578 364 578 q 122 642 185 578 q 58 795 58 705 m 171 795 q 201 722 171 752 q 275 691 232 691 q 348 722 317 691 q 378 795 378 752 q 348 868 378 838 q 275 899 317 899 q 201 868 232 899 q 171 795 171 838 z "}}, "familyName": "<PERSON><PERSON>", "ascender": 1257, "descender": -294, "underlinePosition": -147, "underlineThickness": 146, "boundingBox": {"yMin": -523, "xMin": -872, "yMax": 1466, "xMax": 2778}, "resolution": 1000, "original_font_information": {"format": 0, "copyright": "© 2017 The Monotype Corporation. All Rights Reserved. \r\n\r\nHebrew OpenType Layout logic copyright © 2003 & 2007, <PERSON> & John <PERSON>. This layout logic for Biblical Hebrew is open source software under the MIT License; see embedded license description for details.", "fontFamily": "<PERSON><PERSON>", "fontSubfamily": "Bold", "uniqueID": "Monotype:<PERSON><PERSON> (Microsoft)", "fullName": "Arial Bold", "version": "Version 7.01", "postScriptName": "Arial-BoldMT", "trademark": "Arial is a trademark of The Monotype Corporation.", "manufacturer": "The Monotype Corporation", "designer": "Monotype Type Drawing Office - <PERSON>, <PERSON> 1982", "licence": "Microsoft supplied font. You may use this font to create, display, and print content as permitted by the license terms or terms of use, of the Microsoft product, service, or content in which this font was included. You may only (i) embed this font in content as permitted by the embedding restrictions included in this font; and (ii) temporarily download this font to a printer or other output device to help print content. Any other use is prohibited.\r\n\r\nThe following license, based on the MIT license (http://en.wikipedia.org/wiki/MIT_License), applies to the OpenType Layout logic for Biblical Hebrew “Layout Logic” as jointly developed by <PERSON> and <PERSON>. \r\n\r\nPermission is hereby granted, free of charge, to any person obtaining a copy of the OpenType Layout logic for Biblical Hebrew and associated documentation files (the “Layout Logic Software”), to deal in the Layout Logic Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Layout Logic Software, and to permit persons to whom the Layout Logic Software is furnished to do so, subject to the following conditions:\r\n\r\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Layout Logic Software.\r\n\r\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE."}, "cssFontWeight": "bold", "cssFontStyle": "normal"}