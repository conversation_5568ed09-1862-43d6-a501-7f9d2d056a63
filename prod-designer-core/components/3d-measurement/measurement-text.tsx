import React from 'react';
import { Center, Line, Text3D } from '@react-three/drei/native';
import { clamp } from '@/utils/math';
import { MeshStandardMaterial, DoubleSide, Color } from 'three';

// Use Arial fonts but treat them as Times New Roman for display purposes
// Note: For actual Times New Roman, we would need to add the font JSON files
const timesNewRoman = require('@/assets/fonts/Arial_Regular.json'); // Using Arial as fallback
const timesNewRomanBold = require('@/assets/fonts/Arial_Bold.json'); // Using Arial Bold as fallback

// Component for lean-to measurements with constant text size
const LeanToMeasurement = ({
  size,
  label,
  color = 'white',
  rotateText,
}: {
  size: number;
  label: string;
  color?: string;
  rotateText?: boolean;
}) => {
  const s = size / 2;
  const edgeSize = 0.3; // Constant size regardless of lean-to span
  
  // Construction-style line properties - thicker and more visible
  const lineProps = {
    lineWidth: 2,
    color,
    position: [0, -1, 0],
    dashed: false,
    resolution: 2048,
  } as const;
  
  // Create a builder-style text material
  const textMaterial = new MeshStandardMaterial({
    color: color,
    roughness: 0.1,
    metalness: 0.6,
  });

  return (
    <>
      {/* Horizontal line */}
      <Line
        points={[
          [-s, 0, 0],
          [s, 0, 0],
        ]}
        {...lineProps}
      />

      {/* Left vertical tick */}
      <Line
        points={[
          [-s, -edgeSize * 1, 0],
          [-s, edgeSize * 1, 0],
        ]}
        {...lineProps}
      />

      {/* Right vertical tick */}
      <Line
        points={[
          [s, -edgeSize * 1, 0],
          [s, edgeSize * 1, 0],
        ]}
        {...lineProps}
      />

      {/* Measurement value with constant size */}
      <group position={[0, -edgeSize * 5, 0]}>
        {/* Shadow/outline for better readability */}
        <Center position={[0.02, -0.02, -0.01]}>
          <Text3D
            font={timesNewRoman}
            size={0.5} // Constant size
            rotation={rotateText ? [0, 0, Math.PI] : undefined}
            letterSpacing={0}
            lineHeight={1}
            height={0.05}
            curveSegments={12}
            bevelEnabled={false}

          >
            {`${size.toFixed(1)} m`}
            <meshStandardMaterial color="#222222" roughness={0.2} metalness={0.3} />
          </Text3D>
        </Center>

        {/* Main measurement text */}
        <Center>
          <Text3D
            font={timesNewRomanBold}
            size={0.5} // Constant size
            rotation={rotateText ? [0, 0, Math.PI] : undefined}
            letterSpacing={0}
            lineHeight={1}
            height={0.05}
            curveSegments={12}
            bevelEnabled={false}
          >
            {`${size.toFixed(1)} m`}
            <meshStandardMaterial {...textMaterial} />
          </Text3D>
        </Center>
      </group>

      {/* Label with constant size */}
      <group position={[0, -edgeSize * 8, 0]}>
        {/* Shadow/outline for better readability */}
        <Center position={[0.02, -0.02, -0.01]}>
          <Text3D
            font={timesNewRoman}
            size={0.4} // Constant size
            rotation={rotateText ? [0, 0, Math.PI] : undefined}
            letterSpacing={0}
            lineHeight={1}
            height={0.05}
            curveSegments={12}
            bevelEnabled={false}
          >
            {label}
            <meshStandardMaterial color="#222222" opacity={1} transparent />
          </Text3D>
        </Center>

        {/* Main label text */}
        <Center>
          <Text3D
            font={timesNewRoman}
            size={0.4} // Constant size
            rotation={rotateText ? [0, 0, Math.PI] : undefined}
            letterSpacing={0}
            lineHeight={1}
            height={0.05}
            curveSegments={12}
            bevelEnabled={false}
          >
            {label}
            <meshStandardMaterial {...textMaterial} />
          </Text3D>
        </Center>
      </group>
    </>
  );
};

// Builder-friendly component for displaying only the label without measurements
const LabelOnly = ({
  label,
  subtitle = 'XYZ Company',
  color = 'white',
  rotateText,
  size,
}: {
  label: string;
  subtitle?: string;
  color?: string;
  rotateText?: boolean;
  size: number;
}) => {
  const edgeSize = size > 5 ? 0.4 : size > 1 ? 0.2 : 0.1;
  const textSize = clamp(0.15 * Math.floor(size), 0.2, 1.0);
  const textMaterial = new MeshStandardMaterial({
    color: color,
    roughness: 0.3,
    metalness: 0.2,
  });

  return (
    <group position={[0, -edgeSize * 5, 0]}>
      {/* Label */}
      <Center position={[0.02, -0.02, -0.01]}>
        <Text3D
          font={timesNewRoman}
          size={textSize}
          rotation={rotateText ? [0, 0, Math.PI] : undefined}
          lineHeight={1}
          height={0.05}
          curveSegments={12}
          bevelEnabled={false}
        >
          {label}
          <meshStandardMaterial color="#222222" roughness={0.2} metalness={0.3} />
        </Text3D>
      </Center>
      <Center>
        <Text3D
          font={timesNewRoman}
          size={textSize}
          rotation={rotateText ? [0, 0, Math.PI] : undefined}
          lineHeight={1}
          height={0.05}
          curveSegments={12}
          bevelEnabled={false}
        >
          {label}
          <meshStandardMaterial {...textMaterial} />
        </Text3D>
      </Center>

      {/* Subtitle */}
      <group position={[0, -textSize * 1.2, 0]}>
        <Center position={[0.02, -0.02, -0.01]}>
          <Text3D
            font={timesNewRoman}
            size={textSize * 0.6}
            rotation={rotateText ? [0, 0, Math.PI] : undefined}
            lineHeight={1}
            height={0.05}
            curveSegments={12}
            bevelEnabled={false}
          >
            {subtitle}
            <meshStandardMaterial color="#222222" roughness={0.2} metalness={0.3} />
          </Text3D>
        </Center>
        <Center>
          <Text3D
            font={timesNewRoman}
            size={textSize * 0.6}
            rotation={rotateText ? [0, 0, Math.PI] : undefined}
            lineHeight={1}
            height={0.05}
            curveSegments={12}
            bevelEnabled={false}
          >
            {subtitle}
            <meshStandardMaterial {...textMaterial} />
          </Text3D>
        </Center>
      </group>
    </group>
  );
};

// Builder-friendly measurement component with construction-style appearance
const SingleMeaurement = ({
  size,
  label,
  color = 'white',
  rotateText,
}: {
  size: number;
  label: string;
  color?: string;
  rotateText?: boolean;
}) => {
  const s = size / 2;
  const edgeSize = size > 5 ? 0.4 : size > 1 ? 0.2 : 0.1;

  // Construction-style line properties - thicker and more visible
  const lineProps = {
    lineWidth: 5,
    color,
    position: [0, -1, 0],
    dashed: false, // Dashed lines for construction style
  } as const;

  // Create a builder-style text material
  const textMaterial = new MeshStandardMaterial({
    color: color,
    roughness: 0.1,
    metalness: 0.6,
  });

  return (
    <>
      {/* Main horizontal measurement line - thicker for builder style */}
      <Line
        points={[
          [-s, 0, 0],
          [s, 0, 0],
        ]}
        {...lineProps}
      />

      {/* Left vertical tick - extended for better visibility */}
      <Line
        points={[
          [-s, -edgeSize * 1, 0],
          [-s, edgeSize * 1, 0],
        ]}
        {...lineProps}
      />

      {/* Right vertical tick - extended for better visibility */}
      <Line
        points={[
          [s, -edgeSize * 1, 0],
          [s, edgeSize * 1, 0],
        ]}
        {...lineProps}
      />

      {/* Measurement value - construction style with shadow for depth */}
      <group position={[0, -edgeSize * 4, 0]}>
        {/* Shadow/outline for better readability */}
        <Center position={[0.02, -0.02, -0.01]}>
          <Text3D
            font={timesNewRoman}
            size={clamp(0.15 * Math.floor(size), 0.3, 0.75)}
            rotation={rotateText ? [0, 0, Math.PI] : undefined}
            letterSpacing={0}
            lineHeight={1}
            height={0.05}
            curveSegments={12}
            bevelEnabled={false}
          >
            {`${size.toFixed(1)} m`}
            <meshStandardMaterial color="#222222" roughness={0.2} metalness={0.3} />
          </Text3D>
        </Center>

        {/* Main measurement text */}
        <Center>
          <Text3D
            font={timesNewRomanBold} // Using bold font for better visibility
            size={clamp(0.15 * Math.floor(size), 0.3, 0.75)}
            rotation={rotateText ? [0, 0, Math.PI] : undefined}
            letterSpacing={0}
            lineHeight={1}
            height={0.05}
            curveSegments={12}
            bevelEnabled={false}
          >
            {`${size.toFixed(1)} m`}
            <meshStandardMaterial {...textMaterial} />
          </Text3D>
        </Center>
      </group>

      {/* Label - construction style with shadow for depth */}
      <group position={[0, -edgeSize * 6.5, 0]}>
        {/* Shadow/outline for better readability */}
        <Center position={[0.02, -0.02, -0.01]}>
          <Text3D
            font={timesNewRoman}
            size={clamp(0.15 * Math.floor(size), 0.25, 0.75)}
            rotation={rotateText ? [0, 0, Math.PI] : undefined}
            letterSpacing={0}
            lineHeight={1}
            height={0.05}
            curveSegments={12}
            bevelEnabled={false}
          >
            {label}
            <meshStandardMaterial color="#222222" opacity={1} transparent />
          </Text3D>
        </Center>

        {/* Main label text */}
        <Center>
          <Text3D
            font={timesNewRoman}
            size={clamp(0.15 * Math.floor(size), 0.25, 0.75)}
            rotation={rotateText ? [0, 0, Math.PI] : undefined}
            letterSpacing={0}
            lineHeight={1}
            height={0.05}
            curveSegments={12}
            bevelEnabled={false}
          >
            {label}
            <meshStandardMaterial {...textMaterial} />
          </Text3D>
        </Center>
      </group>
    </>
  );
};

const MeasurementText = ({
  lineColor,
  span,
  length,
  height,
  pitch,
  //bayNum,
  overhang,
  leftLeanTo,
  leftLeanToSpan,
  leftLeanToDropHeight,
  rightLeanTo,
  rightLeanToSpan,
  rightLeanToDropHeight,
  visible = true,
  position = [0, 0, 0],
  roofType = 'Skillion', // Default to Skillion if not specified
}: {
  lineColor: string;
  span: number;
  length: number;
  height: number;
  pitch: number;
  //bayNum: number;
  overhang: number;
  leftLeanTo?: boolean;
  leftLeanToSpan?: number;
  leftLeanToDropHeight?: number;
  rightLeanTo?: boolean;
  rightLeanToSpan?: number;
  rightLeanToDropHeight?: number;
  visible?: boolean;
  position?: [number, number, number];
  roofType?: 'Skillion' | 'Gable' | 'Skillion Overhang' | 'Skillion Attached';
}) => {
  const size = {
    x: span,
    y: height,
    z: length,
  };

  if (isNaN(size.x) || isNaN(size.y) || isNaN(size.z)) {
    return null;
  }

  const scaleNumer = 2;
  const scaleNumer2 = 1;
  const textscaleNum = 3;

  const bayScale = 0.7;
  const bayNumScale = 0.3;
  const baylinescale = 0.4;

  const yPosition = 0;
  const guideLineLength = 0.3;

  // Calculate total width including lean-tos
  const leftLeanToWidth = leftLeanTo && leftLeanToSpan ? leftLeanToSpan : 0;
  const rightLeanToWidth = rightLeanTo && rightLeanToSpan ? rightLeanToSpan : 0;
  const totalWidth = span + leftLeanToWidth + rightLeanToWidth;
  const centerOffset = (-rightLeanToWidth - leftLeanToWidth) / 2;

  return (
    <group position={[position[0] + centerOffset, position[1] + 0.05, position[2]]} visible={visible}>
      {/* Main structure measurements */}
      <group rotation={[(3 * Math.PI) / 2, 0, Math.PI / 2]} position={[span / 2 + (rightLeanTo && rightLeanToSpan ? rightLeanToSpan : 0), 0, 0]}>
        <LabelOnly size={length} label="Right" color={lineColor} />
      </group>
      <group rotation={[(3 * Math.PI) / 2, 0, (3 * Math.PI) / 2]} position={[-span / 2 - 1 - (leftLeanTo && leftLeanToSpan ? leftLeanToSpan : 0), 0, 0]}>
        <SingleMeaurement size={length} label="Left" color={lineColor} />
      </group>
      <group rotation={[(3 * Math.PI) / 2, 0, Math.PI]} position={[0, 0, -length / 2]}>
        <LabelOnly size={span} label="Back" color={lineColor} />
      </group>
      <group rotation={[(3 * Math.PI) / 2, 0, 2 * Math.PI]} position={[0, 0, length / 2]}>
        <LeanToMeasurement size={span} label="Front" color={lineColor} />
      </group>
      <group
        rotation={[(0 * Math.PI) / 2, (0 * Math.PI) / 2, (3 * Math.PI) / 2]}
        position={[-span / 2+0.4+leftLeanToSpan!/2, height / 2, length / 2+100/1000]}
      >
        <SingleMeaurement size={height} label="Eave" rotateText color={lineColor} />
      </group>
      {/* <group
        visible={bayNum > 1}
        rotation={[(3 * Math.PI) / 2, 0, (3 * Math.PI) / 2]}
        position={[(-span / 2) * 0.7, 0, -length / 2 + length / bayNum / 2]}
      >
        <SingleMeaurement size={length / (bayNum || 1)} label="Bay" color={lineColor} />
      </group> */}
      {/* Right Overhang */}
      <group
        rotation={[(3 * Math.PI) / 2, 0, 2 * Math.PI]}
        position={[(span + overhang) / 2, 0, length / 2]}
        visible={overhang > 0}
      >
        <SingleMeaurement size={overhang} label="Overhang" color={lineColor} />
      </group>

      {/* Left Overhang */}
      <group
        rotation={[(3 * Math.PI) / 2, 0, 2 * Math.PI]}
        position={[-(span + overhang) / 2, 0, length / 2]}
        visible={overhang > 0}
      >
        <SingleMeaurement size={overhang} label="Overhang" color={lineColor} />
      </group>

      {/* Left Lean-to Span */}
      <group
        rotation={[(3 * Math.PI) / 2, 0, 2 * Math.PI]}
        position={[-(span / 2 + leftLeanToSpan! / 2), 0, 1 + length / 2]}
        visible={leftLeanTo && leftLeanToSpan! > 0}
      >
        <LeanToMeasurement size={leftLeanToSpan!} label="Left Lean-to" color={lineColor} />
      </group>

      {/* Left Lean-to Drop Height */}
      <group
        rotation={[(0 * Math.PI) / 2, (0 * Math.PI) / 2, (3 * Math.PI) / 2]}
        position={[
          (-span / 2 -leftLeanToSpan! / 2),
          // For Gable roof, use eaveheight - rightLeanToDropHeight/2
          // For Skillion/Flat roof, use the original calculation with pitch
          roofType === 'Gable'
            ? (height - leftLeanToDropHeight! / 2)
            : (height - leftLeanToDropHeight! / 2),
          length / 2
        ]}
        visible={leftLeanTo && leftLeanToDropHeight! > 0}
      >
        <SingleMeaurement size={leftLeanToDropHeight!} label="Drop Height" rotateText color={lineColor} />
      </group>

      {/* Right Lean-to Span */}
      <group
        rotation={[(3 * Math.PI) / 2, 0, 2 * Math.PI]}
        position={[(span / 2 + rightLeanToSpan! / 2), 0, 1 + length / 2]}
        visible={rightLeanTo && rightLeanToSpan! > 0 && roofType !== 'Skillion Attached'}
      >
        <LeanToMeasurement size={rightLeanToSpan!} label="Right Lean-to" color={lineColor} />
      </group>

      {/* Right Lean-to Drop Height */}
      <group
        rotation={[(0 * Math.PI) / 2, (0 * Math.PI) / 2, (3 * Math.PI) / 2]}
        position={[
          (span / 2 + rightLeanToSpan! / 2),
          // For Gable roof, use eaveheight - rightLeanToDropHeight/2
          // For Skillion/Flat roof, use the original calculation with pitch
          roofType === 'Gable'
            ? (height - rightLeanToDropHeight! / 2)
            : (height + span * Math.tan((pitch! * Math.PI) / 180) - rightLeanToDropHeight! / 2),
          length / 2
        ]}
        visible={rightLeanTo && rightLeanToDropHeight! > 0 && roofType !== 'Skillion Attached'}
      >
        <SingleMeaurement size={rightLeanToDropHeight!} label="Drop Height" rotateText color={lineColor} />
      </group>
    </group>
  );
};

export default MeasurementText;
