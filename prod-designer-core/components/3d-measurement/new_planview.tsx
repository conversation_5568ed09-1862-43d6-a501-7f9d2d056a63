import React, { useRef, useState } from 'react';
import { View, StyleSheet, Text, ScrollView } from 'react-native';
import { Canvas } from '@react-three/fiber/native';
import { OrthographicCamera, OrbitControls, Line, Center, Text3D } from '@react-three/drei/native';
import { useSceneContext } from '@/redux/context';
import Carport from '@/domain/carport-models/Carport';
import GableCarport from '@/domain/carport-models/gable_Carport';
import { Platform } from 'react-native';
import {
  MeshStandardMaterial,
  WebGLRenderer,
  Scene,
  OrthographicCamera as ThreeOrthographicCamera,
  Vector2,
  Color,
  LineBasicMaterial,
  BufferGeometry,
  Line as ThreeLine,
} from 'three';
import { Font } from 'three/examples/jsm/loaders/FontLoader';
import { TextGeometry } from 'three/examples/jsm/geometries/TextGeometry';
import * as THREE from 'three';

const timesNewRoman = require('@/assets/fonts/Arial_Regular.json');

interface QuadrantViewProps {
  isMobile: boolean;
}

export const QuadrantView: React.FC<QuadrantViewProps> = ({ isMobile }) => {
  const { dimensions, sceneSettings } = useSceneContext();
  const { length = 12000, span = 6000, height = 2400, pitch = 15, overhang = 200 } = dimensions;

  const {
    leftLeanTo,
    leftLeanToSpan,
    leftLeanToDropHeight,
    rightLeanTo,
    rightLeanToSpan,
    rightLeanToDropHeight,
    viewMode,
    showSlab,
  } = sceneSettings;

  const roofType = dimensions.roofType || 'flat';

  const leftLeanToWidth = leftLeanTo ? leftLeanToSpan || 0 : 0;
  const rightLeanToWidth = rightLeanTo ? rightLeanToSpan || 0 : 0;
  const totalWidth = span + leftLeanToWidth + rightLeanToWidth;

  const [quadSize] = useState(400);
  const largestModelDimension = Math.max(length, totalWidth);
  const complexityAdjustment = Math.min(0.3, Math.max(0.1, 8000 / largestModelDimension));
  const scaleFactor = 320 / (largestModelDimension / 1000);
  const targetSize = quadSize * 0.8;
  const padding = 1.2;
  const widthInMeters = totalWidth / 1000;
  const lengthInMeters = length / 1000;

  const visibleBoundingSize = Math.max(widthInMeters, lengthInMeters) * padding;

  // const scaleFactor = quadSize / visibleBoundingSize;

  const lineThickness = Math.min(4, 2 * (scaleFactor / 40));
  const tickSize = Math.min(0.5, Math.max(0.05, largestModelDimension / 40000));

  const textSize = Math.max(0.2, Math.min(0.8, largestModelDimension / 20000));
  const adaptiveOffset = textSize * 1.2;

  // Create refs for the orbit controls to limit their functionality
  const topControlsRef = useRef(null);
  const frontControlsRef = useRef(null);
  const leftControlsRef = useRef(null);
  const isoControlsRef = useRef(null);

  // Common props for all carport instances
  const carportProps = {
    length,
    span,
    height,
    pitch,
    overhang,
    leftLeanTo,
    leftLeanToSpan,
    leftLeanToDropHeight,
    rightLeanTo,
    rightLeanToSpan,
    rightLeanToDropHeight,
    viewMode,
    showSlab,
  };

  // Render the appropriate carport component based on roof type
  const CarportComponent = roofType === 'Gable' ? GableCarport : Carport;

  // For Front Viewsw
  const frontZoom = scaleFactor * 2.5 * complexityAdjustment;

  // For Isometric View
  const isoZoom = scaleFactor * 2.0 * complexityAdjustment;

  const textOffsetX = 0.6;
  const textZCenter = -length / 2000;

  const spanTextSpacing = 1;

  return (
    <ScrollView style={[styles.modalContainer, isMobile && { maxHeight: 400 }]}>
      <View style={[styles.row, isMobile && styles.column]}>
        <View style={[styles.quadrant, isMobile && styles.fullWidthQuadrant]}>
          <Text style={styles.label}>Top View</Text>
          <Canvas style={styles.canvas} gl={{ preserveDrawingBuffer: true }}>
            <ambientLight intensity={0.5} />
            <directionalLight position={[10, 10, 10]} intensity={1} />
            <OrthographicCamera
              makeDefault
              position={[0, 10, 0]}
              zoom={frontZoom}
              near={0.1}
              far={1000}
            />
            <OrbitControls
              ref={topControlsRef}
              enableRotate={false}
              enablePan={Platform.OS === 'web'}
              enableZoom={true}
              enableDamping={false}
              minZoom={10}
              maxZoom={100}
              makeDefault
            />
            <group position={[-totalWidth / 2000, 0, length / 2000]}>
              <CarportComponent {...carportProps} />
            </group>

            {/* Measurements for top view */}
            <group position={[overhang / 1000 - leftLeanToWidth / 1000, 0, length / 2000]}>
              {/* Length measurement */}
              <group>
                {/* Horizontal line for length */}
                <Line
                  points={[
                    [totalWidth / 2000 + 0.75, 0, -length / 1000],
                    [totalWidth / 2000 + 0.75, 0, 0],
                  ]}
                  color="black"
                  lineWidth={lineThickness}
                  dashed={false}
                />

                {/* Vertical ticks at ends */}
                <Line
                  points={[
                    [totalWidth / 2000 + 0.75 - tickSize, 0, -length / 1000],
                    [totalWidth / 2000 + 0.75 + tickSize, 0, -length / 1000],
                  ]}
                  color="black"
                  lineWidth={lineThickness}
                  dashed={false}
                />
                <Line
                  points={[
                    [totalWidth / 2000 + 0.75 - tickSize, 0, 0],
                    [totalWidth / 2000 + 0.75 + tickSize, 0, 0],
                  ]}
                  color="black"
                  lineWidth={lineThickness}
                  dashed={false}
                />

                {/* Measurement text */}
                <group position={[totalWidth / 2000 + 0.75 + textOffsetX, 0, textZCenter]}>
                  <Center>
                    <Text3D
                      font={timesNewRoman}
                      size={textSize}
                      height={0.01}
                      curveSegments={24}
                      bevelEnabled={false}
                      rotation={[-Math.PI / 2, 0, Math.PI / 2]}
                    >
                      {`Length: ${(length / 1000).toFixed(2)} m`}
                      <meshStandardMaterial color="black" toneMapped={false} />
                    </Text3D>
                  </Center>
                </group>
              </group>

              {/* Total width measurement */}
              <group>
                {/* Total width measurement (including lean-tos) */}
                <Line
                  points={[
                    [-(totalWidth / 2000), 0, 0.75],
                    [totalWidth / 2000, 0, 0.75],
                  ]}
                  color="black"
                  lineWidth={lineThickness}
                  dashed={false}
                />

                {/* Horizontal ticks at ends */}
                <Line
                  points={[
                    [-(totalWidth / 2000), 0, 0.75 - tickSize],
                    [-(totalWidth / 2000), 0, 0.75 + tickSize],
                  ]}
                  color="black"
                  lineWidth={lineThickness}
                  dashed={false}
                />
                <Line
                  points={[
                    [totalWidth / 2000, 0, 0.75 - tickSize],
                    [totalWidth / 2000, 0, 0.75 + tickSize],
                  ]}
                  color="black"
                  lineWidth={lineThickness}
                  dashed={false}
                />

                {/* Measurement text */}
                <group position={[0, 0, 0.75 + adaptiveOffset]}>
                  <Center>
                    <Text3D
                      font={timesNewRoman}
                      size={textSize}
                      height={0.01}
                      curveSegments={24}
                      bevelEnabled={false}
                      rotation={[-Math.PI / 2, 0, 0]}
                    >
                      {`Total Width: ${(totalWidth / 1000).toFixed(2)} m`}
                      <meshStandardMaterial color="black" />
                    </Text3D>
                  </Center>
                </group>
              </group>
            </group>
          </Canvas>
        </View>

        <View style={[styles.quadrant, isMobile && styles.fullWidthQuadrant]}>
          <Text style={styles.label}>Front View</Text>
          <Canvas style={styles.canvas} gl={{ preserveDrawingBuffer: true }}>
            <ambientLight intensity={0.5} />
            <directionalLight position={[0, 10, 10]} intensity={1} />
            <OrthographicCamera
              makeDefault
              position={[0, 0, 10]}
              zoom={frontZoom}
              near={0.1}
              far={1000}
            />
            <OrbitControls
              ref={frontControlsRef}
              enableRotate={false}
              enablePan={Platform.OS === 'web'}
              enableZoom={true}
              enableDamping={false}
              minZoom={10}
              maxZoom={100}
            />
            <group
              position={[
                -totalWidth / 2000 + overhang / 1000 + leftLeanToWidth / 1000,
                -height / 2000,
                0,
              ]}
            >
              <CarportComponent {...carportProps} />
            </group>

            {/* Measurements for front view */}
            <group
              position={[
                -totalWidth / 2000 + (2 * overhang) / 1000 + leftLeanToWidth / 1000 + span / 2000,
                -height / 2000,
                0,
              ]}
            >
              {/* Span measurement (just the main span) */}
              <group>
                <Line
                  points={[
                    [-span / 2000, -height / 2000 - 0.3, 0],
                    [span / 2000, -height / 2000 - 0.3, 0],
                  ]}
                  color="black"
                  lineWidth={lineThickness}
                  dashed={false}
                />

                <Line
                  points={[
                    [-span / 2000, -height / 2000 - 0.3 - tickSize, 0], // Adjusted for larger scale
                    [-span / 2000, -height / 2000 - 0.3 + tickSize, 0],
                  ]}
                  color="black"
                  lineWidth={lineThickness}
                  dashed={false}
                />
                <Line
                  points={[
                    [span / 2000, -height / 2000 - 0.3 - tickSize, 0],
                    [span / 2000, -height / 2000 - 0.3 + tickSize, 0],
                  ]}
                  color="black"
                  lineWidth={lineThickness}
                  dashed={false}
                />

                <group position={[0, -height / 2000 - 1.2 - spanTextSpacing + adaptiveOffset, 0]}>
                  <Center>
                    <Text3D
                      font={timesNewRoman}
                      size={textSize}
                      height={0.01}
                      curveSegments={24}
                      bevelEnabled={false}
                    >
                      {`Span: ${(span / 1000).toFixed(2)} m`}
                      <meshStandardMaterial color="black" />
                    </Text3D>
                  </Center>
                </group>
              </group>
              {/* Left lean-to measurement (if applicable) */}
              {leftLeanTo && leftLeanToWidth && leftLeanToWidth > 0 && (
                <group>
                  <Line
                    points={[
                      [-span / 2000 - leftLeanToWidth / 1000, -height / 2000 - 1.2, 0],
                      [-span / 2000, -height / 2000 - 1.2, 0],
                    ]}
                    color="black"
                    lineWidth={lineThickness}
                    dashed={false}
                  />

                  <Line
                    points={[
                      [-span / 2000 - leftLeanToWidth / 1000, -height / 2000 - 1.2 - tickSize, 0],
                      [-span / 2000 - leftLeanToWidth / 1000, -height / 2000 - 1.2 + tickSize, 0],
                    ]}
                    color="black"
                    lineWidth={lineThickness}
                    dashed={false}
                  />
                  <Line
                    points={[
                      [-span / 2000, -height / 2000 - 1.2 - tickSize, 0],
                      [-span / 2000, -height / 2000 - 1.2 + tickSize, 0],
                    ]}
                    color="black"
                    lineWidth={lineThickness}
                    dashed={false}
                  />

                  {/* Text Above & Below the Horizontal Line */}
                  {(() => {
                    const fromX = -span / 2000 - leftLeanToWidth / 1000;
                    const toX = -span / 2000;
                    const midX = (fromX + toX) / 2;
                    const yBase = -height / 2000 - 1.2;

                    return (
                      <>
                        {/* Top line: value */}
                        <group position={[midX, yBase + textSize * 0.7, 0]}>
                          <Center>
                            <Text3D
                              font={timesNewRoman}
                              size={textSize * 0.8}
                              height={0.01}
                              curveSegments={24}
                              bevelEnabled={false}
                            >
                              {(leftLeanToWidth / 1000).toFixed(2) + ' m'}
                              <meshStandardMaterial color="black" />
                            </Text3D>
                          </Center>
                        </group>

                        {/* Bottom line: label */}
                        <group position={[midX, yBase - textSize * 0.7, 0]}>
                          <Center>
                            <Text3D
                              font={timesNewRoman}
                              size={textSize * 0.7}
                              height={0.01}
                              curveSegments={24}
                              bevelEnabled={false}
                            >
                              Left LT
                              <meshStandardMaterial color="black" />
                            </Text3D>
                          </Center>
                        </group>
                      </>
                    );
                  })()}
                </group>
              )}
              {/* Right lean-to measurement (if applicable) - hide when roof is Skillion Attached */}
              {rightLeanTo &&
                rightLeanToSpan &&
                rightLeanToSpan > 0 &&
                roofType !== 'Skillion Attached' && (
                  <group>
                    {(() => {
                      const fromX = span / 2000;
                      const toX = span / 2000 + rightLeanToSpan / 1000;
                      const midX = (fromX + toX) / 2;
                      const yBase = -height / 2000 - 1.2;

                      return (
                        <>
                          {/* Measurement line */}
                          <Line
                            points={[
                              [fromX, yBase, 0],
                              [toX, yBase, 0],
                            ]}
                            color="black"
                            lineWidth={lineThickness}
                            dashed={false}
                          />

                          {/* Ticks */}
                          <Line
                            points={[
                              [fromX, yBase - tickSize, 0],
                              [fromX, yBase + tickSize, 0],
                            ]}
                            color="black"
                            lineWidth={lineThickness}
                            dashed={false}
                          />
                          <Line
                            points={[
                              [toX, yBase - tickSize, 0],
                              [toX, yBase + tickSize, 0],
                            ]}
                            color="black"
                            lineWidth={lineThickness}
                            dashed={false}
                          />

                          {/* Text above the line: number */}
                          <group position={[midX, yBase + textSize * 0.7, 0]}>
                            <Center>
                              <Text3D
                                font={timesNewRoman}
                                size={textSize * 0.8}
                                height={0.01}
                                curveSegments={24}
                                bevelEnabled={false}
                              >
                                {(rightLeanToSpan / 1000).toFixed(2) + ' m'}
                                <meshStandardMaterial color="black" />
                              </Text3D>
                            </Center>
                          </group>

                          {/* Text below the line: label */}
                          <group position={[midX, yBase - textSize * 0.7, 0]}>
                            <Center>
                              <Text3D
                                font={timesNewRoman}
                                size={textSize * 0.7}
                                height={0.01}
                                curveSegments={24}
                                bevelEnabled={false}
                              >
                                Right LT
                                <meshStandardMaterial color="black" />
                              </Text3D>
                            </Center>
                          </group>
                        </>
                      );
                    })()}
                  </group>
                )}
              {/* Height measurement */}
              <group>
                {(() => {
                  const x = leftLeanTo
                    ? -span / 2000 - leftLeanToWidth / 1000 - 1
                    : -span / 2000 - 1;

                  return (
                    <>
                      {/* Vertical height line */}
                      <Line
                        points={[
                          [x, 0, 0],
                          [x, height / 1000, 0],
                        ]}
                        color="black"
                        lineWidth={lineThickness}
                        dashed={false}
                      />

                      {/* Tick lines at bottom and top */}
                      <Line
                        points={[
                          [x - tickSize, 0, 0],
                          [x + tickSize, 0, 0],
                        ]}
                        color="black"
                        lineWidth={lineThickness}
                        dashed={false}
                      />
                      <Line
                        points={[
                          [x - tickSize, height / 1000, 0],
                          [x + tickSize, height / 1000, 0],
                        ]}
                        color="black"
                        lineWidth={lineThickness}
                        dashed={false}
                      />

                      {/* Label for eave height */}
                      <group position={[x - adaptiveOffset, height / 2000, 0]}>
                        <Center>
                          <Text3D
                            font={timesNewRoman}
                            size={textSize}
                            height={0.01}
                            curveSegments={24}
                            bevelEnabled={false}
                            rotation={[0, 0, Math.PI / 2]}
                          >
                            {`Eave: ${(height / 1000).toFixed(2)} m`}
                            <meshStandardMaterial color="black" />
                          </Text3D>
                        </Center>
                      </group>
                    </>
                  );
                })()}
              </group>
            </group>
          </Canvas>
        </View>

        <View style={[styles.quadrant, isMobile && styles.fullWidthQuadrant]}>
          <Text style={styles.label}>Left View</Text>
          <Canvas style={styles.canvas} gl={{ preserveDrawingBuffer: true }}>
            <ambientLight intensity={0.5} />
            <directionalLight position={[10, 10, 10]} intensity={1} />
            <OrthographicCamera
              makeDefault
              position={[-10, 0, 0]}
              zoom={scaleFactor * 0.8}
              near={0.1}
              far={1000}
            />
            <OrbitControls
              ref={leftControlsRef}
              enableRotate={false}
              enablePan={Platform.OS === 'web'}
              enableZoom={true}
              enableDamping={false}
              minZoom={10}
              maxZoom={100}
              makeDefault
            />
            <group position={[0, -height / 2000, length / 2000]}>
              <CarportComponent {...carportProps} />
            </group>
          </Canvas>
        </View>

        <View style={[styles.quadrant, isMobile && styles.fullWidthQuadrant]}>
          <Text style={styles.label}>Isometric View</Text>
          <Canvas style={styles.canvas} gl={{ preserveDrawingBuffer: true }}>
            <ambientLight intensity={0.5} />
            <directionalLight position={[10, 10, 10]} intensity={1} />
            <OrthographicCamera
              makeDefault
              position={[10, 10, 10]}
              zoom={isoZoom}
              near={0.1}
              far={1000}
            />
            <OrbitControls
              ref={isoControlsRef}
              enableRotate={false}
              enablePan={Platform.OS === 'web'}
              enableZoom={true}
              enableDamping={false}
              minZoom={10}
              maxZoom={100}
              makeDefault
            />
            <group position={[-totalWidth / 2000, 0, length / 2000]}>
              <CarportComponent {...carportProps} />
            </group>
          </Canvas>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flexDirection: 'column',
    backgroundColor: '#eee',
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
  },
  column: {
    flexDirection: 'column',
  },
  quadrant: {
    width: '48%',
    aspectRatio: 1,
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#ccc',
    padding: 5,
  },
  fullWidthQuadrant: {
    width: '100%',
  },
  label: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  canvas: {
    flex: 1,
  },
});
/**
 * Helper function to create a line for measurements
 */
const createLine = (
  start: [number, number, number],
  end: [number, number, number],
  color: string,
  lineWidth: number
): THREE.Line => {
  const material = new LineBasicMaterial({ color, linewidth: lineWidth });
  const points = [];
  points.push(new THREE.Vector3(...start));
  points.push(new THREE.Vector3(...end));

  const geometry = new BufferGeometry().setFromPoints(points);
  return new ThreeLine(geometry, material);
};

/**
 * Helper function to create text for measurements
 */
const createText = (
  text: string,
  position: [number, number, number],
  rotation: [number, number, number],
  size: number
): THREE.Group => {
  const group = new THREE.Group();
  // Try to use real 3D text
  try {
    // Font loader is synchronous if font is already loaded as JSON
    const fontJson = require('@/assets/fonts/Arial_Regular.json');
    const font = new Font(fontJson);
    const textGeometry = new TextGeometry(text, {
      font: font,
      size: size * 0.1, // scale down for scene units
      depth: 0.05 * size * 0.1, // extrusion depth (was 'height')
      curveSegments: 6,
      bevelEnabled: false,
    });
    // Center the text geometry
    textGeometry.computeBoundingBox();
    if (textGeometry.boundingBox) {
      const centerX = (textGeometry.boundingBox.max.x + textGeometry.boundingBox.min.x) / 2;
      const centerY = (textGeometry.boundingBox.max.y + textGeometry.boundingBox.min.y) / 2;
      const centerZ = (textGeometry.boundingBox.max.z + textGeometry.boundingBox.min.z) / 2;
      textGeometry.translate(-centerX, -centerY, -centerZ);
    }
    const textMaterial = new THREE.MeshStandardMaterial({ color: 'red' });
    const textMesh = new THREE.Mesh(textGeometry, textMaterial);
    textMesh.position.set(...position);
    textMesh.rotation.set(...rotation);
    group.add(textMesh);
    return group;
  } catch (e) {
    // Fallback: box placeholder
    const textMaterial = new MeshStandardMaterial({ color: 'red', wireframe: true });
    const textGeometry = new THREE.BoxGeometry(text.length * 0.5, size * 2, 1);
    const textMesh = new THREE.Mesh(textGeometry, textMaterial);
    textMesh.position.set(...position);
    textMesh.rotation.set(...rotation);
    group.add(textMesh);
    return group;
  }
};

/**
 * Generate a Plan View export image
 * This function can be called from ExportViewsSwitch.tsx to generate a consistent Plan View export
 * that matches exactly what users see in the interactive Plan View
 */
export interface PlanViewExportOptions {
  gl: WebGLRenderer;
  scene: Scene;
  camera: ThreeOrthographicCamera;
  dimensions: {
    length: number;
    span: number;
    height: number;
    pitch: number;
    overhang: number;
    roofType?: string;
  };
  sceneSettings: {
    leftLeanTo?: boolean;
    leftLeanToSpan?: number;
    leftLeanToDropHeight?: number;
    rightLeanTo?: boolean;
    rightLeanToSpan?: number;
    rightLeanToDropHeight?: number;
    viewMode?: string;
    showSlab?: boolean;
  };
  imageWidth?: number;
  imageHeight?: number;
}

export const exportPlanView = async (options: PlanViewExportOptions): Promise<string> => {
  const {
    gl,
    scene,
    camera,
    dimensions,
    sceneSettings,
    imageWidth = 600,
    imageHeight = 600,
  } = options;

  // Store original renderer state
  const originalSize = new Vector2();
  gl.getSize(originalSize);
  const originalClearColor = gl.getClearColor(new Color());
  const originalClearAlpha = gl.getClearAlpha();

  try {
    // Set white background for better printing and visibility
    gl.setClearColor(0xffffff, 1);

    // Calculate model dimensions in meters
    const { length = 12000, span = 6000, height = 2400, pitch = 15, overhang = 200 } = dimensions;

    const lengthM = length / 1000;
    const spanM = span / 1000;
    const heightM = height / 1000;

    // Calculate total width including lean-tos
    const leftLeanToSpanM =
      sceneSettings.leftLeanTo && sceneSettings.leftLeanToSpan
        ? sceneSettings.leftLeanToSpan / 1000
        : 0;
    const rightLeanToSpanM =
      sceneSettings.rightLeanTo && sceneSettings.rightLeanToSpan
        ? sceneSettings.rightLeanToSpan / 1000
        : 0;
    const totalWidthM = spanM + leftLeanToSpanM + rightLeanToSpanM;

    // Calculate largest model dimension for scaling
    const largestModelDimension = Math.max(lengthM, totalWidthM);

    // Create a dedicated export camera
    const exportCamera = new ThreeOrthographicCamera(-1, 1, 1, -1, 0.1, 1000);

    // Derived scale factors for measurements with additional constraints
    const lineThickness = Math.min(5, 2 * 0.5); // Scale line thickness
    const tickSize = Math.min(0.4, 0.2 * 0.5); // Scale tick size

    // Fixed text size to ensure visibility regardless of model size
    const textSize = 6; // Fixed text size that works well in all cases

    // // Fixed offset for text positioning
    // const adaptiveOffset = 0.5; // Fixed offset that works well in all cases

    // Define the view configurations - exactly matching the QuadrantView component
    type ViewConfig = {
      name: string;
      pos: [number, number, number];
      look: [number, number, number];
      up: [number, number, number];
      zoom: number;
    };

    const views: ViewConfig[] = [
      // Top View (Top Left)
      {
        name: 'Top',
        pos: [0, 10, 0],
        look: [0, 0, 0],
        up: [0, 0, -1],
        zoom: 0.1,
      },
      // Front View (Top Right)
      {
        name: 'Front',
        pos: [0, 0, lengthM / 2 + 2],
        look: [0, 0, 0],
        up: [0, 1, 0],
        zoom: 0.1,
      },
      // Left View (Bottom Left)
      {
        name: 'Left',
        pos: [-10, 0, 0],
        look: [0, 0, 0],
        up: [0, 1, 0],
        zoom: 0.1, // Base zoom, will be multiplied by 1.5 in the rendering function
      },
      // Isometric View (Bottom Right)
      {
        name: 'Isometric',
        pos: [10, 10, 10],
        look: [0, 0, 0],
        up: [0, 1, 0],
        zoom: 0.1, // Base zoom, will be multiplied by 1.2 in the rendering function
      },
    ];

    // Helper function to render a single view
    const renderView = (view: ViewConfig): Promise<string> => {
      return new Promise((resolve) => {
        const { pos, look, up, name } = view;

        // Set up camera for this view
        exportCamera.position.set(...pos);
        exportCamera.up.set(...up);
        exportCamera.lookAt(...look);

        // Dynamic scaling based on model size - matching the interactive view
        const sizeBasedPercentage = Math.min(0.7, Math.max(0.4, 12 / largestModelDimension));
        const targetSize = imageWidth * sizeBasedPercentage;

        // Calculate base zoom with model size consideration
        let baseZoom = targetSize / (largestModelDimension * 100);

        // Dynamic scale factor constraints based on model size
        const sizeAdjustment = Math.max(0.3, Math.min(1.2, 10 / largestModelDimension));
        const minScaleFactor = 3 * sizeAdjustment;
        const maxScaleFactor = 20 * sizeAdjustment;

        baseZoom = Math.max(minScaleFactor, Math.min(maxScaleFactor, baseZoom));

        // Apply view-specific zoom multiplier with dynamic adjustment
        const viewZoomMultiplier = view.zoom || 0.1;
        const complexityAdjustment = Math.max(0.1, Math.min(0.3, 8 / largestModelDimension));
        let zoom = baseZoom * viewZoomMultiplier * complexityAdjustment;

        // Apply view-specific zoom adjustments
        if (name === 'Left') {
          zoom *= 1.5; // Increased zoom for left view
        } else if (name === 'Isometric') {
          zoom *= 1.2; // Increased zoom for isometric view
        }

        exportCamera.zoom = zoom;
        exportCamera.updateProjectionMatrix();

        // Position adjustments for all views to ensure model is centered in each quadrant
        scene.traverse((object) => {
          if (object.name === 'carport-group') {
            // Apply view-specific positioning to center the model
            switch (name) {
              case 'Top':
                // Center the model in the top view
                object.position.set(-totalWidthM / 2, 0, lengthM / 2);
                break;
              case 'Front':
                // Center the model in the front view
                object.position.set(
                  -totalWidthM / 2 + overhang / 1000 + leftLeanToSpanM,
                  -heightM / 2,
                  0
                );
                break;
              case 'Left':
                // Center the model in the left view
                object.position.set(0, -heightM / 2, lengthM / 2);
                break;
              case 'Isometric':
                // Center the model in the isometric view
                object.position.set(-totalWidthM / 2, -heightM / 2, -lengthM / 2);
                break;
            }
          }
        });

        // Create a temporary scene for this view that includes dimension lines and text
        const tempScene = scene.clone();

        // Add dimension lines and text based on the view
        if (name === 'Top') {
          // Add dimension lines and text for Top view
          const dimensionsGroup = new THREE.Group();
          dimensionsGroup.position.set(overhang / 1000 - leftLeanToSpanM, 0, lengthM / 2);

          // Length measurement
          const lengthGroup = new THREE.Group();

          // Create length line
          const lengthLine = createLine(
            [totalWidthM / 2, 0, -lengthM],
            [totalWidthM / 2, 0, 0],
            'black',
            3
          );
          lengthGroup.add(lengthLine);

          // Create ticks at ends
          const lengthTick1 = createLine(
            [totalWidthM / 2 - tickSize, 0, -lengthM],
            [totalWidthM / 2 + tickSize, 0, -lengthM],
            'black',
            3
          );
          lengthGroup.add(lengthTick1);

          const lengthTick2 = createLine(
            [totalWidthM / 2 - tickSize, 0, 0],
            [totalWidthM / 2 + tickSize, 0, 0],
            'black',
            3
          );
          lengthGroup.add(lengthTick2);

          // Add length text (to the right of the length line, horizontal)
          const lengthText = createText(
            `Length: ${lengthM.toFixed(2)} m`,
            [totalWidthM / 2 + 1.2, 0, -lengthM / 2], // offset to the right
            [-Math.PI / 2, 0, Math.PI / 2], // horizontal
            textSize
          );
          lengthGroup.add(lengthText);
          dimensionsGroup.add(lengthGroup);

          // Width measurement
          const widthGroup = new THREE.Group();

          // Create width line
          const widthLine = createLine(
            [-totalWidthM / 2 - overhang / 1000, 0, 0.75],
            [totalWidthM / 2 - overhang / 1000, 0, 0.75],
            'black',
            lineThickness
          );
          widthGroup.add(widthLine);

          // Create ticks at ends
          const widthTick1 = createLine(
            [-totalWidthM / 2 - overhang / 1000, 0, 0.75 - tickSize],
            [-totalWidthM / 2 - overhang / 1000, 0, 0.75 + tickSize],
            'black',
            lineThickness
          );
          widthGroup.add(widthTick1);

          const widthTick2 = createLine(
            [totalWidthM / 2 - overhang / 1000, 0, 0.75 - tickSize],
            [totalWidthM / 2 - overhang / 1000, 0, 0.75 + tickSize],
            'black',
            lineThickness
          );
          widthGroup.add(widthTick2);

          // Add width text (above the width line, horizontal)
          const widthText = createText(
            `Total Width: ${totalWidthM.toFixed(2)} m`,
            [0, 0, 0.75 + 0.6], // above the width line
            [-Math.PI / 2, 0, 0], // horizontal
            textSize
          );
          widthGroup.add(widthText);
          dimensionsGroup.add(widthGroup);

          tempScene.add(dimensionsGroup);
        } else if (name === 'Front') {
          // Add dimension lines and text for Front view
          const dimensionsGroup = new THREE.Group();

          // Span measurement
          const spanGroup = new THREE.Group();

          // Create span line
          const spanLine = createLine(
            [-spanM / 2 - rightLeanToSpanM / 2 - leftLeanToSpanM / 2, -heightM / 2 - 1, 0],
            [spanM / 2 - rightLeanToSpanM / 2 - leftLeanToSpanM / 2, -heightM / 2 - 1, 0],
            'black',
            lineThickness
          );
          spanGroup.add(spanLine);

          // Create ticks at ends
          const spanTick1 = createLine(
            [
              -spanM / 2 - rightLeanToSpanM / 2 - leftLeanToSpanM / 2,
              -heightM / 2 - 1 - tickSize,
              0,
            ],
            [
              -spanM / 2 - rightLeanToSpanM / 2 - leftLeanToSpanM / 2,
              -heightM / 2 - 1 + tickSize,
              0,
            ],
            'black',
            lineThickness
          );
          spanGroup.add(spanTick1);

          const spanTick2 = createLine(
            [
              spanM / 2 - rightLeanToSpanM / 2 - leftLeanToSpanM / 2,
              -heightM / 2 - 1 - tickSize,
              0,
            ],
            [
              spanM / 2 - rightLeanToSpanM / 2 - leftLeanToSpanM / 2,
              -heightM / 2 - 1 + tickSize,
              0,
            ],
            'black',
            lineThickness
          );
          spanGroup.add(spanTick2);

          // Add span text
          const spanText = createText(
            `Span: ${spanM.toFixed(2)} m`,
            [spanM / 2 - rightLeanToSpanM - leftLeanToSpanM, -heightM / 2 - 2, 0],
            [0, 0, 0],
            textSize
          );
          spanGroup.add(spanText);
          dimensionsGroup.add(spanGroup);

          // Left Lean-to span measurement
          if (leftLeanToSpanM > 0) {
            const leftLeanToSpanGroup = new THREE.Group();
            // Line from leftmost edge to main span edge
            const leftSpanStart =
              -spanM / 2 - rightLeanToSpanM / 2 - leftLeanToSpanM / 2 - leftLeanToSpanM;
            const leftSpanEnd = -spanM / 2 - rightLeanToSpanM / 2 - leftLeanToSpanM / 2;
            const leftSpanY = -heightM / 2 - 3;
            const leftLeanToLine = createLine(
              [leftSpanStart, leftSpanY, 0],
              [leftSpanEnd, leftSpanY, 0],
              'black',
              lineThickness
            );
            leftLeanToSpanGroup.add(leftLeanToLine);
            // Ticks
            const leftTick1 = createLine(
              [leftSpanStart, leftSpanY - tickSize, 0],
              [leftSpanStart, leftSpanY + tickSize, 0],
              'black',
              lineThickness
            );
            leftLeanToSpanGroup.add(leftTick1);
            const leftTick2 = createLine(
              [leftSpanEnd, leftSpanY - tickSize, 0],
              [leftSpanEnd, leftSpanY + tickSize, 0],
              'black',
              lineThickness
            );
            leftLeanToSpanGroup.add(leftTick2);
            // Text
            const leftLeanToText = createText(
              `Left LT: ${leftLeanToSpanM.toFixed(2)} m`,
              [(leftSpanStart + leftSpanEnd) / 2, leftSpanY - 0.5, 0],
              [0, 0, 0],
              textSize
            );
            leftLeanToSpanGroup.add(leftLeanToText);
            dimensionsGroup.add(leftLeanToSpanGroup);
          }

          // Right Lean-to span measurement
          if (rightLeanToSpanM > 0) {
            const rightLeanToSpanGroup = new THREE.Group();
            // Line from main span edge to rightmost edge
            const rightSpanStart = spanM / 2 - rightLeanToSpanM / 2 - leftLeanToSpanM / 2;
            const rightSpanEnd = rightSpanStart + rightLeanToSpanM;
            const rightSpanY = -heightM / 2 - 3;
            const rightLeanToLine = createLine(
              [rightSpanStart, rightSpanY, 0],
              [rightSpanEnd, rightSpanY, 0],
              'black',
              lineThickness
            );
            rightLeanToSpanGroup.add(rightLeanToLine);
            // Ticks
            const rightTick1 = createLine(
              [rightSpanStart, rightSpanY - tickSize, 0],
              [rightSpanStart, rightSpanY + tickSize, 0],
              'black',
              lineThickness
            );
            rightLeanToSpanGroup.add(rightTick1);
            const rightTick2 = createLine(
              [rightSpanEnd, rightSpanY - tickSize, 0],
              [rightSpanEnd, rightSpanY + tickSize, 0],
              'black',
              lineThickness
            );
            rightLeanToSpanGroup.add(rightTick2);
            // Text
            const rightLeanToText = createText(
              `Right LT: ${rightLeanToSpanM.toFixed(2)} m`,
              [(rightSpanStart + rightSpanEnd) / 2, rightSpanY - 0.5, 0],
              [0, 0, 0],
              textSize
            );
            rightLeanToSpanGroup.add(rightLeanToText);
            dimensionsGroup.add(rightLeanToSpanGroup);
          }

          // Height measurement
          const heightGroup = new THREE.Group();
          // Create height line
          const heightLine = createLine(
            [-spanM - 0.3, 0, 0],
            [-spanM - 0.3, heightM, 0],
            'black',
            lineThickness
          );
          heightGroup.add(heightLine);
          // Create ticks at ends
          const heightTick1 = createLine(
            [-spanM - 0.3 - tickSize, 0, 0],
            [-spanM - 0.3 + tickSize, 0, 0],
            'black',
            lineThickness
          );
          heightGroup.add(heightTick1);
          const heightTick2 = createLine(
            [-spanM - 0.3 - tickSize, heightM, 0],
            [-spanM - 0.3 + tickSize, heightM, 0],
            'black',
            lineThickness
          );
          heightGroup.add(heightTick2);
          // Add height text
          const heightText = createText(
            `Height: ${heightM.toFixed(2)} m`,
            [-spanM - 0.5, 0, 0],
            [0, 0, Math.PI / 2],
            textSize
          );
          heightGroup.add(heightText);
          dimensionsGroup.add(heightGroup);

          tempScene.add(dimensionsGroup);
        } else if (name === 'Left') {
          // Add dimension lines and text for Left view
          const dimensionsGroup = new THREE.Group();

          // Length measurement
          const lengthGroup = new THREE.Group();

          // Create length line
          const lengthLine = createLine(
            [0, -heightM / 2 - 0.3, -lengthM / 2],
            [0, -heightM / 2 - 0.3, lengthM / 2],
            'black',
            lineThickness
          );
          //lengthGroup.add(lengthLine);

          // Create ticks at ends
          const lengthTick1 = createLine(
            [0, -heightM / 2 - 0.3 - tickSize, -lengthM / 2],
            [0, -heightM / 2 - 0.3 + tickSize, -lengthM / 2],
            'black',
            lineThickness
          );
          //lengthGroup.add(lengthTick1);

          const lengthTick2 = createLine(
            [0, -heightM / 2 - 0.3 - tickSize, lengthM / 2],
            [0, -heightM / 2 - 0.3 + tickSize, lengthM / 2],
            'black',
            lineThickness
          );
          //lengthGroup.add(lengthTick2);

          // Add length text
          const lengthText = createText(
            `Length: ${lengthM.toFixed(2)} m`,
            [0, -heightM / 2 - 0.8, 0],
            [0, 0, 0],
            textSize
          );
          //lengthGroup.add(lengthText);
          //dimensionsGroup.add(lengthGroup);

          // Height measurement
          const heightGroup = new THREE.Group();

          // Create height line
          const heightLine = createLine(
            [-0.3, -heightM / 2, 0],
            [-0.3, heightM / 2, 0],
            'black',
            lineThickness
          );
          //heightGroup.add(heightLine);

          // Create ticks at ends
          const heightTick1 = createLine(
            [-0.3 - tickSize, -heightM / 2, 0],
            [-0.3 + tickSize, -heightM / 2, 0],
            'black',
            lineThickness
          );
          //heightGroup.add(heightTick1);

          const heightTick2 = createLine(
            [-0.3 - tickSize, heightM / 2, 0],
            [-0.3 + tickSize, heightM / 2, 0],
            'black',
            lineThickness
          );
          //heightGroup.add(heightTick2);

          // Add height text
          const heightText = createText(
            `Height: ${heightM.toFixed(2)} m`,
            [-0.8, 0, 0],
            [0, 0, Math.PI / 2],
            textSize
          );
          //heightGroup.add(heightText);
          //dimensionsGroup.add(heightGroup);

          tempScene.add(dimensionsGroup);
        }

        // Render the view with dimensions
        gl.setSize(imageWidth, imageHeight);
        gl.render(tempScene, exportCamera);

        // Reset model position
        scene.traverse((object) => {
          if (object.name === 'carport-group') {
            object.position.set(0, 0, 0);
          }
        });

        // Get image data
        const dataURL = gl.domElement.toDataURL('image/png', 1.0);
        resolve(dataURL);
      });
    };

    // Render all four views
    const images: string[] = [];
    for (const view of views) {
      images.push(await renderView(view));
    }

    // Create a composite canvas with frames and labels
    const padding = 10; // Padding between views
    const borderWidth = 2; // Width of the border around each view
    const labelHeight = 30; // Height for the label area

    // Create a larger canvas to accommodate borders, labels, and padding
    const gridCanvas = document.createElement('canvas');
    gridCanvas.width = (imageWidth + borderWidth * 2) * 2 + padding;
    gridCanvas.height = (imageHeight + borderWidth * 2 + labelHeight) * 2 + padding;
    const ctx = gridCanvas.getContext('2d');
    if (!ctx) throw new Error('Could not create canvas 2D context');

    // Fill the background with white
    ctx.fillStyle = '#FFFFFF';
    ctx.fillRect(0, 0, gridCanvas.width, gridCanvas.height);

    // View labels
    const viewLabels = ['Top View', 'Front View', 'Left View', 'Isometric View'];

    // Function to draw a framed view with label
    const drawFramedView = (imageIndex: number, x: number, y: number, label: string) => {
      // Create an image element for the view
      const img = document.createElement('img');
      img.src = images[imageIndex];

      // Draw border
      ctx.fillStyle = '#333333';
      ctx.fillRect(x, y, imageWidth + borderWidth * 2, imageHeight + borderWidth * 2 + labelHeight);

      // Draw white background for the image area
      ctx.fillStyle = '#FFFFFF';
      ctx.fillRect(x + borderWidth, y + labelHeight, imageWidth, imageHeight);

      // Draw label background
      ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
      ctx.fillRect(x + 10, y + 10, 100, 24);

      // Draw label text
      ctx.fillStyle = '#FFFFFF';
      ctx.font = 'bold 14px Arial';
      ctx.fillText(label, x + 18, y + 26);

      // Wait for image to load and draw it
      return new Promise<void>((resolve) => {
        img.onload = () => {
          ctx.drawImage(img, x + borderWidth, y + labelHeight, imageWidth, imageHeight);
          resolve();
        };
        // If image is already loaded, draw it immediately
        if (img.complete) {
          ctx.drawImage(img, x + borderWidth, y + labelHeight, imageWidth, imageHeight);
          resolve();
        }
      });
    };

    // Calculate positions for each view
    const topLeftX = 0;
    const topLeftY = 0;
    const topRightX = imageWidth + borderWidth * 2 + padding;
    const topRightY = 0;
    const bottomLeftX = 0;
    const bottomLeftY = imageHeight + borderWidth * 2 + labelHeight + padding;
    const bottomRightX = imageWidth + borderWidth * 2 + padding;
    const bottomRightY = imageHeight + borderWidth * 2 + labelHeight + padding;

    // Draw all views with frames and labels
    await Promise.all([
      drawFramedView(0, topLeftX, topLeftY, viewLabels[0]),
      drawFramedView(1, topRightX, topRightY, viewLabels[1]),
      drawFramedView(2, bottomLeftX, bottomLeftY, viewLabels[2]),
      drawFramedView(3, bottomRightX, bottomRightY, viewLabels[3]),
    ]);

    // Get the final composite image
    const finalDataUrl = gridCanvas.toDataURL('image/png', 1.0);

    // Restore original renderer state
    gl.setSize(originalSize.x, originalSize.y);
    gl.setClearColor(originalClearColor, originalClearAlpha);
    gl.render(scene, camera);

    return finalDataUrl;
  } catch (error) {
    console.error('Error in exportPlanView:', error);

    // Restore original renderer state even if there's an error
    gl.setSize(originalSize.x, originalSize.y);
    gl.setClearColor(originalClearColor, originalClearAlpha);
    gl.render(scene, camera);

    throw error;
  }
};

export default QuadrantView;
