import { PropsWithChildren, useState, useEffect } from 'react';
import { StyleSheet, TouchableOpacity, Animated, Dimensions, Platform, ViewStyle } from 'react-native';
import { ThemedText } from '@/components/themed/ThemedText';
import { ThemedView } from '@/components/themed/ThemedView';
import { IconSymbol } from '@/components/platform-specific/IconSymbol';
import { Colors } from '@/constants/colors/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';

interface HorizontalExpandableProps extends PropsWithChildren {
  title: string;
  icon?: 'straighten' | 'view-quilt' | 'grid-on' | 'settings' | 'palette' | 'directions-car' | 'mail' | '3d-rotation' | 'settings-brightness'; // Valid MaterialIcons names
  isMobile?: boolean; // Flag to determine if we're in mobile layout
  isTopRight?: boolean; // Flag to determine if this is a top-right control
}

export function HorizontalExpandable({ children, title, icon, isMobile = false, isTopRight = false }: HorizontalExpandableProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [dimensions, setDimensions] = useState(Dimensions.get('window'));
  const theme = useColorScheme() ?? 'light';
  
  // Update dimensions when the screen size changes
  useEffect(() => {
    const subscription = Dimensions.addEventListener('change', ({ window }) => {
      setDimensions(window);
    });
    return () => subscription.remove();
  }, []);
  
  // Get screen dimensions to determine the expansion size
  const { width: screenWidth, height: screenHeight } = dimensions;
  // Limit expanded width to 30% of canvas width
  const expandedWidth = Math.min(screenWidth * 0.3, 300); // 30% of screen width or max 300px
  
  // Calculate button width as 5% of screen width for main controls
  const buttonWidth = Math.max(Math.min(screenWidth * 0.05, 50), 40); // 5% of screen width, min 40px, max 50px
  
  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <ThemedView style={[
      styles.container, 
      isMobile ? styles.mobileContainer : (isTopRight ? styles.topRightContainer : styles.desktopContainer)
    ]}>
      <TouchableOpacity
        style={[
          styles.button, 
          isExpanded && styles.buttonExpanded,
          isMobile ? styles.mobileButton : (isTopRight ? styles.topRightButton : styles.desktopButton),
          // Apply dynamic width for main controls
          !isTopRight && !isMobile && { width: buttonWidth, height: buttonWidth }
        ]}
        onPress={toggleExpand}
        activeOpacity={0.8}
      >
        {icon && (
          <IconSymbol
            name={icon}
            size={isTopRight ? 20 : 24}
            weight="medium"
            color={theme === 'light' ? Colors.light.icon : Colors.dark.icon}
            style={styles.icon}
          />
        )}
        <IconSymbol
          name={isMobile ? "expand-more" : "chevron-right"}
          size={14}
          weight="medium"
          color={theme === 'light' ? Colors.light.icon : Colors.dark.icon}
          style={[
            styles.chevron,
            { transform: [{ rotate: isExpanded ? (isMobile ? '180deg' : '90deg') : '0deg' }] }
          ]}
        />
      </TouchableOpacity>
      
      {isExpanded && (
        <ThemedView 
          style={[
            styles.content, 
            isMobile ? 
              { 
                width: '100%', 
                position: 'absolute', 
                bottom: buttonWidth + 10, 
                left: 0,
                maxHeight: screenHeight * 0.7, // Limit height to 70% of screen
                overflow: 'scroll',
                zIndex: 1000, // Ensure it appears above other elements
                borderTopLeftRadius: 12,
                borderTopRightRadius: 12,
                shadowColor: '#000',
                shadowOffset: { width: 0, height: -2 },
                shadowOpacity: 0.1,
                shadowRadius: 4,
                elevation: 5,
              } : 
              isTopRight ?
              {
                width: 250,
                maxHeight: screenHeight * 0.6,
                overflow: 'scroll',
                position: 'absolute',
                right: buttonWidth + 5,
                top: 0,
                zIndex: 1000,
                shadowColor: '#000',
                shadowOffset: { width: -2, height: 2 },
                shadowOpacity: 0.1,
                shadowRadius: 4,
                elevation: 5,
              } :
              { 
                width: expandedWidth,
                maxHeight: screenHeight * 0.9,
                overflow: 'scroll',
                position: 'absolute',
                left: buttonWidth + 5,
                top: 0,
                zIndex: 1000,
                shadowColor: '#000',
                shadowOffset: { width: 2, height: 0 },
                shadowOpacity: 0.1,
                shadowRadius: 4,
                elevation: 5,
              }
          ]}
        >
          {/* Title at the top of expanded area */}
          <ThemedView style={styles.expandedHeader}>
            <ThemedText type="defaultSemiBold" style={styles.expandedTitle}>{title}</ThemedText>
          </ThemedView>
          <ThemedView style={styles.expandedContent}>
            {children}
          </ThemedView>
        </ThemedView>
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 1, // Reduced for tighter spacing
    alignItems: 'flex-start',
    position: 'relative',
  },
  desktopContainer: {
    flexDirection: 'row' as const,
  },
  topRightContainer: {
    flexDirection: 'column' as const,
    alignItems: 'flex-end',
    marginBottom: 1,
  },
  mobileContainer: {
    flexDirection: 'column' as const,
    alignItems: 'center',
    marginBottom: 0,
    marginHorizontal: 2,
  },
  button: {
    flexDirection: 'row' as const,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 6,
    paddingHorizontal: 6,
    borderRadius: 6,
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
  },
  desktopButton: {
    // Width and height set dynamically
  },
  topRightButton: {
    width: 40,
    height: 40,
    marginBottom: 1,
  },
  mobileButton: {
    width: 45,
    height: 45,
    justifyContent: 'center',
    paddingHorizontal: 0,
  },
  buttonExpanded: {
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
  },
  icon: {
    marginRight: 0,
  },
  chevron: {
    marginLeft: 'auto',
    position: 'absolute',
    right: 4,
    bottom: 4,
    opacity: 0.7,
  },
  content: {
    padding: 10,
    borderRadius: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    zIndex: 10,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.1)',
  },
  expandedHeader: {
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
    paddingBottom: 8,
    marginBottom: 8,
  },
  expandedTitle: {
    fontSize: 16,
    textAlign: 'center' as const,
  },
  expandedContent: {
    paddingVertical: 4,
  },
});
