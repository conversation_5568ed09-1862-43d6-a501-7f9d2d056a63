import React from 'react';
import { TextureLoader, THREE } from 'expo-three';

type GrassProps = {
  visible?: boolean;
};

function Grass({ visible = true }: GrassProps) {
  const grass = new TextureLoader().load(require('../../assets/images/grass3.jpg'), (texture) => {
    texture.minFilter = THREE.LinearFilter;
    texture.magFilter = THREE.LinearFilter;
    texture.wrapS = THREE.RepeatWrapping;
    texture.wrapT = THREE.RepeatWrapping;
    texture.repeat.set(25, 25);
    texture.anisotropy = 1;
  });

  return (
    <mesh visible={visible} position-y={-.1} rotation-x={-Math.PI / 2} receiveShadow>
      <planeGeometry args={[500, 500]} />
      <meshStandardMaterial map={grass} />
    </mesh>
  );
}

const GrassMemo = React.memo(Grass);

export default GrassMemo;
