import { useEffect, useRef, useState } from 'react';
import { THREE } from 'expo-three';
import { Asset } from 'expo-asset';
import { Vector3 } from '@react-three/fiber/native';
import { Environment } from './environment';

type SkyBoxProps = {
  time?: number;
  visible?: boolean;
};

const files = [
  Asset.fromModule(require('../../assets/images/px.png')).uri,
  Asset.fromModule(require('../../assets/images/nx.png')).uri,
  Asset.fromModule(require('../../assets/images/py.png')).uri,
  Asset.fromModule(require('../../assets/images/ny.png')).uri,
  Asset.fromModule(require('../../assets/images/pz.png')).uri,
  Asset.fromModule(require('../../assets/images/nz.png')).uri,
  // Asset.fromModule(require('../../assets/images/skybox_px.jpg')).uri,
  // Asset.fromModule(require('../../assets/images/skybox_nx.jpg')).uri,
  // Asset.fromModule(require('../../assets/images/skybox_py.jpg')).uri,
  // Asset.fromModule(require('../../assets/images/skybox_ny.jpg')).uri,
  // Asset.fromModule(require('../../assets/images/skybox_pz.jpg')).uri,
  // Asset.fromModule(require('../../assets/images/skybox_nz.jpg')).uri,
] as const;

function SkyBox({ time = 9, visible = true }: SkyBoxProps) {
  const [sunPosition, setSunPosition] = useState<Vector3>([0, 1, 0]);
  const dirLightRef = useRef<THREE.DirectionalLight>(null!);
  const [ambientLightIntensity, setAmbientLightIntensity] = useState(0.5);

  useEffect(() => {
    if (dirLightRef.current) {
      const dirLight = dirLightRef.current;

      const lightHeight = 50;
      const startPosition =
        time <= 12
          ? new THREE.Vector3(100, lightHeight, 100)
          : new THREE.Vector3(0, lightHeight, 0);
      const endPosition =
        time <= 12
          ? new THREE.Vector3(0, lightHeight, 0)
          : new THREE.Vector3(-100, lightHeight, -100);
      const t = time <= 12 ? time / 12 : (time - 12) / 12;
      const interpolatedPosition = startPosition.clone().lerp(endPosition, t);

      dirLight.position.copy(interpolatedPosition);
      dirLight.target.position.set(0, 0, 0);
      // dirLight.target.updateMatrixWorld();

      if (time <= 6 || time > 18) {
        dirLight.color.set('#1E90FF');
        setAmbientLightIntensity(0.3);
      } else if (time > 6 && time <= 9) {
        dirLight.color.set('#996300');
        setAmbientLightIntensity(0.7);
      } else if (time > 9 && time <= 15) {
        dirLight.color.set('#FFFFFF');
        setAmbientLightIntensity(0.8);
      } else {
        dirLight.color.set('#FFFFFF');
        setAmbientLightIntensity(0.5);
      }
    }
  }, [time]); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    const angle = (time / 36) * Math.PI * 2;
    const x = Math.cos(angle) * 20;
    const y = Math.sin(angle) * 20;
    setSunPosition([x, y, 20]);

    if (dirLightRef.current) {
      dirLightRef.current.position.set(x, y, 20);
      dirLightRef.current.target.position.set(0, 0, 0);
      dirLightRef.current.target.updateMatrixWorld();
    }
  }, [time]);

  return (
    <>
      <ambientLight args={[undefined, ambientLightIntensity]} />
      <directionalLight
        ref={dirLightRef}
        castShadow
        intensity={1}
        shadow-camera-top={100}
        shadow-camera-bottom={-100}
        shadow-camera-left={-100}
        shadow-camera-right={100}
        shadow-camera-near={0.1}
        shadow-camera-far={400}
        shadow-bias={-0.005}
        shadow-mapSize-width={2048}
        shadow-mapSize-height={2048}
      />
      <Environment files={files} background={visible} backgroundBlurriness={0.008} />
    </>
  );
}

export default SkyBox;
