import React, { useState, useEffect } from 'react';
import { StyleSheet, View, TouchableOpacity, Alert, Platform } from 'react-native';
import { useSceneContext } from '@/redux/context';
import { ThemedText } from '@/components/themed/ThemedText';
// @ts-ignore
import * as THREE from 'three';
import { exportPlanView } from '@/components/3d-measurement/new_planview';

// Add global window type extensions for Three.js objects
declare global {
  interface Window {
    __designer3d_gl?: any;
    __designer3d_scene?: any;
    __designer3d_camera?: any;
  }
}

// Type definitions for React Three Fiber ref
interface R3FRef {
  current: {
    gl: THREE.WebGLRenderer;
    scene: THREE.Scene;
    camera: THREE.PerspectiveCamera | THREE.OrthographicCamera;
    [key: string]: any;
  } | null;
}

// Styles for the component
const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 400, // 100 above PlanSwitch (which is at bottom: 300)
    right: 16,
    zIndex: 10,
    flexDirection: 'column',
    alignItems: 'flex-end',
  },
  button: {
    backgroundColor: '#4A90E2',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 22,
    alignItems: 'center',
    elevation: 2,
    marginBottom: 10,
    minWidth: 150,
  },
  buttonDisabled: {
    backgroundColor: '#A0A0A0',
    opacity: 0.7,
  },
  buttonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
});

/**
 * ExportViewsSwitch
 * Provides buttons for exporting the 3D view and Plan view
 * Both buttons only appear when in 3D view mode
 */
const ExportViewsSwitch = () => {
  const { sceneSettings, setSceneSettings, ref, dimensions } = useSceneContext();
  const isPlanView = sceneSettings.isPlanView || false;
  const [export3DInProgress, setExport3DInProgress] = useState(false);
  const [exportPlanInProgress, setExportPlanInProgress] = useState(false);
  
  // Reset export states when switching views
  useEffect(() => {
    setExport3DInProgress(false);
    setExportPlanInProgress(false);
  }, [isPlanView]);

  // Process plan view using the centralized exportPlanView function from new_planview.tsx
  const processPlan = (gl: THREE.WebGLRenderer, scene: THREE.Scene, _camera: THREE.OrthographicCamera) => {
    return new Promise<string>(async (resolve, reject) => {
      try {
        console.log('Starting processPlan using exportPlanView');
        
        // Hide all measurement overlays/groups, Grass, and SkyBox before rendering and remember them to restore later
        const hiddenObjects: THREE.Object3D[] = [];
        scene.traverse((object) => {
          if (object.visible && (
            (object.name && (
              object.name.toLowerCase().includes('measure') ||
              object.name.toLowerCase().includes('dimension') ||
              object.name.toLowerCase().includes('ruler') ||
              object.name === 'Grass' ||
              object.name === 'SkyBox'
            )) ||
            (object.type && (
              object.type.toLowerCase().includes('measure') ||
              object.type.toLowerCase().includes('dimension') ||
              object.type.toLowerCase().includes('ruler')
            ))
          )) {
            object.visible = false;
            hiddenObjects.push(object);
          }
        });
        
        // Use the centralized exportPlanView function from new_planview.tsx
        const finalDataUrl = await exportPlanView({
          gl,
          scene,
          camera: _camera,
          dimensions,
          sceneSettings,
          imageWidth: 600,  // Match the size used previously
          imageHeight: 600
        });
        
        // Restore measurement, Grass, and SkyBox visibility
        hiddenObjects.forEach(obj => { obj.visible = true; });
        
        // Force re-render after restoring objects
        if (gl && typeof gl.render === 'function' && scene) {
          gl.render(scene, _camera);
        }
        
        resolve(finalDataUrl);
      } catch (err) {
        console.error('Error in processPlan:', err);
        reject(err);
      }
    });
  };
  
  // Handler for exporting 3D view
  const handle3DExport = async () => {
    console.log('Export 3D button clicked');
    setExport3DInProgress(true);
    
    if (Platform.OS === 'web') {
      try {
        // Simplest approach: directly capture the canvas that's currently visible
        // This ensures we get exactly what the user is seeing without camera manipulation
        const canvasElement = document.querySelector('canvas');
        
        if (canvasElement) {
          console.log('Canvas found, capturing current view directly');
          
          // Create a temporary canvas with reduced size for smaller file
          const tempCanvas = document.createElement('canvas');
          const originalWidth = canvasElement.width;
          const originalHeight = canvasElement.height;
          const exportWidth = Math.floor(originalWidth / 2);
          const exportHeight = Math.floor(originalHeight / 2);
          
          tempCanvas.width = exportWidth;
          tempCanvas.height = exportHeight;
          const tempCtx = tempCanvas.getContext('2d');
          
          if (tempCtx) {
            // Draw the current canvas view to our temp canvas at reduced size
            tempCtx.drawImage(canvasElement, 0, 0, originalWidth, originalHeight, 0, 0, exportWidth, exportHeight);
            
            // Get the data URL from the temp canvas
            const dataURL = tempCanvas.toDataURL('image/png');
            
            // Create download link
            const a = document.createElement('a');
            a.href = dataURL;
            a.download = '3d-view.png';
            a.click();
            
            console.log('3D view export successful');
          } else {
            console.error('Could not get 2D context');
            Alert.alert('Export Error', 'Could not create export context.');
          }
        } else {
          console.error('Canvas not found');
          Alert.alert('Export Error', 'Canvas not found.');
        }
      } catch (error: any) {
        console.error('Error exporting 3D view:', error);
        Alert.alert('Export Error', 'Could not export 3D view.');
      } finally {
        setExport3DInProgress(false);
      }
    } else {
      console.error('Web platform not available');
      Alert.alert('Export Error', 'Export is only supported on web platforms.');
      setExport3DInProgress(false);
    }
  };
  
  // Helper function to wait for React to re-render
  const waitForRender = async () => {
    return new Promise(resolve => setTimeout(resolve, 100));
  };
  
  // Handler for exporting Plan view
  const handlePlanExport = async () => {
    console.log('Export Plan button clicked');
    setExportPlanInProgress(true);
    
    if (Platform.OS !== 'web') {
      Alert.alert('Export Error', 'Export is only supported on web platforms.');
      setExportPlanInProgress(false);
      return;
    }
    
    // Set export mode to hide measurements, grass, and skybox
    setSceneSettings((prev: any) => ({ ...prev, exportMode: true }));
    // Wait for React to re-render with hidden elements
    await waitForRender();

    // Try to get Three.js objects from ref
    let gl: THREE.WebGLRenderer | null = null;
    let scene: THREE.Scene | null = null;
    let camera: THREE.OrthographicCamera | null = null;
    let refSource = 'ref';
    console.log('[ExportViewsSwitch] Attempting export. Initial window.__designer3d_gl:', window.__designer3d_gl);
    console.log('[ExportViewsSwitch] Initial window.__designer3d_scene:', window.__designer3d_scene);
    console.log('[ExportViewsSwitch] Initial window.__designer3d_camera:', window.__designer3d_camera);

    if (ref?.current) {
      const r3fRef = ref as unknown as R3FRef;
      if (r3fRef.current?.gl && r3fRef.current?.scene && r3fRef.current?.camera) {
        gl = r3fRef.current.gl;
        scene = r3fRef.current.scene;
        camera = r3fRef.current.camera as THREE.OrthographicCamera;
      }
    }

    // Fallback: try to get from window globals (set by Canvas3D)
    if ((!gl || !scene || !camera) && typeof window !== 'undefined') {
      if (window.__designer3d_gl && window.__designer3d_scene && window.__designer3d_camera) {
        gl = window.__designer3d_gl;
        scene = window.__designer3d_scene;
        camera = window.__designer3d_camera;
        refSource = 'window';
      }
    }

    // Fallback: try to get from DOM (canvas)
    if (!gl) {
      const canvas = document.querySelector('canvas');
      if (canvas && (canvas as any).__r3f) {
        const rendererInfo = (canvas as any).__r3f;
        if (rendererInfo.gl && rendererInfo.scene && rendererInfo.camera) {
          gl = rendererInfo.gl;
          scene = rendererInfo.scene;
          camera = rendererInfo.camera as THREE.OrthographicCamera;
          refSource = 'canvas';
        }
      }
    }

    console.log('[ExportViewsSwitch] After fallback checks. gl:', gl, 'scene:', scene, 'camera:', camera);
    if (!gl || !scene || !camera) {
      const missing = [!gl && 'WebGLRenderer', !scene && 'Scene', !camera && 'Camera'].filter(Boolean).join(', ');
      console.error('Missing required Three.js objects:', { gl, scene, camera });
      Alert.alert('Export Error', `Missing required Three.js objects: ${missing}`);
      setExportPlanInProgress(false);
      return;
    }

    console.log('Starting processPlan with source:', refSource, { glExists: !!gl, sceneExists: !!scene, cameraExists: !!camera });

    if (gl && scene && camera) {
      processPlan(gl, scene, camera)
      .then(async (dataURL: string) => {
      console.log('Plan view export successful, opening download');
      const a = document.createElement('a');
      a.href = dataURL;
      a.download = 'plan-view.png';
      a.click();
      
      // Restore visibility of measurements, grass, and skybox
      setSceneSettings((prev: any) => ({ ...prev, exportMode: false }));
      // Wait for React to re-render with visible elements
      await waitForRender();
      
      setExportPlanInProgress(false);
    })
    .catch(async (error: any) => {
      console.error('Error exporting plan view:', error);
      Alert.alert('Export Error', 'Could not export plan view: ' + error.message);
      
      // Ensure we restore visibility even if export fails
      setSceneSettings((prev: any) => ({ ...prev, exportMode: false }));
      await waitForRender();
      
      setExportPlanInProgress(false);
    });
    }
  };

  return (
    <View style={styles.container}>
      {!isPlanView && (
        <>
          <TouchableOpacity 
            style={[styles.button, export3DInProgress && styles.buttonDisabled]} 
            onPress={handle3DExport}
            disabled={export3DInProgress || exportPlanInProgress}
          >
            <ThemedText style={styles.buttonText}>
              {export3DInProgress ? 'Exporting...' : 'Export 3D View'}
            </ThemedText>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.button, exportPlanInProgress && styles.buttonDisabled]} 
            onPress={handlePlanExport}
            disabled={exportPlanInProgress || export3DInProgress}
          >
            <ThemedText style={styles.buttonText}>
              {exportPlanInProgress ? 'Exporting...' : 'Export Plan View'}
            </ThemedText>
          </TouchableOpacity>
        </>
      )}
    </View>
  );
};

export default ExportViewsSwitch;
