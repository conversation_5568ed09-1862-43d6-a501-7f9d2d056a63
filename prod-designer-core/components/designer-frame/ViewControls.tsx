import React, { useState } from 'react';
import { StyleSheet, TouchableOpacity, Text, View } from 'react-native';
import { useSceneContext } from '@/redux/context';
import { ThemedView } from '@/components/themed/ThemedView';
import { ThemedText } from '@/components/themed/ThemedText';
import { useThemeColor } from '@/hooks/useThemeColor';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';

function ViewControls() {
  const { sceneSettings, setSceneSettings } = useSceneContext();
  const [isOpen, setIsOpen] = useState(true); // Set to true to make it expanded by default

  const { t } = useTranslation();
  
  // Map viewMode to selectedOption index
  const getSelectedOptionFromViewMode = (viewMode: string): number => {
    switch (viewMode) {
      case 'normal': return 0;
      case 'transparent': return 1;
      case 'frame': return 2;
      default: return 0;
    }
  };
  
  // Use state to track the selected option
  const [selectedOption, setSelectedOption] = useState(
    getSelectedOptionFromViewMode(sceneSettings.viewMode)
  );
  
  // Update selectedOption when sceneSettings.viewMode changes
  React.useEffect(() => {
    setSelectedOption(getSelectedOptionFromViewMode(sceneSettings.viewMode));
  }, [sceneSettings.viewMode]);
  
  const accent = useThemeColor({}, 'accentColor');
  const backgroundColor = useThemeColor({}, 'background');
  const textColor = useThemeColor({}, 'text');
  
  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };
  
  const selectOption = (optionIndex: number) => {
    setSelectedOption(optionIndex);
    
    // Update the scene settings based on the selected option
    let viewMode: 'normal' | 'transparent' | 'frame' = 'normal';
    switch (optionIndex) {
      case 0:
        viewMode = 'normal';
        break;
      case 1:
        viewMode = 'transparent';
        break;
      case 2:
        viewMode = 'frame';
        break;
    }
    
    // Apply the view mode change to scene settings
    setSceneSettings(prev => ({
      ...prev,
      viewMode
    }));
    
    // Keep dropdown open after selection
    // setIsOpen(false); // Commented out to keep dropdown open
    
    // Log the view mode change
    console.log(`View mode changed to: ${viewMode}`);
  };
  
  return (
    <ThemedView style={styles.rootContainer}>
      <TouchableOpacity 
        style={[styles.dropdownButton, { backgroundColor }]} 
        onPress={toggleDropdown}
      >
        <ThemedText style={styles.dropdownButtonText}>{t('Views')}</ThemedText>
        <Ionicons 
          name={isOpen ? "chevron-up" : "chevron-down"} 
          size={20} 
          color={textColor} 
        />
      </TouchableOpacity>
      
      {isOpen && (
        <ThemedView style={styles.dropdownContent}>
          <TouchableOpacity 
            style={styles.optionContainer} 
            onPress={() => selectOption(0)}
          >
            <View style={styles.checkboxContainer}>
              <View style={[
                styles.checkbox, 
                selectedOption === 0 && { backgroundColor: accent }
              ]}>
                {selectedOption === 0 && (
                  <Ionicons name="checkmark" size={16} color="white" />
                )}
              </View>
              <ThemedText style={styles.optionText}>{t('Normal')}</ThemedText>
            </View>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.optionContainer} 
            onPress={() => selectOption(1)}
          >
            <View style={styles.checkboxContainer}>
              <View style={[
                styles.checkbox, 
                selectedOption === 1 && { backgroundColor: accent }
              ]}>
                {selectedOption === 1 && (
                  <Ionicons name="checkmark" size={16} color="white" />
                )}
              </View>
              <ThemedText style={styles.optionText}>{t('Transparent')}</ThemedText>
            </View>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.optionContainer} 
            onPress={() => selectOption(2)}
          >
            <View style={styles.checkboxContainer}>
              <View style={[
                styles.checkbox, 
                selectedOption === 2 && { backgroundColor: accent }
              ]}>
                {selectedOption === 2 && (
                  <Ionicons name="checkmark" size={16} color="white" />
                )}
              </View>
              <ThemedText style={styles.optionText}>{t('Frame only')}</ThemedText>
            </View>
          </TouchableOpacity>
        </ThemedView>
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  rootContainer: {
    // Removed absolute positioning to let parent container control positioning
    zIndex: 100,
  },
  dropdownButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start', // Changed to left-align content
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 8,
    width: 180,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  dropdownButtonText: {
    fontWeight: '500',
  },
  dropdownContent: {
    marginTop: 8,
    borderRadius: 8,
    width: 180,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  optionContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: 20,
    borderWidth: 2,
    borderColor: '#ccc',
    marginRight: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  optionText: {
    fontSize: 14,
  },
});

export default ViewControls;
