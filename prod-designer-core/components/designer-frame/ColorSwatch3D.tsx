import React from 'react';
import { View, StyleSheet } from 'react-native';

interface Props {
  color: string;
  size?: number; // new optional size prop
}

export const ColorSwatch3D = ({ color, size = 60 }: Props) => {
  const radius = size / 2;

  return (
    <View style={[styles.container, { width: size, height: size }]}>
      <View style={[styles.sphere, {
        backgroundColor: color,
        borderRadius: radius,
        shadowRadius: size * 0.1,
      }]}>
        <View style={[styles.innerGlow, { borderRadius: radius * 0.9 }]} />
        <View style={[styles.highlight, {
          width: size * 0.3,
          height: size * 0.3,
          borderRadius: size * 0.15,
        }]} />
        <View style={[styles.glossArc, {
          width: size * 0.7,
          height: size * 0.3,
        }]} />
        <View style={[styles.bottomShadow, {
          height: size * 0.6,
          borderRadius: radius,
        }]} />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 6,
  },
  sphere: {
    flex: 1,
    overflow: 'hidden',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 2, height: 6 },
    shadowOpacity: 0.5,
    elevation: 10,
    borderWidth: 1,
    borderColor: 'rgba(180,180,180,0.3)',
    position: 'relative',
  },
  innerGlow: {
    position: 'absolute',
    width: '90%',
    height: '90%',
    backgroundColor: 'rgba(255,255,255,0.1)',
  },
  highlight: {
    position: 'absolute',
    top: '10%',
    left: '10%',
    backgroundColor: 'rgba(255,255,255,0.6)',
    opacity: 0.9,
  },
  glossArc: {
    position: 'absolute',
    top: '25%',
    left: '15%',
    borderRadius: 999,
    backgroundColor: 'rgba(255,255,255,0.35)',
    opacity: 0.6,
    transform: [{ rotate: '-25deg' }],
  },
  bottomShadow: {
    position: 'absolute',
    bottom: 0,
    width: '100%',
    backgroundColor: 'rgba(0,0,0,0.25)',
  },
});
