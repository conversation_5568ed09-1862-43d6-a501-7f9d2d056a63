import { View, StyleSheet, Switch, TouchableOpacity, Dimensions, Platform, ScaledSize, ViewStyle, TextStyle } from 'react-native';
import React, { useState, useEffect, useCallback } from 'react';
import { useSceneContext, setSizeValue } from '@/redux/context';
import Slider from '@/components/ui-controls/Slider';
import { ThemedView } from '@/components/themed/ThemedView';
import { ThemedText } from '@/components/themed/ThemedText';
import { ThemedTextInput } from '@/components/themed/ThemedInputText';
import { HorizontalExpandable } from '@/components/HorizontalExpandable';
import { Picker } from '@react-native-picker/picker';
import { ThemedPicker } from '@/components/themed/ThemedPicker';
import { RoofType, roofTypes } from '@/types/carport';
import { ColorPicker } from '@/components/ui-controls/ColorPicker';
import { colorbondColors } from '@/constants/colors/colorbondColors';
import { updateMaterialColor, updateMultipleMaterialColors } from '@/domain/materials/colorManager';
import { CarportMaterialType } from '@/constants/materials/Material';
import { ObjectPicker } from '@/components/ui-controls/ObjectPicker';
import { objectOptions } from '@/constants/objects/objectOptions';
import { WallSectionsVisualizer } from './WallSectionsVisualizer';
import { IconSymbol } from '@/components/platform-specific/IconSymbol';
import { Colors } from '@/constants/colors/Colors';

export function CanvasControls() {
  const { dimensions, dispatch, sceneSettings, setSceneSettings } = useSceneContext();
  const { height, span, length, pitch, overhang, roofType } = dimensions;
  const standardPitchValues = [2, 5];
  const gablePitchValues = [5,11, 15, 22, 30];
  const pitchValues = roofType === 'Gable' ? gablePitchValues : standardPitchValues;
  const [inputValues, setInputValues] = useState({
    length: String(length),
    height: String(height),
    span: String(span),
    overhang: String(overhang),
    pitch: String(pitch),
  });
  
  // Track screen dimensions for responsive layout
  const [screenDimensions, setScreenDimensions] = useState(Dimensions.get('window'));
  const [isMobile, setIsMobile] = useState(screenDimensions.width < 768);
  
  // Track which button is expanded (only one can be expanded at a time)
  const [expandedButton, setExpandedButton] = useState<string | null>(null);
  
  // Calculate safe area for mobile expanded content
  const safeAreaBottom = Platform.OS === 'ios' ? 34 : 16; // Account for iOS safe area
  
  // Update dimensions when screen size changes
  useEffect(() => {
    const handleDimensionsChange = ({ window }: { window: ScaledSize }) => {
      setScreenDimensions(window);
      setIsMobile(window.width < 768); // Consider mobile if width is less than 768px
    };
    
    const subscription = Dimensions.addEventListener('change', handleDimensionsChange);
    return () => subscription.remove();
  }, []);
  
  // Use the color selections from the scene context instead of local state

  useEffect(() => {
    setInputValues({
      length: String(length),
      height: String(height),
      span: String(span),
      overhang: String(overhang),
      pitch: String(pitch),
    });
  }, [length, height, span, overhang, pitch]);

  type DimensionType = 'length' | 'height' | 'span' | 'pitch' | 'overhang';

  const handleValueChange = (type: DimensionType, value: number | string) => {
    if (typeof value === 'string') {
      // Always update the input value state
      setInputValues((prev) => ({ ...prev, [type]: value }));
      if (value === '') return;

      const numValue = parseFloat(value);
      if (isNaN(numValue)) return;

      // Only update the actual dimension if the value is valid
      if (type === 'pitch') {
        if (numValue >= 2 && numValue <= 30) {
          dispatch(setSizeValue({ [type]: numValue }));
        }
      } else {
        const min = type === 'overhang' ? 0 : 1500;
        const max = type === 'overhang' ? 1500 : type === 'length' ? 36000 : 8000;
        if (numValue >= min && numValue <= max) {
          const roundedValue = Math.floor(numValue / 100) * 100;
          dispatch(setSizeValue({ [type]: roundedValue }));
        }
      }
    } else {
      // For slider and picker changes, always update both states
      if (type === 'pitch') {
        dispatch(setSizeValue({ [type]: value }));
      } else {
        const roundedValue = Math.floor(value / 100) * 100;
        dispatch(setSizeValue({ [type]: roundedValue }));
      }
    }
  };

  const handleRoofTypeChange = (value: RoofType) => {
    const newPitch = value === 'Gable' ? 11 : 2;
    const updates: Partial<typeof dimensions> = { roofType: value, pitch: newPitch };

    // Set overhang based on roof type
    if (value === 'Skillion Overhang') {
      updates.overhang = 500; // Set overhang to 500 for Skillion Overhang roof
    } else if (value === 'Skillion' || value === 'Skillion Attached' || value === 'Gable') {
      updates.overhang = 0; // Minimum overhang value for Skillion, Skillion Attached, and Gable
    }

    dispatch(setSizeValue(updates));
  };

  if (sceneSettings.viewOnlyMode) {
    return null;
  }

  // Handle button expansion
  const handleExpand = (buttonName: string, isExpanded: boolean) => {
    if (isExpanded) {
      setExpandedButton(buttonName);
    } else if (expandedButton === buttonName) {
      setExpandedButton(null);
    }
  };

  return (
    <View style={{flex: 1}}>
      {/* Main controls on left edge */}
      <ThemedView style={[styles.container, isMobile ? styles.mobileContainer : styles.desktopContainer]}>
        <HorizontalExpandable 
          title="Size" 
          icon="straighten" 
          isMobile={isMobile}
          isExpanded={expandedButton === 'size'}
          onExpand={(expanded) => handleExpand('size', expanded)}>
        <ThemedView style={styles.content}>
          <ThemedView style={styles.row}>
            <ThemedView style={styles.dropdownContainer}>
              <ThemedText style={styles.label}>Roof</ThemedText>
              <ThemedView style={styles.pickerContainer}>
                <ThemedView style={styles.picker}>
                  <ThemedPicker selectedValue={roofType} onValueChange={handleRoofTypeChange}>
                    {roofTypes.map((type) => (
                      <Picker.Item key={type} label={type} value={type} />
                    ))}
                  </ThemedPicker>
                </ThemedView>
              </ThemedView>
            </ThemedView>
            {
              <ThemedView style={styles.dropdownContainer}>
                <ThemedText style={styles.label}>Pitch</ThemedText>
                <View style={styles.pickerContainer}>
                  <View style={styles.picker}>
                    <ThemedPicker
                      selectedValue={pitch}
                      onValueChange={(value) => handleValueChange('pitch', Number(value))}
                    >
                      {pitchValues.map((value) => (
                        <Picker.Item key={value} label={String(value)} value={value} />
                      ))}
                    </ThemedPicker>
                  </View>
                  <ThemedText style={styles.unit}>°</ThemedText>
                </View>
              </ThemedView>
            }
          </ThemedView>

          <ThemedView style={styles.row}>
            <ThemedText style={styles.label}>Span</ThemedText>
            <Slider
              value={span}
              min={1000}
              max={12000}
              onChange={(v) => handleValueChange('span', v)}
              style={styles.slider}
            />
            <ThemedTextInput
              style={styles.input}
              value={inputValues.span}
              onChangeText={(v) => handleValueChange('span', v)}
              keyboardType="numeric"
            />
              <ThemedText style={styles.unit}>mm</ThemedText>
            </ThemedView>

          <ThemedView style={styles.row}>
            <ThemedText style={styles.label}>Length</ThemedText>
            <Slider
              value={length}
              min={1000}
              max={24000}
              onChange={(v) => handleValueChange('length', v)}
              style={styles.slider}
            />
            <ThemedTextInput
              style={styles.input}
              value={inputValues.length}
              onChangeText={(v) => handleValueChange('length', v)}
              keyboardType="numeric"
            />
            <ThemedText style={styles.unit}>mm</ThemedText>
          </ThemedView>

          <ThemedView style={styles.row}>
            <ThemedText style={styles.label}>Height</ThemedText>
            <Slider
              value={height}
              min={1000}
              max={12000}
              onChange={(v) => handleValueChange('height', v)}
              style={styles.slider}
            />
            <ThemedTextInput
              style={styles.input}
              value={inputValues.height}
              onChangeText={(v) => handleValueChange('height', v)}
              keyboardType="numeric"
            />
            <ThemedText style={styles.unit}>mm</ThemedText>
          </ThemedView>

          {(roofType === 'Skillion Overhang') ? (
            <ThemedView style={styles.row}>
              <ThemedText style={styles.label}>Overhang</ThemedText>
              <Slider
                value={overhang}
                min={0}
                max={1000}
                onChange={(v) => handleValueChange('overhang', v)}
                style={styles.slider}
              />
              <ThemedTextInput
                style={styles.input}
                value={inputValues.overhang}
                onChangeText={(v) => handleValueChange('overhang', v)}
                keyboardType="numeric"
              />
              <ThemedText style={styles.unit}>mm</ThemedText>
            </ThemedView>
          ) : roofType === 'Gable' ? (
            <ThemedView style={styles.row}>
              <ThemedText style={styles.label}>Overhang</ThemedText>
              <ThemedView style={[styles.slider, { opacity: 0.5 } as ViewStyle]}>
                <ThemedText style={{ textAlign: 'left' as const } as TextStyle}>Overhang Feature Coming Soon</ThemedText>
              </ThemedView>
              <ThemedTextInput
                style={[styles.input, { opacity: 0.5 }]}
                value="0"
                editable={false}
                keyboardType="numeric"
              />
              <ThemedText style={styles.unit}>mm</ThemedText>
            </ThemedView>
          ) : null}

          {/* Slab Checkbox */}
          <ThemedView style={styles.row}>
            <ThemedText style={styles.label}>Slab</ThemedText>
            <ThemedView style={styles.switchContainer}>
              <Switch
                value={sceneSettings.showSlab}
                onValueChange={(value) => {
                  setSceneSettings(prev => ({
                    ...prev,
                    showSlab: value
                  }));
                }}
              />
            </ThemedView>
          </ThemedView>
        </ThemedView>
      </HorizontalExpandable>

      <HorizontalExpandable 
        title="Lean-To" 
        icon="view-quilt" 
        isMobile={isMobile}
        isExpanded={expandedButton === 'leanto'}
        onExpand={(expanded) => handleExpand('leanto', expanded)}>
        <ThemedView style={styles.content}>
          {/* Left Lean to Controls */}
          <ThemedView style={styles.row}>
            <ThemedText style={styles.label}>Left Lean to</ThemedText>
            <ThemedView style={styles.switchContainer}>
              <Switch
                value={sceneSettings.leftLeanTo || false}
                onValueChange={(value) => {
                  setSceneSettings(prev => ({
                    ...prev,
                    leftLeanTo: value
                  }));
                }}
              />
            </ThemedView>
          </ThemedView>

          {sceneSettings.leftLeanTo && (
            <>
              <ThemedView style={styles.row}>
                <ThemedText style={styles.label}>Drop Height</ThemedText>
                <Slider
                  value={sceneSettings.leftLeanToDropHeight || 500}
                  min={300}
                  max={1500}
                  onChange={(v) => setSceneSettings(prev => ({
                    ...prev,
                    leftLeanToDropHeight: Math.floor(v / 100) * 100
                  }))}
                  style={styles.slider}
                />
                <ThemedTextInput
                  style={styles.input}
                  value={String(sceneSettings.leftLeanToDropHeight || 500)}
                  onChangeText={(v) => {
                    if (v === '') return;
                    const numValue = parseFloat(v);
                    if (isNaN(numValue)) return;
                    setSceneSettings(prev => ({
                      ...prev,
                      leftLeanToDropHeight: Math.floor(numValue / 100) * 100
                    }));
                  }}
                  keyboardType="numeric"
                />
                <ThemedText style={styles.unit}>mm</ThemedText>
              </ThemedView>

              <ThemedView style={styles.row}>
                <ThemedText style={styles.label}>Span</ThemedText>
                <Slider
                  value={sceneSettings.leftLeanToSpan || 1000}
                  min={1000}
                  max={4000}
                  onChange={(v) => setSceneSettings(prev => ({
                    ...prev,
                    leftLeanToSpan: Math.floor(v / 100) * 100
                  }))}
                  style={styles.slider}
                />
                <ThemedTextInput
                  style={styles.input}
                  value={String(sceneSettings.leftLeanToSpan || 1000)}
                  onChangeText={(v) => {
                    if (v === '') return;
                    const numValue = parseFloat(v);
                    if (isNaN(numValue)) return;
                    setSceneSettings(prev => ({
                      ...prev,
                      leftLeanToSpan: Math.floor(numValue / 100) * 100
                    }));
                  }}
                  keyboardType="numeric"
                />
                <ThemedText style={styles.unit}>mm</ThemedText>
              </ThemedView>
            </>
          )}

          {/* Right Lean to Controls */}
          <ThemedView style={styles.row}>
            <ThemedText style={[styles.label, roofType === 'Skillion Attached' ? { opacity: 0.5 } as TextStyle : {}]}>Right Lean to</ThemedText>
            <ThemedView style={styles.switchContainer}>
              <Switch
                value={sceneSettings.rightLeanTo || false}
                disabled={roofType === 'Skillion Attached'}
                onValueChange={(value) => {
                  setSceneSettings(prev => ({
                    ...prev,
                    rightLeanTo: value
                  }));
                }}
              />
            </ThemedView>
          </ThemedView>

          {sceneSettings.rightLeanTo && (
            <>
              <ThemedView style={styles.row}>
                <ThemedText style={styles.label}>Drop Height</ThemedText>
                <Slider
                  value={sceneSettings.rightLeanToDropHeight || 500}
                  min={300}
                  max={1500}
                  onChange={(v) => setSceneSettings(prev => ({
                    ...prev,
                    rightLeanToDropHeight: Math.floor(v / 100) * 100
                  }))}
                  style={styles.slider}
                />
                <ThemedTextInput
                  style={styles.input}
                  value={String(sceneSettings.rightLeanToDropHeight || 500)}
                  onChangeText={(v) => {
                    if (v === '') return;
                    const numValue = parseFloat(v);
                    if (isNaN(numValue)) return;
                    setSceneSettings(prev => ({
                      ...prev,
                      rightLeanToDropHeight: Math.floor(numValue / 100) * 100
                    }));
                  }}
                  keyboardType="numeric"
                />
                <ThemedText style={styles.unit}>mm</ThemedText>
              </ThemedView>

              <ThemedView style={styles.row}>
                <ThemedText style={styles.label}>Span</ThemedText>
                <Slider
                  value={sceneSettings.rightLeanToSpan || 1000}
                  min={1000}
                  max={4000}
                  onChange={(v) => setSceneSettings(prev => ({
                    ...prev,
                    rightLeanToSpan: Math.floor(v / 100) * 100
                  }))}
                  style={styles.slider}
                />
                <ThemedTextInput
                  style={styles.input}
                  value={String(sceneSettings.rightLeanToSpan || 1000)}
                  onChangeText={(v) => {
                    if (v === '') return;
                    const numValue = parseFloat(v);
                    if (isNaN(numValue)) return;
                    setSceneSettings(prev => ({
                      ...prev,
                      rightLeanToSpan: Math.floor(numValue / 100) * 100
                    }));
                  }}
                  keyboardType="numeric"
                />
                <ThemedText style={styles.unit}>mm</ThemedText>
              </ThemedView>
            </>
          )}
        </ThemedView>
      </HorizontalExpandable>

      <HorizontalExpandable 
        title="Walls" 
        icon="grid-on" 
        isMobile={isMobile}
        isExpanded={expandedButton === 'walls'}
        onExpand={(expanded) => handleExpand('walls', expanded)}>
        <ThemedView style={styles.content}>
          <ThemedText style={styles.placeholderText}>Wall sections coming soon</ThemedText>
        </ThemedView>
      </HorizontalExpandable>
      
      {/* Wall Sections - Original disabled code */}
      {/* <HorizontalExpandable title="Wall Sections" icon="grid-on">
        <ThemedView style={styles.content}>
          <WallSectionsVisualizer 
            onToggleWallSection={(section, index) => {
              if (!sceneSettings.wallSections) return;
              
              const updatedWallSections = { ...sceneSettings.wallSections };
              
              if (section === 'front') {
                updatedWallSections.front = !updatedWallSections.front;
              } else if (section === 'back') {
                updatedWallSections.back = !updatedWallSections.back;
              } else if (section === 'left' && typeof index === 'number') {
                const newLeft = [...updatedWallSections.left];
                newLeft[index] = !newLeft[index];
                updatedWallSections.left = newLeft;
              } else if (section === 'right' && typeof index === 'number') {
                const newRight = [...updatedWallSections.right];
                newRight[index] = !newRight[index];
                updatedWallSections.right = newRight;
              }
              
              setSceneSettings(prev => ({
                ...prev,
                wallSections: updatedWallSections
              }));
            }}
          />
        </ThemedView>
      </HorizontalExpandable> */}
      
      {/* Additional Options */}
      <HorizontalExpandable 
        title="Extras" 
        icon="settings" 
        isMobile={isMobile}
        isExpanded={expandedButton === 'extras'}
        onExpand={(expanded) => handleExpand('extras', expanded)}>
        <ThemedView style={styles.content}>
          <ThemedText style={styles.sectionTitle}>Additional Options</ThemedText>
          
          {/* Roof Profile Dropdown */}
          <ThemedView style={styles.row}>
            <ThemedView style={styles.dropdownContainer}>
              <ThemedText style={styles.label}>Roof Profile</ThemedText>
              <ThemedView style={styles.pickerContainer}>
                <ThemedView style={styles.picker}>
                  <ThemedPicker
                    selectedValue={sceneSettings.roofProfile || 'Corro'}
                    onValueChange={(value) => {
                      setSceneSettings(prev => ({
                        ...prev,
                        roofProfile: value
                      }));
                    }}
                  >
                    <Picker.Item key="corro" label="Corro" value="Corro" />
                    <Picker.Item key="mono" label="Mono" value="Mono" />
                  </ThemedPicker>
                </ThemedView>
              </ThemedView>
            </ThemedView>
          </ThemedView>

          {/* Wall Profile Dropdown */}
          <ThemedView style={styles.row}>
            <ThemedView style={styles.dropdownContainer}>
              <ThemedText style={styles.label}>Wall Profile</ThemedText>
              <ThemedView style={styles.pickerContainer}>
                <ThemedView style={styles.picker}>
                  <ThemedPicker
                    selectedValue={sceneSettings.wallProfile || 'Corro'}
                    onValueChange={(value) => {
                      setSceneSettings(prev => ({
                        ...prev,
                        wallProfile: value
                      }));
                    }}
                  >
                    <Picker.Item key="corro" label="Corro" value="Corro" />
                    <Picker.Item key="mono" label="Mono" value="Mono" />
                  </ThemedPicker>
                </ThemedView>
              </ThemedView>
            </ThemedView>
          </ThemedView>
          
          <ThemedView style={styles.placeholderContainer}>
            <ThemedText style={styles.placeholderText}>
              This is a placeholder for additional options that can be added in the future.
              It could include features like insulation, lighting, ventilation, etc.
            </ThemedText>
          </ThemedView>
        </ThemedView>
      </HorizontalExpandable>

      {/* New Colours Section */}
      <HorizontalExpandable 
        title="Colours" 
        icon="palette" 
        isMobile={isMobile}
        isExpanded={expandedButton === 'colours'}
        onExpand={(expanded) => handleExpand('colours', expanded)}>
        <ThemedView style={styles.content}>
          {/* Roof Colour */}
          <ColorPicker
            label="Roof"
            colors={colorbondColors}
            selectedValue={sceneSettings.roofColor || 'Surfmist'}
            onValueChange={(value) => {
              // Update scene settings
              setSceneSettings(prev => ({
                ...prev,
                roofColor: value
              }));
              
              // Directly update the roof material for immediate effect
              updateMaterialColor(CarportMaterialType.Roof, value);
            }}
          />

          {/* Wall Colour - Disabled but left as placeholder */}
          <ThemedView style={{opacity: 0.5, pointerEvents: 'none' as 'none'} as ViewStyle}>
            <ColorPicker
              label="Wall"
              colors={colorbondColors}
              selectedValue={sceneSettings.wallColor || 'Surfmist'}
              onValueChange={(value) => {
                // Update scene settings
                setSceneSettings(prev => ({
                  ...prev,
                  wallColor: value
                }));
                
                // Directly update the material color for immediate effect
                updateMaterialColor(CarportMaterialType.BrickWall, value);
              }}
            />
          </ThemedView>

          {/* Trim Colour */}
          <ColorPicker
            label="Trim"
            colors={colorbondColors}
            selectedValue={sceneSettings.trimColor || 'Surfmist'}
            onValueChange={(value) => {
              // Update scene settings
              setSceneSettings(prev => ({
                ...prev,
                trimColor: value
              }));
              
              // Directly update the cap material for immediate effect
              // This includes ONLY ridge caps and barge caps (not posts or rafters)
              updateMaterialColor(CarportMaterialType.Cap, value);
            }}
          />

          {/* Door Colour - Disabled but left as placeholder */}
          <ThemedView style={{opacity: 0.5, pointerEvents: 'none' as 'none'} as ViewStyle}>
            <ColorPicker
              label="Door"
              colors={colorbondColors}
              selectedValue={sceneSettings.doorColor || 'Surfmist'}
              onValueChange={(value) => {
                // Update scene settings
                setSceneSettings(prev => ({
                  ...prev,
                  doorColor: value
                }));
                
                // Note: Currently no direct door material in the scene
                // If door materials are added in the future, update them here
              }}
            />
          </ThemedView>

          {/* Disclaimer Box */}
          <ThemedView style={styles.disclaimerBox}>
            <ThemedText style={styles.disclaimerText}>
              Note: Colours shown are representative only and may vary from actual COLORBOND® steel colours. 
              Please refer to actual colour swatches for accurate colour selection.
            </ThemedText>
          </ThemedView>
        </ThemedView>
      </HorizontalExpandable>

      {/* Objects Section */}
      <HorizontalExpandable 
        title="Quote" 
        icon="mail" 
        isMobile={isMobile}
        isExpanded={expandedButton === 'quote'}
        onExpand={(expanded) => handleExpand('quote', expanded)}>
        <ThemedView style={styles.content}>
          <ThemedText style={styles.sectionTitle}>Customer Information</ThemedText>
          
          <ThemedView style={styles.formGroup}>
            <ThemedText style={styles.formLabel}>Full Name</ThemedText>
            <ThemedTextInput 
              style={styles.formInput} 
              placeholder="Enter your full name"
            />
          </ThemedView>
          
          <ThemedView style={styles.formGroup}>
            <ThemedText style={styles.formLabel}>Email</ThemedText>
            <ThemedTextInput 
              style={styles.formInput} 
              placeholder="Enter your email address"
              keyboardType="email-address"
            />
          </ThemedView>
          
          <TouchableOpacity style={styles.quoteButton}>
            <ThemedText style={styles.quoteButtonText}>Request Quote</ThemedText>
          </TouchableOpacity>
          
          <ThemedView style={styles.disclaimerBox}>
            <ThemedText style={styles.disclaimerText}>
              By submitting this form, you agree to be contacted regarding your quote request.
            </ThemedText>
          </ThemedView>
        </ThemedView>
      </HorizontalExpandable>

      {/* Quote Section */}
      {/* Objects section removed as per image */}
      {/* <HorizontalExpandable 
        title="Objects" 
        icon="directions-car" 
        isMobile={isMobile}
        isExpanded={expandedButton === 'objects'}
        onExpand={(expanded) => handleExpand('objects', expanded)}>
        <ThemedView style={styles.content}>
          <ThemedText style={styles.sectionTitle}>Customer Information</ThemedText>
          
          <ThemedView style={styles.formGroup}>
            <ThemedText style={styles.formLabel}>Full Name</ThemedText>
            <ThemedTextInput 
              style={styles.formInput} 
              placeholder="Enter your full name"
            />
          </ThemedView>
          
          <ThemedView style={styles.formGroup}>
            <ThemedText style={styles.formLabel}>Email</ThemedText>
            <ThemedTextInput 
              style={styles.formInput} 
              placeholder="Enter your email address"
              keyboardType="email-address"
            />
          </ThemedView>
          
          <ThemedView style={styles.formGroup}>
            <ThemedText style={styles.formLabel}>Phone</ThemedText>
            <ThemedTextInput 
              style={styles.formInput} 
              placeholder="Enter your phone number"
              keyboardType="phone-pad"
            />
          </ThemedView>
          
          <ThemedView style={styles.formGroup}>
            <ThemedText style={styles.formLabel}>Address</ThemedText>
            <ThemedTextInput 
              style={styles.formInput} 
              placeholder="Enter your address"
            />
          </ThemedView>
          
          <ThemedView style={styles.formGroup}>
            <ThemedText style={styles.formLabel}>Additional Notes</ThemedText>
            <ThemedTextInput 
              style={[styles.formInput, styles.textArea]} 
              placeholder="Any specific requirements or questions"
              multiline
              numberOfLines={4}
            />
          </ThemedView>
          
          <TouchableOpacity style={styles.quoteButton}>
            <ThemedText style={styles.quoteButtonText}>Request Quote</ThemedText>
          </TouchableOpacity>
          
          <ThemedView style={styles.disclaimerBox}>
            <ThemedText style={styles.disclaimerText}>
              By submitting this form, you agree to be contacted regarding your quote request.
              We respect your privacy and will not share your information with third parties.
            </ThemedText>
          </ThemedView>
        </ThemedView>
      </HorizontalExpandable>
      </ThemedView>
      
      {/* Right side action buttons (New/Load/Save/View/Plan) */}
      {!isMobile && (
        <ThemedView style={styles.rightActionContainer}>
          <TouchableOpacity style={styles.actionButton}>
            <IconSymbol
              name="add"
              size={20}
              weight="medium"
              color={Colors.light.icon}
            />
            <ThemedText style={styles.actionButtonText}>New</ThemedText>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.actionButton}>
            <IconSymbol
              name="file-open"
              size={20}
              weight="medium"
              color={Colors.light.icon}
            />
            <ThemedText style={styles.actionButtonText}>Load</ThemedText>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.actionButton}>
            <IconSymbol
              name="save"
              size={20}
              weight="medium"
              color={Colors.light.icon}
            />
            <ThemedText style={styles.actionButtonText}>Save</ThemedText>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.actionButton}>
            <IconSymbol
              name="visibility"
              size={20}
              weight="medium"
              color={Colors.light.icon}
            />
            <ThemedText style={styles.actionButtonText}>View</ThemedText>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.actionButton}>
            <IconSymbol
              name="map"
              size={20}
              weight="medium"
              color={Colors.light.icon}
            />
            <ThemedText style={styles.actionButtonText}>Plan</ThemedText>
          </TouchableOpacity>
        </ThemedView>
      )}
    </>
  );
}

const styles = StyleSheet.create({
  unit: {
    fontSize: 12,
    marginLeft: 4,
  },
  container: {
    position: 'absolute',
    zIndex: 100,
    backgroundColor: 'transparent',
    borderRadius: 8,
    overflow: 'visible',
  },
  desktopContainer: {
    top: 20,
    left: 20,
    padding: 4,
    flexDirection: 'column' as const,
    maxHeight: Platform.OS === 'web' ? '90vh' as any : '92%' as any,
    gap: 1, // Minimal gap between buttons (1mm)
  },
  mobileContainer: {
    bottom: 20,
    left: 0,
    right: 0,
    padding: 8,
    flexDirection: 'row' as const,
    justifyContent: 'center',
    gap: 1, // Minimal gap between buttons
  },
  formGroup: {
    marginBottom: 12,
    width: '100%',
  },
  formLabel: {
    fontSize: 12,
    marginBottom: 4,
    fontWeight: '500',
  },
  formInput: {
    height: 36,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.1)',
    borderRadius: 4,
    paddingHorizontal: 8,
    fontSize: 14,
  },
  quoteButton: {
    backgroundColor: '#2196F3',
    padding: 10,
    borderRadius: 4,
    alignItems: 'center',
    marginTop: 12,
    marginBottom: 16,
  },
  quoteButtonText: {
    color: 'white',
    fontWeight: '500',
    fontSize: 14,
  },
  disclaimerBox: {
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    padding: 8,
    borderRadius: 4,
  },
  disclaimerText: {
    fontSize: 10,
    color: 'rgba(0, 0, 0, 0.6)',
  },
  rightActionContainer: {
    position: 'absolute',
    top: 20,
    left: 80, // Position to the right of the left column
    zIndex: 90, // Lower than the expanded content
    flexDirection: 'column' as const,
    gap: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    borderRadius: 8,
    padding: 8,
  },
  actionButton: {
    flexDirection: 'row' as const,
    alignItems: 'center',
    padding: 8,
    borderRadius: 4,
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    marginBottom: 4,
  },
  actionButtonText: {
    marginLeft: 8,
    fontSize: 14,
  },
  content: {
    padding: 8,
  },
  row: {
    flexDirection: 'row' as const,
    alignItems: 'center',
    marginVertical: 4, // Reduced vertical margin for more compact layout
  },
  label: {
    width: 50, // Reduced width
    fontSize: 12,
  },
  slider: {
    flex: 1,
    marginHorizontal: 6, // Reduced horizontal margin
  },
  input: {
    width: 50, // Reduced width
    textAlign: 'right',
    fontSize: 12,
  },
  switchContainer: {
    flexDirection: 'row' as const,
    alignItems: 'center',
    justifyContent: 'space-between',
    marginVertical: 4, // Reduced vertical margin
  },
  dropdownContainer: {
    flex: 1,
    flexDirection: 'row' as const,
    alignItems: 'center',
    marginRight: 6, // Reduced margin
  },
  pickerContainer: {
    flex: 1,
    flexDirection: 'row' as const,
    alignItems: 'center',
    marginLeft: 6, // Reduced margin
  },
  picker: {
    flex: 1,
    borderRadius: 4,
    height: 30, // Reduced height
    justifyContent: 'center',
    marginRight: 4,
  },
  disclaimerBox: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 4,
    padding: 6, // Reduced padding
    marginTop: 8, // Reduced margin
  },
  disclaimerText: {
    fontSize: 10,
    opacity: 0.7,
    textAlign: 'center',
  },
  sectionTitle: {
    fontSize: 14,
    marginBottom: 8, // Reduced margin
    opacity: 0.9,
  },
  placeholderContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 8,
    padding: 10, // Reduced padding
    marginVertical: 6, // Reduced margin
  },
  placeholderText: {
    fontSize: 12,
    opacity: 0.8,
    textAlign: 'center',
    lineHeight: 18,
  },
  formGroup: {
    marginBottom: 10, // Reduced margin
  },
  formLabel: {
    fontSize: 12,
    marginBottom: 4,
    opacity: 0.9,
  },
  formInput: {
    height: 30, // Reduced height
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 4,
    paddingHorizontal: 6, // Reduced padding
    fontSize: 12,
  },
  textArea: {
    height: 60, // Reduced height
    textAlignVertical: 'top',
    paddingTop: 6, // Reduced padding
  },
  quoteButton: {
    backgroundColor: '#3b82f6',
    borderRadius: 4,
    paddingVertical: 10,
    alignItems: 'center',
    marginTop: 8,
  },
  quoteButtonText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  smallButton: {
    backgroundColor: '#3b82f6',
    paddingVertical: 6,
    paddingHorizontal: 10,
    borderRadius: 4,
  },
  smallButtonText: {
    color: '#ffffff',
    fontSize: 12,
    textAlign: 'center',
  },
});
