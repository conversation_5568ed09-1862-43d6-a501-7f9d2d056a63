import React, { useState, useRef, createContext, useContext } from 'react';
import { View, TouchableOpacity, Animated, ScrollView, StyleSheet } from 'react-native';
import { ThemedText } from '@/components/themed/ThemedText';
import { useSceneContext, setSceneSetting, setSizeValue } from '@/redux/context';
import { RoofType, DimensionType } from '@/types/carport';
import { IconSymbol } from '@/components/platform-specific/IconSymbol';

import { SizePanel } from './panels/SizePanel';
import { LeanToPanel } from './panels/LeantoPanel';
import { WallPanel } from './panels/WallPanel';
import { ExtrasPanel } from './panels/ExtrasPanel';
import { ColoursPanel } from './panels/ColorsPanel';
import { QuotePanel } from './panels/QuotePanel';

const MAIN_CONTROLS = [
  { id: 'size', title: 'Size', icon: 'straighten' },
  { id: 'leanto', title: 'Lean-To', icon: 'view-quilt' },
  { id: 'walls', title: 'Walls', icon: 'grid-on' },
  { id: 'extras', title: 'Extras', icon: 'settings' },
  { id: 'colours', title: 'Colours', icon: 'palette' },
  { id: 'quote', title: 'Quote', icon: 'description' },
];

interface Props {
  isMobile: boolean;
  isTablet: boolean;
  onPanelToggle?: (isExpanded: boolean) => void;
}

export const PanelContext = createContext<{
  isPanelExpanded: boolean;
  panelWidth: number;
}>({ isPanelExpanded: false, panelWidth: 0 });

export const usePanelContext = () => useContext(PanelContext);

export const NewCanvasControls: React.FC<Props> = ({ isMobile, isTablet, onPanelToggle }) => {
  const [activeSection, setActiveSection] = useState<string | null>(null);
  const { dimensions, dispatch, sceneSettings } = useSceneContext();
  const { height, span, length, pitch, overhang, roofType } = dimensions;

  const [inputValues, setInputValues] = useState({
    length: String(length),
    height: String(height),
    span: String(span),
    overhang: String(overhang),
    pitch: String(pitch),
  });

  const isVerticalLayout = !isMobile && !isTablet;
  const collapsedSize = 0;
  const expandedSize = 300;

  const [panelVisible, setPanelVisible] = useState(false);
  const panelAnim = useRef(new Animated.Value(collapsedSize)).current;

  const toggleSection = (id: string) => {
    const isSame = activeSection === id;
    const nextExpanded = !isSame;
    setActiveSection(isSame ? null : id);

    Animated.timing(panelAnim, {
      toValue: nextExpanded ? expandedSize : collapsedSize,
      duration: 250,
      useNativeDriver: false,
    }).start(() => {
      setPanelVisible(nextExpanded);
      onPanelToggle?.(nextExpanded);
    });
  };

  const closePanel = () => {
    setActiveSection(null);
    Animated.timing(panelAnim, {
      toValue: collapsedSize,
      duration: 250,
      useNativeDriver: false,
    }).start(() => {
      setPanelVisible(false);
      onPanelToggle?.(false);
    });
  };

  const handleValueChange = (type: DimensionType, value: number | string) => {
    if (typeof value === 'string') {
      setInputValues((prev) => ({ ...prev, [type]: value }));
      if (value === '') {
        console.clear();
        console.warn(`DEBUG: empty string ${type}: empty string`);
        return;
      }
      const numValue = parseFloat(value);
      if (isNaN(numValue)) {
        console.warn(`DEBUG: cannot parse float for ${type}: ${value}`);
        return;
      }
      const min = type === 'overhang' ? 0 : 1500;
      const max = type === 'overhang' ? 1500 : type === 'length' ? 36000 : 8000;
      if (numValue >= min && numValue <= max) {
        const roundedValue = Math.floor(numValue / 100) * 100;
        dispatch(setSizeValue({ [type]: roundedValue }));
      }
    } else {
      const roundedValue = Math.floor(value / 100) * 100;
      dispatch(setSizeValue({ [type]: roundedValue }));
      setInputValues((prev) => ({ ...prev, [type]: roundedValue.toString() }));
    }
  };

  const handleRoofTypeChange = (value: RoofType) => {
    const newPitch = value === 'Gable' ? 11 : 2;
    const newOverhang = value === 'Skillion Overhang' ? 500 : 0;

    setInputValues((prev) => ({
      ...prev,
      pitch: newPitch.toString(),
      overhang: newOverhang.toString(),
    }));

    dispatch(
      setSizeValue({
        roofType: value,
        pitch: newPitch,
        overhang: newOverhang,
      })
    );

    setTimeout(() => {
      dispatch(setSizeValue({ span: dimensions.span + 1 })); // Khuong: looks weird
      dispatch(setSizeValue({ span: dimensions.span }));
    }, 50);

    const currentWallMatrix = sceneSettings.wallMatrix;
    if (currentWallMatrix) {
      setTimeout(() => {
        dispatch(setSceneSetting('wallMatrix', currentWallMatrix));
      }, 150);
    }
  };

  const renderExpandedContent = (controlId: string) => {
    switch (controlId) {
      case 'size':
        return (
          <SizePanel
            roofType={roofType}
            pitch={pitch}
            span={span}
            length={length}
            height={height}
            overhang={overhang}
            inputValues={inputValues}
            roofTypes={['Gable', 'Skillion', 'Skillion Overhang', 'Skillion Attached']}
            handleValueChange={handleValueChange}
            handleRoofTypeChange={handleRoofTypeChange}
          />
        );
      case 'leanto':
        return <LeanToPanel roofType={roofType} dimensions={{ length: dimensions.length }} />;
      case 'walls':
        return <WallPanel length={length} dimensions={dimensions} />;
      case 'extras':
        return <ExtrasPanel />;
      case 'colours':
        return <ColoursPanel />;
      case 'quote':
        return <QuotePanel />;
      default:
        return null;
    }
  };

  return (
    <View style={[styles.wrapper, isVerticalLayout ? styles.rowLayout : styles.columnLayout]}>
      <View
        style={[styles.buttonBar, isVerticalLayout ? styles.buttonBarLeft : styles.buttonBarBottom]}
      >
        {MAIN_CONTROLS.map((control) => (
          <TouchableOpacity
            key={control.id}
            style={[
              styles.controlButton,
              (isTablet || isMobile) && styles.controlButtonResponsive,
              isMobile && styles.controlButtonMobile,
              activeSection === control.id && styles.activeControl,
            ]}
            onPress={() => toggleSection(control.id)}
          >
            <View style={styles.iconWithLabel}>
              <IconSymbol name={control.icon as any} size={24} weight="medium" color="red" />
              <ThemedText style={[styles.controlLabel, isMobile && styles.controlLabelMobile]}>
                {control.title}
              </ThemedText>
            </View>
          </TouchableOpacity>
        ))}
      </View>

      <Animated.View
        style={[
          styles.animatedPanel,
          isVerticalLayout
            ? { width: panelAnim }
            : {
                position: 'absolute',
                left: 0,
                right: 0,
                bottom: 80,
                height: panelAnim,
                maxHeight: 400,
              },
        ]}
      >
        {panelVisible && activeSection && (
          <>
            <TouchableOpacity style={styles.closeButtonWrapper} onPress={closePanel}>
              <ThemedText style={styles.closeButton}>✕</ThemedText>
            </TouchableOpacity>

            <ScrollView
              style={styles.panelScroll}
              contentContainerStyle={styles.panelContent}
              bounces={false}
              showsVerticalScrollIndicator
            >
              {renderExpandedContent(activeSection)}
            </ScrollView>
          </>
        )}
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    flex: 1,
    position: 'relative',
  },
  rowLayout: {
    flexDirection: 'row',
  },
  columnLayout: {
    flexDirection: 'column',
  },
  buttonBar: {
    backgroundColor: '#f8f8f8',
    padding: 4,
    zIndex: 5,
  },
  buttonBarLeft: {
    width: 80,
    justifyContent: 'flex-start',
    alignItems: 'center',
  },
  buttonBarBottom: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    height: 80,
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  controlButton: {
    width: '100%',
    padding: 8,
    margin: 4,
    backgroundColor: '#fff',
    borderRadius: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowOffset: { width: 0, height: 2 },
    shadowRadius: 6,
    elevation: 3,
  },
  controlButtonResponsive: {
    flexGrow: 1,
    flexBasis: 0,
  },
  controlButtonMobile: {
    padding: 4,
    margin: 2,
  },
  activeControl: {
    backgroundColor: '#fbecec',
    borderWidth: 2,
    borderColor: 'red',
    borderRadius: 16,
  },
  iconWithLabel: {
    alignItems: 'center',
  },
  controlLabel: {
    marginTop: 4,
    fontSize: 12,
    textAlign: 'center',
    color: 'black',
  },
  controlLabelMobile: {
    marginTop: 0,
    fontSize: 8,
  },
  animatedPanel: {
    backgroundColor: '#fcecec',
    overflow: 'hidden',
    flexGrow: 1,
    zIndex: 10,
  },
  panelScroll: {
    flexGrow: 1,
  },
  panelContent: {
    paddingBottom: 100,
  },
  closeButtonWrapper: {
    position: 'absolute',
    top: 8,
    right: 8,
    zIndex: 20,
    backgroundColor: 'red',
    borderRadius: 20,
    width: 32,
    height: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  closeButton: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
  },
});
