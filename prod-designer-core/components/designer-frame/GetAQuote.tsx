import React, { useState } from 'react';
import {
  View,
  TextInput,
  StyleSheet,
  Modal,
  TouchableOpacity,
  Platform,
  Alert
} from 'react-native';
import { ThemedText } from '@/components/themed/ThemedText';
import { ThemedView } from '@/components/themed/ThemedView';
import { generateShareableLink } from './ShareService';
import { useSceneContext } from '@/redux/context';
import { encryptText } from './en-cry-decry';
import { useTranslation } from 'react-i18next';

interface GetAQuoteProps {
  isVisible: boolean;
  onClose: () => void;
}

/**
 * GetAQuote Component
 * Provides a form to request a quote for the current carport design
 */
const GetAQuote: React.FC<GetAQuoteProps> = ({ isVisible, onClose }) => {
  console.log('DEBUG: GetAQuote component initializing, isVisible:', isVisible);
  // Get the current model state from context
  const { dimensions, sceneSettings } = useSceneContext();
  const { t } = useTranslation();
  
  // Form state
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  const [formError, setFormError] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitted, setSubmitted] = useState(false);

  // Email validation
  const isValidEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  // Reset form fields
  const resetForm = () => {
    setName('');
    setEmail('');
    setPhone('');
    setFormError('');
    setIsSubmitting(false);
    setSubmitted(false);
  };

  // Close modal handler
  const handleClose = () => {
    resetForm();
    onClose();
  };

  // Function to encode base64url for the email API
  const base64url = (str: string): string => {
    // First convert to regular base64
    let base64 = btoa(str);
    // Convert to base64url format
    return base64.replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');
  };

  // Submit form handler
  const handleSubmit = async () => {
    console.log('DEBUG: GetAQuote handleSubmit starting');
    
    // Validate form
    if (!name.trim()) {
      console.log('DEBUG: Form validation failed - name is empty');
      setFormError(t('Please enter your name'));
      return;
    }
    if (!email.trim() || !isValidEmail(email)) {
      console.log('DEBUG: Form validation failed - email is invalid', email);
      setFormError(t('Please enter a valid email address'));
      return;
    }
    if (!phone.trim()) {
      console.log('DEBUG: Form validation failed - phone is empty');
      setFormError(t('Please enter your phone number'));
      return;
    }

    console.log('DEBUG: Form validation passed, proceeding with submission');
    setFormError('');
    setIsSubmitting(true);

    try {
      // Get the current model link
      const currentModelLink = generateShareableLink(dimensions, sceneSettings);
      
      // Get the current time for the email header
      const now = new Date();
      const formattedDate = now.toLocaleString();
      
      // Create a detailed email with all the carport specs
      const emailContent = `
        <div style="font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto;">
          <h2 style="color: #4a90e2;">New Quote Request from ${name} at ${formattedDate}</h2>
          
          <div style="margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px;">
            <h3>Customer Information:</h3>
            <p><strong>Name:</strong> ${name}</p>
            <p><strong>Email:</strong> ${email}</p>
            <p><strong>Phone:</strong> ${phone}</p>
          </div>
          
          <div style="margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px;">
            <h3>Carport Specifications:</h3>
            <p><strong>Length:</strong> ${dimensions.length}mm</p>
            <p><strong>Span:</strong> ${dimensions.span}mm</p>
            <p><strong>Height:</strong> ${dimensions.height}mm</p>
            <p><strong>Roof Type:</strong> ${dimensions.roofType}</p>
            <p><strong>Pitch:</strong> ${dimensions.pitch}</p>
            
            ${sceneSettings.leftLeanTo ? `<p><strong>Left Lean-to:</strong> Yes (${sceneSettings.leftLeanToSpan}mm)</p>` : ''}
            ${sceneSettings.rightLeanTo ? `<p><strong>Right Lean-to:</strong> Yes (${sceneSettings.rightLeanToSpan}mm)</p>` : ''}
          </div>
          
          <div style="margin: 20px 0;">
            <h3>Customer Design Link:</h3>
            <p>The customer's exact design can be viewed using this link:</p>
            <div style="background-color: #f5f5f5; padding: 15px; border-radius: 4px;">
              <a href="${currentModelLink}" style="color: #4a90e2; word-break: break-all;">${currentModelLink}</a>
            </div>
          </div>
        </div>
      `;

      // In development/localhost environment, just log and simulate success
      console.log('DEBUG: Checking if running on localhost');
      console.log('DEBUG: Current hostname:', window.location.hostname);
      
      const isLocalhost = window.location.hostname === 'localhost' || 
                        window.location.hostname === '127.0.0.1';
      
      console.log('DEBUG: Is localhost?', isLocalhost);
      
      if (isLocalhost) {
        console.log('DEBUG: Running in development mode');
        console.log('DEBUG: Quote request data:', {
          to: '<EMAIL>',
          subject: `New Quote Request from ${name}`,
          name: name,
          email: email,
          phone: phone,
          linkLength: currentModelLink.length,
          dimensions: dimensions,
          sceneSettings: sceneSettings
        });
        
        // Log the full email content for debugging
        console.log('DEBUG: Full email content:');
        console.log(emailContent);
        
        console.log('DEBUG: Simulating API delay of 1.5 seconds');
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1500));
        
        console.log('DEBUG: Setting submitted to true');
        // Simulate success
        setSubmitted(true);
        setIsSubmitting(false);
        return;
      }
      
      // If we get here, we're not on localhost
      console.log('DEBUG: Not on localhost, proceeding with actual API call');
      
      // For production, use the Firebase Email API
      console.log('DEBUG: Preparing email data for API');
      
      // Create a JSON structure for nodemailer
      const emailData = {
        subject: `New Quote Request from ${name}`,
        body: `Quote request from ${name}. Please view in HTML format for complete details.`,
        html: emailContent
      };
      
      console.log('DEBUG: Email data prepared', emailData);
      
      try {
        // Convert to JSON string and encode as base64url
        console.log('DEBUG: Converting to JSON and encoding...');
        const emailDataJson = JSON.stringify(emailData);
        console.log('DEBUG: JSON stringified length:', emailDataJson.length);
        
        const encodedEmail = base64url(emailDataJson);
        console.log('DEBUG: Base64 encoding successful, length:', encodedEmail.length);
  
        // Endpoint URL for the deployed Firebase Cloud Function
        const emailApiUrl = 'https://us-central1-designmycarport.cloudfunctions.net/sendEmail';
        console.log('DEBUG: API URL:', emailApiUrl);
        
        // Create security token
        const securityToken = encryptText(`email_${new Date().toISOString()}_${email}`);
        console.log('DEBUG: Security token created, length:', securityToken.length);
        
        // Prepare request body
        const requestBody = {
          email: encodedEmail,
          // to: '<EMAIL>', 
          to: '<EMAIL>',// Send all quotes to this email
          token: securityToken
        };
        console.log('DEBUG: Request body prepared', requestBody);
        
        // Call the API
        console.log('DEBUG: Calling API...');
        const response = await fetch(emailApiUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(requestBody)
        });
        
        console.log('DEBUG: API response status:', response.status);
        console.log('DEBUG: API response ok:', response.ok);
  
        // Parse the response
        const data = await response.json();
        console.log('DEBUG: API response data:', data);
        
        if (!response.ok) {
          console.log('DEBUG: API call failed');
          throw new Error(data.error || 'Failed to send quote request');
        }
        
        console.log('DEBUG: Quote request sent successfully:', data);
        
        // Show success state
        setSubmitted(true);
      } catch (apiError) {
        console.error('DEBUG: API call error:', apiError);
        throw apiError; // Re-throw to be caught by the outer try/catch
      }
    } catch (error) {
      console.error('Error sending quote request:', error);
      setFormError('Failed to send quote request. Please try again.');
    }
    
    setIsSubmitting(false);
  };

  return (
    <Modal
      visible={isVisible}
      transparent={true}
      animationType="fade"
      onRequestClose={handleClose}
    >
      <View style={styles.modalOverlay}>
        <ThemedView style={styles.modalContent}>
          <ThemedText style={styles.modalTitle}>
            {t('Request a Quote')}
          </ThemedText>
          
          {!submitted ? (
            <>
              <View style={styles.formGroup}>
                <ThemedText style={styles.label}>{t('Name')}</ThemedText>
                <TextInput
                  style={styles.input}
                  placeholder={t('Enter your name')}
                  value={name}
                  onChangeText={setName}
                />
              </View>
              
              <View style={styles.formGroup}>
                <ThemedText style={styles.label}>{t('Email')}</ThemedText>
                <TextInput
                  style={styles.input}
                  placeholder={t('Enter your email')}
                  value={email}
                  onChangeText={setEmail}
                  keyboardType="email-address"
                  autoCapitalize="none"
                />
              </View>
              
              <View style={styles.formGroup}>
                <ThemedText style={styles.label}>{t('Phone')}</ThemedText>
                <TextInput
                  style={styles.input}
                  placeholder={t('Enter your phone number')}
                  value={phone}
                  onChangeText={setPhone}
                  keyboardType="phone-pad"
                />
              </View>
              
              {formError ? (
                <ThemedText style={styles.errorText}>{formError}</ThemedText>
              ) : null}
              
              <View style={styles.buttonContainer}>
                <TouchableOpacity 
                  style={styles.cancelButton} 
                  onPress={handleClose}
                  disabled={isSubmitting}
                >
                  <ThemedText style={styles.buttonText}>{t('Cancel')}</ThemedText>
                </TouchableOpacity>
                
                <TouchableOpacity 
                  style={styles.submitButton} 
                  onPress={handleSubmit}
                  disabled={isSubmitting}
                >
                  <ThemedText style={styles.buttonText}>
                    {isSubmitting ? 'Sending...' : t('Request Quote')}
                  </ThemedText>
                </TouchableOpacity>
              </View>
            </>
          ) : (
            <View style={styles.successContainer}>
              <ThemedText style={styles.successText}>
                {t('Thankyou')}
              </ThemedText>
              <ThemedText style={styles.successSubtext}>
                {t('WillContact')}
              </ThemedText>
              <TouchableOpacity 
                style={styles.closeButton} 
                onPress={handleClose}
              >
                <ThemedText style={styles.buttonText}>{t('Close')}</ThemedText>
              </TouchableOpacity>
            </View>
          )}
        </ThemedView>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20
  },
  modalContent: {
    width: '100%',
    maxWidth: 500,
    borderRadius: 8,
    padding: 20,
    backgroundColor: '#fff'
  },
  modalTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center'
  },
  formGroup: {
    marginBottom: 15
  },
  label: {
    marginBottom: 5,
    fontWeight: '500'
  },
  input: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 4,
    padding: 10,
    fontSize: 16
  },
  errorText: {
    color: 'red',
    marginBottom: 15
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between'
  },
  cancelButton: {
    flex: 1,
    backgroundColor: '#ccc',
    padding: 12,
    borderRadius: 4,
    alignItems: 'center',
    marginRight: 10
  },
  submitButton: {
    flex: 1,
    backgroundColor: '#4a90e2',
    padding: 12,
    borderRadius: 4,
    alignItems: 'center'
  },
  buttonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16
  },
  successContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20
  },
  successText: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 10,
    textAlign: 'center'
  },
  successSubtext: {
    fontSize: 16,
    marginBottom: 20,
    textAlign: 'center',
    color: '#666'
  },
  closeButton: {
    backgroundColor: '#4a90e2',
    padding: 12,
    borderRadius: 4,
    alignItems: 'center',
    width: '100%'
  }
});

export default GetAQuote;
