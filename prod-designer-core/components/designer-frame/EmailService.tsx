import React, { useState, useEffect } from 'react';
import { StyleSheet, TouchableOpacity, View, TextInput, ActivityIndicator, Platform, Alert } from 'react-native';
import { ThemedText } from '@/components/themed/ThemedText';
import { ThemedView } from '@/components/themed/ThemedView';
import { encryptText } from './en-cry-decry';

interface EmailServiceProps {
  shareLink: string;
  onClose: () => void;
  onSuccess: () => void;
}

/**
 * EmailService Component
 * Handles sending share links via email
 */
const EmailService: React.FC<EmailServiceProps> = ({ shareLink, onClose, onSuccess }) => {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);

  // Validate email format
  const isValidEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  // Gmail API client ID and secret from the secrets directory
  const GMAIL_CLIENT_CONFIG = {
    clientId: "683633751065-26d4g02k3uvsb8v5cgcqc4i1m46edv03.apps.googleusercontent.com",
    clientSecret: "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", 
    projectId: "bimwebapp"
  };

  // Function to encode base64url for the Gmail API
  const base64url = (str: string): string => {
    // First convert to regular base64
    let base64 = btoa(str);
    // Convert to base64url format
    return base64.replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');
  };

  // Function to send email via Gmail API
  const sendEmail = async () => {
    if (!isValidEmail(email)) {
      setError('Please enter a valid email address');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      // Prepare the email content with HTML formatting
      const emailContent = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #4a90e2;">Your Custom Carport Design</h2>
          <p>Thank you for using Design My Carport!</p>
          <p>Click the link below to view your custom carport design:</p>
          <div style="background-color: #f5f5f5; padding: 15px; border-radius: 4px; margin: 20px 0;">
            <a href="${shareLink}" style="color: #4a90e2; word-break: break-all;">${shareLink}</a>
          </div>
          <p>This link contains all the specifications of your design and can be shared with others.</p>
          <p>If you have any questions, please contact us.</p>
          <p>
            Best regards,<br>
            <strong>Design My Carport Team</strong>
          </p>
        </div>
      `;

      // In development/localhost environment, just log and simulate success
      const isLocalhost = window.location.hostname === 'localhost' || 
                         window.location.hostname === '127.0.0.1';
      
      if (isLocalhost) {
        console.log('Development mode - email would be sent with data:', {
          to: email,
          subject: 'Your Custom Carport Design',
          body: emailContent
        });
        
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1500));
        
        // Simulate success
        setSuccess(true);
        setIsLoading(false);
        
        // Notify parent component
        onSuccess();
        
        // Close after delay
        setTimeout(() => {
          onClose();
        }, 2000);
        
        return;
      }
      
      // For production, use the Email API
      // Create a JSON structure for nodemailer
      const emailData = {
        subject: "Your Custom Carport Design",
        body: "Thank you for using Design My Carport! Click the link to view your custom carport design.",
        html: emailContent
      };
      
      // Convert to JSON string and encode as base64url
      const encodedEmail = base64url(JSON.stringify(emailData));

      // Endpoint URL for the deployed Firebase Cloud Function
      const emailApiUrl = 'https://us-central1-designmycarport.cloudfunctions.net/sendEmail';
      
      // Call the API
      const response = await fetch(emailApiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: encodedEmail,
          to: email,
          // Include an encrypted security token using the same encryption method
          token: encryptText(`email_${new Date().toISOString()}_${email}`)
        })
      });

      // Parse the response
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to send email');
      }
      
      console.log('Email sent successfully:', data);
      
      // Show success state
      setSuccess(true);
      setIsLoading(false);
      
      // Notify parent component of success
      onSuccess();
      
      // Close after a delay
      setTimeout(() => {
        onClose();
      }, 2000);
      
    } catch (error) {
      console.error('Error sending email:', error);
      setError('Failed to send email. Please try again.');
      setIsLoading(false);
    }
  };

  return (
    <ThemedView style={styles.container}>
      <ThemedText style={styles.title}>Share via Email</ThemedText>
      
      <ThemedText style={styles.description}>
        Enter the recipient's email address to share your carport design.
      </ThemedText>
      
      <TextInput
        style={styles.input}
        placeholder="Email address"
        placeholderTextColor="#999"
        value={email}
        onChangeText={setEmail}
        keyboardType="email-address"
        autoCapitalize="none"
        autoCorrect={false}
      />
      
      {error ? <ThemedText style={styles.errorText}>{error}</ThemedText> : null}
      
      {success ? (
        <ThemedText style={styles.successText}>
          Email sent successfully!
        </ThemedText>
      ) : (
        <View style={styles.buttonContainer}>
          <TouchableOpacity 
            style={[styles.button, styles.sendButton, !isValidEmail(email) && styles.disabledButton]} 
            onPress={sendEmail}
            disabled={!isValidEmail(email) || isLoading}
          >
            {isLoading ? (
              <ActivityIndicator color="#fff" size="small" />
            ) : (
              <ThemedText style={styles.buttonText}>Send</ThemedText>
            )}
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.button, styles.cancelButton]}
            onPress={onClose}
            disabled={isLoading}
          >
            <ThemedText style={styles.cancelButtonText}>Cancel</ThemedText>
          </TouchableOpacity>
        </View>
      )}
    </ThemedView>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
    borderRadius: 8,
    width: '100%'
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16
  },
  description: {
    marginBottom: 16,
    lineHeight: 22
  },
  input: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 4,
    padding: 12,
    marginBottom: 16,
    fontSize: 16,
    backgroundColor: '#f9f9f9'
  },
  buttonContainer: {
    flexDirection: 'column',
    gap: 12
  },
  button: {
    paddingVertical: 12,
    borderRadius: 4,
    alignItems: 'center',
    justifyContent: 'center'
  },
  sendButton: {
    backgroundColor: '#4a90e2',
  },
  disabledButton: {
    backgroundColor: '#a0c0e4',
    opacity: 0.7
  },
  cancelButton: {
    borderWidth: 1,
    borderColor: '#ccc'
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16
  },
  cancelButtonText: {
    fontWeight: 'bold',
    fontSize: 16
  },
  errorText: {
    color: '#e74c3c',
    marginBottom: 16
  },
  successText: {
    color: '#2ecc71',
    fontWeight: 'bold',
    fontSize: 16,
    textAlign: 'center',
    marginVertical: 16
  }
});

export default EmailService;
