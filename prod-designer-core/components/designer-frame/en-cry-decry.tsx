/**
 * en-cry-decry.tsx
 * Utility for storing and retrieving carport model data using UTC timestamp keys
 * with encryption to protect sensitive data
 */

/**
 * Interface for model data structure
 */
export interface CarportModelData {
  name: string;
  timestamp: string;
  dimensions: any;
  sceneSettings: any;
  displayInfo?: {
    roofType: string;
    length: number;
    span: number;
    hasLeanTo: boolean;
  };
  encryptionInfo?: {
    useCustomKey: boolean;
    nameUsedForKey: string;
  };
}

/**
 * Default encryption key - you can change this to any string you prefer
 * A longer, more complex key will provide better security
 */
const DEFAULT_ENCRYPTION_KEY = 'carport-designer-secure-key-2025';

/**
 * Encrypts a string using a simple XOR cipher with the given key
 * @param text - The text to encrypt
 * @param key - The encryption key (defaults to DEFAULT_ENCRYPTION_KEY)
 * @returns The encrypted text as a base64 string
 */
export function encryptText(text: string, key: string = DEFAULT_ENCRYPTION_KEY): string {
  // Convert text to bytes
  const textBytes = new TextEncoder().encode(text);
  const keyBytes = new TextEncoder().encode(key);
  const result = new Uint8Array(textBytes.length);
  
  // XOR each byte with the corresponding byte from the key (cycling the key if needed)
  for (let i = 0; i < textBytes.length; i++) {
    result[i] = textBytes[i] ^ keyBytes[i % keyBytes.length];
  }
  
  // Convert to base64 for storage
  return btoa(String.fromCharCode.apply(null, Array.from(result)));
}

/**
 * Decrypts a string that was encrypted with encryptText
 * @param encryptedText - The encrypted text (base64 string)
 * @param key - The encryption key (must match the one used for encryption)
 * @returns The decrypted text
 */
export function decryptText(encryptedText: string, key: string = DEFAULT_ENCRYPTION_KEY): string {
  try {
    // Convert from base64 to bytes
    const bytes = Uint8Array.from(atob(encryptedText), c => c.charCodeAt(0));
    const keyBytes = new TextEncoder().encode(key);
    const result = new Uint8Array(bytes.length);
    
    // XOR each byte with the corresponding byte from the key (cycling the key if needed)
    for (let i = 0; i < bytes.length; i++) {
      result[i] = bytes[i] ^ keyBytes[i % keyBytes.length];
    }
    
    // Convert back to text
    return new TextDecoder().decode(result);
  } catch (error) {
    console.error('Error decrypting text:', error);
    return '';
  }
}

/**
 * Saves a carport model to localStorage using a UTC timestamp as the key
 * with encryption to protect the data
 * @param modelData - The model data to save
 * @param utcTimestamp - Optional custom UTC timestamp (format: YYYYMMDD_HHMM)
 * @param encryptionKey - Optional custom encryption key
 * @returns The UTC timestamp used as the key
 */
export function saveModelWithTimestamp(modelData: CarportModelData, utcTimestamp?: string, encryptionKey?: string): string {
  try {
    // Generate a timestamp if not provided
    if (!utcTimestamp) {
      const now = new Date();
      utcTimestamp = `${now.getUTCFullYear()}${String(now.getUTCMonth() + 1).padStart(2, '0')}${String(now.getUTCDate()).padStart(2, '0')}_${String(now.getUTCHours()).padStart(2, '0')}${String(now.getUTCMinutes()).padStart(2, '0')}`;
    }
    
    // Create the storage key
    const storageKey = `carport_model_${utcTimestamp}`;
    
    // Convert model data to JSON string
    const jsonData = JSON.stringify(modelData);
    
    // Encrypt the data before saving
    const encryptedData = encryptText(jsonData, encryptionKey);
    
    // Save the encrypted data to localStorage
    localStorage.setItem(storageKey, encryptedData);
    
    // Also update the index of all saved models
    updateModelIndex(utcTimestamp, modelData.name);
    
    return utcTimestamp;
  } catch (error) {
    console.error('Error saving model:', error);
    return '';
  }
}

/**
 * Retrieves a carport model from localStorage using its UTC timestamp
 * and decrypts the data
 * @param utcTimestamp - The UTC timestamp key (format: YYYYMMDD_HHMM)
 * @param encryptionKey - Optional custom encryption key
 * @returns The model data or null if not found
 */
export function getModelByTimestamp(utcTimestamp: string, encryptionKey?: string): CarportModelData | null {
  try {
    const storageKey = `carport_model_${utcTimestamp}`;
    console.log(`Attempting to retrieve model with key: ${storageKey}`);
    console.log(`Using custom encryption key: ${encryptionKey ? 'Yes' : 'No'}`);
    
    const encryptedData = localStorage.getItem(storageKey);
    
    if (!encryptedData) {
      console.warn(`No data found for storage key: ${storageKey}`);
      return null;
    }
    
    console.log(`Found encrypted data of length: ${encryptedData.length}`);
    
    // Try to decrypt the data
    try {
      // Decrypt the data
      console.log('Attempting to decrypt data...');
      const decryptedData = decryptText(encryptedData, encryptionKey);
      console.log(`Decryption successful, data length: ${decryptedData.length}`);
      
      // Parse the JSON data
      console.log('Parsing JSON data...');
      const modelData = JSON.parse(decryptedData) as CarportModelData;
      console.log('JSON parsing successful');
      console.log('Model data:', { 
        name: modelData.name,
        timestamp: modelData.timestamp,
        hasDimensions: !!modelData.dimensions,
        hasSceneSettings: !!modelData.sceneSettings
      });
      
      return modelData;
    } catch (decryptError) {
      // If decryption fails, try parsing directly (for backward compatibility with unencrypted data)
      console.warn('Decryption failed:', decryptError);
      console.log('Attempting to parse directly as unencrypted data...');
      try {
        const modelData = JSON.parse(encryptedData) as CarportModelData;
        console.log('Direct parsing successful (unencrypted data)');
        return modelData;
      } catch (parseError) {
        console.error('Direct parsing also failed:', parseError);
        throw new Error('Both decryption and direct parsing failed');
      }
    }
  } catch (error) {
    console.error('Error retrieving model:', error);
    return null;
  }
}

/**
 * Gets a list of all saved model timestamps and names
 * @returns Array of model info objects with timestamp and name
 */
export function getAllSavedModels(): { timestamp: string; name: string }[] {
  try {
    const indexKey = 'carport_models_index';
    const indexString = localStorage.getItem(indexKey);
    
    if (!indexString) {
      return [];
    }
    
    return JSON.parse(indexString);
  } catch (error) {
    console.error('Error getting saved models list:', error);
    return [];
  }
}

/**
 * Updates the index of saved models
 * @param timestamp - The UTC timestamp of the model
 * @param name - The name of the model
 */
function updateModelIndex(timestamp: string, name: string): void {
  try {
    const indexKey = 'carport_models_index';
    const existingIndexString = localStorage.getItem(indexKey) || '[]';
    const modelIndex = JSON.parse(existingIndexString);
    
    // Check if this timestamp already exists in the index
    const existingIndex = modelIndex.findIndex((item: any) => item.timestamp === timestamp);
    
    if (existingIndex >= 0) {
      // Update existing entry
      modelIndex[existingIndex].name = name;
    } else {
      // Add new entry
      modelIndex.push({ timestamp, name });
    }
    
    // Save updated index
    localStorage.setItem(indexKey, JSON.stringify(modelIndex));
  } catch (error) {
    console.error('Error updating model index:', error);
  }
}

/**
 * Deletes a saved model by its timestamp
 * @param timestamp - The UTC timestamp of the model to delete
 * @returns True if deletion was successful
 */
export function deleteModelByTimestamp(timestamp: string): boolean {
  try {
    // Remove the model data
    const storageKey = `carport_model_${timestamp}`;
    localStorage.removeItem(storageKey);
    
    // Update the index
    const indexKey = 'carport_models_index';
    const indexString = localStorage.getItem(indexKey) || '[]';
    const modelIndex = JSON.parse(indexString);
    
    // Filter out the deleted model
    const updatedIndex = modelIndex.filter((item: any) => item.timestamp !== timestamp);
    
    // Save updated index
    localStorage.setItem(indexKey, JSON.stringify(updatedIndex));
    
    return true;
  } catch (error) {
    console.error('Error deleting model:', error);
    return false;
  }
}
