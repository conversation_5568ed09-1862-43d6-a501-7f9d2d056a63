import { StyleSheet, Animated } from 'react-native';
import { Canvas as R3fCanvas } from '@react-three/fiber';
import Scene from '@/domain/scene/scene';
import { Colors } from '@/constants/colors/Colors';
import { useSceneContext } from '@/redux/context';
import { usePanelContext } from './NewCanvasControls';

// Extend the Window interface to include Three.js globals for export
declare global {
  interface Window {
    __designer3d_gl?: any;
    __designer3d_scene?: any;
    __designer3d_camera?: any;
  }
}

export default function Canvas3D() {
  const { ref } = useSceneContext();
  const { isPanelExpanded, panelWidth } = usePanelContext();

  // Canvas takes up the remaining space
  const canvasStyle = StyleSheet.create({
    canvasInner: {
      ...styles.canvas,
      backgroundColor: Colors.dark.background,
    }
  });

  return (
    <R3fCanvas
      frameloop='demand'
      ref={ref}
      gl={{
        preserveDrawingBuffer: true,
        debug: { checkShaderErrors: false, onShaderError: null },
      }}
      style={canvasStyle.canvasInner}
      onCreated={(state) => {
        const _gl = state.gl.getContext();
        const pixelStorei = _gl.pixelStorei.bind(_gl);
        _gl.pixelStorei = function (...args) {
          const [parameter] = args;
          switch (parameter) {
            case _gl.UNPACK_FLIP_Y_WEBGL:
              return pixelStorei(...args);
          }
        };
        // Set window globals for export
        if (typeof window !== 'undefined') {
          window.__designer3d_gl = state.gl;
          window.__designer3d_scene = state.scene;
          window.__designer3d_camera = state.camera;
        }
      }}
    >
      <Scene />
    </R3fCanvas>
  );
}

const styles = StyleSheet.create({
  canvas: {
    width: '100%',
    height: '100%',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
});
