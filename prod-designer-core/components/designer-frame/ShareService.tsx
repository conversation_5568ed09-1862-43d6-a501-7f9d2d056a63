import { Platform } from 'react-native';
import { encryptText } from './en-cry-decry';

/**
 * Generates a shareable link containing the current model state
 * This is a standalone function that can be used anywhere in the application
 * @param dimensions The dimensions object from context
 * @param sceneSettings The sceneSettings object from context
 */
export const generateShareableLink = (dimensions: any, sceneSettings: any) => {

  // Create a comprehensive object with all model parameters
  const shareParams = {
    dimensions: {
      // Copy all dimension properties
      ...dimensions
    },
    sceneSettings: {
      // Copy all scene settings with explicit defaults for important properties
      ...sceneSettings,
      // Ensure these properties have default values if not set
      leftLeanTo: sceneSettings.leftLeanTo || false,
      leftLeanToSpan: sceneSettings.leftLeanToSpan || 0,
      leftLeanToDropHeight: sceneSettings.leftLeanToDropHeight || 500,
      rightLeanTo: sceneSettings.rightLeanTo || false,
      rightLeanToSpan: sceneSettings.rightLeanToSpan || 0,
      rightLeanToDropHeight: sceneSettings.rightLeanToDropHeight || 500,
      rightLeanToAttached: sceneSettings.rightLeanToAttached || false,
      wallSections: sceneSettings.wallSections || {},
      isPlanView: sceneSettings.isPlanView || false,
      defaultCamera: sceneSettings.defaultCamera || 'perspective',
      viewMode: sceneSettings.viewMode || 'normal',
      showMeasurements: sceneSettings.showMeasurements !== undefined ? sceneSettings.showMeasurements : true,
      showSlab: sceneSettings.showSlab !== undefined ? sceneSettings.showSlab : true,
      roofColor: sceneSettings.roofColor || 'NightSky',
      wallColor: sceneSettings.wallColor || 'Classic Cream',
      trimColor: sceneSettings.trimColor || 'Surfmist',
      doorColor: sceneSettings.doorColor || 'Monument',
    },
    timestamp: new Date().toISOString(),
    version: '1.0' // For future compatibility
  };

  // Convert to encrypted string
  let encryptedParams;
  try {
    encryptedParams = encryptText(JSON.stringify(shareParams));
  } catch (error) {
    console.error('Error encrypting share parameters:', error);
    return '';
  }

  // Get the current URL base
  const baseUrl = getBaseUrl();
  
  // Create the shareable link with the encrypted parameters
  return `${baseUrl}?model=${encodeURIComponent(encryptedParams)}`;
};

/**
 * Get the current URL base (without query parameters)
 */
export const getBaseUrl = () => {
  if (Platform.OS !== 'web') {
    return 'https://yourwebsite.com'; // Replace with actual URL when deployed
  }
  
  // For development on localhost, use a hardcoded value if needed
  // This ensures the share link will work even during development
  const isLocalhost = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
  if (isLocalhost) {
    console.log('Using localhost development URL');
    const port = window.location.port || '8081';
    return `http://${window.location.hostname}:${port}`;
  }
  
  const url = window.location.href.split('?')[0];
  console.log('Current URL base:', url);
  return url;
};
