import { Linking, Platform, StyleSheet } from 'react-native';
import { useSceneContext } from '@/redux/context';
import { ThemedView } from '@/components/themed/ThemedView';
import { ThemedIconButton } from '@/components/themed/ThemedIconButton';
import { useThemeColor } from '@/hooks/useThemeColor';
import { useScreenshot } from '@/hooks/platform-specific/useScreenshot.web';
import { toBase64 } from '@/utils/base64Utils';

function Toolbar() {
  const { sceneSettings, setSceneSettings, ref, dimensions } = useSceneContext();
  const { takeScreenShot } = useScreenshot(ref);

  const accent = useThemeColor({}, 'accentColor');

  async function screenshot() {
    const url = await takeScreenShot();
    if (url && Platform.OS === 'web') {
      window.open(url, '_blank');
    }
  }

  async function share() {
    const currentUrl = await Linking.getInitialURL();
    if (currentUrl) {
      const url = new URL(currentUrl);

      const b64 = toBase64(JSON.stringify(dimensions));
      if (b64) {
        url.searchParams.append('d', b64);
        Linking.openURL(url.toString());
      }
    }
  }

  return (
    <ThemedView style={styles.rootContainer}>
      <ThemedView style={styles.relativeContainer}>
        {/* Disabled screenshot export to enforce use of ExportViewsSwitch (which hides measurements) */}
        {/* <ThemedIconButton name="camera-alt" onPress={() => screenshot()} /> */}
        <ThemedIconButton
          name="grid-4x4"
          style={sceneSettings.defaultCamera === 'orthographic' && { backgroundColor: accent }}
          onPress={() => {
            setSceneSettings((p) => ({
              ...p,
              defaultCamera: p.defaultCamera === 'orthographic' ? 'perspective' : 'orthographic',
            }));
          }}
        />
        <ThemedIconButton
          style={sceneSettings.gridView && { backgroundColor: accent }}
          name="grid-view"
          onPress={() => setSceneSettings((p) => ({ ...p, gridView: !p.gridView }))}
        />
        <ThemedIconButton
          visible={!sceneSettings.viewOnlyMode}
          style={sceneSettings.isAddingCar && { backgroundColor: accent }}
          name="directions-car"
          onPress={() => setSceneSettings((p) => ({ ...p, isAddingCar: !p.isAddingCar }))}
        />
        <ThemedIconButton
          style={sceneSettings.showRule && { backgroundColor: accent }}
          name="horizontal-rule"
          onPress={() => setSceneSettings((p) => ({ ...p, showRule: !p.showRule }))}
        />
        <ThemedIconButton
          visible={!sceneSettings.viewOnlyMode}
          name="share"
          onPress={() => share()}
        />
      </ThemedView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  rootContainer: {
    flexDirection: 'column',
    position: 'absolute',
    top: 16,
    right: 16,
  },
  relativeContainer: {
    position: 'relative',
  },
});

export default Toolbar;
