import React from 'react';
import { Switch } from 'react-native';
import { useTranslation } from 'react-i18next';

import { ThemedView } from '@/components/themed/ThemedView';
import { ThemedText } from '@/components/themed/ThemedText';
import { ThemedTextInput } from '@/components/themed/ThemedInputText';
import Slider from '@/components/ui-controls/Slider';
import { styles } from './styles';
import { useSceneContext, setSceneSetting } from '@/redux/context';

interface LeanToPanelProps {
  roofType: string;
  dimensions: {
    length: number;
  };
}

export const LeanToPanel: React.FC<LeanToPanelProps> = ({ roofType, dimensions }) => {
  const { t } = useTranslation();
  const { sceneSettings, setSceneSettings, dispatch } = useSceneContext();

  const handleLeanToToggle = (side: 'left' | 'right', value: boolean) => {
    const key = side === 'left' ? 'leftLeanTo' : 'rightLeanTo';
    const wallKeyPrefix = side === 'left' ? 'left' : 'right';

    setSceneSettings((prev) => ({
      ...prev,
      [key]: value,
    }));

    if (sceneSettings.wallMatrix) {
      const sections = Math.ceil(dimensions.length / 4000);
      const updatedMatrix = { ...sceneSettings.wallMatrix };

      if (value) {
        updatedMatrix[`${wallKeyPrefix}LeanToWall`] = Array(sections).fill(false);
        updatedMatrix[`${wallKeyPrefix}LeanToDividerWall`] = Array(Math.max(1, sections - 1)).fill(
          false
        );
        updatedMatrix[`${wallKeyPrefix}LeanToFrontWall`] = false;
        updatedMatrix[`${wallKeyPrefix}LeanToBackWall`] = false;
      } else {
        updatedMatrix[`${wallKeyPrefix}LeanToWall`] = [];
        updatedMatrix[`${wallKeyPrefix}LeanToDividerWall`] = [];
        updatedMatrix[`${wallKeyPrefix}LeanToFrontWall`] = null;
        updatedMatrix[`${wallKeyPrefix}LeanToBackWall`] = null;
      }

      dispatch(setSceneSetting('wallMatrix', updatedMatrix));
    }
  };

  const renderLeanToControls = (side: 'left' | 'right') => {
    const label = side === 'left' ? t('LeftLeanTo') : t('RightLeanTo');
    const dropKey = side === 'left' ? 'leftLeanToDropHeight' : 'rightLeanToDropHeight';
    const spanKey = side === 'left' ? 'leftLeanToSpan' : 'rightLeanToSpan';
    const isEnabled = sceneSettings[`${side}LeanTo` as 'leftLeanTo' | 'rightLeanTo'];

    return (
      <>
        <ThemedView style={styles.row}>
          <ThemedText
            style={[
              styles.label,
              side === 'right' && roofType === 'Skillion Attached' ? { opacity: 0.5 } : {},
            ]}
          >
            {label}
          </ThemedText>
          <ThemedView style={[styles.switchContainer, { marginLeft: 12 }]}>
            <Switch
              value={!!isEnabled}
              disabled={side === 'right' && roofType === 'Skillion Attached'}
              onValueChange={(value) => handleLeanToToggle(side, value)}
            />
          </ThemedView>
        </ThemedView>
        {isEnabled && (
          <>
            {/* Drop Height */}
            <ThemedView
              style={{ marginBottom: 16, backgroundColor: '#fff', padding: 4, borderRadius: 6 }}
            >
              <ThemedText style={{ fontSize: 14, color: '#333', marginBottom: 4 }}>
                {t('Drop Height')}
              </ThemedText>
              <Slider
                value={sceneSettings[dropKey] || 500}
                min={300}
                max={1500}
                onChange={(v) =>
                  setSceneSettings((prev) => ({
                    ...prev,
                    [dropKey]: Math.floor(v / 100) * 100,
                  }))
                }
                style={{ marginBottom: 8 }}
              />
              <ThemedView
                style={{ flexDirection: 'row', alignItems: 'center', backgroundColor: '#fff' }}
              >
                <ThemedTextInput
                  style={{
                    flex: 1,
                    height: 36,
                    backgroundColor: 'red',
                    borderRadius: 8,
                    paddingHorizontal: 10,
                    color: '#fff',
                  }}
                  value={String(sceneSettings[dropKey] || 500)}
                  onChangeText={(v) => {
                    if (v === '') return;
                    const num = parseFloat(v);
                    if (!isNaN(num)) {
                      setSceneSettings((prev) => ({
                        ...prev,
                        [dropKey]: Math.floor(num / 100) * 100,
                      }));
                    }
                  }}
                  keyboardType="numeric"
                />
                <ThemedText style={{ fontSize: 13, color: '#555', marginLeft: 6 }}>mm</ThemedText>
              </ThemedView>
            </ThemedView>

            {/* Span */}
            <ThemedView
              style={{ marginBottom: 16, backgroundColor: '#fff', padding: 4, borderRadius: 6 }}
            >
              <ThemedText style={{ fontSize: 14, color: '#333', marginBottom: 4 }}>
                {t('Span')}
              </ThemedText>

              <Slider
                value={sceneSettings[spanKey] || 1000}
                min={1000}
                max={4000}
                onChange={(v) =>
                  setSceneSettings((prev) => ({
                    ...prev,
                    [spanKey]: Math.floor(v / 100) * 100,
                  }))
                }
                style={{ marginBottom: 8 }}
              />

              <ThemedView
                style={{ flexDirection: 'row', alignItems: 'center', backgroundColor: '#fff' }}
              >
                <ThemedTextInput
                  style={{
                    flex: 1,
                    height: 36,
                    backgroundColor: 'red',
                    borderRadius: 8,
                    paddingHorizontal: 10,
                    color: '#fff',
                  }}
                  value={String(sceneSettings[spanKey] || 1000)}
                  onChangeText={(v) => {
                    if (v === '') return;
                    const num = parseFloat(v);
                    if (!isNaN(num)) {
                      setSceneSettings((prev) => ({
                        ...prev,
                        [spanKey]: Math.floor(num / 100) * 100,
                      }));
                    }
                  }}
                  keyboardType="numeric"
                />
                <ThemedText style={{ fontSize: 13, color: '#555', marginLeft: 6 }}>mm</ThemedText>
              </ThemedView>
            </ThemedView>
          </>
        )}
      </>
    );
  };

  return (
    <ThemedView style={styles.expandedContent}>
      <ThemedView style={styles.expandedHeader}>
        <ThemedText type="defaultSemiBold" style={styles.expandedTitle}>
          {t('LeanTo')}
        </ThemedText>
      </ThemedView>

      <ThemedView style={styles.content}>
        {renderLeanToControls('left')}
        {renderLeanToControls('right')}
      </ThemedView>
    </ThemedView>
  );
};
