import React, { useState } from 'react';
import { TextInput, TouchableOpacity, View } from 'react-native';
import { ThemedView } from '@/components/themed/ThemedView';
import { ThemedText } from '@/components/themed/ThemedText';
import { useTranslation } from 'react-i18next';
import { useSceneContext } from '@/redux/context';
import { generateShareableLink } from '@/components/designer-frame/ShareService';
import { encryptText } from '@/components/designer-frame/en-cry-decry';
import { styles } from './styles';
import { ThemedTextInput } from '@/components/themed/ThemedInputText';

export const QuotePanel :React.FC = () => {
  const { t } = useTranslation();
  const { dimensions, sceneSettings } = useSceneContext();

  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  const [errors, setErrors] = useState<{ name?: string; email?: string; phone?: string }>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitted, setSubmitted] = useState(false);

  const isValidEmail = (email: string) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);

  const handleSubmit = async () => {
    const newErrors: typeof errors = {};
    if (!name.trim()) newErrors.name = t('Please enter your name');
    if (!email.trim() || !isValidEmail(email)) newErrors.email = t('Please enter a valid email address');
    if (!phone.trim()) newErrors.phone = t('Please enter your phone number');

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    setErrors({});
    setIsSubmitting(true);

    try {
      const currentModelLink = generateShareableLink(dimensions, sceneSettings);
      const now = new Date().toLocaleString();

      const emailContent = `<h2>New Quote Request from ${name} at ${now}</h2>
        <p><strong>Name:</strong> ${name}<br/>
        <strong>Email:</strong> ${email}<br/>
        <strong>Phone:</strong> ${phone}</p>
        <p><strong>Design Link:</strong> ${currentModelLink}</p>`;

      const encoded = btoa(JSON.stringify({ subject: `Quote from ${name}`, html: emailContent }));
      const token = encryptText(`email_${new Date().toISOString()}_${email}`);

      const response = await fetch('https://us-central1-designmycarport.cloudfunctions.net/sendEmail', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email: encoded, to: '<EMAIL>', token })
      });

      if (!response.ok) throw new Error('Failed to send quote request');

      setSubmitted(true);
    } catch (err) {
      console.error(err);
      setErrors({ email: t('Failed to send quote request. Please try again.') });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <ThemedView style={styles.expandedContent}>
      <ThemedView style={styles.expandedHeader}>
        <ThemedText type="defaultSemiBold" style={styles.expandedTitle}>
          {t('Quote')}
        </ThemedText>
      </ThemedView>

      <ThemedView style={styles.content}>
        {/* Name */}
        <ThemedText style={styles.label}>{t('Full Name')}</ThemedText>
        <ThemedView style={[styles.fullWidthControl, errors.name && { borderColor: 'yellow' }]}>
          <ThemedTextInput
            style={[styles.input, { backgroundColor: 'transparent', color: '#fff' }]}
            placeholder={t('Enter your full name')}
            placeholderTextColor="#fff"
            value={name}
            onChangeText={setName}
          />
        </ThemedView>
        {errors.name && <ThemedText style={styles.errorText}>{errors.name}</ThemedText>}

        {/* Email */}
        <ThemedText style={styles.label}>{t('Email')}</ThemedText>
        <ThemedView style={[styles.fullWidthControl, errors.email && { borderColor: 'yellow' }]}>
          <ThemedTextInput
            style={[styles.input, { backgroundColor: 'transparent', color: '#fff' }]}
            placeholder={t('Enter your email')}
            placeholderTextColor="#fff"
            keyboardType="email-address"
            autoCapitalize="none"
            value={email}
            onChangeText={setEmail}
          />
        </ThemedView>
        {errors.email && <ThemedText style={styles.errorText}>{errors.email}</ThemedText>}

        {/* Phone */}
        <ThemedText style={styles.label}>{t('Phone')}</ThemedText>
        <ThemedView style={[styles.fullWidthControl, errors.phone && { borderColor: 'yellow' }]}>
          <ThemedTextInput
            style={[styles.input, { backgroundColor: 'transparent', color: '#fff' }]}
            placeholder={t('Enter your phone number')}
            placeholderTextColor="#fff"
            keyboardType="phone-pad"
            value={phone}
            onChangeText={setPhone}
          />
        </ThemedView>
        {errors.phone && <ThemedText style={styles.errorText}>{errors.phone}</ThemedText>}

        {/* Submit Button */}
        <TouchableOpacity
          style={[
            styles.fullWidthControl,
            {
              backgroundColor: 'red',
              alignItems: 'center',
              paddingVertical: 12,
              marginTop: 12,
            },
          ]}
          onPress={handleSubmit}
        >
          <ThemedText style={{ color: '#fff', fontWeight: '600' }}>
            {isSubmitting ? t('Sending...') : t('Request Quote')}
          </ThemedText>
        </TouchableOpacity>
      </ThemedView>
    </ThemedView>
  );
};

