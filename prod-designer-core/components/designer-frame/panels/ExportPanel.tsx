import React from 'react';
import { Platform, TouchableOpacity } from 'react-native';
import { ThemedView } from '@/components/themed/ThemedView';
import { ThemedText } from '@/components/themed/ThemedText';
import { useSceneContext } from '@/redux/context';
import { exportPlanView } from '@/components/3d-measurement/new_planview';
import * as THREE from 'three';
import { styles } from './styles';

interface ExportPanelProps {
  isMobile: boolean;
  isTablet: boolean;
}

export const ExportPanel: React.FC<ExportPanelProps> = ({ isMobile, isTablet }) => {
  const { sceneSettings, setSceneSettings } = useSceneContext();

  const handle3DExport = async () => {
    if (sceneSettings.isPlanView) {
      alert('Please switch to 3D View to export the 3D model');
      return;
    }

    if (Platform.OS === 'web') {
      try {
        const canvasElement = document.querySelector('canvas');
        if (!canvasElement) throw new Error('Canvas not found.');

        const tempCanvas = document.createElement('canvas');
        const exportWidth = canvasElement.width / 2;
        const exportHeight = canvasElement.height / 2;

        tempCanvas.width = exportWidth;
        tempCanvas.height = exportHeight;

        const tempCtx = tempCanvas.getContext('2d');
        if (!tempCtx) throw new Error('Could not create export context.');

        tempCtx.drawImage(
          canvasElement,
          0,
          0,
          canvasElement.width,
          canvasElement.height,
          0,
          0,
          exportWidth,
          exportHeight
        );
        const dataURL = tempCanvas.toDataURL('image/png');

        const a = document.createElement('a');
        a.href = dataURL;
        a.download = '3d-view.png';
        a.click();
      } catch (error: any) {
        alert(`Export Error: ${error.message}`);
      }
    } else {
      alert('Export Error: Export is only supported on web platforms.');
    }
  };

  const handlePlanExport = async () => {
    if (sceneSettings.isPlanView) {
      alert('Please switch to 3D View to export the plan');
      return;
    }

    if (Platform.OS !== 'web') {
      alert('Export Error: Export is only supported on web platforms.');
      return;
    }

    const dimensions = {
      length: sceneSettings.length ?? 6000,
      span: sceneSettings.span ?? 3000,
      height: sceneSettings.height ?? 2400,
      pitch: sceneSettings.pitch ?? 11,
      overhang: sceneSettings.overhang ?? 0,
      roofType: sceneSettings.roofType,
    };

    setSceneSettings((prev) => ({ ...prev, exportMode: true }));
    await waitForRender();

    try {
      const gl = (window as any).__designer3d_gl;
      const scene = (window as any).__designer3d_scene;
      const camera = (window as any).__designer3d_camera;

      if (!gl || !scene || !camera) throw new Error('Missing WebGL context');

      const dataURL = await processPlan(gl, scene, camera, dimensions, sceneSettings);
      const a = document.createElement('a');
      a.href = dataURL;
      a.download = 'plan-view.png';
      a.click();
    } catch (error: any) {
      alert(`Export Error: ${error.message}`);
    } finally {
      setSceneSettings((prev) => ({ ...prev, exportMode: false }));
      await waitForRender();
    }
  };

  return (
    <ThemedView style={styles.container}>
      <TouchableOpacity
        style={[styles.button, isMobile && styles.buttonSmall]}
        onPress={handle3DExport}
      >
        <ThemedText style={[styles.buttonText, isMobile && styles.buttonTextSmall]}>
          Export 3D View
        </ThemedText>
      </TouchableOpacity>

      <TouchableOpacity
        style={[styles.button, isMobile && styles.buttonSmall]}
        onPress={handlePlanExport}
      >
        <ThemedText style={[styles.buttonText, isMobile && styles.buttonTextSmall]}>
          Export Plan View
        </ThemedText>
      </TouchableOpacity>
    </ThemedView>
  );
};

// Inline waitForRender
const waitForRender = () => new Promise<void>((resolve) => setTimeout(resolve, 100));

// Inline processPlan
const processPlan = async (
  gl: THREE.WebGLRenderer,
  scene: THREE.Scene,
  camera: THREE.OrthographicCamera,
  dimensions: {
    length: number;
    span: number;
    height: number;
    pitch: number;
    overhang: number;
    roofType?: string;
  },
  sceneSettings: any
): Promise<string> => {
  const hiddenObjects: THREE.Object3D[] = [];

  scene.traverse((obj) => {
    const name = obj.name?.toLowerCase();
    const type = obj.type?.toLowerCase();
    if (
      obj.visible &&
      (name?.includes('measure') ||
        name?.includes('dimension') ||
        name?.includes('ruler') ||
        name === 'grass' ||
        name === 'skybox' ||
        type?.includes('measure') ||
        type?.includes('dimension') ||
        type?.includes('ruler'))
    ) {
      obj.visible = false;
      hiddenObjects.push(obj);
    }
  });

  const dataURL = await exportPlanView({
    gl,
    scene,
    camera,
    dimensions,
    sceneSettings,
    imageWidth: 600,
    imageHeight: 600,
  });

  hiddenObjects.forEach((obj) => (obj.visible = true));
  gl.render(scene, camera);

  return dataURL;
};
