import React, { useState } from 'react';
import { StyleSheet, TouchableOpacity } from 'react-native';
import { ThemedText } from '@/components/themed/ThemedText';
import { useSceneContext } from '@/redux/context';
import { useTranslation } from 'react-i18next';
import { styles } from './styles';

/**
 * HideDimensionsUtility Component
 * Provides functionality to toggle the visibility of dimensions in the 3D view
 */

interface DimensionPanelProps {
  isMobile: boolean;
  isTablet: boolean;
}

export const DimensionPanel: React.FC<DimensionPanelProps> = ({ isMobile, isTablet }) => {
  const { sceneSettings, setSceneSettings } = useSceneContext();
  const [hideDimensions, setHideDimensions] = useState(sceneSettings.showMeasurements === false);

  const { t } = useTranslation();

  // Handler for Hide Dimension button
  const handleToggleDimensions = () => {
    console.log('Hide Dimension button clicked');

    const newValue = !hideDimensions;
    setHideDimensions(newValue);

    // Update the scene settings to control measurement visibility
    // Set to explicit true/false values to ensure proper toggling
    setSceneSettings((prev) => ({
      ...prev,
      showMeasurements: !newValue,
    }));
  };

  return (
    <TouchableOpacity
      style={[styles.button, isMobile && styles.buttonSmall]}
      onPress={handleToggleDimensions}
    >
      <ThemedText style={[styles.buttonText, isMobile && styles.buttonTextSmall]}>
        {hideDimensions ? t('Show Diemnsions') : t('Hide Diemnsions')}
      </ThemedText>
    </TouchableOpacity>
  );
};
