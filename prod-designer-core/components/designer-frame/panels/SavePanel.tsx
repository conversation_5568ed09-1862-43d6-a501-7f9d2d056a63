import React, { useState } from 'react';
import { View, TouchableOpacity, Modal, TextInput } from 'react-native';
import { ThemedText } from '@/components/themed/ThemedText';
import { ThemedView } from '@/components/themed/ThemedView';
import { useSceneContext } from '@/redux/context';
import { saveModelWithTimestamp, CarportModelData } from '../en-cry-decry';
import { useTranslation } from 'react-i18next';
import { styles } from './styles';

interface SavePanelProps {
  isMobile: boolean;
  isTablet: boolean;
}

export const SavePanel: React.FC<SavePanelProps> = ({ isMobile, isTablet }) => {
  const { sceneSettings, dimensions } = useSceneContext();
  const [showSaveModal, setShowSaveModal] = useState(false);
  const [modelName, setModelName] = useState('');
  const [errors, setErrors] = useState<{ modelName?: string }>({});
  const { t } = useTranslation();

  const handleSaveModel = () => {
    const trimmedName = modelName.trim();

    if (!trimmedName) {
      setErrors({ modelName: t('Model name is required') });
      return;
    }

    const timestamp = new Date().toISOString().replace(/[-:.]/g, '').slice(0, 13);
    const fullName = `${timestamp}_${trimmedName}`;

    const modelData: CarportModelData = {
      name: fullName,
      timestamp: new Date().toISOString(),
      dimensions: { ...dimensions },
      sceneSettings: { ...sceneSettings },
      displayInfo: {
        roofType: dimensions.roofType,
        length: dimensions.length,
        span: dimensions.span,
        hasLeanTo: sceneSettings.leftLeanTo || sceneSettings.rightLeanTo || false,
      },
    };

    try {
      saveModelWithTimestamp(modelData, timestamp);
      alert(`${t('Model saved as')} ${fullName}`);
      setModelName('');
      setErrors({});
      setShowSaveModal(false);
    } catch (err) {
      console.error('Save error:', err);
      alert(t('Failed to save model.'));
    }
  };

  return (
    <>
      <TouchableOpacity
        style={[styles.button, isMobile && styles.buttonSmall]}
        onPress={() => setShowSaveModal(true)}
      >
        <ThemedText style={[styles.buttonText, isMobile && styles.buttonTextSmall]}>
          {t('Save')}
        </ThemedText>
      </TouchableOpacity>

      <Modal visible={showSaveModal} transparent animationType="fade">
        <View style={styles.modalOverlay}>
          <ThemedView style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <ThemedText style={styles.modalTitle}>{t('Save Model')}</ThemedText>
              <TouchableOpacity onPress={() => setShowSaveModal(false)}>
                <ThemedText style={styles.closeButton}>✕</ThemedText>
              </TouchableOpacity>
            </View>

            <View style={{ backgroundColor: '#fff', borderRadius: 4 }}>
              <ThemedText style={styles.label}>{t('Model Name')}</ThemedText>
              <ThemedView
                style={{
                  height: 60,
                  backgroundColor: 'red',
                  justifyContent: 'center',
                  paddingHorizontal: 12,
                  borderWidth: 1,
                  borderColor: errors.modelName ? 'yellow' : 'transparent',
                  margin: 12,
                  borderRadius: 4,
                }}
              >
                <TextInput
                  placeholder={t('Enter model name (optional)')}
                  placeholderTextColor="#fff"
                  value={modelName}
                  onChangeText={(text) => {
                    setModelName(text);
                    if (errors.modelName && text.trim()) {
                      setErrors({});
                    }
                  }}
                  style={{
                    flex: 1,
                    color: '#fff',
                    backgroundColor: 'transparent',
                    fontSize: 16,
                  }}
                />
              </ThemedView>

              {errors.modelName && (
                <ThemedText style={styles.errorText}>{errors.modelName}</ThemedText>
              )}
              <ThemedText style={styles.note}>
                {t(
                  'Note: Models are saved to local storage only. Clearing your browser cache will remove all saved models.'
                )}
              </ThemedText>
            </View>

            <View style={[styles.modelItem, { marginTop: 24, backgroundColor: 'red' }]}>
              <TouchableOpacity
                style={[styles.button, styles.cancel]}
                onPress={() => setShowSaveModal(false)}
              >
                <ThemedText style={styles.buttonText}>{t('Cancel')}</ThemedText>
              </TouchableOpacity>
              <TouchableOpacity style={styles.button} onPress={handleSaveModel}>
                <ThemedText style={styles.buttonText}>{t('Save')}</ThemedText>
              </TouchableOpacity>
            </View>
          </ThemedView>
        </View>
      </Modal>
    </>
  );
};
