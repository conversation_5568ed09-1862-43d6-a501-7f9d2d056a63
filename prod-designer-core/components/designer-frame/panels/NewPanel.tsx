import React, { useState } from 'react';
import { View, TouchableOpacity, Modal, Image, ScrollView } from 'react-native';
import { ThemedText } from '@/components/themed/ThemedText';
import { ThemedView } from '@/components/themed/ThemedView';
import { useSceneContext, setSizeValue, setSceneSetting } from '@/redux/context';
import { useTranslation } from 'react-i18next';
import { TemplateOption, templateOptions } from '@/constants/carport/templates';
import { styles } from './styles';

const TemplateThumbnail = ({
  template,
  onSelect,
  isMobile,
}: {
  template: TemplateOption;
  onSelect: () => void;
  isMobile: boolean;
}) => {
  const imageSource =
    typeof template.imageSrc === 'string' ? { uri: template.imageSrc } : template.imageSrc;

  const { t } = useTranslation();

  return (
    <TouchableOpacity style={[styles.thumbnailContainer, { flex: 1 }]} onPress={onSelect}>
      <Image source={imageSource} style={styles.thumbnailImage} />
      <View style={styles.thumbnailInfo}>
        <ThemedText style={[styles.thumbnailTitle, isMobile && { fontSize: 14 }]}>
          {t(template.name)}
        </ThemedText>
        <ThemedText style={styles.thumbnailDescription}>{t(template.description)}</ThemedText>
      </View>
    </TouchableOpacity>
  );
};

interface NewPanelProps {
  isMobile: boolean;
  isTablet: boolean;
}

export const NewPanel: React.FC<NewPanelProps> = ({ isMobile, isTablet }) => {
  const { dispatch, setSceneSettings } = useSceneContext();
  const { t } = useTranslation();
  const [showTemplateModal, setShowTemplateModal] = useState(false);

  const getModalWidth = () => {
    if (isMobile) return '95%';
    if (isTablet) return '90%';
    return 800;
  };

  const getThumbnailWidth = () => {
    if (isMobile) return '100%';
    if (isTablet) return '48%';
    return '31.5%';
  };

  const handleTemplateSelect = (template: TemplateOption) => {
    const { dimensions, sceneSettings: settings } = template.settings;
    const isGableType = ['Gable', 'Skillion Attached'].includes(dimensions.roofType || '');

    const updatedDimensions = isGableType ? { ...dimensions, overhang: 0 } : dimensions;

    dispatch(setSizeValue(updatedDimensions));

    const completeSceneSettings = {
      viewMode: 'normal' as 'normal' | 'transparent' | 'frame',
      showSlab: true,
      leftLeanTo: false,
      leftLeanToSpan: 0,
      leftLeanToDropHeight: 0,
      rightLeanTo: false,
      rightLeanToSpan: 0,
      rightLeanToDropHeight: 0,
      rightLeanToAttached: false,
      roofColor: 'NightSky',
      wallColor: 'Classic Cream',
      trimColor: 'Surfmist',
      ...settings,
    };

    setSceneSettings((prev) => ({ ...prev, ...completeSceneSettings }));

    setTimeout(() => {
      Object.entries(completeSceneSettings).forEach(([key, value]) => {
        dispatch(setSceneSetting(key as any, value));
      });
    }, 50);

    setShowTemplateModal(false);
  };

  return (
    <>
      <TouchableOpacity
        style={[styles.button, isMobile && styles.buttonSmall]}
        onPress={() => setShowTemplateModal(true)}
      >
        <ThemedText style={[styles.buttonText, isMobile && styles.buttonTextSmall]}>
          {t('New')}
        </ThemedText>
      </TouchableOpacity>

      <Modal
        visible={showTemplateModal}
        transparent
        animationType="fade"
        onRequestClose={() => setShowTemplateModal(false)}
      >
        <View style={styles.modalOverlay}>
          <ThemedView style={[styles.modalContent, { width: getModalWidth() }]}>
            <View style={styles.modalHeader}>
              <ThemedText style={styles.modalTitle}>{t('Select a Template')}</ThemedText>
              <TouchableOpacity onPress={() => setShowTemplateModal(false)}>
                <ThemedText style={styles.closeButton}>✕</ThemedText>
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.templateList}>
              <View style={styles.thumbnailGrid}>
                {templateOptions.map((template) => (
                  <View
                    key={template.id}
                    style={[styles.thumbnailContainerWrapper, { width: getThumbnailWidth() }]}
                  >
                    <TemplateThumbnail
                      template={template}
                      isMobile={isMobile}
                      onSelect={() => handleTemplateSelect(template)}
                    />
                  </View>
                ))}
              </View>
            </ScrollView>
          </ThemedView>
        </View>
      </Modal>
    </>
  );
};
