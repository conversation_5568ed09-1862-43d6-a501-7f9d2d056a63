import React, { useState, useEffect } from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { useSceneContext } from '@/redux/context';
import { ThemedText } from '@/components/themed/ThemedText';
import { ThemedView } from '@/components/themed/ThemedView';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { styles } from './styles';

interface ViewsPanelProps {
  isMobile: boolean;
}

export const ViewsPanel: React.FC<ViewsPanelProps> = ({ isMobile }) => {
  const { sceneSettings, setSceneSettings } = useSceneContext();
  const [isOpen, setIsOpen] = useState(false);
  const { t } = useTranslation();

  const getSelectedOptionFromViewMode = (viewMode: string): number => {
    switch (viewMode) {
      case 'normal':
        return 0;
      case 'transparent':
        return 1;
      case 'frame':
        return 2;
      default:
        return 0;
    }
  };

  const [selectedOption, setSelectedOption] = useState(
    getSelectedOptionFromViewMode(sceneSettings.viewMode)
  );

  useEffect(() => {
    setSelectedOption(getSelectedOptionFromViewMode(sceneSettings.viewMode));
  }, [sceneSettings.viewMode]);

  const toggleDropdown = () => setIsOpen(!isOpen);

  const selectOption = (optionIndex: number) => {
    setSelectedOption(optionIndex);
    const mode = ['normal', 'transparent', 'frame'][optionIndex];
    setSceneSettings((prev) => ({ ...prev, viewMode: mode as any }));
  };

  const options = ['Normal', 'Transparent', 'Frame only'];

  return (
    <ThemedView style={styles.rootContainer}>
      <TouchableOpacity
        style={[styles.button, isMobile && styles.buttonSmall]}
        onPress={toggleDropdown}
      >
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <ThemedText style={[styles.buttonText, isMobile && styles.buttonTextSmall]}>
            {t('Views')}
          </ThemedText>
          <Ionicons
            name={isOpen ? 'chevron-up' : 'chevron-down'}
            size={16}
            color="white"
            style={{ marginLeft: 4 }}
          />
        </View>
      </TouchableOpacity>

      {isOpen && (
        <ThemedView style={styles.dropdownContent}>
          {options.map((label, index) => (
            <TouchableOpacity
              key={label}
              style={styles.optionRow}
              onPress={() => selectOption(index)}
            >
              <View style={styles.circle}>
                {selectedOption === index && <Ionicons name="checkmark" size={12} color="#fff" />}
              </View>
              <ThemedText style={styles.optionLabel}>{t(label)}</ThemedText>
            </TouchableOpacity>
          ))}
        </ThemedView>
      )}
    </ThemedView>
  );
};
