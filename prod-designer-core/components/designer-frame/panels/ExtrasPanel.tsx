import React from 'react';
import { Picker } from '@react-native-picker/picker';
import { useTranslation } from 'react-i18next';

import { ThemedView } from '@/components/themed/ThemedView';
import { ThemedText } from '@/components/themed/ThemedText';
import { ThemedPicker } from '@/components/themed/ThemedPicker';
import { useSceneContext } from '@/redux/context';
import { styles } from './styles';

export const ExtrasPanel: React.FC = () => {
  const { t } = useTranslation();
  const { sceneSettings, setSceneSettings } = useSceneContext();

  return (
    <ThemedView style={styles.expandedContent}>
      <ThemedView style={styles.expandedHeader}>
        <ThemedText type="defaultSemiBold" style={styles.expandedTitle}>
          {t('Extras')}
        </ThemedText>
      </ThemedView>

      <ThemedView style={styles.content}>
        {/* Roof Profile */}
        <ThemedView style={styles.row}>
          <ThemedView style={[styles.pickerContainer, { backgroundColor: '#fff' }]}>
            <ThemedText style={styles.label}>{t('Roof Profile')}</ThemedText>
            <ThemedView style={styles.fullWidthControl}>
              <ThemedPicker
                selectedValue={sceneSettings.roofProfile || 'Corro'}
                onValueChange={(value) =>
                  setSceneSettings((prev) => ({ ...prev, roofProfile: value }))
                }
                style={styles.picker}
              >
                <Picker.Item key="corro" label="Corro" value="Corro" />
                <Picker.Item key="mono" label="Mono" value="Mono" />
              </ThemedPicker>
            </ThemedView>
          </ThemedView>
        </ThemedView>

        {/* Wall Profile */}
        <ThemedView style={styles.row}>
          <ThemedView style={[styles.pickerContainer, { backgroundColor: '#fff' }]}>
            <ThemedText style={styles.label}>{t('Wall Profile')}</ThemedText>
            <ThemedView style={styles.fullWidthControl}>
              <ThemedPicker
                selectedValue={sceneSettings.wallProfile || 'Corro'}
                onValueChange={(value) =>
                  setSceneSettings((prev) => ({ ...prev, wallProfile: value }))
                }
                style={styles.picker}
              >
                <Picker.Item key="corro" label="Corro" value="Corro" />
                <Picker.Item key="mono" label="Mono" value="Mono" />
              </ThemedPicker>
            </ThemedView>
          </ThemedView>
        </ThemedView>
      </ThemedView>
    </ThemedView>
  );
};
