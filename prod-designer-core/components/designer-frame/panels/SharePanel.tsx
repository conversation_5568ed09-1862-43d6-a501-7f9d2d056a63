// SharePanel.tsx
import React, { useState, useCallback } from 'react';
import { TouchableOpacity, View, Modal, TextInput, Platform, Clipboard } from 'react-native';
import { ThemedText } from '@/components/themed/ThemedText';
import { ThemedView } from '@/components/themed/ThemedView';
import { useSceneContext } from '@/redux/context';
import { encryptText } from '../en-cry-decry';
import EmailService from '../EmailService';
import { useTranslation } from 'react-i18next';
import { styles } from './styles';

interface SharePanelProps {
  isMobile: boolean;
  isTablet: boolean;
}

export const SharePanel: React.FC<SharePanelProps> = ({ isMobile, isTablet }) => {
  const { dimensions, sceneSettings } = useSceneContext();
  const { t } = useTranslation();

  const [showShareModal, setShowShareModal] = useState(false);
  const [showEmailModal, setShowEmailModal] = useState(false);
  const [shareLink, setShareLink] = useState('');
  const [linkCopied, setLinkCopied] = useState(false);
  const [emailSent, setEmailSent] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);

  const getBaseUrl = () => {
    if (Platform.OS !== 'web') return 'https://designmycarport.web.app';
    try {
      return window.location.href.split('?')[0];
    } catch {
      return 'https://designmycarport.web.app';
    }
  };

  const buildShareParams = () => ({
    dimensions: {
      length: dimensions.length || 12000,
      height: dimensions.height || 2400,
      span: dimensions.span || 6000,
      pitch: dimensions.pitch || 15,
      overhang: dimensions.overhang || 0,
      roofType: dimensions.roofType || 'Gable',
    },
    sceneSettings: {
      leftLeanTo: sceneSettings.leftLeanTo || false,
      leftLeanToSpan: sceneSettings.leftLeanToSpan || 0,
      leftLeanToDropHeight: sceneSettings.leftLeanToDropHeight || 500,
      rightLeanTo: sceneSettings.rightLeanTo || false,
      rightLeanToSpan: sceneSettings.rightLeanToSpan || 0,
      rightLeanToDropHeight: sceneSettings.rightLeanToDropHeight || 500,
      rightLeanToAttached: sceneSettings.rightLeanToAttached || false,
      isPlanView: sceneSettings.isPlanView || false,
      defaultCamera: sceneSettings.defaultCamera || 'perspective',
      viewMode: sceneSettings.viewMode || 'normal',
      showMeasurements: sceneSettings.showMeasurements ?? true,
      showSlab: sceneSettings.showSlab ?? true,
      roofColor: sceneSettings.roofColor || 'NightSky',
      wallColor: sceneSettings.wallColor || 'Classic Cream',
      trimColor: sceneSettings.trimColor || 'Surfmist',
      doorColor: sceneSettings.doorColor || 'Monument',
    },
    timestamp: new Date().toISOString(),
    version: '1.0',
  });

  const handleShare = useCallback(() => {
    setIsGenerating(true);
    setShowShareModal(true);
    setShareLink(t('Generating link...'));

    setTimeout(() => {
      try {
        const encodedParams = encryptText(JSON.stringify(buildShareParams()));
        const url = `${getBaseUrl()}?model=${encodedParams}`;
        setShareLink(url);
      } catch {
        setShareLink(t('Error generating link.'));
      } finally {
        setIsGenerating(false);
      }
    }, 100);
  }, [dimensions, sceneSettings]);

  const handleCopyLink = useCallback(() => {
    if (Platform.OS === 'web' && navigator.clipboard) {
      navigator.clipboard.writeText(shareLink).then(() => {
        setLinkCopied(true);
        setTimeout(() => setLinkCopied(false), 3000);
      });
    } else {
      Clipboard.setString(shareLink);
      setLinkCopied(true);
      setTimeout(() => setLinkCopied(false), 3000);
    }
  }, [shareLink]);

  const handleEmailShare = () => setShowEmailModal(true);
  const handleEmailSuccess = () => {
    setEmailSent(true);
    setTimeout(() => setEmailSent(false), 3000);
  };

  return (
    <>
      <TouchableOpacity
        style={[styles.button, isMobile && styles.buttonSmall]}
        onPress={handleShare}
      >
        <ThemedText style={[styles.buttonText, isMobile && styles.buttonTextSmall]}>
          {t('Share')}
        </ThemedText>
      </TouchableOpacity>

      <Modal visible={showShareModal} transparent animationType="fade">
        <View style={styles.modalOverlay}>
          <ThemedView style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <ThemedText style={styles.modalTitle}>{t('Share Design')}</ThemedText>
              <TouchableOpacity onPress={() => setShowShareModal(false)}>
                <ThemedText style={styles.closeButton}>✕</ThemedText>
              </TouchableOpacity>
            </View>

            <ThemedText style={styles.modalText}>{t('ShareNote')}</ThemedText>

            <View style={styles.linkContainer}>
              <TextInput style={styles.linkInput} value={shareLink} editable={false} multiline />
            </View>

            <TouchableOpacity style={styles.copyButton} onPress={handleCopyLink}>
              <ThemedText style={styles.copyButtonText}>
                {linkCopied ? t('Link Copied!') : t('Copy Link')}
              </ThemedText>
            </TouchableOpacity>

            <TouchableOpacity style={styles.emailButton} onPress={handleEmailShare}>
              <ThemedText style={styles.emailButtonText}>
                {emailSent ? t('Email Sent!') : t('Share via Email')}
              </ThemedText>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.cancelButton, { backgroundColor: '#ccc' }]}
              onPress={() => setShowShareModal(false)}
            >
              <ThemedText style={styles.cancelButtonText}>{t('Close')}</ThemedText>
            </TouchableOpacity>
          </ThemedView>
        </View>
      </Modal>

      <Modal visible={showEmailModal} transparent animationType="fade">
        <View style={styles.modalOverlay}>
          <ThemedView style={styles.modalContent}>
            <EmailService
              shareLink={shareLink}
              onClose={() => setShowEmailModal(false)}
              onSuccess={handleEmailSuccess}
            />
          </ThemedView>
        </View>
      </Modal>
    </>
  );
};
