import React, { useState } from 'react';
import { View, TouchableOpacity, Modal, ScrollView } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { ThemedText } from '@/components/themed/ThemedText';
import { ThemedView } from '@/components/themed/ThemedView';
import QuadrantView from '@/components/3d-measurement/new_planview';
import { styles } from './styles';
interface PlanPanelProps {
  isMobile: boolean;
  isTablet: boolean;
}

export const PlanPanel: React.FC<PlanPanelProps> = ({ isMobile, isTablet }) => {
  const [showPlanModal, setShowPlanModal] = useState(false);

  const getModalWidth = () => {
    if (isMobile) return '95%';
    if (isTablet) return '90%';
    return 800;
  };

  return (
    <>
      <TouchableOpacity
        style={[styles.button, isMobile && styles.buttonSmall]}
        onPress={() => setShowPlanModal(true)}
      >
        <ThemedText style={[styles.buttonText, isMobile && styles.buttonTextSmall]}>
          Plan
        </ThemedText>
      </TouchableOpacity>
      <Modal
        visible={showPlanModal}
        transparent
        animationType="fade"
        onRequestClose={() => setShowPlanModal(false)}
      >
        <View style={styles.modalOverlay}>
          <ThemedView style={[styles.modalContent, { width: getModalWidth() }]}>
            <View style={styles.modalHeader}>
              <ThemedText style={styles.modalTitle}>Plan View</ThemedText>
              <TouchableOpacity onPress={() => setShowPlanModal(false)}>
                <Ionicons name="close" size={24} color="#fff" style={styles.closeIcon} />
              </TouchableOpacity>
            </View>
            <ScrollView contentContainerStyle={styles.scrollContainer}>
              <QuadrantView isMobile={isMobile} />
            </ScrollView>
          </ThemedView>
        </View>
      </Modal>
    </>
  );
};
