import React, { useState, useEffect } from 'react';
import { StyleSheet, View, TouchableOpacity, Modal, ScrollView } from 'react-native';
import { ThemedText } from '@/components/themed/ThemedText';
import { ThemedView } from '@/components/themed/ThemedView';
import { useSceneContext, setSizeValue, setSceneSetting } from '@/redux/context';
import { getAllSavedModels, getModelByTimestamp } from '../en-cry-decry';
import { useTranslation } from 'react-i18next';
import { styles } from './styles';

interface LoadPanelProps {
  isMobile: boolean;
  isTablet: boolean;
}

export const LoadPanel: React.FC<LoadPanelProps> = ({ isMobile, isTablet }) => {
  const { dispatch, setSceneSettings, sceneSettings } = useSceneContext();
  const [showLoadModal, setShowLoadModal] = useState(false);
  const [savedModels, setSavedModels] = useState<{ timestamp: string; name: string }[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const modelsPerPage = 4;
  const { t } = useTranslation();

  useEffect(() => {
    if (showLoadModal) loadSavedModels();
  }, [showLoadModal]);

  const handleLoad = () => {
    setCurrentPage(1);
    setShowLoadModal(true);
  };

  const loadSavedModels = () => {
    setIsLoading(true);
    try {
      const models = getAllSavedModels().sort((a, b) => b.timestamp.localeCompare(a.timestamp));
      setSavedModels(models);
    } catch (error) {
      console.error('Error loading saved models:', error);
      alert(t('Failed to load saved models. Please try again.'));
    } finally {
      setIsLoading(false);
    }
  };

  const applyModelData = (modelData: any) => {
    try {
      const dimensionsPayload: Partial<any> = {};
      if (modelData.dimensions) {
        ['length', 'span', 'height', 'pitch', 'overhang', 'roofType'].forEach((key) => {
          if (modelData.dimensions[key] !== undefined) {
            dimensionsPayload[key] = modelData.dimensions[key];
          }
        });
        dispatch(setSizeValue(dimensionsPayload));
      }

      if (modelData.sceneSettings) {
        const leanToSettings = {
          leftLeanTo: !!modelData.sceneSettings.leftLeanTo,
          leftLeanToSpan: modelData.sceneSettings.leftLeanToSpan || 0,
          leftLeanToDropHeight: modelData.sceneSettings.leftLeanToDropHeight || 0,
          rightLeanTo: !!modelData.sceneSettings.rightLeanTo,
          rightLeanToSpan: modelData.sceneSettings.rightLeanToSpan || 0,
          rightLeanToDropHeight: modelData.sceneSettings.rightLeanToDropHeight || 0,
          rightLeanToAttached: !!modelData.sceneSettings.rightLeanToAttached,
        };

        const newSettings = { ...sceneSettings, ...modelData.sceneSettings, ...leanToSettings };
        setSceneSettings(newSettings);

        Object.entries(leanToSettings).forEach(([key, value]) => {
          dispatch(setSceneSetting(key, value));
        });
      }

      alert(t('Model loaded successfully') + `: ${modelData.name}`);
      setShowLoadModal(false);
    } catch (error) {
      console.error('Error applying model data:', error);
      alert(t('Failed to apply model data. Please try again.'));
    }
  };

  const loadModel = async (timestamp: string, modelName?: string) => {
    setIsLoading(true);
    try {
      const modelData = getModelByTimestamp(timestamp);
      if (!modelData) throw new Error('Model not found or corrupted.');
      applyModelData(modelData);
    } catch (error) {
      console.error('Error loading model:', error);
      alert(t('Failed to load model.'));
    } finally {
      setIsLoading(false);
    }
  };

  const paginatedModels = savedModels.slice(
    (currentPage - 1) * modelsPerPage,
    currentPage * modelsPerPage
  );

  return (
    <>
      <TouchableOpacity
        style={[styles.button, isMobile && styles.buttonSmall]}
        onPress={handleLoad}
      >
        <ThemedText style={[styles.buttonText, isMobile && styles.buttonTextSmall]}>
          {t('Load')}
        </ThemedText>
      </TouchableOpacity>

      <Modal
        visible={showLoadModal}
        transparent
        animationType="fade"
        onRequestClose={() => setShowLoadModal(false)}
      >
        <View style={styles.modalOverlay}>
          <ThemedView style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <ThemedText style={styles.modalTitle}>{t('Load Saved Model')}</ThemedText>
              <TouchableOpacity onPress={() => setShowLoadModal(false)}>
                <ThemedText style={styles.closeButton}>✕</ThemedText>
              </TouchableOpacity>
            </View>

            {isLoading ? (
              <View style={styles.loadingContainer}>
                <ThemedText>{t('Loading saved models...')}</ThemedText>
              </View>
            ) : savedModels.length === 0 ? (
              <View style={styles.loadingContainer}>
                <ThemedText>{t('No Saved Model found')}</ThemedText>
              </View>
            ) : (
              <>
                <ScrollView key={currentPage} style={styles.modelList}>
                  {paginatedModels.map((model) => (
                    <View key={model.timestamp} style={styles.modelItem}>
                      <View style={styles.modelInfo}>
                        <ThemedText style={styles.modelName}>{model.name}</ThemedText>
                        <ThemedText style={styles.modelDate}>{model.timestamp}</ThemedText>
                      </View>
                      <TouchableOpacity
                        style={styles.loadButton}
                        onPress={() => loadModel(model.timestamp, model.name)}
                      >
                        <ThemedText style={styles.loadButtonText}>{t('Load')}</ThemedText>
                      </TouchableOpacity>
                    </View>
                  ))}
                </ScrollView>

                {savedModels.length > modelsPerPage && (
                  <View style={styles.paginationContainer}>
                    <TouchableOpacity
                      style={[styles.paginationButton, currentPage === 1 && styles.disabledButton]}
                      disabled={currentPage === 1}
                      onPress={() => setCurrentPage((prev) => prev - 1)}
                    >
                      <ThemedText style={styles.paginationButtonText}>{t('Previous')}</ThemedText>
                    </TouchableOpacity>

                    <ThemedText style={styles.paginationText}>
                      {t('Page')} {currentPage} {t('of')}{' '}
                      {Math.ceil(savedModels.length / modelsPerPage)}
                    </ThemedText>

                    <TouchableOpacity
                      style={[
                        styles.paginationButton,
                        currentPage >= Math.ceil(savedModels.length / modelsPerPage) &&
                          styles.disabledButton,
                      ]}
                      disabled={currentPage >= Math.ceil(savedModels.length / modelsPerPage)}
                      onPress={() => setCurrentPage((prev) => prev + 1)}
                    >
                      <ThemedText style={styles.paginationButtonText}>{t('Next')}</ThemedText>
                    </TouchableOpacity>
                  </View>
                )}
              </>
            )}
          </ThemedView>
        </View>
      </Modal>
    </>
  );
};
