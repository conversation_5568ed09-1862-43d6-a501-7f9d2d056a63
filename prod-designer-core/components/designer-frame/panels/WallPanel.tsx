import React, { useState, useEffect, useRef, createContext, useCallback, useContext } from 'react';
import { useSceneContext, setSceneSetting, setSizeValue, setWallMatrix } from '@/redux/context';
import { useTranslation } from 'react-i18next';
import { ThemedText } from '@/components/themed/ThemedText';
import { ThemedView } from '@/components/themed/ThemedView';
import {
  TouchableOpacity,
  ScrollView,
} from 'react-native';

import { styles } from './styles';


export const WallPanel: React.FC<{ length: number, dimensions: any }> = ({ length, dimensions }) => {
  // Get scene context and dispatch
  const { dispatch } = useSceneContext();
    const { t } = useTranslation();
  
  // Get scene settings for lean-to status and wall matrix
  const { sceneSettings, setSceneSettings } = useSceneContext();
  
  // Wall matrix ref to store the current wall configuration
  const wallMatrixRef = useRef<WallMatrixType | null>(null);
  
  // Track when wall matrix changes to sync with scene context
  const [wallMatrixChanged, setWallMatrixChanged] = useState(false);
  
  // UI state for bay walls (wall toggles in the UI)
  const [bayWalls, setBayWalls] = useState<Record<string, boolean>>({});
  
  // Calculate carport sections based on length
  const numberOfSections = Math.ceil(length / 4000);
  // Check both dimensions and sceneSettings for lean-to status
  const hasLeftLeanTo = !!dimensions.leftLeanTo || !!sceneSettings.leftLeanTo;
  const hasRightLeanTo = !!dimensions.rightLeanTo || !!sceneSettings.rightLeanTo;

  // Effect to sync wallMatrix to scene context whenever it changes
  // This avoids updating state during render
  useEffect(() => {
    if (wallMatrixChanged && wallMatrixRef.current && dispatch) {
      
      // Dispatch the updated wall matrix to scene context
      dispatch(setSceneSetting('wallMatrix', wallMatrixRef.current));
      
      setWallMatrixChanged(false); // Reset the flag
    }
  }, [wallMatrixChanged, dispatch]);
  
  // Wall matrix state to track wall status
  // Main carport walls
  // - mainLeftWall[]: array of boolean values for main left wall segments
  // - mainRightWall[]: array of boolean values for main right wall segments
  // - mainDividerWall[]: array of boolean values for main divider wall segments
  // - mainFrontWall: boolean for the front wall
  // - mainBackWall: boolean for the back wall
  // Lean-to walls - completely independent from main structure
  // - leftLeanToWall[]: array of boolean values for left lean-to side wall segments
  // - rightLeanToWall[]: array of boolean values for right lean-to side wall segments
  // - leftLeanToDividerWall[]: array of boolean values for left lean-to divider walls
  // - rightLeanToDividerWall[]: array of boolean values for right lean-to divider walls
  // - leftLeanToFrontWall: boolean for the left lean-to front wall
  // - leftLeanToBackWall: boolean for the left lean-to back wall
  // - rightLeanToFrontWall: boolean for the right lean-to front wall
  // - rightLeanToBackWall: boolean for the right lean-to back wall
  type WallMatrixType = {
    // Main carport walls
    mainLeftWall: boolean[];
    mainRightWall: boolean[];
    mainDividerWall: boolean[];
    mainFrontWall: boolean;
    mainBackWall: boolean;
    // Lean-to walls
    leftLeanToWall: boolean[];
    rightLeanToWall: boolean[];
    leftLeanToDividerWall: boolean[];
    rightLeanToDividerWall: boolean[];
    // Front/back walls for lean-tos
    leftLeanToFrontWall: boolean | null;
    leftLeanToBackWall: boolean | null;
    rightLeanToFrontWall: boolean | null;
    rightLeanToBackWall: boolean | null;
  };

  const [wallMatrix, setWallMatrix] = useState<WallMatrixType>({
    // Main carport walls
    mainLeftWall: [] as boolean[],
    mainRightWall: [] as boolean[],
    mainDividerWall: [] as boolean[],
    mainFrontWall: false,
    mainBackWall: false,
    // Lean-to walls
    leftLeanToWall: [] as boolean[],
    rightLeanToWall: [] as boolean[],
    leftLeanToDividerWall: [] as boolean[],
    rightLeanToDividerWall: [] as boolean[],
    // Front/back walls for lean-tos
    leftLeanToFrontWall: null,
    leftLeanToBackWall: null,
    rightLeanToFrontWall: null,
    rightLeanToBackWall: null
  });
  
  // These variables are now defined at the top of the component

  // Effect to sync with scene context's wall matrix when it changes (like when switching roof types)
  useEffect(() => {
    if (sceneSettings.wallMatrix) {
      // Update the local wallMatrix state for UI highlighting
      setWallMatrix(sceneSettings.wallMatrix);
      
      // Update wallMatrixRef for future toggles
      wallMatrixRef.current = sceneSettings.wallMatrix;
      
      // Update the UI state (bayWalls) for highlighting based on the context wall matrix
      const updatedBayWalls: Record<string, boolean> = {};
      
      // Main walls
      for (let i = 0; i < numberOfSections; i++) {
        if (sceneSettings.wallMatrix.mainLeftWall && i < sceneSettings.wallMatrix.mainLeftWall.length) {
          updatedBayWalls[`leftWall${i+1}`] = sceneSettings.wallMatrix.mainLeftWall[i];
        }
        if (sceneSettings.wallMatrix.mainRightWall && i < sceneSettings.wallMatrix.mainRightWall.length) {
          updatedBayWalls[`rightWall${i+1}`] = sceneSettings.wallMatrix.mainRightWall[i];
        }
      }
      
      // Divider walls
      for (let i = 0; i < numberOfSections - 1; i++) {
        if (sceneSettings.wallMatrix.mainDividerWall && i < sceneSettings.wallMatrix.mainDividerWall.length) {
          updatedBayWalls[`dividerWall${i+1}`] = sceneSettings.wallMatrix.mainDividerWall[i];
        }
      }
      
      // Front/back walls
      updatedBayWalls['frontWall'] = sceneSettings.wallMatrix.mainFrontWall || false;
      updatedBayWalls['backWall'] = sceneSettings.wallMatrix.mainBackWall || false;
      
      // Add lean-to wall UI states if they exist
      if (dimensions.leftLeanTo && sceneSettings.wallMatrix.leftLeanToWall) {
        for (let i = 0; i < sceneSettings.wallMatrix.leftLeanToWall.length; i++) {
          updatedBayWalls[`leftLeanToWall${i+1}`] = sceneSettings.wallMatrix.leftLeanToWall[i];
        }
        for (let i = 0; i < (sceneSettings.wallMatrix.leftLeanToDividerWall?.length || 0); i++) {
          updatedBayWalls[`leftLeanToDividerWall${i+1}`] = sceneSettings.wallMatrix.leftLeanToDividerWall[i];
        }
        if (sceneSettings.wallMatrix.leftLeanToFrontWall !== null) {
          updatedBayWalls['leftLeanToFrontWall'] = sceneSettings.wallMatrix.leftLeanToFrontWall || false;
        }
        if (sceneSettings.wallMatrix.leftLeanToBackWall !== null) {
          updatedBayWalls['leftLeanToBackWall'] = sceneSettings.wallMatrix.leftLeanToBackWall || false;
        }
      }
      
      if (dimensions.rightLeanTo && sceneSettings.wallMatrix.rightLeanToWall) {
        for (let i = 0; i < sceneSettings.wallMatrix.rightLeanToWall.length; i++) {
          updatedBayWalls[`rightLeanToWall${i+1}`] = sceneSettings.wallMatrix.rightLeanToWall[i];
        }
        for (let i = 0; i < (sceneSettings.wallMatrix.rightLeanToDividerWall?.length || 0); i++) {
          updatedBayWalls[`rightLeanToDividerWall${i+1}`] = sceneSettings.wallMatrix.rightLeanToDividerWall[i];
        }
        if (sceneSettings.wallMatrix.rightLeanToFrontWall !== null) {
          updatedBayWalls['rightLeanToFrontWall'] = sceneSettings.wallMatrix.rightLeanToFrontWall || false;
        }
        if (sceneSettings.wallMatrix.rightLeanToBackWall !== null) {
          updatedBayWalls['rightLeanToBackWall'] = sceneSettings.wallMatrix.rightLeanToBackWall || false;
        }
      }
      
      // Update the UI state
      setBayWalls(updatedBayWalls);
    }
  }, [sceneSettings.wallMatrix, numberOfSections, dimensions.leftLeanTo, dimensions.rightLeanTo]);
  
  // Initialize wall states based on carport length and lean-to configuration
  useEffect(() => {
    // Check if we already have a wall matrix in the scene context or ref
    const existingMatrix = sceneSettings.wallMatrix || wallMatrixRef.current;
    
    // If we have an existing matrix, adjust it for length changes
    // Otherwise create a fresh one
    const prevMatrix = existingMatrix || {} as WallMatrixType;
    
    // Initialize wall matrix, preserving main carport wall settings if they exist
    const initialMatrix: WallMatrixType = {
      // Preserve main carport walls if they exist and length hasn't changed
      mainLeftWall: Array.isArray(prevMatrix.mainLeftWall) ? 
        (prevMatrix.mainLeftWall.length === numberOfSections ?
          prevMatrix.mainLeftWall : 
          // If length changed, preserve existing walls as much as possible
          [...prevMatrix.mainLeftWall.slice(0, Math.min(prevMatrix.mainLeftWall.length, numberOfSections)), 
           ...Array(Math.max(0, numberOfSections - prevMatrix.mainLeftWall.length)).fill(false)]) :
        Array(numberOfSections).fill(false),
      
      mainRightWall: Array.isArray(prevMatrix.mainRightWall) ?
        (prevMatrix.mainRightWall.length === numberOfSections ?
          prevMatrix.mainRightWall :
          [...prevMatrix.mainRightWall.slice(0, Math.min(prevMatrix.mainRightWall.length, numberOfSections)),
           ...Array(Math.max(0, numberOfSections - prevMatrix.mainRightWall.length)).fill(false)]) :
        Array(numberOfSections).fill(false),
      
      mainDividerWall: Array.isArray(prevMatrix.mainDividerWall) ?
        (prevMatrix.mainDividerWall.length === numberOfSections - 1 ?
          prevMatrix.mainDividerWall :
          [...prevMatrix.mainDividerWall.slice(0, Math.min(prevMatrix.mainDividerWall.length, numberOfSections - 1)),
           ...Array(Math.max(0, numberOfSections - 1 - prevMatrix.mainDividerWall.length)).fill(false)]) :
        Array(numberOfSections - 1).fill(false),
      
      mainFrontWall: typeof prevMatrix.mainFrontWall === 'boolean' ? prevMatrix.mainFrontWall : false,
      mainBackWall: typeof prevMatrix.mainBackWall === 'boolean' ? prevMatrix.mainBackWall : false,
      
      // Lean-to walls - reinitialize when lean-to settings change
      leftLeanToWall: hasLeftLeanTo ? 
        (Array.isArray(prevMatrix.leftLeanToWall) && prevMatrix.leftLeanToWall.length > 0 ?
          [...prevMatrix.leftLeanToWall.slice(0, Math.min(prevMatrix.leftLeanToWall.length, numberOfSections)),
           ...Array(Math.max(0, numberOfSections - prevMatrix.leftLeanToWall.length)).fill(false)] :
          Array(numberOfSections).fill(false)) : [],
      
      rightLeanToWall: hasRightLeanTo ? 
        (Array.isArray(prevMatrix.rightLeanToWall) && prevMatrix.rightLeanToWall.length > 0 ?
          [...prevMatrix.rightLeanToWall.slice(0, Math.min(prevMatrix.rightLeanToWall.length, numberOfSections)),
           ...Array(Math.max(0, numberOfSections - prevMatrix.rightLeanToWall.length)).fill(false)] :
          Array(numberOfSections).fill(false)) : [],
      
      // Divider walls for lean-to sections
      leftLeanToDividerWall: hasLeftLeanTo ? 
        (Array.isArray(prevMatrix.leftLeanToDividerWall) && prevMatrix.leftLeanToDividerWall.length > 0 ?
          [...prevMatrix.leftLeanToDividerWall.slice(0, Math.min(prevMatrix.leftLeanToDividerWall.length, numberOfSections - 1)),
           ...Array(Math.max(0, numberOfSections - 1 - prevMatrix.leftLeanToDividerWall.length)).fill(false)] :
          Array(numberOfSections - 1).fill(false)) : [],
      
      rightLeanToDividerWall: hasRightLeanTo ? 
        (Array.isArray(prevMatrix.rightLeanToDividerWall) && prevMatrix.rightLeanToDividerWall.length > 0 ?
          [...prevMatrix.rightLeanToDividerWall.slice(0, Math.min(prevMatrix.rightLeanToDividerWall.length, numberOfSections - 1)),
           ...Array(Math.max(0, numberOfSections - 1 - prevMatrix.rightLeanToDividerWall.length)).fill(false)] :
          Array(numberOfSections - 1).fill(false)) : [],
      
      // Front/back walls for lean-to sections
      leftLeanToFrontWall: hasLeftLeanTo ? 
        (typeof prevMatrix.leftLeanToFrontWall === 'boolean' ? prevMatrix.leftLeanToFrontWall : false) : null,
      leftLeanToBackWall: hasLeftLeanTo ? 
        (typeof prevMatrix.leftLeanToBackWall === 'boolean' ? prevMatrix.leftLeanToBackWall : false) : null,
      rightLeanToFrontWall: hasRightLeanTo ? 
        (typeof prevMatrix.rightLeanToFrontWall === 'boolean' ? prevMatrix.rightLeanToFrontWall : false) : null,
      rightLeanToBackWall: hasRightLeanTo ? 
        (typeof prevMatrix.rightLeanToBackWall === 'boolean' ? prevMatrix.rightLeanToBackWall : false) : null
    };
    
    
    // Update both the local state and the ref
    setWallMatrix(initialMatrix);
    wallMatrixRef.current = initialMatrix;
    setWallMatrixChanged(true);
    
    // Initialize UI state for bay walls
    const initialBayWalls: Record<string, boolean> = {};
    
    // Main walls
    for (let i = 0; i < numberOfSections; i++) {
      initialBayWalls[`leftWall${i+1}`] = initialMatrix.mainLeftWall[i];
      initialBayWalls[`rightWall${i+1}`] = initialMatrix.mainRightWall[i];
    }
    
    // Divider walls
    for (let i = 0; i < numberOfSections - 1; i++) {
      initialBayWalls[`dividerWall${i+1}`] = initialMatrix.mainDividerWall[i];
    }
    
    // Front/back walls
    initialBayWalls['frontWall'] = initialMatrix.mainFrontWall;
    initialBayWalls['backWall'] = initialMatrix.mainBackWall;
    
    // Update UI state
    setBayWalls(initialBayWalls);
  }, [dimensions, length, hasLeftLeanTo, hasRightLeanTo]);
  
  // Toggle wall state and update both the matrix and UI object
  const toggleWall = (wallId: string) => {
    // Parse the wall type and index
    const type = wallId.replace(/\d+$/, '');
    const index = parseInt(wallId.match(/\d+$/)?.[0] || '1') - 1;
    
    // Update the UI state object
    setBayWalls(prev => {
      const newState = { ...prev, [wallId]: !prev[wallId] };
      return newState;
    });
    
    // Update the wall matrix
    if (wallMatrixRef.current) {
      // Create a deep copy of the previous matrix
      const newMatrix: WallMatrixType = { ...wallMatrixRef.current };
      
      // Update the appropriate wall in the matrix
      if (type === 'frontWall' || type === 'frontBackWall') {
        if (index === 0) {
          // Back wall
          newMatrix.mainBackWall = !newMatrix.mainBackWall;
        } else if (index === numberOfSections) {
          // Front wall
          newMatrix.mainFrontWall = !newMatrix.mainFrontWall;
        } else {
          // This is a divider wall
          const dividerWalls = [...newMatrix.mainDividerWall];
          dividerWalls[index - 1] = !dividerWalls[index - 1];
          newMatrix.mainDividerWall = dividerWalls;
        }
      } else if (type === 'leftWall') {
        // Left wall section
        const leftWalls = [...newMatrix.mainLeftWall];
        leftWalls[index] = !leftWalls[index];
        newMatrix.mainLeftWall = leftWalls;
      } else if (type === 'rightWall') {
        // Right wall section
        const rightWalls = [...newMatrix.mainRightWall];
        rightWalls[index] = !rightWalls[index];
        newMatrix.mainRightWall = rightWalls;
      } else if (type === 'dividerWall') {
        // Interior divider wall
        const dividerWalls = [...newMatrix.mainDividerWall];
        dividerWalls[index] = !dividerWalls[index];
        newMatrix.mainDividerWall = dividerWalls;
      } else if (type === 'leftLeanToWall') {
        // Left lean-to wall section
        const leftLeanToWalls = [...newMatrix.leftLeanToWall];
        leftLeanToWalls[index] = !leftLeanToWalls[index];
        newMatrix.leftLeanToWall = leftLeanToWalls;
      } else if (type === 'rightLeanToWall') {
        // Right lean-to wall section
        const rightLeanToWalls = [...newMatrix.rightLeanToWall];
        rightLeanToWalls[index] = !rightLeanToWalls[index];
        newMatrix.rightLeanToWall = rightLeanToWalls;
      } else if (type === 'leftLeanToDividerWall') {
        // Left lean-to divider wall
        const leftLeanToDividerWalls = [...newMatrix.leftLeanToDividerWall];
        leftLeanToDividerWalls[index] = !leftLeanToDividerWalls[index];
        newMatrix.leftLeanToDividerWall = leftLeanToDividerWalls;
      } else if (type === 'rightLeanToDividerWall') {
        // Right lean-to divider wall
        const rightLeanToDividerWalls = [...newMatrix.rightLeanToDividerWall];
        rightLeanToDividerWalls[index] = !rightLeanToDividerWalls[index];
        newMatrix.rightLeanToDividerWall = rightLeanToDividerWalls;
      } else if (type === 'leftLeanToFrontWall') {
        // Left lean-to front wall
        newMatrix.leftLeanToFrontWall = !newMatrix.leftLeanToFrontWall;
      } else if (type === 'leftLeanToBackWall') {
        // Left lean-to back wall
        newMatrix.leftLeanToBackWall = !newMatrix.leftLeanToBackWall;
      } else if (type === 'rightLeanToFrontWall') {
        // Right lean-to front wall
        newMatrix.rightLeanToFrontWall = !newMatrix.rightLeanToFrontWall;
      } else if (type === 'rightLeanToBackWall') {
        // Right lean-to back wall
        newMatrix.rightLeanToBackWall = !newMatrix.rightLeanToBackWall;
      }
      
      // Log the updated wall matrix with info about triggering girt creation
      
      // Store the updated matrix in the ref for future toggles
      wallMatrixRef.current = newMatrix;
      
      // Also update the wallMatrix state for UI highlighting
      setWallMatrix(newMatrix);
      
      // For lean-to walls, we need special handling
      if (type.includes('leanTo')) {
        
        // Make sure the proper lean-to setting is enabled in scene settings
        // This ensures the girt.config will process the lean-to walls
        const updatedSettings = {...sceneSettings};
        
        if (type.includes('leftLeanTo')) {
          updatedSettings.leftLeanTo = true;
        } else if (type.includes('rightLeanTo')) {
          updatedSettings.rightLeanTo = true;
        }
        
        // Update scene settings with wall matrix and ensure lean-to is enabled
        updatedSettings.wallMatrix = newMatrix;
        setSceneSettings(updatedSettings);
        
        // Force a refresh after a small delay to ensure changes are applied
        setTimeout(() => {
          dispatch(setSceneSetting('forceRefresh', Date.now()));
        }, 50);
      } else {
        // Regular walls use the standard update path
        dispatch(setSceneSetting('wallMatrix', newMatrix));
      }
      
      // Mark as changed to trigger additional scene context processing
      setWallMatrixChanged(true);
    }
  };

  // Render the component
  return (
    <ThemedView style={styles.expandedContent}>
      <ThemedView style={styles.expandedHeader}>
        <ThemedText style={styles.expandedTitle}>{t('Walls')}</ThemedText>
      </ThemedView>
      
      <ScrollView 
        style={styles.contentScrollView} 
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={true}
      >
        <ThemedView style={styles.content}>
          <ThemedText style={styles.subtitle}>
            {t('Wall Configuration', { count: numberOfSections })}
          </ThemedText>

          
          {/* Carport Structure Visualization */}
          <ThemedView style={styles.carportContainer}>
            {/* Left Lean-to Section (if enabled) */}
            {hasLeftLeanTo && (
              <ThemedView style={styles.leanToSection}>
                <ThemedText style={styles.leanToLabel}>{t('LeftLeanTo')}</ThemedText>
                
                {/* Left Lean-to Back Wall - independent */}
                <ThemedView style={styles.horizontalRow}>
                  <ThemedView style={styles.leanToCornerPost} />
                  
                  <TouchableOpacity 
                    style={[styles.leanToDividerWall, wallMatrix.leftLeanToBackWall ? styles.activeWall : {}]}
                    onPress={() => toggleWall(`leftLeanToBackWall`)}
                    accessibilityLabel="Left Lean-to Back Wall"
                  />
                  
                  <ThemedView style={styles.leanToCornerPost} />
                </ThemedView>
                
                {/* Left Lean-to Side Walls */}
                {Array.from({ length: numberOfSections }).map((_, sectionIndex) => (
                  <ThemedView key={`leftLeanto-section-${sectionIndex}`} style={styles.carportSection}>
                    <ThemedView style={styles.middleRow}>
                      {/* Left lean-to outer wall */}
                      <TouchableOpacity 
                        style={[styles.leanToWall, wallMatrix.leftLeanToWall[sectionIndex] ? styles.activeWall : {}]}
                        onPress={() => toggleWall(`leftLeanToWall${sectionIndex + 1}`)}
                        accessibilityLabel={`Left Lean-to Wall ${sectionIndex + 1}`}
                      />
                      
                      <ThemedView style={styles.middleSpace} />
                      
                      {/* Inner wall is just the main structure's left wall */}
                    </ThemedView>
                    
                    {/* Add divider walls if needed - each lean-to has independent divider walls */}
                    {sectionIndex < numberOfSections - 1 && (
                      <ThemedView style={styles.horizontalRow}>
                        <ThemedView style={styles.leanToCornerPost} />
                        <TouchableOpacity 
                          style={[styles.leanToDividerWall, wallMatrix.leftLeanToDividerWall[sectionIndex] ? styles.activeWall : {}]}
                          onPress={() => toggleWall(`leftLeanToDividerWall${sectionIndex + 1}`)}
                          accessibilityLabel={`Left Lean-to Divider ${sectionIndex + 1}`}
                        />
                        <ThemedView style={styles.leanToCornerPost} />
                      </ThemedView>
                    )}
                  </ThemedView>
                ))}
                
                {/* Left Lean-to Front Wall - independent */}
                <ThemedView style={styles.horizontalRow}>
                  <ThemedView style={styles.leanToCornerPost} />
                  
                  <TouchableOpacity 
                    style={[styles.leanToDividerWall, wallMatrix.leftLeanToFrontWall ? styles.activeWall : {}]}
                    onPress={() => toggleWall(`leftLeanToFrontWall`)}
                    accessibilityLabel="Left Lean-to Front Wall"
                  />
                  
                  <ThemedView style={styles.leanToCornerPost} />
                </ThemedView>
              </ThemedView>
            )}
            
            {/* Main Carport Section */}
            <ThemedView style={styles.mainCarportSection}>
              {/* Back Label */}
              <ThemedText style={styles.orientationLabel}>{t('Back')}</ThemedText>
              
              {/* First row - back wall with corner posts */}
              <ThemedView style={styles.horizontalRow}>
                <ThemedView style={styles.cornerPost} />
                              <TouchableOpacity 
                  style={[styles.frontBackWall, wallMatrix.mainBackWall ? styles.activeWall : {}]}
                  onPress={() => toggleWall(`frontBackWall1`)}
                  accessibilityLabel="Back Wall"
                />
                
                <ThemedView style={styles.cornerPost} />
              </ThemedView>
              
              {/* Generate middle sections with side walls */}
              {Array.from({ length: numberOfSections }).map((_, sectionIndex) => (
                <ThemedView key={`section-${sectionIndex}`} style={styles.carportSection}>
                  {/* Middle row with left and right side walls */}
                  <ThemedView style={styles.middleRow}>
                    {/* Left wall - toggle functionality */}
                    <TouchableOpacity 
                      style={[styles.sideWall, wallMatrix.mainLeftWall[sectionIndex] ? styles.activeWall : {}]}
                      onPress={() => toggleWall(`leftWall${sectionIndex + 1}`)}
                      accessibilityLabel={`Left Wall ${sectionIndex + 1}`}
                    />
                    
                    {/* Middle space */}
                    <ThemedView style={styles.middleSpace} />
                    
                    {/* Right wall */}
                    <TouchableOpacity 
                      style={[styles.sideWall, wallMatrix.mainRightWall[sectionIndex] ? styles.activeWall : {}]}
                      onPress={() => toggleWall(`rightWall${sectionIndex + 1}`)}
                      accessibilityLabel={`Right Wall ${sectionIndex + 1}`}
                    />
                  </ThemedView>
                  
                  {/* Add division wall between sections (except for the last section) */}
                  {sectionIndex < numberOfSections - 1 && (
                    <ThemedView style={styles.horizontalRow}>
                      <ThemedView style={styles.cornerPost} />
                      <TouchableOpacity 
                        style={[styles.frontBackWall, wallMatrix.mainDividerWall[sectionIndex] ? styles.activeWall : {}]}
                        onPress={() => toggleWall(`dividerWall${sectionIndex + 1}`)}
                        accessibilityLabel={`Divider Wall ${sectionIndex + 1}`}
                      />
                      <ThemedView style={styles.cornerPost} />
                    </ThemedView>
                  )}
                </ThemedView>
              ))}
              
              {/* Last row - front wall with corner posts */}
              <ThemedView style={styles.horizontalRow}>
                <ThemedView style={styles.cornerPost} />
                
                <TouchableOpacity 
                  style={[styles.frontBackWall, wallMatrix.mainFrontWall ? styles.activeWall : {}]}
                  onPress={() => toggleWall(`frontBackWall${numberOfSections + 1}`)}
                  accessibilityLabel="Front Wall"
                />
                
                <ThemedView style={styles.cornerPost} />
              </ThemedView>
              
              {/* Front Label */}
              <ThemedText style={styles.orientationLabel}>{t('Front')}</ThemedText>
            </ThemedView>
            
            {/* Right Lean-to Section (if enabled) */}
            {hasRightLeanTo && (
              <ThemedView style={styles.leanToSection}>
                <ThemedText style={styles.leanToLabel}>Right Lean-to</ThemedText>
                
                {/* Right Lean-to Back Wall - independent */}
                <ThemedView style={styles.horizontalRow}>
                  <ThemedView style={styles.leanToCornerPost} />
                  
                  <TouchableOpacity 
                    style={[styles.leanToDividerWall, wallMatrix.rightLeanToBackWall ? styles.activeWall : {}]}
                    onPress={() => toggleWall(`rightLeanToBackWall`)}
                    accessibilityLabel="Right Lean-to Back Wall"
                  />
                  
                  <ThemedView style={styles.leanToCornerPost} />
                </ThemedView>
                
                {/* Right Lean-to Side Walls */}
                {Array.from({ length: numberOfSections }).map((_, sectionIndex) => (
                  <ThemedView key={`rightLeanto-section-${sectionIndex}`} style={styles.carportSection}>
                    <ThemedView style={styles.middleRow}>
                      {/* Inner wall is just the main structure's right wall */}
                      <ThemedView style={styles.middleSpace} />
                      
                      {/* Right lean-to outer wall */}
                      <TouchableOpacity 
                        style={[styles.leanToWall, wallMatrix.rightLeanToWall[sectionIndex] ? styles.activeWall : {}]}
                        onPress={() => toggleWall(`rightLeanToWall${sectionIndex + 1}`)}
                        accessibilityLabel={`Right Lean-to Wall ${sectionIndex + 1}`}
                      />
                    </ThemedView>
                    
                    {/* Add divider walls if needed - each lean-to has independent divider walls */}
                    {sectionIndex < numberOfSections - 1 && (
                      <ThemedView style={styles.horizontalRow}>
                        <ThemedView style={styles.leanToCornerPost} />
                        <TouchableOpacity 
                          style={[styles.leanToDividerWall, wallMatrix.rightLeanToDividerWall[sectionIndex] ? styles.activeWall : {}]}
                          onPress={() => toggleWall(`rightLeanToDividerWall${sectionIndex + 1}`)}
                          accessibilityLabel={`Right Lean-to Divider ${sectionIndex + 1}`}
                        />
                        <ThemedView style={styles.leanToCornerPost} />
                      </ThemedView>
                    )}
                  </ThemedView>
                ))}
                
                {/* Right Lean-to Front Wall - independent */}
                <ThemedView style={styles.horizontalRow}>
                  <ThemedView style={styles.leanToCornerPost} />
                  
                  <TouchableOpacity 
                    style={[styles.leanToDividerWall, wallMatrix.rightLeanToFrontWall ? styles.activeWall : {}]}
                    onPress={() => toggleWall(`rightLeanToFrontWall`)}
                    accessibilityLabel="Right Lean-to Front Wall"
                  />
                  
                  <ThemedView style={styles.leanToCornerPost} />
                </ThemedView>
              </ThemedView>
            )}
          </ThemedView>
          
          {/* Wall configuration instructions - removed as requested */}
        </ThemedView>
      </ScrollView>
    </ThemedView>
  );
};