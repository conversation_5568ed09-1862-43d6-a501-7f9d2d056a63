import React from 'react';
import { Switch } from 'react-native';
import { Picker } from '@react-native-picker/picker';
import { useTranslation } from 'react-i18next';

import Slider from '@/components/ui-controls/Slider';
import { ThemedView } from '@/components/themed/ThemedView';
import { ThemedText } from '@/components/themed/ThemedText';
import { ThemedTextInput } from '@/components/themed/ThemedInputText';
import { ThemedPicker } from '@/components/themed/ThemedPicker';
import { useSceneContext, setSizeValue } from '@/redux/context';

import { styles } from './styles';
import type { RoofType, DimensionType } from '@/types/carport';

interface SizePanelProps {
  roofType: RoofType;
  pitch: number;
  span: number;
  length: number;
  height: number;
  overhang: number;
  inputValues: Record<string, string>;
  roofTypes: readonly RoofType[];
  handleValueChange: (type: DimensionType, value: number | string) => void;
  handleRoofTypeChange: (value: RoofType) => void;
}

export const SizePanel: React.FC<SizePanelProps> = ({
  roofType,
  pitch,
  span,
  length,
  height,
  overhang,
  inputValues,
  roofTypes,
  handleValueChange,
  handleRoofTypeChange,
}) => {
  const { t } = useTranslation();
  const { sceneSettings, setSceneSettings, dispatch } = useSceneContext();

  return (
    <ThemedView style={styles.expandedContent}>
      <ThemedView style={styles.expandedHeader}>
        <ThemedText type="defaultSemiBold" style={styles.expandedTitle}>
          {t('Size')}
        </ThemedText>
      </ThemedView>

      <ThemedView style={styles.content}>
        {/* Roof Type */}
        <ThemedText style={styles.label}>{t('Roof')}</ThemedText>
        <ThemedView style={styles.fullWidthControl}>
          <ThemedPicker
            selectedValue={roofType}
            onValueChange={handleRoofTypeChange}
            style={styles.picker}
          >
            {roofTypes.map((type) => (
              <Picker.Item key={type} label={type} value={type} />
            ))}
          </ThemedPicker>
        </ThemedView>

        {/* Span */}
        <ThemedText style={styles.label}>{t('Span')}</ThemedText>
        <Slider
          value={span}
          min={1000}
          max={12000}
          onChange={(v) => handleValueChange('span', v)}
          style={styles.slider}
        />
        <ThemedView style={styles.inlineInputRow}>
          <ThemedTextInput
            style={styles.input}
            value={inputValues.span}
            onChangeText={(v) => handleValueChange('span', v)}
            keyboardType="numeric"
          />
          <ThemedText style={styles.unit}>mm</ThemedText>
        </ThemedView>

        {/* Length */}
        <ThemedText style={styles.label}>{t('Length')}</ThemedText>
        <Slider
          value={length}
          min={1000}
          max={24000}
          onChange={(v) => handleValueChange('length', v)}
          style={styles.slider}
        />
        <ThemedView style={styles.inlineInputRow}>
          <ThemedTextInput
            style={styles.input}
            value={inputValues.length}
            onChangeText={(v) => handleValueChange('length', v)}
            keyboardType="numeric"
          />
          <ThemedText style={styles.unit}>mm</ThemedText>
        </ThemedView>

        {/* Height */}
        <ThemedText style={styles.label}>{t('Height')}</ThemedText>
        <Slider
          value={height}
          min={1000}
          max={12000}
          onChange={(v) => handleValueChange('height', v)}
          style={styles.slider}
        />
        <ThemedView style={styles.inlineInputRow}>
          <ThemedTextInput
            style={styles.input}
            value={inputValues.height}
            onChangeText={(v) => handleValueChange('height', v)}
            keyboardType="numeric"
          />
          <ThemedText style={styles.unit}>mm</ThemedText>
        </ThemedView>

        {/* Overhang */}
        {roofType === 'Skillion Overhang' && (
          <>
            <ThemedText style={styles.label}>{t('Overhang')}</ThemedText>
            <Slider
              value={overhang}
              min={200}
              max={1500}
              onChange={(v) => handleValueChange('overhang', v)}
              style={styles.slider}
            />
            <ThemedView style={styles.inlineInputRow}>
              <ThemedTextInput
                style={styles.input}
                value={inputValues.overhang || '500'}
                onChangeText={(v) => handleValueChange('overhang', v)}
                keyboardType="numeric"
              />
              <ThemedText style={styles.unit}>mm</ThemedText>
            </ThemedView>
          </>
        )}

        {/* Pitch */}
        <ThemedText style={styles.label}>{t('Pitch')}</ThemedText>
        <ThemedView style={styles.fullWidthControl}>
          <ThemedPicker
            selectedValue={pitch.toString()}
            onValueChange={(v) => {
              const newPitch = parseInt(v);
              handleValueChange('pitch', newPitch);
              dispatch(setSizeValue({ pitch: newPitch }));
            }}
            style={styles.picker}
          >
            {roofType === 'Gable' ? (
              <>
                <Picker.Item key="5" label="5°" value="5" />
                <Picker.Item key="11" label="11°" value="11" />
                <Picker.Item key="15" label="15°" value="15" />
                <Picker.Item key="22" label="22°" value="22" />
                <Picker.Item key="30" label="30°" value="30" />
              </>
            ) : (
              <>
                <Picker.Item key="2" label="2°" value="2" />
                <Picker.Item key="5" label="5°" value="5" />
              </>
            )}
          </ThemedPicker>
        </ThemedView>

        {/* Slab */}
        <ThemedText style={styles.label}>{t('Slab')}</ThemedText>
        <ThemedView style={styles.switchContainer}>
          <Switch
            value={sceneSettings.showSlab}
            onValueChange={(value) =>
              setSceneSettings((prev) => ({
                ...prev,
                showSlab: value,
              }))
            }
            thumbColor="#4B5563"
            trackColor={{ false: '#ccc', true: '#fca5a5' }}
          />
        </ThemedView>
      </ThemedView>
    </ThemedView>
  );
};
