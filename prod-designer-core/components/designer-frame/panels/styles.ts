import { StyleSheet } from 'react-native';

export const styles = StyleSheet.create({
  expandedContent: {
    padding: 12,
    width: '100%',
    backgroundColor: '#fff',
    borderRadius: 10,
    marginTop: 40,
  },
  expandedHeader: {
    backgroundColor: '#fff',
  },
  expandedTitle: {
    fontSize: 14,
    marginBottom: 12,
    textAlign: 'center',
    color: '#000',
    fontWeight: '600',
  },
  content: {
    backgroundColor: '#fff',
  },

  label: {
    fontSize: 14,
    margin: 8,
    color: '#333',
  },

  fullWidthControl: {
    width: '100%',
    borderWidth: 1,
    borderColor: 'red',
    borderRadius: 6,
    overflow: 'hidden',
    marginBottom: 6,
    backgroundColor: 'red',
  },
  picker: {
    height: 36,
    width: '100%',
    color: '#fff',
    backgroundColor: 'red',
  },
  pickerContainer: {
    flex: 1,
  },
  dropdownWrapper: {
    flex: 2,
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 6,
    overflow: 'hidden',
  },

  slider: {
    width: '100%',
    marginVertical: 4,
  },
  inlineInputRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    marginBottom: 8,
    backgroundColor: '#fff',
    overflow: 'hidden',
  },
  input: {
    flex: 1,
    height: 36,
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 6,
    paddingHorizontal: 8,
    color: '#fff',
  },
  unit: {
    fontSize: 13,
    color: '#555',
  },

  switchContainer: {
    backgroundColor: 'red',
    padding: 8,
    borderRadius: 10,
    margin: 4,
  },
  switchWrapper: {
    paddingHorizontal: 8,
  },

  row: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 14,
    backgroundColor: '#fff',
  },
  contentScrollView: {
    flex: 1,
    maxHeight: 400,
  },
  scrollContent: {
    paddingBottom: 40,
  },
  colorSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 8,
    marginVertical: 6,
    backgroundColor: '#fff',
  },
  colorSwatch: {
    width: 24,
    height: 24,
    borderRadius: 12,
    margin: 10,
    borderWidth: 1,
    borderColor: '#aaa',
  },
  formGroup: {
    marginBottom: 16,
  },

  submitButton: {
    backgroundColor: '#007bff',
    paddingVertical: 14,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 12,
  },
  submitButtonText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 16,
  },
  errorText: {
    color: '#ffb3c1',
    fontSize: 14,
    marginTop: 4,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    fontWeight: '600',
    marginTop: 10,
    marginBottom: 8,
    color: '#000',
  },
  carportContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    marginHorizontal: 0,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    paddingHorizontal: 10,
    flexDirection: 'column',
    width: '100%',
  },
  leanToSection: {
    flexDirection: 'column',
    width: '100%',
    marginVertical: 10,
    padding: 10,
    backgroundColor: '#e6f7ff',
    borderRadius: 8,
    alignItems: 'center',
  },
  leanToLabel: {
    fontSize: 12,
    fontWeight: '600',
    marginBottom: 5,
    textAlign: 'center',
    color: 'red',
  },
  horizontalRow: {
    flexDirection: 'row',
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  middleRow: {
    flexDirection: 'row',
    width: '100%',
    justifyContent: 'space-between',
    height: 80,
  },
  leanToDividerWall: {
    height: 20,
    flex: 1,
    backgroundColor: 'transparent',
    borderWidth: 2,
    borderColor: '#ffd166',
    borderRadius: 2,
    margin: 5,
  },
  leanToCornerPost: {
    width: 28,
    height: 28,
    backgroundColor: '#777777',
    borderRadius: 14,
  },
  carportSection: {
    width: '100%',
    alignItems: 'center',
  },
  activeWallStatus: {
    backgroundColor: '#2196F3',
  },
  leanToWall: {
    width: 20,
    height: '100%',
    backgroundColor: 'transparent',
    borderWidth: 2,
    borderColor: '#4dabf7',
    borderRadius: 2,
    margin: 5,
  },
  middleSpace: {
    flex: 1,
    height: '100%',
  },
  activeWall: {
    backgroundColor: '#4dabf7',
  },
  mainCarportSection: {
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    marginVertical: 10,
    backgroundColor: '#fff',
    borderRadius: 8,
    minHeight: 200,
  },
  cornerPost: {
    width: 28,
    height: 28,
    backgroundColor: '#777777',
    borderRadius: 14,
  },
  frontBackWall: {
    height: 20,
    flex: 1,
    backgroundColor: 'transparent',
    borderWidth: 2,
    borderColor: '#ffd166',
    borderRadius: 2,
    margin: 5,
  },
  orientationLabel: {
    fontSize: 14,
    fontWeight: '600',
    marginVertical: 5,
    textAlign: 'center',
    color: 'red',
  },
  sideWall: {
    width: 20,
    height: '100%',
    backgroundColor: 'transparent',
    borderWidth: 2,
    borderColor: '#4dabf7',
    borderRadius: 2,
    margin: 5,
  },
  button: {
    backgroundColor: 'red',
    paddingVertical: 4,
    paddingHorizontal: 12,
    borderRadius: 6,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 2,
    marginBottom: 4,
  },
  buttonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 14,
  },
  buttonSmall: {
    paddingVertical: 0,
    paddingHorizontal: 4,
    alignSelf: 'flex-start',
    minWidth: 0,
  },
  buttonTextSmall: {
    fontSize: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.4)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    maxHeight: '85%',
    backgroundColor: 'red',
    borderRadius: 16,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.2,
    shadowRadius: 10,
    elevation: 10,
    alignSelf: 'center',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    paddingBottom: 8,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '600',
  },
  closeButton: {
    fontSize: 24,
    color: '#999',
    paddingHorizontal: 10,
  },
  templateList: {
    flex: 1,
  },
  thumbnailGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  thumbnailContainerWrapper: {
    marginBottom: 16,
  },
  thumbnailContainer: {
    borderRadius: 12,
    overflow: 'hidden',
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#ccc',
    elevation: 3,
  },
  thumbnailImage: {
    width: '100%',
    height: 160,
    resizeMode: 'cover',
  },
  thumbnailInfo: {
    padding: 12,
    backgroundColor: '#fafafa',
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  thumbnailTitle: {
    fontWeight: '600',
    fontSize: 16,
    color: '#333',
    marginBottom: 4,
  },
  thumbnailDescription: {
    fontSize: 13,
    color: '#666',
  },
  modelList: {
    marginTop: 10,
  },
  modelItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    padding: 12,
    marginBottom: 10,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  modelInfo: {
    flex: 1,
    marginRight: 10,
  },
  modelName: {
    fontWeight: '600',
    fontSize: 15,
    color: '#333',
  },
  modelDate: {
    fontSize: 12,
    color: '#666',
  },
  loadButton: {
    paddingVertical: 6,
    paddingHorizontal: 14,
    borderRadius: 6,
    borderWidth: 2,
    borderColor: 'red',
  },
  loadButtonText: {
    color: '#000',
    fontWeight: 'bold',
    fontSize: 13,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 30,
  },

  paginationContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 16,
    gap: 10,
  },
  paginationButton: {
    backgroundColor: '#4a90e2',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 4,
  },
  paginationButtonText: {
    color: 'white',
    fontSize: 13,
  },
  disabledButton: {
    backgroundColor: '#ccc',
    opacity: 0.6,
  },
  paginationText: {
    fontSize: 13,
    color: '#444',
    fontWeight: '500',
  },

  cancelButton: {
    backgroundColor: '#fdd',
    borderColor: '#fdd',
  },
  textInput: {
    backgroundColor: '#fff',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#fff',
    color: '#000',
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 10,
  },
  note: {
    fontSize: 12,
    color: '#999',
    margin: 12,
  },
  cancel: {
    backgroundColor: '#aaa',
  },

  modalText: {
    marginBottom: 16,
    lineHeight: 22,
  },
  linkContainer: {
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 4,
    padding: 8,
  },
  linkInput: {
    minHeight: 60,
    textAlignVertical: 'top',
    padding: 4,
  },
  copyButton: {
    backgroundColor: '#4a90e2',
    paddingVertical: 12,
    borderRadius: 4,
    alignItems: 'center',
    marginBottom: 12,
  },
  copyButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
  emailButton: {
    backgroundColor: '#27ae60',
    paddingVertical: 12,
    borderRadius: 4,
    alignItems: 'center',
    marginBottom: 12,
  },
  emailButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },

  cancelButtonText: {
    fontWeight: 'bold',
    color: '#000',
  },
  secondarySwitch: {
    marginTop: 10,
  },
  switchButton: {
    paddingHorizontal: 16,
    paddingVertical: 10,
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 60,
  },
  leftButton: {
    borderTopLeftRadius: 8,
    borderBottomLeftRadius: 8,
  },
  rightButton: {
    borderTopRightRadius: 8,
    borderBottomRightRadius: 8,
  },
  switchText: {
    fontWeight: '500',
    fontSize: 14,
  },
  activeText: {
    color: 'white',
  },
  rootContainer: {
    zIndex: 100,
    alignSelf: 'flex-start',
    backgroundColor: 'transparent',
  },

  dropdownContent: {
    borderRadius: 12,
    paddingVertical: 4,
    width: 140,
  },
  optionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
  },
  checkbox: {
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: 'white',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
  },
  optionText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 14,
  },
  optionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 16,
  },
  circle: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
    backgroundColor: 'transparent',
  },
  optionLabel: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 14,
  },
  container: {
    flexDirection: 'column',
    marginVertical: 8,
    backgroundColor: 'Transparent',
  },
  activeButton: {
    backgroundColor: '#ffdddd',
  },
  toggleGroup: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderRadius: 20,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#ddd',
  },
  toggleButton: {
    flex: 1,
    paddingVertical: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  toggleButtonMobile: {
    paddingVertical: 0,
  },
  selectedButton: {
    backgroundColor: 'red',
  },
  toggleText: {
    fontWeight: 'bold',
    color: '#888',
    fontSize: 14,
  },
  toggleTextMobile: {
    fontSize: 8,
  },
  selectedText: {
    color: '#fff',
  },
  leftToggle: {
    borderTopLeftRadius: 20,
    borderBottomLeftRadius: 20,
  },
  rightToggle: {
    borderTopRightRadius: 20,
    borderBottomRightRadius: 20,
  },
  closeIcon: {
    backgroundColor: 'red',
    borderRadius: 16,
    padding: 4,
  },
  scrollContainer: {
    flexGrow: 1,
    padding: 16,
  },
});
