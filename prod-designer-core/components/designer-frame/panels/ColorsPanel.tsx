import React, { useState } from 'react';
import { TouchableOpacity } from 'react-native';
import { useTranslation } from 'react-i18next';

import { ThemedView } from '@/components/themed/ThemedView';
import { ThemedText } from '@/components/themed/ThemedText';
import { ColorPicker } from '@/components/ui-controls/ColorPicker';
import { updateMaterialColor } from '@/domain/materials/colorManager';
import { useSceneContext } from '@/redux/context';
import { CarportMaterialType } from '@/constants/materials/Material';
import { colorbondColors } from '@/constants/colors/colorbondColors';
import { IconSymbol } from '@/components/platform-specific/IconSymbol';
import { ColorSwatch3D } from '../ColorSwatch3D';
import { styles } from './styles';

export const ColoursPanel: React.FC = () => {
  const { t } = useTranslation();
  const { sceneSettings, setSceneSettings } = useSceneContext();
  const [activePicker, setActivePicker] = useState<'roof' | 'trim' | null>(null);

  const getColorHex = (colorValue?: string | null) => {
    if (!colorValue || typeof colorValue !== 'string') return '#ccc';
    const match = colorbondColors.find((c) => c.value === colorValue);
    return match ? match.hex : '#ccc';
  };

  const roofColor = sceneSettings.roofColor ?? 'NightSky';
  const trimColor = sceneSettings.trimColor ?? 'Surfmist';

  return (
    <ThemedView style={styles.expandedContent}>
      <ThemedView style={styles.expandedHeader}>
        <ThemedText type="defaultSemiBold" style={styles.expandedTitle}>
          {t('Colours')}
        </ThemedText>
      </ThemedView>

      <ThemedView style={styles.content}>
        {/* Roof Selector */}
        <TouchableOpacity
          style={[
            styles.colorSelector,
            { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' },
          ]}
          onPress={() => setActivePicker(activePicker === 'roof' ? null : 'roof')}
        >
          <ThemedView
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              backgroundColor: '#fff',
              borderRadius: 10,
            }}
          >
            <ColorSwatch3D color={getColorHex(roofColor)} size={28} />
            <ThemedText style={styles.label}>
              {t('Roof')}: {roofColor}
            </ThemedText>
          </ThemedView>
          <IconSymbol
            name={activePicker === 'roof' ? 'chevron-up' : 'chevron-down'}
            size={32}
            weight="medium"
            color="#555"
          />
        </TouchableOpacity>

        {activePicker === 'roof' && (
          <ColorPicker
            label={t('Roof')}
            colors={colorbondColors}
            selectedValue={roofColor}
            onValueChange={(value: string) => {
              setSceneSettings((prev) => ({ ...prev, roofColor: value }));
              updateMaterialColor(CarportMaterialType.Roof, value);
            }}
          />
        )}

        {/* Trim Selector */}
        <TouchableOpacity
          style={[
            styles.colorSelector,
            {
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
              marginTop: 16,
            },
          ]}
          onPress={() => setActivePicker(activePicker === 'trim' ? null : 'trim')}
        >
          <ThemedView
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              backgroundColor: '#fff',
              borderRadius: 10,
            }}
          >
            <ColorSwatch3D color={getColorHex(trimColor)} size={28} />
            <ThemedText style={styles.label}>
              {t('Trim')}: {trimColor}
            </ThemedText>
          </ThemedView>
          <IconSymbol
            name={activePicker === 'trim' ? 'chevron-up' : 'chevron-down'}
            size={32}
            weight="medium"
            color="#555"
          />
        </TouchableOpacity>

        {activePicker === 'trim' && (
          <ColorPicker
            label={t('Trim')}
            colors={colorbondColors}
            selectedValue={trimColor}
            onValueChange={(value: string) => {
              setSceneSettings((prev) => ({ ...prev, trimColor: value }));
              updateMaterialColor(CarportMaterialType.Cap, value);
            }}
          />
        )}
      </ThemedView>
    </ThemedView>
  );
};
