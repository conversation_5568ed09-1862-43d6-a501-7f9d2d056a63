// PlanSwitch.tsx
import React from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { useSceneContext } from '@/redux/context';
import { ThemedView } from '@/components/themed/ThemedView';
import { ThemedText } from '@/components/themed/ThemedText';
import { useThemeColor } from '@/hooks/useThemeColor';
import { CameraType } from '@/types/threejs';
import { useTranslation } from 'react-i18next';

function PlanSwitch() {
  const { sceneSettings, setSceneSettings } = useSceneContext();
  const { t } = useTranslation();

  const isPlanView = sceneSettings.isPlanView || false;
  const isOrthoView = sceneSettings.defaultCamera === 'orthographic';

  const accent = useThemeColor({}, 'accentColor');

  const toggleView = (planView: boolean) => {
    setSceneSettings(prev => ({
      ...prev,
      isPlanView: planView,
      defaultCamera: planView ? 'orthographic' : 'perspective',
    }));

    if (!planView) {
      // Full refresh for 3D fix
      setTimeout(() => {
        setSceneSettings(prev => ({
          ...prev,
          exportMode: true,
          refresh3D: Math.random(), // trigger scene refresh
        }));

        setTimeout(() => {
          setSceneSettings(prev => ({
            ...prev,
            exportMode: false,
          }));
        }, 100);
      }, 50);
    }
  };

  const toggleCameraType = (orthoView: boolean) => {
    if (!isPlanView) {
      const cameraType: CameraType = orthoView ? 'orthographic' : 'perspective';
      setSceneSettings(prev => ({
        ...prev,
        defaultCamera: cameraType,
      }));

      setTimeout(() => {
        setSceneSettings(prev => ({
          ...prev,
          defaultCamera: prev.defaultCamera,
        }));
      }, 50);
    }
  };

  return (
    <ThemedView style={styles.container}>
      {/* Plan / 3D Switch */}
      <View style={styles.switchContainer}>
        <TouchableOpacity
          style={[styles.switchButton, styles.leftButton, !isPlanView && { backgroundColor: accent }]}
          onPress={() => toggleView(false)}
        >
          <ThemedText style={[styles.switchText, !isPlanView && styles.activeText]}>3D</ThemedText>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.switchButton, styles.rightButton, isPlanView && { backgroundColor: accent }]}
          onPress={() => toggleView(true)}
        >
          <ThemedText style={[styles.switchText, isPlanView && styles.activeText]}>{t('Plan')}</ThemedText>
        </TouchableOpacity>
      </View>

      {/* Camera Type Switch - Only show in 3D */}
      {!isPlanView && (
        <View style={[styles.switchContainer, styles.secondarySwitch]}>
          <TouchableOpacity
            style={[styles.switchButton, styles.leftButton, !isOrthoView && { backgroundColor: accent }]}
            onPress={() => toggleCameraType(false)}
          >
            <ThemedText style={[styles.switchText, !isOrthoView && styles.activeText]}>{t('Persp')}</ThemedText>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.switchButton, styles.rightButton, isOrthoView && { backgroundColor: accent }]}
            onPress={() => toggleCameraType(true)}
          >
            <ThemedText style={[styles.switchText, isOrthoView && styles.activeText]}>{t('Ortho')}</ThemedText>
          </TouchableOpacity>
        </View>
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    zIndex: 100,
  },
  switchContainer: {
    flexDirection: 'row',
    borderRadius: 8,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  secondarySwitch: {
    marginTop: 10,
  },
  switchButton: {
    paddingHorizontal: 16,
    paddingVertical: 10,
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 60,
  },
  leftButton: {
    borderTopLeftRadius: 8,
    borderBottomLeftRadius: 8,
  },
  rightButton: {
    borderTopRightRadius: 8,
    borderBottomRightRadius: 8,
  },
  switchText: {
    fontWeight: '500',
    fontSize: 14,
  },
  activeText: {
    color: 'white',
  },
});

export default PlanSwitch;

