/**
 * Simple encryption/decryption utility for sharing carport designs
 * Uses a simple encoding approach that works reliably in web environments
 */

// Type definition for Carport model data
export interface CarportModelData {
  name: string;
  timestamp: string;
  dimensions: {
    roofType: string;
    length: number;
    span: number;
    height: number;
    pitch: number;
    overhang: number;
    [key: string]: any;
  };
  sceneSettings: {
    leftLeanTo?: boolean;
    leftLeanToSpan?: number;
    leftLeanToDropHeight?: number;
    rightLeanTo?: boolean;
    rightLeanToSpan?: number;
    rightLeanToDropHeight?: number;
    rightLeanToAttached?: boolean;
    viewMode?: string;
    showSlab?: boolean;
    showMeasurements?: boolean;
    roofColor?: string;
    wallColor?: string;
    trimColor?: string;
    doorColor?: string;
    [key: string]: any;
  };
  displayInfo: {
    roofType: string;
    length: number;
    span: number;
    hasLeanTo: boolean;
    [key: string]: any;
  };
}

// A simple key for basic obfuscation (not for production security)
const SECRET_KEY = 'designmycarport-key-2025';

/**
 * Safely encodes a string to base64 in a way that works in all browsers
 * @param str - String to encode
 * @returns - Base64 encoded string
 */
function safeBase64Encode(str: string): string {
  try {
    // For browsers
    return btoa(unescape(encodeURIComponent(str)));
  } catch (e) {
    console.error('Error in base64 encoding:', e);
    // Fallback to simple encoding if btoa fails
    return encodeURIComponent(str);
  }
}

/**
 * Safely decodes a base64 string in a way that works in all browsers
 * @param str - Base64 encoded string
 * @returns - Decoded string
 */
function safeBase64Decode(str: string): string {
  try {
    // For browsers
    return decodeURIComponent(escape(atob(str)));
  } catch (e) {
    console.error('Error in base64 decoding:', e);
    // Fallback to simple decoding if atob fails
    return decodeURIComponent(str);
  }
}

/**
 * Encrypts text using a simple encoding approach
 * @param text - Plain text to encrypt
 * @returns - Encoded string safe for URL transmission
 */
export function encryptText(text: string): string {
  try {
    // First, convert the text to a JSON string if it's an object
    const textToEncode = typeof text === 'object' ? JSON.stringify(text) : text;
    
    // Then encode it to base64
    return safeBase64Encode(textToEncode);
  } catch (error) {
    console.error('Encryption error:', error);
    // Fallback to simple encoding
    return encodeURIComponent(text);
  }
}

/**
 * Decrypts text that was encrypted with encryptText
 * @param encryptedText - Encoded string
 * @returns - Decrypted plain text
 */
export function decryptText(encryptedText: string): string {
  try {
    // Decode from base64
    return safeBase64Decode(encryptedText);
  } catch (error) {
    console.error('Error decrypting text:', error);
    // Try fallback decoding
    try {
      return decodeURIComponent(encryptedText);
    } catch (e) {
      console.error('Fallback decryption failed:', e);
      return '';
    }
  }
}

/**
 * Parses a URL to extract and decrypt the model parameter
 * @param url - URL containing the encrypted model parameter
 * @returns - Parsed model object or null if invalid
 */
export function parseModelFromUrl(url: string): any {
  try {
    // Handle both full URLs and just query strings
    let modelParam: string | null = null;
    
    if (url.includes('?')) {
      // Try parsing as a full URL first
      try {
        const urlObj = new URL(url);
        modelParam = urlObj.searchParams.get('model');
      } catch (e) {
        // If URL parsing fails, try extracting the query string manually
        const queryString = url.split('?')[1];
        const params = new URLSearchParams(queryString);
        modelParam = params.get('model');
      }
    }
    
    if (!modelParam) return null;
    
    // Decrypt the model parameter
    const decrypted = decryptText(modelParam);
    
    // Parse the JSON
    try {
      return JSON.parse(decrypted);
    } catch (jsonError) {
      console.error('Error parsing JSON from decrypted text:', jsonError);
      return null;
    }
  } catch (error) {
    console.error('Error parsing model from URL:', error);
    return null;
  }
}

/**
 * Saves a carport model with a specific timestamp
 * @param modelData - The complete model data to save
 * @param timestamp - Unique timestamp to identify the model
 */
export function saveModelWithTimestamp(modelData: CarportModelData, timestamp: string): void {
  try {
    // Convert model data to JSON string
    const jsonData = JSON.stringify(modelData);
    
    // Encrypt the model data
    const encryptedData = encryptText(jsonData);
    
    // Create storage key based on timestamp
    const storageKey = `carport_model_${timestamp}`;
    
    // Save to localStorage
    localStorage.setItem(storageKey, encryptedData);
    
    // Also save a model index entry
    updateSavedModelIndex(modelData.name, timestamp);
    
    console.log(`Model saved with timestamp: ${timestamp}`);
  } catch (error) {
    console.error('Error saving model with timestamp:', error);
    throw error;
  }
}

/**
 * Updates the saved model index with a new model entry
 * @param name - Display name of the model
 * @param timestamp - Unique timestamp identifier
 */
function updateSavedModelIndex(name: string, timestamp: string): void {
  try {
    // Get existing index or create new one
    const existingIndexStr = localStorage.getItem('carport_models_index');
    const modelIndex = existingIndexStr ? JSON.parse(existingIndexStr) : [];
    
    // Add new model to index
    modelIndex.push({ timestamp, name });
    
    // Save updated index
    localStorage.setItem('carport_models_index', JSON.stringify(modelIndex));
  } catch (error) {
    console.error('Error updating model index:', error);
  }
}

/**
 * Gets all saved models from storage
 * @returns Array of model metadata with timestamps and names
 */
export function getAllSavedModels(): Array<{ timestamp: string; name: string }> {
  try {
    // Get model index
    const indexStr = localStorage.getItem('carport_models_index');
    if (!indexStr) return [];
    
    // Parse and return the index
    return JSON.parse(indexStr);
  } catch (error) {
    console.error('Error getting saved models:', error);
    return [];
  }
}

/**
 * Gets a specific model by its timestamp
 * @param timestamp - The unique timestamp identifier
 * @returns The model data or null if not found
 */
export function getModelByTimestamp(timestamp: string): CarportModelData | null {
  try {
    // Get encrypted model data
    const storageKey = `carport_model_${timestamp}`;
    const encryptedData = localStorage.getItem(storageKey);
    
    if (!encryptedData) return null;
    
    // Decrypt the data
    const decryptedJson = decryptText(encryptedData);
    
    // Parse and return the model
    return JSON.parse(decryptedJson);
  } catch (error) {
    console.error(`Error getting model with timestamp ${timestamp}:`, error);
    return null;
  }
}
