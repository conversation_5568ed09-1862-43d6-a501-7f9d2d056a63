import React, { useState } from 'react';
import { View } from 'react-native';
import { NewPanel } from './panels/NewPanel';
import { LoadPanel } from './panels/LoadPanel';
import { SavePanel } from './panels/SavePanel';
import { SharePanel } from './panels/SharePanel';
import { DimensionPanel } from './panels/DimensionPanel';
import { PlanPanel } from './panels/PlanPanel';

interface UtilityContainerProps {
  isMobile: boolean;
  isTablet: boolean;
}

export const UtilityContainer: React.FC<UtilityContainerProps> = ({ isTablet, isMobile }) => {
  return (
    <View style={{ maxWidth: 80 }}>
      <NewPanel isTablet={isTablet} isMobile={isMobile} />
      <LoadPanel isTablet={isTablet} isMobile={isMobile} />
      <SavePanel isTablet={isTablet} isMobile={isMobile} />
      <SharePanel isTablet={isTablet} isMobile={isMobile} />
      <DimensionPanel isTablet={isTablet} isMobile={isMobile} />
      <PlanPanel isTablet={isTablet} isMobile={isMobile} />
    </View>
  );
};
