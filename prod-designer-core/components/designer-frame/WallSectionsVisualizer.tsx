import React, { useEffect, useMemo } from 'react';
import { View, StyleSheet, TouchableOpacity, Text, ImageBackground } from 'react-native';
import { ThemedView } from '@/components/themed/ThemedView';
import { ThemedText } from '@/components/themed/ThemedText';
import { useSceneContext } from '@/redux/context';

interface WallSectionsVisualizerProps {
  onToggleWallSection: (section: string, index?: number) => void;
}

export function WallSectionsVisualizer({ onToggleWallSection }: WallSectionsVisualizerProps) {
  const { dimensions, sceneSettings, setSceneSettings } = useSceneContext();
  const { length, roofType } = dimensions;
  
  // Calculate number of bays based on length, similar to carport models
  const bay_no = Math.max(1, Math.floor(length / 4000));
  
  // Initialize wall sections if needed
  useEffect(() => {
    if (!sceneSettings.wallSections) {
      setSceneSettings(prev => ({
        ...prev,
        wallSections: {
          front: false,
          back: false,
          frontLeftLeanTo: false,
          frontRightLeanTo: false,
          backLeftLeanTo: false,
          backRightLeanTo: false,
          left: Array(bay_no).fill(false),
          right: Array(bay_no).fill(false),
          leftLeanTo: Array(bay_no).fill(false),
          rightLeanTo: Array(bay_no).fill(false),
        }
      }));
    } else {
      // Update arrays if bay_no has changed
      const currentLeftLength = sceneSettings.wallSections.left?.length || 0;
      const currentRightLength = sceneSettings.wallSections.right?.length || 0;
      const currentLeftLeanToLength = sceneSettings.wallSections.leftLeanTo?.length || 0;
      const currentRightLeanToLength = sceneSettings.wallSections.rightLeanTo?.length || 0;
      
      if (currentLeftLength !== bay_no || currentRightLength !== bay_no || 
          currentLeftLeanToLength !== bay_no || currentRightLeanToLength !== bay_no) {
        const newLeft = [...(sceneSettings.wallSections.left || [])];
        const newRight = [...(sceneSettings.wallSections.right || [])];
        const newLeftLeanTo = [...(sceneSettings.wallSections.leftLeanTo || [])];
        const newRightLeanTo = [...(sceneSettings.wallSections.rightLeanTo || [])];
        
        // Adjust arrays to match bay_no
        while (newLeft.length < bay_no) newLeft.push(false);
        while (newLeft.length > bay_no) newLeft.pop();
        while (newRight.length < bay_no) newRight.push(false);
        while (newRight.length > bay_no) newRight.pop();
        while (newLeftLeanTo.length < bay_no) newLeftLeanTo.push(false);
        while (newLeftLeanTo.length > bay_no) newLeftLeanTo.pop();
        while (newRightLeanTo.length < bay_no) newRightLeanTo.push(false);
        while (newRightLeanTo.length > bay_no) newRightLeanTo.pop();
        
        setSceneSettings(prev => ({
          ...prev,
          wallSections: {
            ...prev.wallSections!,
            left: newLeft,
            right: newRight,
            leftLeanTo: newLeftLeanTo,
            rightLeanTo: newRightLeanTo,
            // Initialize front/back lean-to sections if they don't exist
            frontLeftLeanTo: prev.wallSections?.frontLeftLeanTo || false,
            frontRightLeanTo: prev.wallSections?.frontRightLeanTo || false,
            backLeftLeanTo: prev.wallSections?.backLeftLeanTo || false,
            backRightLeanTo: prev.wallSections?.backRightLeanTo || false,
          }
        }));
      }
    }
  }, [bay_no, sceneSettings.wallSections, setSceneSettings]);
  
  // Generate bay sections for visualization
  const baySections = useMemo(() => {
    if (!sceneSettings.wallSections) return [];
    
    return Array(bay_no).fill(0).map((_, index) => ({
      leftActive: sceneSettings.wallSections?.left?.[index] || false,
      rightActive: sceneSettings.wallSections?.right?.[index] || false,
      leftLeanToActive: sceneSettings.wallSections?.leftLeanTo?.[index] || false,
      rightLeanToActive: sceneSettings.wallSections?.rightLeanTo?.[index] || false,
    }));
  }, [bay_no, sceneSettings.wallSections]);
  
  // Handle wall section toggle
  const handleWallSectionToggle = (section: string, index?: number) => {
    onToggleWallSection(section, index);
  };
  
  // Check if we have lean-to sections
  const hasLeftLeanTo = sceneSettings.leftLeanTo;
  const hasRightLeanTo = sceneSettings.rightLeanTo;
  
  // Determine if we should show brick texture (for Skillion Attached)
  const showBrickTexture = roofType === 'Skillion Attached' || false;
  
  if (!sceneSettings.wallSections) {
    return null;
  }
  
  return (
    <ThemedView style={styles.container}>
      <ThemedText style={styles.instructions}>Tap a wall section to add or remove it.</ThemedText>
      
      <ThemedView style={styles.visualizer}>
        {/* Back wall with lean-to sections */}
        <ThemedView style={styles.endWallRow}>
          {hasLeftLeanTo && (
            <TouchableOpacity 
              style={[styles.leanToEndWall, sceneSettings.wallSections.backLeftLeanTo ? styles.activeWall : {}]} 
              onPress={() => handleWallSectionToggle('backLeftLeanTo')}
            />
          )}
          
          <TouchableOpacity 
            style={[styles.backWall, sceneSettings.wallSections.back ? styles.activeWall : {}, showBrickTexture && sceneSettings.wallSections.back ? styles.brickWall : {}]} 
            onPress={() => handleWallSectionToggle('back')}
          >
            <ThemedText style={styles.wallLabel}>Back</ThemedText>
          </TouchableOpacity>
          
          {hasRightLeanTo && (
            <TouchableOpacity 
              style={[styles.leanToEndWall, sceneSettings.wallSections.backRightLeanTo ? styles.activeWall : {}]} 
              onPress={() => handleWallSectionToggle('backRightLeanTo')}
            />
          )}
        </ThemedView>
        
        <ThemedView style={styles.middleSection}>
          {/* Left lean-to section */}
          {hasLeftLeanTo && (
            <ThemedView style={styles.leanToContainer}>
              {baySections.map((section, index) => (
                <TouchableOpacity
                  key={`left-leanto-${index}`}
                  style={[styles.leanToWall, section.leftLeanToActive ? styles.activeWall : {}]}
                  onPress={() => handleWallSectionToggle('leftLeanTo', index)}
                />
              ))}
            </ThemedView>
          )}
          
          {/* Spacer */}
          {hasLeftLeanTo && <ThemedView style={styles.leanToSpacer} />}
          
          {/* Left wall sections */}
          <ThemedView style={styles.sideWallContainer}>
            {baySections.map((section, index) => (
              <TouchableOpacity
                key={`left-${index}`}
                style={[styles.sideWall, section.leftActive ? styles.activeWall : {}, showBrickTexture && section.leftActive ? styles.brickWall : {}]}
                onPress={() => handleWallSectionToggle('left', index)}
              />
            ))}
          </ThemedView>
          
          {/* Center space */}
          <ThemedView style={styles.centerSpace}>
            {baySections.map((_, index) => (
              <React.Fragment key={`bay-fragment-${index}`}>
                <ThemedView key={`bay-${index}`} style={styles.bayDivider} />
                {index < baySections.length - 1 && (
                  <ThemedView style={styles.bayNumberContainer}>
                    <ThemedText style={styles.bayNumber}>{index + 1}</ThemedText>
                  </ThemedView>
                )}
              </React.Fragment>
            ))}
          </ThemedView>
          
          {/* Right wall sections */}
          <ThemedView style={styles.sideWallContainer}>
            {baySections.map((section, index) => (
              <TouchableOpacity
                key={`right-${index}`}
                style={[styles.sideWall, section.rightActive ? styles.activeWall : {}, showBrickTexture && section.rightActive ? styles.brickWall : {}]}
                onPress={() => handleWallSectionToggle('right', index)}
              />
            ))}
          </ThemedView>
          
          {/* Spacer */}
          {hasRightLeanTo && <ThemedView style={styles.leanToSpacer} />}
          
          {/* Right lean-to section */}
          {hasRightLeanTo && (
            <ThemedView style={styles.leanToContainer}>
              {baySections.map((section, index) => (
                <TouchableOpacity
                  key={`right-leanto-${index}`}
                  style={[styles.leanToWall, section.rightLeanToActive ? styles.activeWall : {}]}
                  onPress={() => handleWallSectionToggle('rightLeanTo', index)}
                />
              ))}
            </ThemedView>
          )}
        </ThemedView>
        
        {/* Front wall with lean-to sections */}
        <ThemedView style={styles.endWallRow}>
          {hasLeftLeanTo && (
            <TouchableOpacity 
              style={[styles.leanToEndWall, sceneSettings.wallSections.frontLeftLeanTo ? styles.activeWall : {}]} 
              onPress={() => handleWallSectionToggle('frontLeftLeanTo')}
            />
          )}
          
          <TouchableOpacity 
            style={[styles.frontWall, sceneSettings.wallSections.front ? styles.activeWall : {}, showBrickTexture && sceneSettings.wallSections.front ? styles.brickWall : {}]} 
            onPress={() => handleWallSectionToggle('front')}
          >
            <ThemedText style={styles.wallLabel}>Front</ThemedText>
          </TouchableOpacity>
          
          {hasRightLeanTo && (
            <TouchableOpacity 
              style={[styles.leanToEndWall, sceneSettings.wallSections.frontRightLeanTo ? styles.activeWall : {}]} 
              onPress={() => handleWallSectionToggle('frontRightLeanTo')}
            />
          )}
        </ThemedView>
      </ThemedView>
      
      <ThemedView style={styles.buttonContainer}>
        <TouchableOpacity 
          style={styles.button} 
          onPress={() => {
            // Set all wall sections to true
            const newLeft = Array(bay_no).fill(true);
            const newRight = Array(bay_no).fill(true);
            const newLeftLeanTo = Array(bay_no).fill(true);
            const newRightLeanTo = Array(bay_no).fill(true);
            
            setSceneSettings(prev => ({
              ...prev,
              wallSections: {
                front: true,
                back: true,
                frontLeftLeanTo: true,
                frontRightLeanTo: true,
                backLeftLeanTo: true,
                backRightLeanTo: true,
                left: newLeft,
                right: newRight,
                leftLeanTo: newLeftLeanTo,
                rightLeanTo: newRightLeanTo
              }
            }));
          }}
        >
          <ThemedText style={styles.buttonText}>Enclose</ThemedText>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.button} 
          onPress={() => {
            // Set all wall sections to false
            const newLeft = Array(bay_no).fill(false);
            const newRight = Array(bay_no).fill(false);
            const newLeftLeanTo = Array(bay_no).fill(false);
            const newRightLeanTo = Array(bay_no).fill(false);
            
            setSceneSettings(prev => ({
              ...prev,
              wallSections: {
                front: false,
                back: false,
                frontLeftLeanTo: false,
                frontRightLeanTo: false,
                backLeftLeanTo: false,
                backRightLeanTo: false,
                left: newLeft,
                right: newRight,
                leftLeanTo: newLeftLeanTo,
                rightLeanTo: newRightLeanTo
              }
            }));
          }}
        >
          <ThemedText style={styles.buttonText}>Open</ThemedText>
        </TouchableOpacity>
      </ThemedView>
    </ThemedView>
  );
  
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  instructions: {
    fontSize: 12,
    textAlign: 'center',
    marginBottom: 12,
    opacity: 0.7,
  },
  visualizer: {
    width: '100%',
    aspectRatio: 1.8, // Adjusted to accommodate lean-to sections
    borderRadius: 8,
    backgroundColor: 'white',
    padding: 12,
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backWall: {
    width: '80%',
    height: 24,
    backgroundColor: 'white',
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.2)',
  },
  frontWall: {
    width: '80%',
    height: 24,
    backgroundColor: 'white',
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.2)',
  },
  middleSection: {
    width: '100%',
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    marginVertical: 8,
  },
  sideWallContainer: {
    width: 24,
    height: '100%',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  sideWall: {
    width: 24,
    flex: 1,
    marginVertical: 2,
    backgroundColor: 'white',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.2)',
  },
  leanToContainer: {
    width: 24,
    height: '100%',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  leanToSpacer: {
    width: 8,
    height: '100%',
  },
  leanToWall: {
    width: 24,
    flex: 1,
    marginVertical: 2,
    backgroundColor: 'rgba(200, 200, 200, 0.3)',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.1)',
  },
  endWallRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  leanToEndWall: {
    width: 40,
    height: 24,
    backgroundColor: 'rgba(200, 200, 200, 0.3)',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.1)',
    marginHorizontal: 4,
  },
  centerSpace: {
    flex: 1,
    height: '100%',
    justifyContent: 'space-between',
    marginHorizontal: 8,
  },
  bayDivider: {
    width: '100%',
    height: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
  },
  activeWall: {
    backgroundColor: '#3b82f6',
    borderColor: '#3b82f6',
  },
  brickWall: {
    backgroundColor: '#c84a2e', // Updated terracotta color matching the Material.ts definition
    borderColor: '#a63a20',
    // Adding a subtle pattern effect with border styling
    borderWidth: 1,
    borderStyle: 'solid',
    borderRadius: 4,
  },
  wallLabel: {
    fontSize: 10,
    textAlign: 'center',
    color: '#666',
  },
  // Removed side label
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 12,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.1)',
  },
  button: {
    flex: 1,
    padding: 12,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'white',
  },
  buttonText: {
    fontSize: 14,
    color: '#666',
  },
  bayNumberContainer: {
    position: 'absolute',
    alignSelf: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    width: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
    display: 'none', // Hide bay numbers as they're not in the reference image
  },
  bayNumber: {
    fontSize: 10,
    color: 'rgba(0, 0, 0, 0.4)',
  },
});
