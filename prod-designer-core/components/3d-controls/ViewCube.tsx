import React from 'react';
import { View, StyleSheet, TouchableOpacity, Text } from 'react-native';
import { useSceneContext } from '@/redux/context';
import { ThemedView } from '@/components/themed/ThemedView';
import { useThemeColor } from '@/hooks/useThemeColor';

/**
 * ViewCube component provides a UI cube for orthographic view orientation
 * Clicking on different faces of the cube dispatches camera orientation events
 */
const ViewCube = () => {
  const { sceneSettings, setSceneSettings } = useSceneContext();
  const accent = useThemeColor({}, 'accentColor');
  const backgroundColor = useThemeColor({}, 'background');
  const textColor = useThemeColor({}, 'text');
  
  // Only show in orthographic mode and not in plan view
  if (sceneSettings.isPlanView || sceneSettings.defaultCamera !== 'orthographic') {
    return null;
  }
  
  // Function to set camera orientation via scene settings
  const setCameraOrientation = (orientation: string) => {
    // Store the desired orientation in scene settings
    // The actual camera positioning will be handled by the scene component
    setSceneSettings(prev => ({
      ...prev,
      cameraOrientation: orientation,
      activeOrientation: orientation // Store the active orientation
    }));
  };
  
  // Get the current active orientation
  const activeOrientation = sceneSettings.activeOrientation;
  
  return (
    <ThemedView style={styles.container}>
      <View style={styles.cubeContainer}>
        {/* Top row */}
        <View style={styles.row}>
          <TouchableOpacity 
            style={[styles.cubeButton, styles.cornerButton]} 
            onPress={() => {}}
          >
            <Text style={styles.buttonText}></Text>
          </TouchableOpacity>
          <TouchableOpacity 
            style={[styles.cubeButton, styles.faceButton, 
              activeOrientation === 'top' ? styles.activeButton : null]} 
            onPress={() => setCameraOrientation('top')}
          >
            <Text style={[styles.buttonText, 
              activeOrientation === 'top' ? styles.activeButtonText : null]}>Top</Text>
          </TouchableOpacity>
          <TouchableOpacity 
            style={[styles.cubeButton, styles.cornerButton]} 
            onPress={() => {}}
          >
            <Text style={styles.buttonText}></Text>
          </TouchableOpacity>
        </View>
        
        {/* Middle row */}
        <View style={styles.row}>
          <TouchableOpacity 
            style={[styles.cubeButton, styles.faceButton, 
              activeOrientation === 'left' ? styles.activeButton : null]} 
            onPress={() => setCameraOrientation('left')}
          >
            <Text style={[styles.buttonText, 
              activeOrientation === 'left' ? styles.activeButtonText : null]}>Left</Text>
          </TouchableOpacity>
          <TouchableOpacity 
            style={[styles.cubeButton, styles.centerButton, 
              activeOrientation === 'iso' ? styles.activeButton : null]} 
            onPress={() => setCameraOrientation('iso')}
          >
            <Text style={[styles.buttonText, 
              activeOrientation === 'iso' ? styles.activeButtonText : null]}>ISO</Text>
          </TouchableOpacity>
          <TouchableOpacity 
            style={[styles.cubeButton, styles.faceButton, 
              activeOrientation === 'right' ? styles.activeButton : null]} 
            onPress={() => setCameraOrientation('right')}
          >
            <Text style={[styles.buttonText, 
              activeOrientation === 'right' ? styles.activeButtonText : null]}>Right</Text>
          </TouchableOpacity>
        </View>
        
        {/* Bottom row */}
        <View style={styles.row}>
          <TouchableOpacity 
            style={[styles.cubeButton, styles.cornerButton]} 
            onPress={() => {}}
          >
            <Text style={styles.buttonText}></Text>
          </TouchableOpacity>
          <TouchableOpacity 
            style={[styles.cubeButton, styles.faceButton, 
              activeOrientation === 'front' ? styles.activeButton : null]} 
            onPress={() => setCameraOrientation('front')}
          >
            <Text style={[styles.buttonText, 
              activeOrientation === 'front' ? styles.activeButtonText : null]}>Front</Text>
          </TouchableOpacity>
          <TouchableOpacity 
            style={[styles.cubeButton, styles.cornerButton]} 
            onPress={() => {}}
          >
            <Text style={styles.buttonText}></Text>
          </TouchableOpacity>
        </View>
      </View>
    </ThemedView>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 420, // Position higher above the controls
    right: 20,
    zIndex: 100,
  },
  cubeContainer: {
    width: 120,
    height: 120,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    borderRadius: 8,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  row: {
    flexDirection: 'row',
    height: '33.33%',
  },
  cubeButton: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 0.5,
    borderColor: '#ccc',
  },
  faceButton: {
    backgroundColor: 'rgba(200, 200, 200, 0.5)',
  },
  cornerButton: {
    backgroundColor: 'rgba(180, 180, 180, 0.3)',
  },
  centerButton: {
    backgroundColor: 'rgba(100, 150, 200, 0.7)',
  },
  buttonText: {
    fontSize: 12,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  activeButton: {
    backgroundColor: '#4a90e2', // Highlight color for active button
    borderColor: '#2a70c2',
  },
  activeButtonText: {
    color: 'white',
  },
});

export default ViewCube;
