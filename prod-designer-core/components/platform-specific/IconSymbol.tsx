import React from 'react';
import { StyleProp, ViewStyle, OpaqueColorValue } from 'react-native';
import { SymbolWeight } from 'expo-symbols';
import { MaterialIcon, IconName } from '../icons/MaterialIcons';

// Re-export the IconName type from MaterialIcons
export type IconSymbolName = IconName;

export type IconSymbolProps = {
  name: IconSymbolName;
  size?: number;
  color: string | OpaqueColorValue;
  style?: StyleProp<ViewStyle>;
  weight?: SymbolWeight;
};

export function IconSymbol({ name, size = 24, color, style }: IconSymbolProps) {
  // Use the MaterialIcon component which renders inline SVG
  return (
    <MaterialIcon 
      name={name} 
      size={size} 
      color={color as string}
      style={style}
    />
  );
}
