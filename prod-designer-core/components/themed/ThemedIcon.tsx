import { OpaqueColorValue } from 'react-native';
import { useThemeColor } from '@/hooks/useThemeColor';
import { IconSymbol, IconSymbolProps } from '@/components/platform-specific/IconSymbol';

export type ThemedIconProps = Omit<IconSymbolProps, 'color'> & {
  lightColor?: string;
  darkColor?: string;
  color?: string | OpaqueColorValue;
  visible?: boolean;
};

export function ThemedIcon({
  style,
  lightColor,
  darkColor,
  color,
  visible = true,
  ...otherProps
}: ThemedIconProps) {
  const themedColor = useThemeColor({ light: lightColor, dark: darkColor }, 'text');

  if (!visible) {
    return null;
  }
  return <IconSymbol style={[style]} color={color ?? themedColor} {...otherProps} />;
}
