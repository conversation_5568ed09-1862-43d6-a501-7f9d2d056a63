import { View, type ViewProps } from 'react-native';

import { useThemeColor } from '@/hooks/useThemeColor';

export type ThemedViewProps = ViewProps & {
  lightColor?: string;
  darkColor?: string;
  visible?: boolean;
};

export function ThemedView({
  style,
  visible = true,
  lightColor,
  darkColor,
  ...otherProps
}: ThemedViewProps) {
  const backgroundColor = useThemeColor({ light: lightColor, dark: darkColor }, 'background');

  if (!visible) {
    return null;
  }

  return <View style={[{ backgroundColor }, style]} {...otherProps} />;
}
