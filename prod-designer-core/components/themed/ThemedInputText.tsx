import { TextInput, type TextInputProps, StyleSheet } from 'react-native';
import { useThemeColor } from '@/hooks/useThemeColor';

export type ThemedTextInputProps = TextInputProps & {
  lightColor?: string;
  darkColor?: string;
  type?: 'default' | 'title' | 'defaultSemiBold' | 'subtitle' | 'link';
  visible?: boolean;
};

export function ThemedTextInput({
  visible = true,
  style,
  lightColor,
  darkColor,
  type = 'default',
  ...rest
}: ThemedTextInputProps) {
  // Override with solid black text
  const textColor = '#fff';

  if (!visible) return null;

  return (
    <TextInput
      style={[
        {
          color: textColor,
          backgroundColor: 'red',
          borderWidth: 1,
          borderColor: 'darkred',
          borderRadius: 8,
          paddingHorizontal: 10,
          paddingVertical: 8,
          fontSize: 14,
          height: 32,
        },
        type === 'title' && styles.title,
        type === 'defaultSemiBold' && styles.defaultSemiBold,
        type === 'subtitle' && styles.subtitle,
        type === 'link' && styles.link,
        style,
      ]}
      placeholderTextColor="#fff"
      {...rest}
    />
  );
}


const styles = StyleSheet.create({
  defaultSemiBold: {
    fontWeight: '600',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    lineHeight: 34,
  },
  subtitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  link: {
    color: '#fff',
    textDecorationLine: 'underline',
  },
});
