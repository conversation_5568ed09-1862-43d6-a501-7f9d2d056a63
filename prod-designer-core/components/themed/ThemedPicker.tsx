import { useThemeColor } from '@/hooks/useThemeColor';
import { Picker, PickerProps } from '@react-native-picker/picker';

export type ThemedPickerProps<T> = PickerProps<T> & {
  lightColor?: string;
  darkColor?: string;
  visible?: boolean;
};

export function ThemedPicker<T>({
  style,
  lightColor,
  darkColor,
  children,
  visible = true,
  ...otherProps
}: ThemedPickerProps<T>) {
  const backgroundColor = useThemeColor({ light: lightColor, dark: darkColor }, 'background');
  const color = useThemeColor({ light: lightColor, dark: darkColor }, 'text');

  if (!visible) {
    return null;
  }
  return (
    <Picker style={[{ backgroundColor, color }, style]} {...otherProps}>
      {children}
    </Picker>
  );
}
