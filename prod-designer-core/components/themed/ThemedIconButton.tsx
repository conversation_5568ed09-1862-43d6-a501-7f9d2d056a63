import { Pressable, PressableProps, StyleProp, ViewStyle } from 'react-native';
import { ThemedIcon, ThemedIconProps } from '@/components/themed/ThemedIcon';
import { useThemeColor } from '@/hooks/useThemeColor';

export type ThemedIconButtonProps = Omit<PressableProps, 'style'> &
  Pick<ThemedIconProps, 'name'> & {
    style?: StyleProp<ViewStyle>;
  } & {
    visible?: boolean;
  };

export function ThemedIconButton({
  visible = true,
  name,
  onPress,
  style,
  ...props
}: ThemedIconButtonProps) {
  const backgroundColor = useThemeColor({}, 'background');
  if (!visible) {
    return null;
  }
  return (
    <Pressable onPress={onPress} style={[{ backgroundColor, padding: 8 }, style]} {...props}>
      <ThemedIcon size={24} name={name} />
    </Pressable>
  );
}
