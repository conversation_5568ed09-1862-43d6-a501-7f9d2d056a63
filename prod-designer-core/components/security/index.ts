// Security components and utilities
export {
  AccessControlProvider,
  SecurityGuard,
  useAccessControl,
  type SecurityConfig,
  type SecurityInfo,
  type SecurityContextType
} from './AccessControlProvider';

export {
  ReferrerGuard,
  useReferrerInfo,
  type ReferrerConfig,
  type ReferrerInfo
} from './ReferrerGuard';

export {
  SecurityHeaders,
  useSecurityHeaders,
  SecurityHeadersUtils,
  SECURITY_HEADER_PRESETS,
  type SecurityHeadersConfig
} from './SecurityHeaders';

export {
  SecurityManager,
  useSecurityManager,
  createEnvironmentConfig,
  type SecurityManagerConfig,
  type SecurityStatus
} from './SecurityManager';

export {
  getEnvironment,
  getSecurityConfig,
  SECURITY_PRESETS,
  mergeSecurityConfig,
  validateSecurityConfig,
  getConfigFromEnv,
  createSecurityConfig
} from './SecurityConfig';
