import React, { useEffect } from 'react';

// Security headers configuration
interface SecurityHeadersConfig {
  enableCSP: boolean;
  enableXFrameOptions: boolean;
  enableReferrerPolicy: boolean;
  enablePermissionsPolicy: boolean;
  allowedFrameAncestors: string[];
  cspDirectives: Record<string, string[]>;
  developmentMode: boolean;
  enableLogging: boolean;
}

// Default security headers configuration
const DEFAULT_HEADERS_CONFIG: SecurityHeadersConfig = {
  enableCSP: true,
  enableXFrameOptions: false, // Disabled because we want iframe embedding
  enableReferrerPolicy: true,
  enablePermissionsPolicy: true,
  allowedFrameAncestors: [
    'http://localhost:8080',
    'http://localhost:3000',
    'http://127.0.0.1:8080',
    'http://127.0.0.1:3000',
    'https://designmycarport.firebaseapp.com',
    'https://designmycarport.web.app',
    'https://website-463606.firebaseapp.com',
    'https://website-463606.web.app'
  ],
  cspDirectives: {
    'default-src': ["'self'"],
    'script-src': ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
    'style-src': ["'self'", "'unsafe-inline'"],
    'img-src': ["'self'", 'data:', 'blob:', 'https:'],
    'font-src': ["'self'", 'data:', 'https:'],
    'connect-src': ["'self'", 'https:', 'wss:', 'ws:'],
    'media-src': ["'self'", 'data:', 'blob:'],
    'object-src': ["'none'"],
    'base-uri': ["'self'"],
    'form-action': ["'self'"],
    'frame-ancestors': [] // Will be populated from allowedFrameAncestors
  },
  developmentMode: process.env.NODE_ENV === 'development',
  enableLogging: true
};

// Security headers utility functions
export const SecurityHeadersUtils = {
  // Build CSP header value
  buildCSPHeader: (config: SecurityHeadersConfig): string => {
    const directives = { ...config.cspDirectives };
    
    // Add frame-ancestors from allowedFrameAncestors
    if (config.allowedFrameAncestors.length > 0) {
      directives['frame-ancestors'] = ["'self'", ...config.allowedFrameAncestors];
    } else {
      directives['frame-ancestors'] = ["'none'"];
    }

    // Build CSP string
    const cspParts = Object.entries(directives).map(([directive, sources]) => {
      return `${directive} ${sources.join(' ')}`;
    });

    return cspParts.join('; ');
  },

  // Apply security headers via meta tags (for client-side)
  applyMetaHeaders: (config: SecurityHeadersConfig): void => {
    if (typeof document === 'undefined') return;

    // Remove existing security meta tags
    const existingTags = document.querySelectorAll('meta[data-security-header]');
    existingTags.forEach(tag => tag.remove());

    // Content Security Policy
    if (config.enableCSP) {
      const cspMeta = document.createElement('meta');
      cspMeta.setAttribute('http-equiv', 'Content-Security-Policy');
      cspMeta.setAttribute('content', SecurityHeadersUtils.buildCSPHeader(config));
      cspMeta.setAttribute('data-security-header', 'csp');
      document.head.appendChild(cspMeta);
    }

    // X-Frame-Options (if enabled - usually disabled for iframe embedding)
    if (config.enableXFrameOptions) {
      const xFrameMeta = document.createElement('meta');
      xFrameMeta.setAttribute('http-equiv', 'X-Frame-Options');
      xFrameMeta.setAttribute('content', 'DENY');
      xFrameMeta.setAttribute('data-security-header', 'x-frame-options');
      document.head.appendChild(xFrameMeta);
    }

    // Referrer Policy
    if (config.enableReferrerPolicy) {
      const referrerMeta = document.createElement('meta');
      referrerMeta.setAttribute('name', 'referrer');
      referrerMeta.setAttribute('content', 'strict-origin-when-cross-origin');
      referrerMeta.setAttribute('data-security-header', 'referrer-policy');
      document.head.appendChild(referrerMeta);
    }

    // Permissions Policy
    if (config.enablePermissionsPolicy) {
      const permissionsMeta = document.createElement('meta');
      permissionsMeta.setAttribute('http-equiv', 'Permissions-Policy');
      permissionsMeta.setAttribute('content', 'camera=(), microphone=(), geolocation=()');
      permissionsMeta.setAttribute('data-security-header', 'permissions-policy');
      document.head.appendChild(permissionsMeta);
    }

    if (config.enableLogging) {
      console.log('🔒 Security headers applied via meta tags');
    }
  },

  // Validate current security headers
  validateHeaders: (config: SecurityHeadersConfig): boolean => {
    if (typeof document === 'undefined') return false;

    let isValid = true;
    const issues: string[] = [];

    // Check CSP
    if (config.enableCSP) {
      const cspMeta = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
      if (!cspMeta) {
        issues.push('Missing Content-Security-Policy header');
        isValid = false;
      }
    }

    // Check frame-ancestors in CSP
    const cspContent = document.querySelector('meta[http-equiv="Content-Security-Policy"]')?.getAttribute('content');
    if (cspContent && !cspContent.includes('frame-ancestors')) {
      issues.push('CSP missing frame-ancestors directive');
      isValid = false;
    }

    if (config.enableLogging && issues.length > 0) {
      console.warn('🚫 Security header validation issues:', issues);
    }

    return isValid;
  },

  // Get current security status
  getSecurityStatus: (): Record<string, any> => {
    if (typeof window === 'undefined') return {};

    return {
      isInIframe: window !== window.top,
      origin: window.location.origin,
      referrer: document.referrer,
      userAgent: navigator.userAgent,
      timestamp: new Date().toISOString(),
      headers: {
        csp: document.querySelector('meta[http-equiv="Content-Security-Policy"]')?.getAttribute('content'),
        xFrameOptions: document.querySelector('meta[http-equiv="X-Frame-Options"]')?.getAttribute('content'),
        referrerPolicy: document.querySelector('meta[name="referrer"]')?.getAttribute('content'),
        permissionsPolicy: document.querySelector('meta[http-equiv="Permissions-Policy"]')?.getAttribute('content')
      }
    };
  }
};

// Security Headers Component
export const SecurityHeaders: React.FC<{
  config?: Partial<SecurityHeadersConfig>;
  onApplied?: (status: Record<string, any>) => void;
}> = ({ 
  config: userConfig = {},
  onApplied 
}) => {
  const config: SecurityHeadersConfig = { ...DEFAULT_HEADERS_CONFIG, ...userConfig };

  useEffect(() => {
    // Apply security headers
    SecurityHeadersUtils.applyMetaHeaders(config);

    // Validate headers
    const isValid = SecurityHeadersUtils.validateHeaders(config);
    
    // Get security status
    const status = SecurityHeadersUtils.getSecurityStatus();

    if (config.enableLogging) {
      console.log('🔒 Security Headers Status:', {
        valid: isValid,
        ...status
      });
    }

    // Call callback if provided
    if (onApplied) {
      onApplied({ valid: isValid, ...status });
    }

    // Cleanup function
    return () => {
      if (config.enableLogging) {
        console.log('🔒 Security headers cleanup');
      }
    };
  }, []);

  // This component doesn't render anything visible
  return null;
};

// Hook for security headers management
export const useSecurityHeaders = (config?: Partial<SecurityHeadersConfig>) => {
  const [status, setStatus] = React.useState<Record<string, any> | null>(null);
  const finalConfig: SecurityHeadersConfig = { ...DEFAULT_HEADERS_CONFIG, ...config };

  React.useEffect(() => {
    SecurityHeadersUtils.applyMetaHeaders(finalConfig);
    const currentStatus = SecurityHeadersUtils.getSecurityStatus();
    setStatus(currentStatus);
  }, []);

  const reapplyHeaders = () => {
    SecurityHeadersUtils.applyMetaHeaders(finalConfig);
    const currentStatus = SecurityHeadersUtils.getSecurityStatus();
    setStatus(currentStatus);
  };

  const validateHeaders = () => {
    return SecurityHeadersUtils.validateHeaders(finalConfig);
  };

  return {
    status,
    reapplyHeaders,
    validateHeaders,
    utils: SecurityHeadersUtils
  };
};

// Environment-specific configurations
export const SECURITY_HEADER_PRESETS = {
  DEVELOPMENT: {
    enableCSP: true,
    enableXFrameOptions: false,
    enableReferrerPolicy: true,
    enablePermissionsPolicy: false,
    developmentMode: true,
    enableLogging: true,
    cspDirectives: {
      'default-src': ["'self'"],
      'script-src': ["'self'", "'unsafe-inline'", "'unsafe-eval'", 'localhost:*'],
      'style-src': ["'self'", "'unsafe-inline'"],
      'img-src': ["'self'", 'data:', 'blob:', 'https:', 'http:'],
      'font-src': ["'self'", 'data:', 'https:', 'http:'],
      'connect-src': ["'self'", 'https:', 'http:', 'wss:', 'ws:', 'localhost:*'],
      'media-src': ["'self'", 'data:', 'blob:'],
      'object-src': ["'none'"],
      'base-uri': ["'self'"],
      'form-action': ["'self'"]
    }
  },

  PRODUCTION: {
    enableCSP: true,
    enableXFrameOptions: false,
    enableReferrerPolicy: true,
    enablePermissionsPolicy: true,
    developmentMode: false,
    enableLogging: false,
    cspDirectives: {
      'default-src': ["'self'"],
      'script-src': ["'self'"],
      'style-src': ["'self'", "'unsafe-inline'"],
      'img-src': ["'self'", 'data:', 'blob:', 'https:'],
      'font-src': ["'self'", 'data:', 'https:'],
      'connect-src': ["'self'", 'https:', 'wss:'],
      'media-src': ["'self'", 'data:', 'blob:'],
      'object-src': ["'none'"],
      'base-uri': ["'self'"],
      'form-action': ["'self'"]
    }
  }
};

// Export types
export type { SecurityHeadersConfig };
