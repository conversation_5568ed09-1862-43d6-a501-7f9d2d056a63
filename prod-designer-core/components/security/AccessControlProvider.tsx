import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { View, Text, StyleSheet } from 'react-native';

// Security configuration interface
interface SecurityConfig {
  allowedOrigins: string[];
  allowedReferrers: string[];
  blockDirectAccess: boolean;
  developmentMode: boolean;
  enableLogging: boolean;
}

// Security context interface
interface SecurityContextType {
  isAuthorized: boolean;
  isLoading: boolean;
  securityInfo: SecurityInfo;
  config: SecurityConfig;
}

// Security information interface
interface SecurityInfo {
  origin: string | null;
  referrer: string | null;
  userAgent: string | null;
  isInIframe: boolean;
  parentOrigin: string | null;
  accessMethod: 'direct' | 'iframe' | 'unknown';
  timestamp: string;
}

// Default security configuration
const DEFAULT_CONFIG: SecurityConfig = {
  allowedOrigins: [
    'http://localhost:8080',
    'http://localhost:3000',
    'http://127.0.0.1:8080',
    'http://127.0.0.1:3000',
    'https://designmycarport.firebaseapp.com',
    'https://designmycarport.web.app',
    'https://website-463606.firebaseapp.com',
    'https://website-463606.web.app'
  ],
  allowedReferrers: [
    'http://localhost:8080/',
    'http://localhost:3000/',
    'http://127.0.0.1:8080/',
    'http://127.0.0.1:3000/',
    'https://designmycarport.firebaseapp.com/',
    'https://designmycarport.web.app/',
    'https://website-463606.firebaseapp.com/',
    'https://website-463606.web.app/'
  ],
  blockDirectAccess: true,
  developmentMode: process.env.NODE_ENV === 'development',
  enableLogging: true
};

// Create security context
const SecurityContext = createContext<SecurityContextType | null>(null);

// Security provider component
export const AccessControlProvider: React.FC<{ children: ReactNode; config?: Partial<SecurityConfig> }> = ({ 
  children, 
  config: userConfig = {} 
}) => {
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [securityInfo, setSecurityInfo] = useState<SecurityInfo>({
    origin: null,
    referrer: null,
    userAgent: null,
    isInIframe: false,
    parentOrigin: null,
    accessMethod: 'unknown',
    timestamp: new Date().toISOString()
  });

  const config: SecurityConfig = { ...DEFAULT_CONFIG, ...userConfig };

  // Security validation function
  const validateAccess = (): boolean => {
    try {
      const info: SecurityInfo = {
        origin: window.location.origin,
        referrer: document.referrer || null,
        userAgent: navigator.userAgent || null,
        isInIframe: window !== window.top,
        parentOrigin: null,
        accessMethod: 'unknown',
        timestamp: new Date().toISOString()
      };

      // Detect if running in iframe
      if (info.isInIframe) {
        info.accessMethod = 'iframe';
        
        // Try to get parent origin safely
        try {
          if (window.parent && window.parent !== window) {
            // This might throw if cross-origin
            info.parentOrigin = window.parent.location.origin;
          }
        } catch (e) {
          // Cross-origin iframe - use referrer as fallback
          if (info.referrer) {
            try {
              const referrerUrl = new URL(info.referrer);
              info.parentOrigin = referrerUrl.origin;
            } catch (urlError) {
              if (config.enableLogging) {
                console.warn('Failed to parse referrer URL:', urlError);
              }
            }
          }
        }
      } else {
        info.accessMethod = 'direct';
      }

      setSecurityInfo(info);

      // Log security information if enabled
      if (config.enableLogging) {
        console.log('🔒 Security Check:', {
          accessMethod: info.accessMethod,
          isInIframe: info.isInIframe,
          origin: info.origin,
          referrer: info.referrer,
          parentOrigin: info.parentOrigin,
          timestamp: info.timestamp
        });
      }

      // Development mode - allow all access with warnings
      if (config.developmentMode) {
        if (info.accessMethod === 'direct' && config.blockDirectAccess) {
          console.warn('⚠️ Direct access detected in development mode - would be blocked in production');
        }
        return true;
      }

      // Production security checks
      if (config.blockDirectAccess && info.accessMethod === 'direct') {
        if (config.enableLogging) {
          console.error('🚫 Direct access blocked - application must be accessed through authorized iframe');
        }
        return false;
      }

      // Validate iframe access
      if (info.accessMethod === 'iframe') {
        // Check parent origin
        if (info.parentOrigin) {
          const isOriginAllowed = config.allowedOrigins.some(allowedOrigin => 
            info.parentOrigin === allowedOrigin || 
            info.parentOrigin?.startsWith(allowedOrigin)
          );
          
          if (!isOriginAllowed) {
            if (config.enableLogging) {
              console.error('🚫 Unauthorized parent origin:', info.parentOrigin);
            }
            return false;
          }
        }

        // Check referrer
        if (info.referrer) {
          const isReferrerAllowed = config.allowedReferrers.some(allowedReferrer => 
            info.referrer?.startsWith(allowedReferrer)
          );
          
          if (!isReferrerAllowed) {
            if (config.enableLogging) {
              console.error('🚫 Unauthorized referrer:', info.referrer);
            }
            return false;
          }
        }
      }

      return true;
    } catch (error) {
      if (config.enableLogging) {
        console.error('🚫 Security validation error:', error);
      }
      return false;
    }
  };

  // Run security check on mount
  useEffect(() => {
    const checkAccess = async () => {
      setIsLoading(true);
      
      // Small delay to ensure DOM is ready
      await new Promise(resolve => setTimeout(resolve, 100));
      
      const authorized = validateAccess();
      setIsAuthorized(authorized);
      setIsLoading(false);

      if (config.enableLogging) {
        console.log(`🔒 Access ${authorized ? 'GRANTED' : 'DENIED'} for ${securityInfo.accessMethod} access`);
      }
    };

    checkAccess();
  }, []);

  const contextValue: SecurityContextType = {
    isAuthorized,
    isLoading,
    securityInfo,
    config
  };

  return (
    <SecurityContext.Provider value={contextValue}>
      {children}
    </SecurityContext.Provider>
  );
};

// Hook to use security context
export const useAccessControl = (): SecurityContextType => {
  const context = useContext(SecurityContext);
  if (!context) {
    throw new Error('useAccessControl must be used within an AccessControlProvider');
  }
  return context;
};

// Security guard component that blocks unauthorized access
export const SecurityGuard: React.FC<{ children: ReactNode; fallback?: ReactNode }> = ({
  children,
  fallback
}) => {
  const { isAuthorized, isLoading, securityInfo, config } = useAccessControl();

  // Loading state
  if (isLoading) {
    return (
      <View style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>🔒 Validating Access...</Text>
          <Text style={styles.subText}>Checking security permissions</Text>
        </View>
      </View>
    );
  }

  // Unauthorized access
  if (!isAuthorized) {
    if (fallback) {
      return <>{fallback}</>;
    }

    return (
      <View style={styles.container}>
        <View style={styles.blockedContainer}>
          <Text style={styles.blockedTitle}>🚫 Access Denied</Text>
          <Text style={styles.blockedMessage}>
            This application must be accessed through an authorized parent application.
          </Text>
          <View style={styles.infoContainer}>
            <Text style={styles.infoTitle}>Security Information:</Text>
            <Text style={styles.infoText}>Access Method: {securityInfo.accessMethod}</Text>
            <Text style={styles.infoText}>Origin: {securityInfo.origin}</Text>
            <Text style={styles.infoText}>Referrer: {securityInfo.referrer || 'None'}</Text>
            <Text style={styles.infoText}>In Iframe: {securityInfo.isInIframe ? 'Yes' : 'No'}</Text>
            {securityInfo.parentOrigin && (
              <Text style={styles.infoText}>Parent Origin: {securityInfo.parentOrigin}</Text>
            )}
          </View>
          {config.developmentMode && (
            <View style={styles.devContainer}>
              <Text style={styles.devTitle}>Development Mode</Text>
              <Text style={styles.devText}>
                In production, direct access would be blocked.
                Access this application through the authorized iframe embedding.
              </Text>
            </View>
          )}
        </View>
      </View>
    );
  }

  // Authorized access
  return <>{children}</>;
};

// Styles
const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    padding: 20,
  },
  loadingContainer: {
    alignItems: 'center',
    padding: 30,
    backgroundColor: 'white',
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  loadingText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  subText: {
    fontSize: 14,
    color: '#666',
  },
  blockedContainer: {
    alignItems: 'center',
    padding: 30,
    backgroundColor: 'white',
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    maxWidth: 500,
  },
  blockedTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#d32f2f',
    marginBottom: 15,
  },
  blockedMessage: {
    fontSize: 16,
    color: '#333',
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 22,
  },
  infoContainer: {
    backgroundColor: '#f8f9fa',
    padding: 15,
    borderRadius: 8,
    width: '100%',
    marginBottom: 15,
  },
  infoTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#495057',
    marginBottom: 8,
  },
  infoText: {
    fontSize: 12,
    color: '#6c757d',
    marginBottom: 4,
    fontFamily: 'monospace',
  },
  devContainer: {
    backgroundColor: '#fff3cd',
    padding: 15,
    borderRadius: 8,
    width: '100%',
    borderLeftWidth: 4,
    borderLeftColor: '#ffc107',
  },
  devTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#856404',
    marginBottom: 8,
  },
  devText: {
    fontSize: 12,
    color: '#856404',
    lineHeight: 16,
  },
});

// Export types for external use
export type { SecurityConfig, SecurityInfo, SecurityContextType };
