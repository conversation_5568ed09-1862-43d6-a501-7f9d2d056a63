// Security configuration utilities
import { SecurityConfig } from './AccessControlProvider';

// Environment detection
export const getEnvironment = (): 'development' | 'staging' | 'production' => {
  if (process.env.NODE_ENV === 'production') {
    // Check if it's staging based on URL or environment variables
    if (typeof window !== 'undefined') {
      const hostname = window.location.hostname;
      if (hostname.includes('staging') || hostname.includes('test')) {
        return 'staging';
      }
    }
    return 'production';
  }
  return 'development';
};

// Get security configuration based on environment
export const getSecurityConfig = (environment?: string): Partial<SecurityConfig> => {
  const env = environment || getEnvironment();
  
  const baseConfig = {
    enableLogging: true,
    developmentMode: env === 'development'
  };

  switch (env) {
    case 'development':
      return {
        ...baseConfig,
        allowedOrigins: [
          'http://localhost:8080',
          'http://localhost:3000',
          'http://localhost:8084',
          'http://127.0.0.1:8080',
          'http://127.0.0.1:3000',
          'http://127.0.0.1:8084',
          // Add local network IPs for development
          'http://*************:8080',
          'http://*************:3000',
          'http://**********:8080',
          'http://**********:3000'
        ],
        allowedReferrers: [
          'http://localhost:8080/',
          'http://localhost:3000/',
          'http://localhost:8084/',
          'http://127.0.0.1:8080/',
          'http://127.0.0.1:3000/',
          'http://127.0.0.1:8084/',
          'http://*************:8080/',
          'http://*************:3000/',
          'http://**********:8080/',
          'http://**********:3000/'
        ],
        blockDirectAccess: false, // Allow direct access in development for testing
        developmentMode: true,
        enableLogging: true
      };

    case 'staging':
      return {
        ...baseConfig,
        allowedOrigins: [
          'https://staging.designmycarport.firebaseapp.com',
          'https://staging.designmycarport.web.app',
          'https://staging-website-463606.firebaseapp.com',
          'https://staging-website-463606.web.app',
          // Keep localhost for staging testing
          'http://localhost:8080',
          'http://localhost:3000'
        ],
        allowedReferrers: [
          'https://staging.designmycarport.firebaseapp.com/',
          'https://staging.designmycarport.web.app/',
          'https://staging-website-463606.firebaseapp.com/',
          'https://staging-website-463606.web.app/',
          'http://localhost:8080/',
          'http://localhost:3000/'
        ],
        blockDirectAccess: true,
        developmentMode: false,
        enableLogging: true
      };

    case 'production':
      return {
        ...baseConfig,
        allowedOrigins: [
          'https://designmycarport.firebaseapp.com',
          'https://designmycarport.web.app',
          'https://website-463606.firebaseapp.com',
          'https://website-463606.web.app',
          // Add your production domains here
          // 'https://yourdomain.com',
          // 'https://app.yourdomain.com'
        ],
        allowedReferrers: [
          'https://designmycarport.firebaseapp.com/',
          'https://designmycarport.web.app/',
          'https://website-463606.firebaseapp.com/',
          'https://website-463606.web.app/',
          // Add your production domains here
          // 'https://yourdomain.com/',
          // 'https://app.yourdomain.com/'
        ],
        blockDirectAccess: true,
        developmentMode: false,
        enableLogging: false // Disable detailed logging in production
      };

    default:
      return baseConfig;
  }
};

// Security configuration presets
export const SECURITY_PRESETS = {
  // Strict security - blocks all direct access
  STRICT: {
    blockDirectAccess: true,
    developmentMode: false,
    enableLogging: true
  },

  // Permissive security - allows direct access with warnings
  PERMISSIVE: {
    blockDirectAccess: false,
    developmentMode: true,
    enableLogging: true
  },

  // Development security - allows all access with detailed logging
  DEVELOPMENT: {
    blockDirectAccess: false,
    developmentMode: true,
    enableLogging: true
  },

  // Production security - strict access control with minimal logging
  PRODUCTION: {
    blockDirectAccess: true,
    developmentMode: false,
    enableLogging: false
  }
};

// Utility to merge security configurations
export const mergeSecurityConfig = (
  base: Partial<SecurityConfig>, 
  override: Partial<SecurityConfig>
): Partial<SecurityConfig> => {
  return {
    ...base,
    ...override,
    // Merge arrays instead of replacing them
    allowedOrigins: [
      ...(base.allowedOrigins || []),
      ...(override.allowedOrigins || [])
    ].filter((origin, index, array) => array.indexOf(origin) === index), // Remove duplicates
    allowedReferrers: [
      ...(base.allowedReferrers || []),
      ...(override.allowedReferrers || [])
    ].filter((referrer, index, array) => array.indexOf(referrer) === index) // Remove duplicates
  };
};

// Validate security configuration
export const validateSecurityConfig = (config: Partial<SecurityConfig>): boolean => {
  try {
    // Check required fields
    if (!config.allowedOrigins || config.allowedOrigins.length === 0) {
      console.warn('Security config missing allowedOrigins');
      return false;
    }

    if (!config.allowedReferrers || config.allowedReferrers.length === 0) {
      console.warn('Security config missing allowedReferrers');
      return false;
    }

    // Validate URL formats
    for (const origin of config.allowedOrigins) {
      try {
        new URL(origin);
      } catch (e) {
        console.warn(`Invalid origin URL: ${origin}`);
        return false;
      }
    }

    return true;
  } catch (error) {
    console.error('Security config validation error:', error);
    return false;
  }
};

// Get configuration from environment variables
export const getConfigFromEnv = (): Partial<SecurityConfig> => {
  if (typeof process === 'undefined') {
    return {};
  }

  const config: Partial<SecurityConfig> = {};

  // Parse allowed origins from environment
  if (process.env.ALLOWED_ORIGINS) {
    config.allowedOrigins = process.env.ALLOWED_ORIGINS.split(',').map(s => s.trim());
  }

  // Parse allowed referrers from environment
  if (process.env.ALLOWED_REFERRERS) {
    config.allowedReferrers = process.env.ALLOWED_REFERRERS.split(',').map(s => s.trim());
  }

  // Parse boolean flags
  if (process.env.BLOCK_DIRECT_ACCESS !== undefined) {
    config.blockDirectAccess = process.env.BLOCK_DIRECT_ACCESS === 'true';
  }

  if (process.env.ENABLE_SECURITY_LOGGING !== undefined) {
    config.enableLogging = process.env.ENABLE_SECURITY_LOGGING === 'true';
  }

  if (process.env.DEVELOPMENT_MODE !== undefined) {
    config.developmentMode = process.env.DEVELOPMENT_MODE === 'true';
  }

  return config;
};

// Create final security configuration
export const createSecurityConfig = (overrides?: Partial<SecurityConfig>): Partial<SecurityConfig> => {
  const envConfig = getConfigFromEnv();
  const environmentConfig = getSecurityConfig();
  
  let finalConfig = mergeSecurityConfig(environmentConfig, envConfig);
  
  if (overrides) {
    finalConfig = mergeSecurityConfig(finalConfig, overrides);
  }

  // Validate the final configuration
  if (!validateSecurityConfig(finalConfig)) {
    console.warn('Invalid security configuration, falling back to defaults');
    return getSecurityConfig('development');
  }

  return finalConfig;
};
