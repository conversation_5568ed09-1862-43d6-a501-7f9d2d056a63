import React, { ReactNode, useEffect, useState } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { AccessControlProvider, SecurityGuard, SecurityConfig } from './AccessControlProvider';
import { ReferrerGuard, ReferrerConfig } from './ReferrerGuard';
import { SecurityHeaders, SecurityHeadersConfig, SECURITY_HEADER_PRESETS } from './SecurityHeaders';
import { getEnvironment, createSecurityConfig, SECURITY_PRESETS } from './SecurityConfig';

// Comprehensive security configuration
interface SecurityManagerConfig {
  // Environment settings
  environment: 'development' | 'staging' | 'production';
  
  // Access control settings
  accessControl: Partial<SecurityConfig>;
  
  // Referrer validation settings
  referrerValidation: Partial<ReferrerConfig>;
  
  // Security headers settings
  securityHeaders: Partial<SecurityHeadersConfig>;
  
  // Feature flags
  enableAccessControl: boolean;
  enableReferrerValidation: boolean;
  enableSecurityHeaders: boolean;
  
  // Logging and monitoring
  enableLogging: boolean;
  enableMonitoring: boolean;
  
  // Development overrides
  developmentOverrides?: {
    bypassAccessControl?: boolean;
    bypassReferrerValidation?: boolean;
    allowDirectAccess?: boolean;
  };
}

// Default security manager configuration
const DEFAULT_SECURITY_MANAGER_CONFIG: SecurityManagerConfig = {
  environment: getEnvironment(),
  accessControl: {},
  referrerValidation: {},
  securityHeaders: {},
  enableAccessControl: true,
  enableReferrerValidation: true,
  enableSecurityHeaders: true,
  enableLogging: true,
  enableMonitoring: true,
  developmentOverrides: {
    bypassAccessControl: false,
    bypassReferrerValidation: false,
    allowDirectAccess: true // Allow direct access in development
  }
};

// Security status interface
interface SecurityStatus {
  accessControl: {
    enabled: boolean;
    authorized: boolean;
    info: any;
  };
  referrerValidation: {
    enabled: boolean;
    valid: boolean;
    info: any;
  };
  securityHeaders: {
    enabled: boolean;
    applied: boolean;
    info: any;
  };
  overall: {
    secure: boolean;
    issues: string[];
    timestamp: string;
  };
}

// Create environment-specific configurations
const createEnvironmentConfig = (environment: string): Partial<SecurityManagerConfig> => {
  switch (environment) {
    case 'development':
      return {
        environment: 'development',
        accessControl: {
          ...SECURITY_PRESETS.DEVELOPMENT,
          blockDirectAccess: false // Allow direct access in development
        },
        referrerValidation: {
          allowEmptyReferrer: true,
          strictMode: false,
          developmentMode: true
        },
        securityHeaders: SECURITY_HEADER_PRESETS.DEVELOPMENT,
        enableLogging: true,
        enableMonitoring: true,
        developmentOverrides: {
          bypassAccessControl: false,
          bypassReferrerValidation: false,
          allowDirectAccess: true
        }
      };

    case 'staging':
      return {
        environment: 'staging',
        accessControl: {
          ...SECURITY_PRESETS.STRICT,
          blockDirectAccess: true,
          developmentMode: false
        },
        referrerValidation: {
          allowEmptyReferrer: false,
          strictMode: true,
          developmentMode: false
        },
        securityHeaders: {
          ...SECURITY_HEADER_PRESETS.PRODUCTION,
          enableLogging: true
        },
        enableLogging: true,
        enableMonitoring: true
      };

    case 'production':
      return {
        environment: 'production',
        accessControl: SECURITY_PRESETS.PRODUCTION,
        referrerValidation: {
          allowEmptyReferrer: false,
          strictMode: true,
          developmentMode: false,
          enableLogging: false
        },
        securityHeaders: SECURITY_HEADER_PRESETS.PRODUCTION,
        enableLogging: false,
        enableMonitoring: true
      };

    default:
      return {};
  }
};

// Security Manager Component
export const SecurityManager: React.FC<{
  children: ReactNode;
  config?: Partial<SecurityManagerConfig>;
  onSecurityStatus?: (status: SecurityStatus) => void;
  fallback?: ReactNode;
}> = ({ 
  children, 
  config: userConfig = {},
  onSecurityStatus,
  fallback 
}) => {
  const [securityStatus, setSecurityStatus] = useState<SecurityStatus | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);

  // Merge configurations
  const environmentConfig = createEnvironmentConfig(userConfig.environment || getEnvironment());
  const config: SecurityManagerConfig = {
    ...DEFAULT_SECURITY_MANAGER_CONFIG,
    ...environmentConfig,
    ...userConfig
  };

  // Initialize security status
  useEffect(() => {
    const initializeStatus = () => {
      const status: SecurityStatus = {
        accessControl: {
          enabled: config.enableAccessControl,
          authorized: false,
          info: null
        },
        referrerValidation: {
          enabled: config.enableReferrerValidation,
          valid: false,
          info: null
        },
        securityHeaders: {
          enabled: config.enableSecurityHeaders,
          applied: false,
          info: null
        },
        overall: {
          secure: false,
          issues: [],
          timestamp: new Date().toISOString()
        }
      };

      setSecurityStatus(status);
      setIsInitialized(true);

      if (config.enableLogging) {
        console.log('🔒 Security Manager initialized:', {
          environment: config.environment,
          features: {
            accessControl: config.enableAccessControl,
            referrerValidation: config.enableReferrerValidation,
            securityHeaders: config.enableSecurityHeaders
          }
        });
      }
    };

    initializeStatus();
  }, []);

  // Update security status
  const updateSecurityStatus = (updates: Partial<SecurityStatus>) => {
    setSecurityStatus(prev => {
      if (!prev) return null;
      
      const newStatus = { ...prev, ...updates };
      
      // Calculate overall security status
      const issues: string[] = [];
      
      if (newStatus.accessControl.enabled && !newStatus.accessControl.authorized) {
        issues.push('Access control authorization failed');
      }
      
      if (newStatus.referrerValidation.enabled && !newStatus.referrerValidation.valid) {
        issues.push('Referrer validation failed');
      }
      
      if (newStatus.securityHeaders.enabled && !newStatus.securityHeaders.applied) {
        issues.push('Security headers not applied');
      }

      newStatus.overall = {
        secure: issues.length === 0,
        issues,
        timestamp: new Date().toISOString()
      };

      // Call status callback
      if (onSecurityStatus) {
        onSecurityStatus(newStatus);
      }

      if (config.enableLogging) {
        console.log('🔒 Security status updated:', {
          secure: newStatus.overall.secure,
          issues: newStatus.overall.issues
        });
      }

      return newStatus;
    });
  };

  // Security component wrapper
  const SecurityWrapper: React.FC<{ children: ReactNode }> = ({ children }) => {
    // Apply security headers first
    if (config.enableSecurityHeaders) {
      return (
        <>
          <SecurityHeaders
            config={config.securityHeaders}
            onApplied={(headerStatus) => {
              updateSecurityStatus({
                securityHeaders: {
                  enabled: true,
                  applied: headerStatus.valid,
                  info: headerStatus
                }
              });
            }}
          />
          {children}
        </>
      );
    }
    
    return <>{children}</>;
  };

  // Access control wrapper
  const AccessControlWrapper: React.FC<{ children: ReactNode }> = ({ children }) => {
    if (!config.enableAccessControl || config.developmentOverrides?.bypassAccessControl) {
      return <>{children}</>;
    }

    return (
      <AccessControlProvider config={config.accessControl}>
        <SecurityGuard fallback={fallback}>
          {children}
        </SecurityGuard>
      </AccessControlProvider>
    );
  };

  // Referrer validation wrapper
  const ReferrerWrapper: React.FC<{ children: ReactNode }> = ({ children }) => {
    if (!config.enableReferrerValidation || config.developmentOverrides?.bypassReferrerValidation) {
      return <>{children}</>;
    }

    return (
      <ReferrerGuard
        config={config.referrerValidation}
        fallback={fallback}
        onValidation={(referrerInfo) => {
          updateSecurityStatus({
            referrerValidation: {
              enabled: true,
              valid: referrerInfo.isValid,
              info: referrerInfo
            }
          });
        }}
      >
        {children}
      </ReferrerGuard>
    );
  };

  // Loading state
  if (!isInitialized) {
    return (
      <View style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>🔒 Initializing Security...</Text>
          <Text style={styles.subText}>Setting up security policies</Text>
        </View>
      </View>
    );
  }

  // Render with security layers
  return (
    <SecurityWrapper>
      <AccessControlWrapper>
        <ReferrerWrapper>
          {children}
        </ReferrerWrapper>
      </AccessControlWrapper>
    </SecurityWrapper>
  );
};

// Hook for security manager status
export const useSecurityManager = (config?: Partial<SecurityManagerConfig>) => {
  const [status, setStatus] = useState<SecurityStatus | null>(null);
  
  return {
    status,
    isSecure: status?.overall.secure || false,
    issues: status?.overall.issues || [],
    updateStatus: setStatus
  };
};

// Styles
const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    padding: 20,
  },
  loadingContainer: {
    alignItems: 'center',
    padding: 30,
    backgroundColor: 'white',
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  loadingText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  subText: {
    fontSize: 14,
    color: '#666',
  },
});

// Export types and utilities
export type { SecurityManagerConfig, SecurityStatus };
export { createEnvironmentConfig };
