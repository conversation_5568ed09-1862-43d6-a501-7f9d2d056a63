import React, { useEffect, useState, ReactNode } from 'react';
import { View, Text, StyleSheet } from 'react-native';

// Referrer validation configuration
interface ReferrerConfig {
  allowedReferrers: string[];
  allowEmptyReferrer: boolean;
  strictMode: boolean;
  developmentMode: boolean;
  enableLogging: boolean;
}

// Referrer information
interface ReferrerInfo {
  referrer: string | null;
  isValid: boolean;
  matchedPattern: string | null;
  validationMethod: 'exact' | 'prefix' | 'pattern' | 'empty' | 'none';
  timestamp: string;
}

// Default configuration
const DEFAULT_REFERRER_CONFIG: ReferrerConfig = {
  allowedReferrers: [
    'http://localhost:8080/',
    'http://localhost:3000/',
    'http://127.0.0.1:8080/',
    'http://127.0.0.1:3000/',
    'https://designmycarport.firebaseapp.com/',
    'https://designmycarport.web.app/',
    'https://website-463606.firebaseapp.com/',
    'https://website-463606.web.app/'
  ],
  allowEmptyReferrer: false, // Don't allow empty referrer in production
  strictMode: true, // Strict referrer validation
  developmentMode: process.env.NODE_ENV === 'development',
  enableLogging: true
};

// Referrer validation function
const validateReferrer = (config: ReferrerConfig): ReferrerInfo => {
  const referrer = typeof document !== 'undefined' ? document.referrer : null;
  const timestamp = new Date().toISOString();

  const info: ReferrerInfo = {
    referrer,
    isValid: false,
    matchedPattern: null,
    validationMethod: 'none',
    timestamp
  };

  // Log referrer information
  if (config.enableLogging) {
    console.log('🔍 Referrer Check:', {
      referrer: referrer || 'None',
      allowedReferrers: config.allowedReferrers,
      strictMode: config.strictMode,
      developmentMode: config.developmentMode
    });
  }

  // Handle empty referrer
  if (!referrer || referrer.trim() === '') {
    info.validationMethod = 'empty';
    
    if (config.allowEmptyReferrer || config.developmentMode) {
      info.isValid = true;
      if (config.enableLogging) {
        console.log('✅ Empty referrer allowed');
      }
    } else {
      info.isValid = false;
      if (config.enableLogging) {
        console.warn('⚠️ Empty referrer not allowed in strict mode');
      }
    }
    return info;
  }

  // Validate against allowed referrers
  for (const allowedReferrer of config.allowedReferrers) {
    // Exact match
    if (referrer === allowedReferrer) {
      info.isValid = true;
      info.matchedPattern = allowedReferrer;
      info.validationMethod = 'exact';
      break;
    }

    // Prefix match (most common for referrers)
    if (referrer.startsWith(allowedReferrer)) {
      info.isValid = true;
      info.matchedPattern = allowedReferrer;
      info.validationMethod = 'prefix';
      break;
    }

    // Pattern match for wildcards (if allowed referrer contains *)
    if (allowedReferrer.includes('*')) {
      const pattern = allowedReferrer
        .replace(/\*/g, '.*')
        .replace(/\./g, '\\.');
      
      const regex = new RegExp(`^${pattern}$`);
      if (regex.test(referrer)) {
        info.isValid = true;
        info.matchedPattern = allowedReferrer;
        info.validationMethod = 'pattern';
        break;
      }
    }
  }

  // Development mode override
  if (!info.isValid && config.developmentMode && !config.strictMode) {
    info.isValid = true;
    info.validationMethod = 'none';
    if (config.enableLogging) {
      console.warn('⚠️ Referrer validation bypassed in development mode');
    }
  }

  // Log validation result
  if (config.enableLogging) {
    if (info.isValid) {
      console.log(`✅ Referrer validated: ${info.validationMethod} match with ${info.matchedPattern || 'development override'}`);
    } else {
      console.error(`🚫 Referrer validation failed: ${referrer} not in allowed list`);
    }
  }

  return info;
};

// Referrer Guard Component
export const ReferrerGuard: React.FC<{
  children: ReactNode;
  config?: Partial<ReferrerConfig>;
  fallback?: ReactNode;
  onValidation?: (info: ReferrerInfo) => void;
}> = ({ 
  children, 
  config: userConfig = {}, 
  fallback,
  onValidation 
}) => {
  const [referrerInfo, setReferrerInfo] = useState<ReferrerInfo | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const config: ReferrerConfig = { ...DEFAULT_REFERRER_CONFIG, ...userConfig };

  useEffect(() => {
    const checkReferrer = () => {
      setIsLoading(true);
      
      // Small delay to ensure document is ready
      setTimeout(() => {
        const info = validateReferrer(config);
        setReferrerInfo(info);
        setIsLoading(false);
        
        // Call validation callback if provided
        if (onValidation) {
          onValidation(info);
        }
      }, 50);
    };

    checkReferrer();
  }, []);

  // Loading state
  if (isLoading) {
    return (
      <View style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>🔍 Checking Referrer...</Text>
          <Text style={styles.subText}>Validating request source</Text>
        </View>
      </View>
    );
  }

  // Invalid referrer
  if (!referrerInfo?.isValid) {
    if (fallback) {
      return <>{fallback}</>;
    }

    return (
      <View style={styles.container}>
        <View style={styles.blockedContainer}>
          <Text style={styles.blockedTitle}>🚫 Invalid Request Source</Text>
          <Text style={styles.blockedMessage}>
            This application must be accessed from an authorized source.
          </Text>
          <View style={styles.infoContainer}>
            <Text style={styles.infoTitle}>Referrer Information:</Text>
            <Text style={styles.infoText}>
              Current Referrer: {referrerInfo?.referrer || 'None'}
            </Text>
            <Text style={styles.infoText}>
              Validation Method: {referrerInfo?.validationMethod}
            </Text>
            <Text style={styles.infoText}>
              Matched Pattern: {referrerInfo?.matchedPattern || 'None'}
            </Text>
            <Text style={styles.infoText}>
              Timestamp: {referrerInfo?.timestamp}
            </Text>
          </View>
          <View style={styles.allowedContainer}>
            <Text style={styles.allowedTitle}>Allowed Sources:</Text>
            {config.allowedReferrers.map((referrer, index) => (
              <Text key={index} style={styles.allowedText}>• {referrer}</Text>
            ))}
          </View>
          {config.developmentMode && (
            <View style={styles.devContainer}>
              <Text style={styles.devTitle}>Development Mode</Text>
              <Text style={styles.devText}>
                Referrer validation is active. In production, unauthorized sources will be blocked.
              </Text>
            </View>
          )}
        </View>
      </View>
    );
  }

  // Valid referrer - render children
  return <>{children}</>;
};

// Hook for referrer information
export const useReferrerInfo = (config?: Partial<ReferrerConfig>): ReferrerInfo | null => {
  const [referrerInfo, setReferrerInfo] = useState<ReferrerInfo | null>(null);
  const finalConfig: ReferrerConfig = { ...DEFAULT_REFERRER_CONFIG, ...config };

  useEffect(() => {
    const info = validateReferrer(finalConfig);
    setReferrerInfo(info);
  }, []);

  return referrerInfo;
};

// Styles
const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    padding: 20,
  },
  loadingContainer: {
    alignItems: 'center',
    padding: 30,
    backgroundColor: 'white',
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  loadingText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  subText: {
    fontSize: 14,
    color: '#666',
  },
  blockedContainer: {
    alignItems: 'center',
    padding: 30,
    backgroundColor: 'white',
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    maxWidth: 600,
  },
  blockedTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#d32f2f',
    marginBottom: 15,
  },
  blockedMessage: {
    fontSize: 16,
    color: '#333',
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 22,
  },
  infoContainer: {
    backgroundColor: '#f8f9fa',
    padding: 15,
    borderRadius: 8,
    width: '100%',
    marginBottom: 15,
  },
  infoTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#495057',
    marginBottom: 8,
  },
  infoText: {
    fontSize: 12,
    color: '#6c757d',
    marginBottom: 4,
    fontFamily: 'monospace',
  },
  allowedContainer: {
    backgroundColor: '#e8f5e8',
    padding: 15,
    borderRadius: 8,
    width: '100%',
    marginBottom: 15,
  },
  allowedTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#2e7d32',
    marginBottom: 8,
  },
  allowedText: {
    fontSize: 12,
    color: '#388e3c',
    marginBottom: 2,
    fontFamily: 'monospace',
  },
  devContainer: {
    backgroundColor: '#fff3cd',
    padding: 15,
    borderRadius: 8,
    width: '100%',
    borderLeftWidth: 4,
    borderLeftColor: '#ffc107',
  },
  devTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#856404',
    marginBottom: 8,
  },
  devText: {
    fontSize: 12,
    color: '#856404',
    lineHeight: 16,
  },
});

// Export types
export type { ReferrerConfig, ReferrerInfo };
