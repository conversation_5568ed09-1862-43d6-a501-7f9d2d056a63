import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  PanResponder,
  Animated,
  StyleSheet,
  StyleProp,
  ViewStyle,
  LayoutChangeEvent,
  Platform,
} from 'react-native';
import { ThemedView } from '@/components/themed/ThemedView';
import { clamp } from '@/utils/math';

type SliderProps = {
  value?: number;
  min?: number;
  max?: number;
  step?: number;
  onChange?: (v: number) => void;
  style?: StyleProp<ViewStyle>;
};

const Slider = ({
  value = 0,
  min = 0,
  max = 100,
  onChange = (v: number) => void 0,
  style,
}: SliderProps) => {
  const [pan, setPan] = useState(new Animated.ValueXY({ x: 0, y: 0 }));
  const [sliderWidth, setSliderWidth] = useState(0);
  const [xOffset, setXOffset] = useState(0);
  const [isDragging, setIsDragging] = useState(false);

  const ref = useRef<View>(null!);

  const thumbWidth = 20;

  const panResponder = PanResponder.create({
    onStartShouldSetPanResponder: () => true,
    onMoveShouldSetPanResponder: () => true,
    onPanResponderGrant: () => {
      setIsDragging(true);
    },
    onPanResponderMove: (e, gestureState) => {
      e.preventDefault?.();
      const moveX = gestureState.moveX - xOffset;
      const trackWidth = sliderWidth - thumbWidth;
      const clampedMoveX = clamp(moveX, 0, trackWidth);
      const newSliderValue = min + (clampedMoveX / trackWidth) * (max - min);
      onChange && onChange(newSliderValue);
    },
    onPanResponderRelease: () => {
      setIsDragging(false);
    },
  });

  const onLayout = (e: LayoutChangeEvent) => {
    setSliderWidth(e.nativeEvent.layout.width);
    ref.current.measure((x, y, w, h, px, py) => {
      setXOffset(px);
    });
  };

  useEffect(() => {
    const trackWidth = sliderWidth - thumbWidth;
    const percentage = (value - min) / (max - min);
    const clampedX = clamp(percentage * trackWidth, 0, trackWidth);
    pan.setValue({ x: clampedX, y: 0 });
  }, [sliderWidth, value, max, min]);

  useEffect(() => {
    if (Platform.OS === 'web') {
      if (isDragging) {
        document.body.style.userSelect = 'none';
      } else {
        document.body.style.userSelect = '';
      }
      return () => {
        document.body.style.userSelect = '';
      };
    }
  }, [isDragging]);

  return (
    <View ref={ref} style={[style, styles.sliderContainer]} onLayout={onLayout}>
      <ThemedView style={styles.track}>
        <Animated.View
          style={[
            styles.thumb,
            { left: pan.x },
            isDragging && Platform.OS === 'web' ? ({ userSelect: 'none' } as any) : {},
          ]}
          {...panResponder.panHandlers}
        />
      </ThemedView>
    </View>
  );
};

const styles = StyleSheet.create({
  sliderContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  track: {
    height: 10,
    alignSelf: 'stretch',
    backgroundColor: '#ccc',
    borderRadius: 5,
    position: 'relative',
  },
  thumb: {
    width: 20,
    height: 20,
    backgroundColor: '#4B5563',
    borderRadius: 10,
    position: 'absolute',
    top: -5,
    borderWidth: 1,
    borderColor: '#fff',
  },
});

export default Slider;
