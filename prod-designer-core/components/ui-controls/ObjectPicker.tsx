import React from 'react';
import { View, StyleSheet, TouchableOpacity, Image, ScrollView } from 'react-native';
import { ThemedView } from '@/components/themed/ThemedView';
import { ThemedText } from '@/components/themed/ThemedText';

export interface ObjectOption {
  id: string;
  name: string;
  imageUrl: string;
}

interface ObjectPickerProps {
  options: ObjectOption[];
  selectedObjectId: string | null;
  onSelectObject: (objectId: string) => void;
}

export function ObjectPicker({ options, selectedObjectId, onSelectObject }: ObjectPickerProps) {
  return (
    <ThemedView style={styles.container}>
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.scrollView}>
        {options.map((option) => (
          <TouchableOpacity
            key={option.id}
            style={[
              styles.objectItem,
              selectedObjectId === option.id && styles.selectedItem
            ]}
            onPress={() => onSelectObject(option.id)}
          >
            <Image
              source={{ uri: option.imageUrl }}
              style={styles.objectImage}
              resizeMode="contain"
            />
            <ThemedText style={styles.objectName}>{option.name}</ThemedText>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
  },
  scrollView: {
    flexDirection: 'row',
  },
  objectItem: {
    width: 100,
    height: 120,
    marginRight: 12,
    padding: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.2)',
  },
  selectedItem: {
    borderColor: '#3b82f6',
    borderWidth: 2,
    backgroundColor: 'rgba(59, 130, 246, 0.1)',
  },
  objectImage: {
    width: 80,
    height: 80,
    marginBottom: 4,
  },
  objectName: {
    fontSize: 12,
    textAlign: 'center',
  },
});
