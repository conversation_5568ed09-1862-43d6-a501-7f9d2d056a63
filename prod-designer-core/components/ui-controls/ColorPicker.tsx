import React from 'react';
import { View, StyleSheet, TouchableOpacity, FlatList, Text } from 'react-native';
import { ThemedView } from '@/components/themed/ThemedView';
import { ThemedText } from '@/components/themed/ThemedText';
import { ColorbondColor } from '@/constants/colors/colorbondColors';

import { ColorSwatch3D } from '../designer-frame/ColorSwatch3D';

interface ColorPickerProps {
  label: string;
  colors: ColorbondColor[];
  selectedValue: string;
  onValueChange: (value: string) => void;
}

export function ColorPicker({ label, colors, selectedValue, onValueChange }: ColorPickerProps) {
  function brightenColor(hex: string, percent: number): string {
    const num = parseInt(hex.replace('#', ''), 16);
    const amt = Math.round(2.55 * percent);
    const R = (num >> 16) + amt;
    const G = ((num >> 8) & 0x00ff) + amt;
    const B = (num & 0x0000ff) + amt;
    return (
      '#' +
      (
        0x1000000 +
        (R < 255 ? (R < 1 ? 0 : R) : 255) * 0x10000 +
        (G < 255 ? (G < 1 ? 0 : G) : 255) * 0x100 +
        (B < 255 ? (B < 1 ? 0 : B) : 255)
      )
        .toString(16)
        .slice(1)
    );
  }

  return (
    <ThemedView style={styles.container}>
      <ThemedText style={styles.label}>{label}</ThemedText>
      <FlatList
        data={colors}
        keyExtractor={(item) => item.value}
        numColumns={3}
        columnWrapperStyle={styles.row}
        contentContainerStyle={styles.grid}
        renderItem={({ item }) => {
          const isSelected = item.value === selectedValue;

          return (
            <TouchableOpacity
              style={[styles.swatchContainer, isSelected && styles.selected]}
              onPress={() => onValueChange(item.value)}
            >
              <ColorSwatch3D color={brightenColor(item.hex, 15)} />
              <Text style={styles.swatchLabel}>{item.name}</Text>
            </TouchableOpacity>
          );
        }}
      />
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
    backgroundColor: '#e6e6e6',
    maxHeight: 360,
  },
  label: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
    marginLeft: 4,
    color: '#000'
  },
  grid: {
    paddingHorizontal: 4,
  },
  row: {
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  swatchContainer: {
    alignItems: 'center',
    width: '30%',
  },
  swatch: {
    width: 48,
    height: 48,
    borderRadius: 24,
    marginBottom: 6,
    justifyContent: 'center',
    alignItems: 'center',

    // Simulated lighting
    backgroundColor: '#ccc', // fallback
    shadowColor: '#000',
    shadowOffset: { width: 1, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
    elevation: 5,

    // Metal-like border
    borderWidth: 1,
    borderColor: '#999',

    // Inner fake gradient highlight
    overflow: 'hidden',
  },

  swatchLabel: {
    fontSize: 12,
    textAlign: 'center',
  },
  selected: {
    borderColor: '#2196f3',
    borderWidth: 2,
  },
});
