import React, { useState, useEffect } from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { ThemedView } from '@/components/themed/ThemedView';
import { ThemedText } from '@/components/themed/ThemedText';
import { useSceneContext } from '@/redux/context';

export function PriceIndicator() {
  const { dimensions, sceneSettings } = useSceneContext();
  const [expanded, setExpanded] = useState(false);
  
  // Calculate a basic indicative price based on dimensions and options
  const calculateIndicativePrice = () => {
    const basePrice = 5000; // Base price for a standard carport
    const areaPrice = (dimensions.length * dimensions.span) / 1000000 * 500; // Price per square meter
    
    // Additional costs for options
    let additionalCosts = 0;
    
    // Add cost for gable roof (if applicable)
    if (dimensions.roofType === 'Gable') {
      additionalCosts += 1500;
    }
    
    // Add cost for lean-tos (if applicable)
    if (sceneSettings.leftLeanTo) {
      additionalCosts += (dimensions.length * (sceneSettings.leftLeanToSpan || 1000)) / 1000000 * 300;
    }
    if (sceneSettings.rightLeanTo) {
      additionalCosts += (dimensions.length * (sceneSettings.rightLeanToSpan || 1000)) / 1000000 * 300;
    }
    
    const totalPrice = basePrice + areaPrice + additionalCosts;
    return Math.round(totalPrice / 100) * 100; // Round to nearest $100
  };
  
  const indicativePrice = calculateIndicativePrice();
  
  return (
    <ThemedView style={styles.container}>
      <TouchableOpacity 
        style={styles.priceTab} 
        onPress={() => setExpanded(!expanded)}
      >
        <ThemedText style={styles.priceText}>
          ${indicativePrice.toLocaleString()}
        </ThemedText>
        <ThemedText style={styles.indicativeText}>Indicative Price</ThemedText>
      </TouchableOpacity>
      
      {expanded && (
        <ThemedView style={styles.detailsContainer}>
          <ThemedText style={styles.detailsTitle}>Price Breakdown</ThemedText>
          
          <View style={styles.detailRow}>
            <ThemedText style={styles.detailLabel}>Base Price:</ThemedText>
            <ThemedText style={styles.detailValue}>$5,000</ThemedText>
          </View>
          
          <View style={styles.detailRow}>
            <ThemedText style={styles.detailLabel}>Size Cost:</ThemedText>
            <ThemedText style={styles.detailValue}>
              ${Math.round((dimensions.length * dimensions.span) / 1000000 * 500).toLocaleString()}
            </ThemedText>
          </View>
          
          {dimensions.roofType === 'Gable' && (
            <View style={styles.detailRow}>
              <ThemedText style={styles.detailLabel}>Gable Roof:</ThemedText>
              <ThemedText style={styles.detailValue}>$1,500</ThemedText>
            </View>
          )}
          
          {sceneSettings.leftLeanTo && (
            <View style={styles.detailRow}>
              <ThemedText style={styles.detailLabel}>Left Lean-to:</ThemedText>
              <ThemedText style={styles.detailValue}>
                ${Math.round((dimensions.length * (sceneSettings.leftLeanToSpan || 1000)) / 1000000 * 300).toLocaleString()}
              </ThemedText>
            </View>
          )}
          
          {sceneSettings.rightLeanTo && (
            <View style={styles.detailRow}>
              <ThemedText style={styles.detailLabel}>Right Lean-to:</ThemedText>
              <ThemedText style={styles.detailValue}>
                ${Math.round((dimensions.length * (sceneSettings.rightLeanToSpan || 1000)) / 1000000 * 300).toLocaleString()}
              </ThemedText>
            </View>
          )}
          
          <View style={styles.totalRow}>
            <ThemedText style={styles.totalLabel}>Total:</ThemedText>
            <ThemedText style={styles.totalValue}>${indicativePrice.toLocaleString()}</ThemedText>
          </View>
          
          <ThemedText style={styles.disclaimer}>
            * This is an indicative price only. Contact us for an accurate quote.
          </ThemedText>
        </ThemedView>
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    zIndex: 100,
  },
  priceTab: {
    backgroundColor: '#3b82f6',
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  priceText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
  },
  indicativeText: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  detailsContainer: {
    position: 'absolute',
    bottom: 80,
    right: 0,
    width: 280,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    borderRadius: 8,
    padding: 16,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  detailsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
    textAlign: 'center',
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  detailLabel: {
    fontSize: 14,
    opacity: 0.8,
  },
  detailValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.2)',
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  totalValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#3b82f6',
  },
  disclaimer: {
    fontSize: 10,
    opacity: 0.7,
    marginTop: 12,
    textAlign: 'center',
    fontStyle: 'italic',
  },
});
