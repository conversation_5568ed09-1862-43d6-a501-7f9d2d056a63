import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import * as Localization from 'expo-localization';

import en from './locales/en/translation.json';
import zh from './locales/zh/translation.json';
import ko from './locales/ko/translation.json';
import ja from './locales/ja/translation.json';

i18n
  .use(initReactI18next)
  .init({
    // lng: Localization.locale.split('-')[0],
    lng: 'en',
    fallbackLng: 'en',
    resources: {
      en: { translation: en },
      zh: { translation: zh },
      ko: { translation: ko },
      ja: { translation: ja },
    },
    interpolation: {
      escapeValue: false,
    },
  });

export default i18n;

