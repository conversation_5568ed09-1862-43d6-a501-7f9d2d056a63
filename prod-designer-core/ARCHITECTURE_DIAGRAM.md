# DesignerCore - Ultra-Deep Architectural Diagram

## System Overview

```mermaid
graph TB
    subgraph "User Interface Layer"
        UI[Mobile/Tablet/Desktop UI]
        UI --> Responsive[Responsive Layout System]
        Responsive --> Controls[Control Panels]
        Responsive --> Canvas[3D Canvas]
    end
    
    subgraph "Application Layer"
        App[App Entry Point]
        App --> Router[Expo Router]
        Router --> Context[SceneContextProvider]
        Context --> State[Global State Management]
    end
    
    subgraph "3D Rendering Layer"
        Canvas --> R3F[React Three Fiber]
        R3F --> Scene[3D Scene]
        Scene --> Models[Carport Models]
        Scene --> Environment[Environment]
        Scene --> Lights[Lighting System]
    end
    
    subgraph "Business Logic Layer"
        State --> Dimensions[Dimension State]
        State --> Settings[Scene Settings]
        Dimensions --> Validation[Business Rules]
        Settings --> Materials[Material System]
    end
    
    subgraph "Data Layer"
        Export[Export/Share]
        Import[Import/Load]
        Export --> Encryption[Model Encryption]
        Import --> Decryption[Model Decryption]
    end
```

## Detailed Component Architecture

```mermaid
graph TB
    subgraph "Entry Point"
        Index[app/index.tsx]
        Index --> SceneProvider[SceneContextProvider]
    end
    
    subgraph "Layout System"
        SceneProvider --> Layout{Screen Size?}
        Layout -->|Mobile/Tablet| MobileLayout[Column Layout]
        Layout -->|Desktop| DesktopLayout[Row Layout]
        
        MobileLayout --> MobileCanvas[Canvas3D Top]
        MobileLayout --> MobileControls[Controls Bottom]
        
        DesktopLayout --> DesktopControls[Controls Left]
        DesktopLayout --> DesktopCanvas[Canvas3D Right]
        DesktopLayout --> FloatingControls[Floating Controls]
    end
    
    subgraph "Control Panels"
        NewControls[NewCanvasControls]
        NewControls --> Size[SizePanel]
        NewControls --> LeanTo[LeanToPanel]
        NewControls --> Walls[WallPanel]
        NewControls --> Extras[ExtrasPanel]
        NewControls --> Colors[ColoursPanel]
        NewControls --> Quote[QuotePanel]
        
        Size --> SizeInputs[Length/Height/Span/Pitch]
        Colors --> ColorPicker[Colorbond Colors]
        Quote --> PriceCalc[Price Calculation]
    end
    
    subgraph "3D Scene"
        Canvas3D --> Scene3D[Scene Component]
        Scene3D --> Camera[Camera System]
        Scene3D --> Lighting[Lighting Setup]
        Scene3D --> CarportModel[Carport Rendering]
        Scene3D --> Measurements[Measurement Display]
        Scene3D --> Environment3D[Environment]
        
        Camera --> PerspectiveCamera
        Camera --> OrthographicCamera
        Camera --> OrbitControls
        Camera --> ViewCube
        
        Lighting --> AmbientLight
        Lighting --> DirectionalLight
        Lighting --> ShadowMapping[Shadow Maps]
        
        Environment3D --> SkyBox
        Environment3D --> Grass[Ground Plane]
    end
```

## State Management Architecture

```mermaid
graph TB
    subgraph "Context System"
        SceneContext[SceneContextProvider]
        SceneContext --> ContextValue{Context Value}
        
        ContextValue --> Dimensions[dimensions: CarportState]
        ContextValue --> Settings[sceneSettings: SceneSettings]
        ContextValue --> Dispatch[dispatch: Function]
        ContextValue --> CanvasRef[ref: Canvas Reference]
    end
    
    subgraph "CarportState"
        Dimensions --> Length[length: number]
        Dimensions --> Height[height: number]
        Dimensions --> Span[span: number]
        Dimensions --> Pitch[pitch: number]
        Dimensions --> Overhang[overhang: number]
        Dimensions --> RoofType[roofType: enum]
    end
    
    subgraph "SceneSettings (72 Properties)"
        Settings --> ViewMode[viewMode: normal/transparent/frame]
        Settings --> Camera[defaultCamera: perspective/orthographic]
        Settings --> LeanTo[leftLeanTo/rightLeanTo: boolean]
        Settings --> Colors[roofColor/wallColor/trimColor]
        Settings --> Walls[wallSections: WallMatrixType]
        Settings --> Extras[gridView/showSlab/showMeasurements]
    end
    
    subgraph "State Updates"
        UserInput[User Input] --> Validation[Input Validation]
        Validation --> Dispatch
        Dispatch --> Reducer[State Reducer]
        Reducer --> StateUpdate[State Update]
        StateUpdate --> Rerender[Component Re-render]
        Rerender --> ThreeJS[3D Scene Update]
    end
```

## 3D Rendering Pipeline

```mermaid
graph TB
    subgraph "React Three Fiber"
        Canvas[R3F Canvas]
        Canvas --> FrameLoop[Frame Loop: 'demand']
        Canvas --> GLContext[WebGL Context]
        Canvas --> SceneGraph[Scene Graph]
    end
    
    subgraph "Scene Composition"
        SceneGraph --> RootScene[Root Scene]
        RootScene --> CameraGroup[Camera Group]
        RootScene --> LightGroup[Light Group]
        RootScene --> ModelGroup[Model Group]
        RootScene --> UIGroup[UI Group]
        
        CameraGroup --> CameraControls[Camera Controls]
        CameraGroup --> CameraPosition[Auto Positioning]
        
        LightGroup --> AmbientLight[Ambient Light: 0.5]
        LightGroup --> DirectionalLight[Directional Light + Shadows]
        
        ModelGroup --> CarportGeometry[Carport Geometry]
        ModelGroup --> CarModel[Car Model]
        ModelGroup --> EnvironmentObjects[Environment Objects]
        
        UIGroup --> MeasurementText[3D Measurement Text]
        UIGroup --> ViewCube[View Cube]
    end
    
    subgraph "Geometry Generation"
        CarportGeometry --> RoofTypeCheck{Roof Type?}
        RoofTypeCheck -->|Gable| GableCarport[Gable Carport]
        RoofTypeCheck -->|Skillion| SkilliCarport[Skillion Carport]
        
        GableCarport --> GableGeometry[Gable Geometry Calculation]
        SkilliCarport --> SkilliGeometry[Skillion Geometry Calculation]
        
        GableGeometry --> GableComponents[Posts/Rafters/Sheets/Braces]
        SkilliGeometry --> SkilliComponents[Posts/Rafters/Sheets]
        
        GableComponents --> MaterialApplication[Material Application]
        SkilliComponents --> MaterialApplication
    end
```

## Material System Architecture

```mermaid
graph TB
    subgraph "Material Pool"
        MaterialPool[materials-pool.ts]
        MaterialPool --> MaterialTypes[Material Types]
        MaterialTypes --> Zinc[ZincFinish]
        MaterialTypes --> Concrete[Concrete] 
        MaterialTypes --> Colorbond[Colorbond Steel]
        
        Zinc --> ZincProperties[metalness: 0.9, roughness: 0.3]
        Concrete --> ConcreteProperties[roughness: 0.8, metalness: 0.0]
        Colorbond --> ColorbondProperties[metalness: 0.8, roughness: 0.4]
    end
    
    subgraph "Color Management"
        ColorManager[colorManager.ts]
        ColorManager --> ColorbondColors[34 Official Colors]
        ColorManager --> ColorUpdate[updateMaterialColor()]
        ColorUpdate --> MaterialInstance[Material Instance Update]
        MaterialInstance --> SceneRerender[Scene Re-render]
    end
    
    subgraph "View Modes"
        ViewModes[materialViewModes.ts]
        ViewModes --> Normal[Normal: Full Opacity]
        ViewModes --> Transparent[Transparent: 50% Opacity]
        ViewModes --> Frame[Frame: Structural Only]
        
        Normal --> FullMaterial[Full Material Properties]
        Transparent --> AlphaMaterial[Alpha Blended Materials]
        Frame --> ConditionalRender[Conditional Rendering]
    end
    
    subgraph "Texture System"
        TextureLoader[textureLoader.ts]
        TextureLoader --> ProceduralTextures[Procedural Generation]
        ProceduralTextures --> ZincTexture[Zinc Surface]
        ProceduralTextures --> ConcreteTexture[Concrete Surface]
        ProceduralTextures --> CorrugatedPattern[Corrugated Pattern]
    end
```

## Business Logic Flow

```mermaid
graph TB
    subgraph "Carport Configuration"
        UserInput[User Input] --> Validation[Business Rules Validation]
        Validation --> DimensionCheck{Dimension Valid?}
        DimensionCheck -->|Yes| StateUpdate[Update State]
        DimensionCheck -->|No| ErrorDisplay[Display Error]
        
        StateUpdate --> GeometryCalc[Geometry Calculation]
        GeometryCalc --> StructuralCalc[Structural Calculations]
        StructuralCalc --> RenderUpdate[Update 3D Render]
    end
    
    subgraph "Engineering Calculations"
        StructuralCalc --> BayCalculation[Bay Count = floor(length/4000)]
        StructuralCalc --> PitchCalculation[Pitch-based Heights]
        StructuralCalc --> LoadPath[Load Path Analysis]
        StructuralCalc --> ComponentSizing[Component Sizing]
        
        BayCalculation --> PostPositions[Post Positions]
        PitchCalculation --> RoofProfile[Roof Profile]
        ComponentSizing --> MaterialQuantities[Material Quantities]
    end
    
    subgraph "Pricing Engine"
        MaterialQuantities --> CostCalculation[Cost Calculation]
        CostCalculation --> BasePrice[Base Price: $5000]
        CostCalculation --> AreaPrice[Area: $500/m²]
        CostCalculation --> ComplexityPrice[Complexity Premium]
        
        BasePrice --> TotalPrice[Total Price]
        AreaPrice --> TotalPrice
        ComplexityPrice --> TotalPrice
        
        TotalPrice --> QuoteGeneration[Quote Generation]
        QuoteGeneration --> EmailQuote[Email Quote]
    end
```

## Data Flow & Export System

```mermaid
graph TB
    subgraph "Data Persistence"
        ModelState[Current Model State]
        ModelState --> Serialization[Model Serialization]
        Serialization --> Encryption[Data Encryption]
        Encryption --> URLGeneration[URL Generation]
        URLGeneration --> ShareLink[Shareable Link]
        
        ShareLink --> URLDecoding[URL Decoding]
        URLDecoding --> Decryption[Data Decryption]
        Decryption --> Deserialization[Model Deserialization]
        Deserialization --> StateRestore[State Restoration]
    end
    
    subgraph "Export Formats"
        ExportSystem[Export System]
        ExportSystem --> Screenshot[Screenshot Export]
        ExportSystem --> PlanView[Plan View Export]
        ExportSystem --> ModelShare[Model Share]
        
        Screenshot --> HTML2Canvas[HTML2Canvas]
        PlanView --> OrthographicView[Orthographic Projection]
        ModelShare --> EncryptedURL[Encrypted URL]
    end
    
    subgraph "Import System"
        URLParams[URL Parameters]
        URLParams --> LegacyFormat[Legacy Format 'd']
        URLParams --> ModernFormat[Modern Format 'model']
        
        LegacyFormat --> Base64Decode[Base64 Decode]
        ModernFormat --> DecryptText[Decrypt Text]
        
        Base64Decode --> LegacyState[Legacy State Load]
        DecryptText --> FullState[Full State Load]
        
        LegacyState --> StateApplication[Apply to Context]
        FullState --> StateApplication
    end
```

## Performance & Optimization

```mermaid
graph TB
    subgraph "Rendering Optimization"
        FrameLoop[frameloop='demand']
        FrameLoop --> ConditionalRender[Conditional Rendering]
        ConditionalRender --> MaterialPooling[Material Instance Pooling]
        MaterialPooling --> GeometryDisposal[Geometry Disposal]
        
        GeometryDisposal --> MemoryManagement[Memory Management]
        MemoryManagement --> GCOptimization[GC Optimization]
    end
    
    subgraph "Component Optimization"
        ReactMemo[React.memo]
        ReactMemo --> UseMemo[useMemo for calculations]
        UseMemo --> UseCallback[useCallback for functions]
        UseCallback --> ContextMemo[Context value memoization]
        
        ContextMemo --> ReduceRerender[Reduce Re-renders]
        ReduceRerender --> PerformanceGain[Performance Improvement]
    end
    
    subgraph "Bundle Optimization"
        CodeSplitting[Code Splitting]
        CodeSplitting --> LazyLoading[Lazy Loading]
        LazyLoading --> TreeShaking[Tree Shaking]
        TreeShaking --> MinimalBundle[Minimal Bundle Size]
    end
    
    subgraph "3D Optimization"
        ScissorTest[Scissor Testing]
        ScissorTest --> MultiViewport[Multi-viewport Rendering]
        MultiViewport --> ViewFrustum[View Frustum Culling]
        ViewFrustum --> LOD[Level of Detail]
        
        LOD --> PerformanceBalance[Performance Balance]
        PerformanceBalance --> SmoothExperience[Smooth User Experience]
    end
```

## Platform Architecture

```mermaid
graph TB
    subgraph "Cross-Platform Support"
        ExpoRouter[Expo Router]
        ExpoRouter --> WebPlatform[Web Platform]
        ExpoRouter --> iOSPlatform[iOS Platform]
        ExpoRouter --> AndroidPlatform[Android Platform]
        
        WebPlatform --> WebSpecific[Web-specific Features]
        iOSPlatform --> iOSSpecific[iOS-specific Features]
        AndroidPlatform --> AndroidSpecific[Android-specific Features]
        
        WebSpecific --> HTML2Canvas[HTML2Canvas]
        WebSpecific --> WebGL[WebGL Context]
        iOSSpecific --> NativeRendering[Native Rendering]
        AndroidSpecific --> NativeRendering
    end
    
    subgraph "Responsive Design"
        ScreenSize[Screen Size Detection]
        ScreenSize --> Mobile[Mobile: ≤600px]
        ScreenSize --> Tablet[Tablet: 601-1024px]
        ScreenSize --> Desktop[Desktop: >1024px]
        
        Mobile --> ColumnLayout[Column Layout]
        Tablet --> ColumnLayout
        Desktop --> RowLayout[Row Layout]
        
        ColumnLayout --> StackedInterface[Stacked Interface]
        RowLayout --> SidebarInterface[Sidebar Interface]
    end
    
    subgraph "Feature Adaptation"
        PlatformCheck[Platform.OS Check]
        PlatformCheck --> TouchControls[Touch Controls]
        PlatformCheck --> MouseControls[Mouse Controls]
        PlatformCheck --> PanControls[Pan Controls]
        
        TouchControls --> MobileOptimized[Mobile Optimized]
        MouseControls --> DesktopOptimized[Desktop Optimized]
        PanControls --> WebOptimized[Web Optimized]
    end
```

## Integration Points

```mermaid
graph TB
    subgraph "External Integrations"
        i18n[Internationalization]
        i18n --> Languages[EN/CN/KR/JP]
        i18n --> ResourceLoader[Resource Loading]
        i18n --> DynamicSwitching[Dynamic Switching]
        
        Firebase[Firebase Hosting]
        Firebase --> StaticSite[Static Site Deployment]
        Firebase --> CDN[Content Delivery]
        
        Colorbond[Colorbond Standards]
        Colorbond --> OfficialColors[Official Color Palette]
        Colorbond --> MaterialSpecs[Material Specifications]
    end
    
    subgraph "API Integrations"
        PostMessage[PostMessage API]
        PostMessage --> LanguageChange[Language Change]
        PostMessage --> ExternalComm[External Communication]
        
        Screenshot[Screenshot API]
        Screenshot --> ImageCapture[Image Capture]
        Screenshot --> ExportImage[Export Image]
        
        EmailAPI[Email API]
        EmailAPI --> QuoteRequest[Quote Request]
        EmailAPI --> ContactForm[Contact Form]
    end
    
    subgraph "Standards Compliance"
        AustralianStandards[Australian Building Standards]
        AustralianStandards --> AS4600[AS4600: Steel Structures]
        AustralianStandards --> AS1397[AS1397: Steel Sheeting]
        AustralianStandards --> BCA[Building Code Australia]
        
        CADStandards[CAD Standards]
        CADStandards --> DimensionSystem[Dimension System]
        CADStandards --> DrawingConventions[Drawing Conventions]
        CADStandards --> ScaleSystem[Scale System]
    end
```

---

**Architecture Summary:**

This comprehensive architectural diagram reveals a sophisticated single-page application that successfully combines:

1. **Cross-platform compatibility** using Expo and React Native
2. **Professional 3D rendering** with Three.js and React Three Fiber
3. **Complex state management** with custom Context/Reducer pattern
4. **Industry-standard business logic** for carport design and engineering
5. **Responsive design** adapting to mobile, tablet, and desktop platforms
6. **Performance optimization** through conditional rendering and memory management
7. **Professional workflow integration** with quote generation and model sharing

The architecture demonstrates enterprise-level complexity while maintaining clean separation of concerns and scalable patterns for future development.