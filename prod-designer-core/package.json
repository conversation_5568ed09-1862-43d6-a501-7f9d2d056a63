{"name": "designercore", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "npx --no-install expo start", "reset-project": "node ./scripts/reset-project.js", "android": "npx --no-install expo start --android", "ios": "npx --no-install expo start --ios", "web": "npx --no-install expo start --web", "build:web": "npx --no-install expo export -p web", "test": "jest --watchAll", "lint": "npx --no-install expo lint"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/vector-icons": "^14.0.2", "@react-native-picker/picker": "^2.11.0", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@react-three/drei": "^9.121.5", "@react-three/fiber": "^8.18.0", "dotenv": "^16.5.0", "expo": "~52.0.37", "expo-blur": "~14.0.3", "expo-constants": "~17.0.7", "expo-font": "~13.0.4", "expo-gl": "^15.0.4", "expo-haptics": "~14.0.1", "expo-linking": "~7.0.5", "expo-localization": "~16.0.1", "expo-router": "~4.0.17", "expo-splash-screen": "~0.29.22", "expo-status-bar": "~2.0.1", "expo-symbols": "~0.2.2", "expo-system-ui": "~4.0.8", "expo-three": "^8.0.0", "expo-web-browser": "~14.0.2", "html2canvas": "^1.4.1", "i18next": "^25.1.3", "i18next-browser-languagedetector": "^8.1.0", "i18next-http-backend": "^3.0.2", "react": "18.3.1", "react-dom": "18.3.1", "react-i18next": "^15.5.1", "react-native": "0.76.7", "react-native-gesture-handler": "~2.20.2", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-svg": "^15.11.2", "react-native-web": "~0.19.13", "react-native-webview": "13.12.5", "three": "^0.166.0"}, "devDependencies": {"@babel/cli": "^7.27.0", "@babel/core": "^7.26.10", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-private-methods": "^7.18.6", "@babel/plugin-transform-class-properties": "^7.25.9", "@babel/plugin-transform-private-methods": "^7.25.9", "@babel/plugin-transform-runtime": "^7.26.10", "@babel/preset-env": "^7.26.9", "@babel/preset-react": "^7.26.3", "@babel/preset-typescript": "^7.27.0", "@types/jest": "^29.5.12", "@types/react": "~18.3.12", "@types/react-test-renderer": "^18.3.0", "babel-plugin-module-resolver": "^5.0.2", "core-js": "^3.41.0", "javascript-obfuscator": "^4.1.1", "jest": "^29.2.1", "jest-expo": "~52.0.4", "react-test-renderer": "18.3.1", "regenerator-runtime": "^0.14.1", "typescript": "^5.3.3"}, "private": true}