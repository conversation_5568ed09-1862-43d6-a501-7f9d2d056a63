import React, { createContext, useContext, ReactNode, useReducer, useRef, useState, useCallback, useEffect } from 'react';
import { CameraType } from '@/types/threejs';
import { CarportState } from '@/types/carport';
import { WallMatrixType } from '@/domain/config/girt.config';

interface SceneSettings {
  defaultCamera: CameraType;
  gridView: boolean;
  isAddingCar: boolean;
  showRule: boolean;
  viewOnlyMode: boolean;
  showSlab: boolean;
  viewMode: 'normal' | 'transparent' | 'frame';
  isPlanView?: boolean; // Added for Plan/3D view toggle
  exportMode?: boolean; // Flag to indicate when export is in progress
  showMeasurements?: boolean; // Flag to control visibility of dimension measurements
  
  // Wall matrix for controlling girt rendering
  wallMatrix?: WallMatrixType;
  
  // Lean to properties
  leftLeanTo?: boolean;
  leftLeanToDropHeight?: number;
  leftLeanToSpan?: number;
  rightLeanTo?: boolean;
  rightLeanToDropHeight?: number;
  rightLeanToSpan?: number;
  rightLeanToAttached?: boolean;

  // Color selections
  roofColor?: string;
  wallColor?: string;
  trimColor?: string;
  doorColor?: string;
  
  // Roof type - matches the types defined in carport.ts
  roofType?: 'Skillion Overhang' | 'Skillion Attached' | 'Gable' | 'Skillion';
  
  // Carport dimensions
  span?: number;
  height?: number;
  length?: number;
  pitch?: number;
  overhang?: number;
  
  // Object selection
  selectedObjectId?: string | null;
  
  // Camera orientation for ViewCube
  cameraOrientation?: string;
  
  // Active orientation for ViewCube highlighting
  activeOrientation?: string;
  
  // Wall sections
  
  // Profile types
  roofProfile?: string;
  wallProfile?: string;
  wallSections?: {
    front: boolean;
    back: boolean;
    frontLeftLeanTo: boolean;
    frontRightLeanTo: boolean;
    backLeftLeanTo: boolean;
    backRightLeanTo: boolean;
    left: boolean[];
    right: boolean[];
    leftLeanTo: boolean[];
    rightLeanTo: boolean[];
  };
}

const initialState: CarportState = {
  length: 12000,
  height: 2400,
  span: 6000,
  pitch: 15,
  overhang: 0,
  roofType: 'Gable',
};

const sceneInitialState: SceneSettings = {
  defaultCamera: 'perspective',
  gridView: false,
  isAddingCar: false,
  showRule: false,
  viewOnlyMode: false,
  showSlab: true,
  viewMode: 'normal',
  exportMode: false, // Default to false (not exporting)
  showMeasurements: true, // Default to showing measurements
  // Initialize lean-to properties with default values
  leftLeanTo: false,
  leftLeanToDropHeight: 500,
  leftLeanToSpan: 1000,
  rightLeanTo: false,
  rightLeanToDropHeight: 500,
  rightLeanToSpan: 1000,
  // Initialize color selections with default values
  roofColor: 'NightSky',
  wallColor: 'Classic Cream', 
  trimColor: 'Surfmist',
  doorColor: 'Surfmist',
  // Initialize object selection
  selectedObjectId: 'car',
  // Initialize wall sections
  wallSections: {
    front: false,
    back: false,
    frontLeftLeanTo: false,
    frontRightLeanTo: false,
    backLeftLeanTo: false,
    backRightLeanTo: false,
    left: [],
    right: [],
    leftLeanTo: [],
    rightLeanTo: [],
  },
};

interface SceneCtxType {
  dimensions: CarportState;
  dispatch: React.Dispatch<Action>;

  ref: React.MutableRefObject<HTMLCanvasElement>;

  sceneSettings: SceneSettings;
  setSceneSettings: React.Dispatch<React.SetStateAction<SceneSettings>>;
}

const SceneContext = createContext<SceneCtxType | undefined>(undefined);

interface Props {
  children: ReactNode;
  defaultState?: CarportState;
  initialSceneSettings?: Partial<SceneSettings>;
}

type Action = 
  | { type: 'setSizeValue'; payload: Partial<CarportState> }
  | { type: 'SET_SCENE_SETTING'; payload: { key: string; value: any } };

function setSizeValue(payload: Partial<CarportState>) {
  return { type: 'setSizeValue', payload } as const;
}

function setSceneSetting(key: string, value: any) {
  return { type: 'SET_SCENE_SETTING', payload: { key, value } } as const;
}

function setWallMatrix(wallMatrix: WallMatrixType) {
  return setSceneSetting('wallMatrix', wallMatrix);
}

function reducer(state: CarportState, action: Action): CarportState {
  switch (action.type) {
    case 'setSizeValue':
      return { ...state, ...action.payload };
    case 'SET_SCENE_SETTING':
      // FIXED: Actually update the scene settings instead of ignoring them
      return state;
    default:
      return state;
  }
}

export const SceneContextProvider: React.FC<Props> = ({ defaultState, initialSceneSettings, children }) => {
  const [dimensions, dispatch] = useReducer(reducer, defaultState ?? initialState);
  const ref = useRef<HTMLCanvasElement>(null!);

  // Initialize scene settings with any passed settings from shared model
  const [sceneSettings, setSceneSettings] = useState({
    ...sceneInitialState,
    viewOnlyMode: defaultState !== undefined,
    // Apply any initial scene settings passed in (for shared models)
    ...(initialSceneSettings || {})
  });
  
  
  // Handle SET_SCENE_SETTING action
  const handleSetSceneSetting = useCallback((key: string, value: any) => {
    setSceneSettings(prev => ({
      ...prev,
      [key]: value,
    }));
  }, []);

  // Custom dispatch function that handles both reducer state and scene settings
  const customDispatch = useCallback(
    (action: Action) => {
      // First, update the reducer state as normal
      dispatch(action);
      
      // Additionally, if it's a SET_SCENE_SETTING action, directly update scene settings
      if (action.type === 'SET_SCENE_SETTING') {
        const { key, value } = action.payload;
        handleSetSceneSetting(key, value);
      }
    },
    [dispatch, handleSetSceneSetting]
  );

  return (
    <SceneContext.Provider
      value={{
        dimensions,
        dispatch: customDispatch, // Use our enhanced dispatch function instead
        ref,
        sceneSettings,
        setSceneSettings,
      }}
    >
      {children}
    </SceneContext.Provider>
  );
};

export const useSceneContext = (): SceneCtxType => {
  const context = useContext(SceneContext);

  if (!context) {
    throw new Error('Context provider needs to be mounted');
  }

  return context;
};

export { setSizeValue, setSceneSetting, setWallMatrix };
