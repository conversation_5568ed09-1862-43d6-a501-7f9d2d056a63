import { useEffect, useMemo } from 'react';
import { THREE } from 'expo-three';
import { useSceneContext } from '@/redux/context';
import { WallMatrixType } from '@/domain/config/girt.config';
import calculateGirtGeometriesAndPositions from '@/domain/config/girt.config';
import calculateWallSheetGeometriesAndPositions from '@/domain/config/gable.wallsheet.config';
import {
  Girt_web,
  Girt_flangeE,
  Girt_flangeF,
  Girt_lip,
  Girt_thickness
} from '@/constants/carport/CarportParams';
import {
  createCeeSectionShape,
  createMonoClad2DSection,
  createSHSShape,
  createStandardZeeSectionShape,
  createCorro2DSection,
  createGableRidgeCap2DShape,
  createBargeCapFlashing2DShape,
  createRightBargeCapFlashing2DShape,
  createGableLeftBargeCapFlashing2DShape,
  createGableRightBargeCapFlashing2DShape,
} from '@/domain/helpers/SectionHelper';
import { processGeometry } from '@/domain/helpers/Geofunctions';
import { knee_web, knee_flange, post_size, Rafter_flange, apex_web, apex_flange, Rafter_web } from '@/constants/carport/CarportParams';
import calculateGeometriesAndPositions from '@/domain/config/Carportconfig';
import { applyViewMode, createViewModeMaterial, ElementType } from '@/domain/materials/materialViewModes';
import { materialsUpdated } from '@/domain/materials/materials-pool';
import {
  footingMaterial,
  postMaterial,
  rafterMaterial,
  roofMaterial,
  slabMaterial,
  brickWallMaterial,
  zincFinishMaterial,
  capMaterial,
} from '@/domain/materials/materials-pool';

function disposeAll(obj: THREE.Object3D) {
  for (const c of obj.children) {
    disposeAll(c);
  }
  if (obj instanceof THREE.Mesh && obj.isMesh) {
    obj.geometry.dispose();
  }
  obj.parent?.remove(obj);
}

function GableCarport({
  length,
  span,
  height,
  pitch,
  overhang,
  leftLeanTo,
  leftLeanToSpan,
  leftLeanToDropHeight,
  rightLeanTo,
  rightLeanToSpan,
  rightLeanToDropHeight,
  rightLeanToAttached,
  viewMode,
  showSlab,
  wallMatrix,
}: {
  length?: number;
  span?: number;
  height?: number;
  pitch?: number;
  overhang?: number;
  leftLeanTo?: boolean;
  leftLeanToSpan?: number;
  leftLeanToDropHeight?: number;
  rightLeanTo?: boolean;
  rightLeanToSpan?: number;
  rightLeanToDropHeight?: number;
  rightLeanToAttached?: boolean;
  viewMode?: 'normal' | 'transparent' | 'frame';
  showSlab?: boolean;
  wallMatrix?: WallMatrixType;
}) {
  const { dimensions, sceneSettings } = useSceneContext();
  
  // Use the provided props or fall back to sceneSettings
  const currentViewMode = viewMode || sceneSettings.viewMode || 'normal';
  const showSlabValue = showSlab !== undefined ? showSlab : sceneSettings.showSlab;
  
  // Default wall matrix - all walls disabled by default for gable carport
  const defaultWallMatrix = {
    mainLeftWall: [false, false, false], 
    mainRightWall: [false, false, false],
    mainDividerWall: [false, false],
    mainFrontWall: false,
    mainBackWall: false,
    leftLeanToWall: leftLeanTo ? [false, false, false] : [],
    leftLeanToFrontWall: leftLeanTo ? false : null,
    leftLeanToBackWall: leftLeanTo ? false : null,
    leftLeanToDividerWall: leftLeanTo ? [false, false] : [],
    rightLeanToWall: rightLeanTo ? [false, false, false] : [],
    rightLeanToFrontWall: rightLeanTo ? false : null,
    rightLeanToBackWall: rightLeanTo ? false : null,
    rightLeanToDividerWall: rightLeanTo ? [false, false] : []
  };
  
  // Get the wall matrix from scene context if available, or from props, or use default
  const contextWallMatrix = sceneSettings.wallMatrix;
  const currentWallMatrix = contextWallMatrix || wallMatrix || defaultWallMatrix;
  
  // Create a stringified version of the wall matrix to ensure React detects changes properly
  // This is necessary because React might not detect nested object changes in the dependency array
  const wallMatrixString = JSON.stringify(currentWallMatrix);
  
  // Log when the wall matrix changes from scene context
  useEffect(() => {
    if (contextWallMatrix) {
    }
  }, [contextWallMatrix, wallMatrix, currentWallMatrix, wallMatrixString]);
  const scene = useMemo(() => {
    const s = new THREE.Scene();
    // Rotate and place
    s.rotation.x = -Math.PI / 2;
    return s;
  }, []);

  const carportConfig = useMemo(
    () => calculateGeometriesAndPositions(span, pitch, height, overhang, length, leftLeanTo, leftLeanToSpan, leftLeanToDropHeight, rightLeanTo, rightLeanToSpan, rightLeanToDropHeight),
    [span, pitch, height, overhang, length, leftLeanTo, leftLeanToSpan, leftLeanToDropHeight, rightLeanTo, rightLeanToSpan, rightLeanToDropHeight],
  );
  
  // Calculate girt geometries and positions based on wall matrix
  const girtConfig = useMemo(
    () => {
      
      const result = calculateGirtGeometriesAndPositions(
        span, 
        pitch, 
        height, 
        overhang, 
        length, 
        leftLeanTo, 
        leftLeanToSpan, 
        leftLeanToDropHeight,
        rightLeanTo,
        rightLeanToSpan,
        rightLeanToDropHeight,
        currentWallMatrix,
        'Gable' // Explicitly pass 'Gable' as the roof type
      );
      
      
      return result;
    },
    [span, height, length, pitch, overhang, leftLeanTo, leftLeanToSpan, leftLeanToDropHeight, 
     rightLeanTo, rightLeanToSpan, rightLeanToDropHeight, wallMatrixString], // use string version to detect all changes
  );
  
  // Calculate wall sheet geometries and positions
  const wallSheetConfig = useMemo(
    () => {
      
      const result = calculateWallSheetGeometriesAndPositions(
        length,
        span,
        height,
        leftLeanToSpan,
        leftLeanToDropHeight,
        rightLeanToSpan,
        rightLeanToDropHeight,
        overhang,
        pitch, // Add pitch parameter
        currentWallMatrix
      );
      
      
      return result;
    },
    [span, height, length, pitch, overhang, leftLeanTo, leftLeanToSpan, leftLeanToDropHeight,
     rightLeanTo, rightLeanToSpan, rightLeanToDropHeight, wallMatrixString] // use same dependencies as girtConfig
  );

  useEffect(() => {
    const group = new THREE.Group();
    
    // Create material with view mode applied - only posts are structural
    const postMaterialWithViewMode = createViewModeMaterial(postMaterial, viewMode, ElementType.POST);
    
    // Left Posts
    carportConfig.gable_post_left_Positions.forEach((position, index) => {
      const geo = carportConfig.gable_post_left_Geo[index];
      const shsShape = createSHSShape(geo.geo_x, geo.geo_y);
      const extrudeSettings = { depth: geo.geo_z, bevelEnabled: false };
      const postGeometry = new THREE.ExtrudeGeometry(shsShape, extrudeSettings);
      const post = new THREE.Mesh(postGeometry, postMaterialWithViewMode);
      post.position.set(position.x, position.y, position.z);
      group.add(post);
    });

    scene.add(group);
    return () => disposeAll(group);
  }, [scene, carportConfig.post_left_Geo, carportConfig.post_left_Positions, viewMode]);

  useEffect(() => {
    const group = new THREE.Group();
    
    // Create material with view mode applied - only posts are structural
    const postMaterialWithViewMode = createViewModeMaterial(postMaterial, viewMode, ElementType.POST);
    
    // Right Posts
    carportConfig.gable_post_right_Positions.forEach((position, index) => {
      const geo = carportConfig.gable_post_right_Geo[index];
      const shsShape = createSHSShape(geo.geo_x, geo.geo_y);
      const extrudeSettings = { depth: geo.geo_z, bevelEnabled: false };
      const postGeometry = new THREE.ExtrudeGeometry(shsShape, extrudeSettings);
      const post = new THREE.Mesh(postGeometry, postMaterialWithViewMode);
      post.position.set(position.x, position.y, position.z);
      group.add(post);
    });

    scene.add(group);
    return () => disposeAll(group);
  }, [scene, carportConfig.post_right_Geo, carportConfig.post_right_Positions, viewMode]);

  // Calculate gable-specific dimensions
  const pitchRadians = (carportConfig.pitch * Math.PI) / 180;
  const roofHeight = (span! / 2000) * Math.tan(pitchRadians);

  useEffect(() => {
    const group = new THREE.Group();
    
    // Create material with view mode applied - mark rafters as structural (like knee braces)
    const rafterMaterialWithViewMode = createViewModeMaterial(rafterMaterial, viewMode, ElementType.KNEE_BRACE);
    
    // Create the Cee section shape using the helper function
    const leftCeeShape = createCeeSectionShape(
      carportConfig.gable_left_rafter_Geo.geo_x,
      carportConfig.gable_left_rafter_Geo.geo_y,
      carportConfig.gable_left_rafter_Geo.flange_thickness,
    );

    // Process the geometry for rafters
    const leftRafterGeometry = processGeometry(
      new THREE.ExtrudeGeometry(leftCeeShape, {
        depth: carportConfig.gable_left_rafter_Geo.geo_z,
        bevelEnabled: false,
      }),
      Math.PI / 2,
      0,
      -Math.PI / 2,
    );

    const rightCeeShape = createCeeSectionShape(
      carportConfig.gable_right_rafter_Geo.geo_x,
      carportConfig.gable_right_rafter_Geo.geo_y,
      carportConfig.gable_right_rafter_Geo.flange_thickness,
    );

    // Process the geometry for rafters
    const rightRafterGeometry = processGeometry(
      new THREE.ExtrudeGeometry(rightCeeShape, {
        depth: carportConfig.gable_right_rafter_Geo.geo_z,
        bevelEnabled: false,
      }),
      Math.PI / 2,
      0,
      -Math.PI / 2,
    );

    carportConfig.gable_left_rafter_Positions.forEach((position, index) => {
      const rafter = new THREE.Mesh(leftRafterGeometry, rafterMaterialWithViewMode);
      rafter.position.set(position.x, position.y, position.z);
      //rafter.position.set(0,0,0);
      // Adjust rafter rotation for gable roof

      rafter.rotation.y = -pitchRadians;
      /*
      if (index === carportConfig.gable_left_rafter_Positions.length - 1) {
        rafter.rotation.z = Math.PI;
      }
        */
      group.add(rafter);
    });

    carportConfig.gable_right_rafter_Positions.forEach((position, index) => {
      const rafter = new THREE.Mesh(rightRafterGeometry, rafterMaterial);
      rafter.position.set(position.x, position.y, position.z);
      // Adjust rafter rotation for gable roof

      rafter.rotation.y = pitchRadians;
      if (index === carportConfig.gable_right_rafter_Positions.length - 1) {
        rafter.rotation.z = Math.PI;
      }

      group.add(rafter);
    });

    scene.add(group);
    return () => disposeAll(group);
  }, [
    scene,
    carportConfig.pitch,
    carportConfig.gable_left_rafter_Geo.flange_thickness,
    carportConfig.gable_left_rafter_Geo.geo_x,
    carportConfig.gable_left_rafter_Geo.geo_y,
    carportConfig.gable_left_rafter_Geo.geo_z,
    carportConfig.gable_left_rafter_Positions,
  ]);

  // Gable Purlin Left //////////////
  useEffect(() => {
    const group = new THREE.Group();
    const zGeo = carportConfig.gable_left_purlin_Geo;
    const standardZeeShape = createStandardZeeSectionShape(zGeo.web, zGeo.flangeE, zGeo.flangeF, zGeo.lip, zGeo.thickness);
    // Process the geometry
    const purlinGeometry = processGeometry(
      new THREE.ExtrudeGeometry(standardZeeShape, {
        depth: zGeo.length,
        bevelEnabled: false,
      }),
      Math.PI / 2,
      0,
      0,
    );

    carportConfig.gable_left_purlin_Positions.forEach((position: { x: number; y: number; z: number }) => {
      const purlin = new THREE.Mesh(purlinGeometry, rafterMaterial);
      purlin.position.set(position.x, position.y, position.z);
      purlin.rotation.y = -pitchRadians; // Apply the same pitch as the gable roof
      group.add(purlin);
    });
    scene.add(group);
    return () => disposeAll(group);
  }, [
    scene,
    carportConfig.gable_left_purlin_Geo,
    carportConfig.gable_left_purlin_Positions,
    pitchRadians,
  ]);

  // Gable Purlin Right //////////////
  useEffect(() => {
    const group = new THREE.Group();
    const zGeo = carportConfig.gable_right_purlin_Geo;
    const standardZeeShape = createStandardZeeSectionShape(zGeo.web, zGeo.flangeE, zGeo.flangeF, zGeo.lip, zGeo.thickness);
    // Process the geometry
    const purlinGeometry = processGeometry(
      new THREE.ExtrudeGeometry(standardZeeShape, {
        depth: zGeo.length,
        bevelEnabled: false,
      }),
      -Math.PI / 2,
      0,
      0,
    );

    carportConfig.gable_right_purlin_Positions.forEach((position: { x: number; y: number; z: number }) => {
      const purlin = new THREE.Mesh(purlinGeometry, rafterMaterial);
      purlin.position.set(position.x, position.y, position.z);
      purlin.rotation.y = pitchRadians; // Apply the same pitch as the gable roof
      group.add(purlin);
    });
    scene.add(group);
    return () => disposeAll(group);
  }, [
    scene,
    carportConfig.gable_right_purlin_Geo,
    carportConfig.gable_right_purlin_Positions,
    pitchRadians,
  ]);

  // Right Rafter //////////////
  useEffect(() => {
    const group = new THREE.Group();
    
    // Create material with view mode applied - mark rafters as structural (like knee braces)
    const rafterMaterialWithViewMode = createViewModeMaterial(rafterMaterial, viewMode, ElementType.KNEE_BRACE);
    
    // Create the Cee section shape using the helper function
    const rightCeeShape = createCeeSectionShape(
      carportConfig.gable_right_rafter_Geo.geo_x,
      carportConfig.gable_right_rafter_Geo.geo_y,
      carportConfig.gable_right_rafter_Geo.flange_thickness,
    );

    // Process the geometry for rafters
    const rightRafterGeometry = processGeometry(
      new THREE.ExtrudeGeometry(rightCeeShape, {
        depth: carportConfig.gable_right_rafter_Geo.geo_z,
        bevelEnabled: false,
      }),
      Math.PI / 2,
      0,
      -Math.PI / 2,
    );

    // Add rafters along the roof line
    carportConfig.gable_right_rafter_Positions.forEach((position, index) => {
      const rafter = new THREE.Mesh(rightRafterGeometry, rafterMaterialWithViewMode);
      rafter.position.set(position.x, position.y, position.z);
      // Adjust rafter rotation for gable roof
      rafter.rotation.y = pitchRadians;
      if (index === carportConfig.gable_right_rafter_Positions.length - 1) {
        rafter.rotation.z = Math.PI;
      }
      group.add(rafter);
    });

    scene.add(group);
    return () => disposeAll(group);
  }, [
    scene,
    carportConfig.gable_right_rafter_Geo.flange_thickness,
    carportConfig.gable_right_rafter_Geo.geo_x,
    carportConfig.gable_right_rafter_Geo.geo_y,
    carportConfig.gable_right_rafter_Geo.geo_z,
    carportConfig.gable_right_rafter_Positions,
    span,
    height,
    length,
    overhang,
    roofHeight,
    pitchRadians,
    viewMode, // Add viewMode to dependencies
  ]);

  useEffect(() => {
    // Left EP //////////////////////////////////////////////
    // Create the Cee section shape using the helper function
    const ceeShape_EP = createCeeSectionShape(
      carportConfig.gable_left_EP_Geo.geo_x,
      carportConfig.gable_left_EP_Geo.geo_y,
      carportConfig.gable_left_EP_Geo.flange_thickness,
    );
    const group = new THREE.Group();
    const EP_LEFT_Geometry = processGeometry(
      new THREE.ExtrudeGeometry(ceeShape_EP, {
        depth: carportConfig.gable_left_EP_Geo.geo_z,
        bevelEnabled: false,
      }),
      Math.PI / 2,
      Math.PI,
      0,
    );

    carportConfig.gable_left_EP_Positions.forEach((position) => {
      const LEFT_EP = new THREE.Mesh(EP_LEFT_Geometry, rafterMaterial);
      LEFT_EP.position.set(position.x, position.y, position.z);
      // Adjust end plate rotation for gable roof
      LEFT_EP.rotation.x = 0;
      group.add(LEFT_EP);
    });

    scene.add(group);
    return () => disposeAll(group);
  }, [
    scene,
    carportConfig.gable_left_EP_Geo.geo_z,
    carportConfig.gable_left_EP_Positions,
    pitchRadians,
  ]);

  useEffect(() => {
    const group = new THREE.Group();
    // Right EP
    // Create the Cee section shape using the helper function
    const ceeShape_EP_R = createCeeSectionShape(
      carportConfig.gable_right_EP_Geo.geo_x,
      carportConfig.gable_right_EP_Geo.geo_y,
      carportConfig.gable_right_EP_Geo.flange_thickness,
    );

    // Process the geometry
    const EP_RIGHT_Geometry = processGeometry(
      new THREE.ExtrudeGeometry(ceeShape_EP_R, {
        depth: carportConfig.gable_right_EP_Geo.geo_z,
        bevelEnabled: false,
      }),
      -Math.PI / 2,
      Math.PI,
      Math.PI,
    );

    carportConfig.gable_right_EP_Positions.forEach((position) => {
      const RIGHT_EP = new THREE.Mesh(EP_RIGHT_Geometry, rafterMaterial);
      RIGHT_EP.position.set(position.x, position.y, position.z);
      // Adjust end plate rotation for gable roof
      RIGHT_EP.rotation.x = 0;
      group.add(RIGHT_EP);
    });

    scene.add(group);
    return () => disposeAll(group);
  }, [
    scene,
    carportConfig.gable_right_EP_Geo.geo_z,
    carportConfig.gable_right_EP_Positions,
  ]);

  useEffect(() => {
    const group = new THREE.Group();
    // Gable Footings //////////////////////////////////////////////
    carportConfig.gable_footing_Positions.forEach((position) => {
      const geometry = new THREE.CylinderGeometry(
        carportConfig.gable_footing_Geo.radius,
        carportConfig.gable_footing_Geo.radius,
        carportConfig.gable_footing_Geo.height,
        32,
      );

      const FOOTING_GEO = processGeometry(geometry, Math.PI / 2, 0, 0);
      const footing = new THREE.Mesh(FOOTING_GEO, footingMaterial);
      footing.position.set(position.x, position.y, position.z);
      group.add(footing);
    });

    scene.add(group);
    return () => disposeAll(group);
  }, [
    scene,
    carportConfig.gable_footing_Geo.height,
    carportConfig.gable_footing_Geo.radius,
    carportConfig.gable_footing_Positions,
  ]);

  useEffect(() => {
    // Slab //////////////////////////////////////////////
    // Only create and add slab if showSlab is true
    if (showSlabValue) {
      const slab_GEO = processGeometry(
        new THREE.BoxGeometry(
          carportConfig.slab_Geo.width,
          carportConfig.slab_Geo.thickness,
          carportConfig.slab_Geo.length,
        ),
        Math.PI / 2,
        0,
        0,
      );
      const slab = new THREE.Mesh(slab_GEO, slabMaterial);
      slab.position.set(
        carportConfig.slab_Position.x,
        carportConfig.slab_Position.y,
        carportConfig.slab_Position.z,
      );

      scene.add(slab);
      return () => disposeAll(slab);
    }

    // Return empty cleanup function if no slab was created
    return () => { };
  }, [
    scene,
    showSlabValue, // Add showSlab to dependencies
    carportConfig.slab_Geo,
    carportConfig.slab_Position,
    span,
    length,
    height,
    overhang,
  ]);

  useEffect(() => {
    // Gable Roofs - Corro Type //////////////////////////////////////////////
    const group = new THREE.Group();
    const corroPlateLine = createCorro2DSection(
      carportConfig.gable_roof_Geo_full.height,
      carportConfig.gable_roof_Geo_full.thickness,
      carportConfig.gable_roof_Geo_full.singlelength,
    );
    const lastCorroPlateLine = createCorro2DSection(
      carportConfig.gable_last_roof_plate_Geo.height,
      carportConfig.gable_last_roof_plate_Geo.thickness,
      carportConfig.gable_last_roof_plate_Geo.singlelength,
    );
    const noPlates = carportConfig.gable_roof_Positions.length;
    carportConfig.gable_roof_Positions.forEach((position, index) => {
      const geometry = processGeometry(
        new THREE.ExtrudeGeometry(index < noPlates - 2 ? corroPlateLine : lastCorroPlateLine, {
          depth: carportConfig.gable_roof_Geo_full.length,
          bevelEnabled: false,
        }),
        Math.PI / 2 + position.rot_y,
        0,
        Math.PI / 2,
      );
      // Create material with view mode applied for roof sheets
      const roofMaterialWithViewMode = createViewModeMaterial(roofMaterial, viewMode, ElementType.ROOF_SHEET);
      const roof = new THREE.Mesh(geometry, roofMaterialWithViewMode);
      roof.position.set(position.x, position.y, position.z);
      group.add(roof);
    });
    scene.add(group);
    return () => disposeAll(group);
  }, [
    scene,
    carportConfig.gable_roof_Positions,
    carportConfig.gable_roof_Geo_full,
    carportConfig.gable_last_roof_plate_Geo,
    carportConfig.pitch,
    viewMode,
    materialsUpdated, // Add materialsUpdated to trigger re-renders when colors change
  ]);

  // Ridge Cap for Gable Roof
  useEffect(() => {
    const group = new THREE.Group();

    // Ridge cap parameters
    const ridgeCapWidth = 0.3; // 300mm width
    const ridgeCapThickness = 0.001; // 1mm thickness
    const ridgeCapLength = length! / 1000; // Same length as the carport

    // Create the ridge cap shape based on the roof pitch
    const ridgeCapShape = createGableRidgeCap2DShape(ridgeCapWidth, ridgeCapThickness, pitch!);

    // Process the geometry
    const ridgeCapGeometry = processGeometry(
      new THREE.ExtrudeGeometry(ridgeCapShape, {
        depth: ridgeCapLength,
        bevelEnabled: false,
      }),
      -Math.PI / 2,
      0,
      0,
    );
    // Create material with view mode applied for ridge cap (using capMaterial for trim color control)
    const ridgeMaterialWithViewMode = createViewModeMaterial(capMaterial, viewMode, ElementType.RIDGE);
    const ridgeCap = new THREE.Mesh(ridgeCapGeometry, ridgeMaterialWithViewMode);

    // Calculate the position for the ridge cap
    // It should be at the top center of the roof
    const x = (span! / 2) / 1000; // Center of the span
    const y = length! / 2 / 1000; // Center position along the length
    const z = (height! / 1000) + (Math.tan(pitchRadians) * (span! / 2) / 1000); // Height + rise due to pitch

    // Create the mesh and add to the scene
    ridgeCap.position.set(x, y, z + .05);

    group.add(ridgeCap);
    scene.add(group);

    return () => disposeAll(group);
  }, [
    scene,
    span,
    length,
    height,
    pitch,
    pitchRadians,
    viewMode, // Add viewMode to dependencies to ensure it responds to view mode changes
    materialsUpdated, // Add materialsUpdated to trigger re-renders when colors change
  ]);

  // Left Barge Cap Flashing
  useEffect(() => {
    const group = new THREE.Group();

    // Barge cap parameters
    const plateWidth = 0.17; // 170mm width for each plate
    const bargeCapThickness = 0.001; // 1mm thickness
    const bargeCapLength = length! / 1000; // Same length as the carport

    // Create the barge cap shape based on the roof pitch
    const bargeCapShape = createBargeCapFlashing2DShape(plateWidth, bargeCapThickness, pitch!);

    // Process the geometry
    const bargeCapGeometry = processGeometry(
      new THREE.ExtrudeGeometry(bargeCapShape, {
        depth: bargeCapLength,
        bevelEnabled: false,
      }),
      Math.PI,
      Math.PI,
      Math.PI / 2
    );

    // Calculate the position for the barge cap
    // It should be at the left edge of the roof
    const x = 0.06; // Left edge
    const y = length! / 2 / 1000; // Front position
    // Use the same z-value as the left EP
    const z = carportConfig.gable_left_EP_Positions[0]?.z + .02 || height! / 1000; // Base height matching left EP

    // Create material with view mode applied for barge cap
    const bargeMaterialWithViewMode = createViewModeMaterial(capMaterial, viewMode, ElementType.BARGE);
    // Create the mesh and add to the scene
    const bargeCap = new THREE.Mesh(bargeCapGeometry, bargeMaterialWithViewMode);
    bargeCap.position.set(x, y, z);

    // Rotate the barge cap to align with the roof
    bargeCap.rotation.x = Math.PI / 2;

    group.add(bargeCap);
    scene.add(group);

    return () => disposeAll(group);
  }, [
    scene,
    span,
    length,
    height,
    pitch,
    pitchRadians,
    viewMode, // Add viewMode to dependencies to ensure it responds to view mode changes
    materialsUpdated, // Add materialsUpdated to trigger re-renders when colors change
  ]);

  // Right Barge Cap Flashing
  useEffect(() => {
    const group = new THREE.Group();

    // Calculate the pitch in radians
    const pitchRadians = (pitch! * Math.PI) / 180;

    // Barge cap parameters
    const plateWidth = 0.17; // 170mm width for each plate
    const bargeCapThickness = 0.001; // 1mm thickness
    const bargeCapLength = length! / 1000; // Same length as the carport

    // Create the right barge cap shape based on the roof pitch
    // For gable roofs, the angle is the same as the left barge (90 degrees plus pitch)
    const bargeCapShape = createRightBargeCapFlashing2DShape(plateWidth, bargeCapThickness, pitch!, true);

    // Process the geometry
    const bargeCapGeometry = processGeometry(
      new THREE.ExtrudeGeometry(bargeCapShape, {
        depth: bargeCapLength,
        bevelEnabled: false,
      }),
      Math.PI,
      Math.PI,
      -Math.PI / 2
    );

    // Calculate the position for the barge cap
    // It should be at the right edge of the roof
    const x = span! / 1000 - 0.06; // Right edge (span minus offset)
    const y = length! / 2 / 1000; // Center position along the length
    // Use the same z-value as the right EP
    const z = carportConfig.gable_right_EP_Positions[0]?.z + .02 || height! / 1000; // Base height matching right EP

    // Create material with view mode applied for barge cap
    const bargeMaterialWithViewMode = createViewModeMaterial(capMaterial, viewMode, ElementType.BARGE);
    // Create the mesh and add to the scene
    const bargeCap = new THREE.Mesh(bargeCapGeometry, bargeMaterialWithViewMode);
    bargeCap.position.set(x, y, z);

    // Rotate the barge cap to align with the roof
    bargeCap.rotation.x = Math.PI / 2;

    group.add(bargeCap);
    scene.add(group);

    return () => disposeAll(group);
  }, [
    scene,
    span,
    length,
    height,
    pitch,
    pitchRadians,
    viewMode, // Add viewMode to dependencies to ensure it responds to view mode changes
    materialsUpdated, // Add materialsUpdated to trigger re-renders when colors change
  ]);

  // Front Barge Left (along the front rafter)
  useEffect(() => {
    const group = new THREE.Group();
    
    // Barge cap parameters
    const plateWidth = 0.4; // Width for each plate
    const bargeCapThickness = 0.002; // 1mm thickness
    const bargeCapLength = (span! /2+ 2*overhang!+100)/Math.cos(pitchRadians)/ 1000; // Length is the span of the carport
    
    // Create the barge cap shape based on the roof pitch
    const bargeCapShape = createGableLeftBargeCapFlashing2DShape(plateWidth, bargeCapThickness, 0);
    
    // Process the geometry
    const bargeCapGeometry = processGeometry(
      new THREE.ExtrudeGeometry(bargeCapShape, {
        depth: bargeCapLength,
        bevelEnabled: false,
      }),
      0,
      0,
      0
    );
    
    // Calculate the position for the front barge cap
    // It should be at the front edge of the roof along the left rafter
    // Get the front rafter position (first rafter position)
    const frontRafterPos = carportConfig.gable_left_rafter_Positions[0];
    if (!frontRafterPos) return () => {}; // Safety check
    
    const x = (span! /2 + 2*overhang!+40) / 1000/2; // Left edge
    const y = frontRafterPos.y - 0.064 + 0.1 || 0.2; // Front position
    const z = frontRafterPos.z +(0.095/Math.cos(pitchRadians)); // Adjusted to match the gable left rafter height
    
    // Create the mesh and add to the scene
    const bargeMaterialWithViewMode = createViewModeMaterial(capMaterial, viewMode, ElementType.BARGE);
    const frontBargeLeft = new THREE.Mesh(bargeCapGeometry, bargeMaterialWithViewMode);
    frontBargeLeft.position.set(x, y, z);
    
    // Rotate the barge cap to align with the front rafter
    frontBargeLeft.rotation.y = -pitchRadians + Math.PI/2;
    frontBargeLeft.rotation.z = 0;
    
    group.add(frontBargeLeft);
    scene.add(group);
    
    return () => disposeAll(group);
  }, [
    scene,
    span,
    length,
    height,
    pitch,
    pitchRadians,
    overhang,
    viewMode,
    materialsUpdated,
  ]);
  
  // Front Barge Right (along the front rafter)
  useEffect(() => {
    const group = new THREE.Group();
    
    // Barge cap parameters
    const plateWidth = 0.4; // Width for each plate
    const bargeCapThickness = 0.002; // 2mm thickness
    const bargeCapLength = (span! /2+ 2*overhang!+100)/Math.cos(pitchRadians)/ 1000; // Length is the span of the carport
    
    // Create the barge cap shape based on the roof pitch
    const bargeCapShape = createGableRightBargeCapFlashing2DShape(plateWidth, bargeCapThickness, 0);
    
    // Process the geometry
    const bargeCapGeometry = processGeometry(
      new THREE.ExtrudeGeometry(bargeCapShape, {
        depth: bargeCapLength,
        bevelEnabled: false,
      }),
      0,
      Math.PI,
      0
    );
    
    // Calculate the position for the front right barge cap
    // It should be at the front edge of the roof along the right rafter
    // Get the front rafter position (first rafter position)
    const frontRafterPos = carportConfig.gable_right_rafter_Positions[0];
    if (!frontRafterPos) return () => {}; // Safety check
    
    const x = (span! /2 + 2*overhang!+40) / 1000/2; // Right edge
    const y = frontRafterPos.y - 0.064 + 0.1 || 0.2; // Front position
    const z = frontRafterPos.z +(0.095/Math.cos(pitchRadians)); // Adjusted to match the gable right rafter height
    
    // Create the mesh and add to the scene
    const bargeMaterialWithViewMode = createViewModeMaterial(capMaterial, viewMode, ElementType.BARGE);
    const frontBargeRight = new THREE.Mesh(bargeCapGeometry, bargeMaterialWithViewMode);
    frontBargeRight.position.set(span! / 1000 - x, y, z); // Mirror position for right side
    
    // Rotate the barge cap to align with the front rafter
    frontBargeRight.rotation.y = pitchRadians + Math.PI/2; // Mirror rotation for right side
    frontBargeRight.rotation.z = 0;
    
    group.add(frontBargeRight);
    scene.add(group);
    
    return () => disposeAll(group);
  }, [
    scene,
    span,
    length,
    height,
    pitch,
    pitchRadians,
    overhang,
    viewMode,
    materialsUpdated,
  ]);
  
  // Back Barge Left (along the last rafter at the back)
  useEffect(() => {
    const group = new THREE.Group();
    
    // Barge cap parameters
    const plateWidth = 0.4; // Width for each plate
    const bargeCapThickness = 0.002; // 2mm thickness
    const bargeCapLength = (span! /2+ 2*overhang!+100)/Math.cos(pitchRadians)/ 1000; // Length is the span of the carport
    
    // Create the barge cap shape based on the roof pitch
    const bargeCapShape = createGableLeftBargeCapFlashing2DShape(plateWidth, bargeCapThickness, 0);
    
    // Process the geometry
    const bargeCapGeometry = processGeometry(
      new THREE.ExtrudeGeometry(bargeCapShape, {
        depth: bargeCapLength,
        bevelEnabled: false,
      }),
      Math.PI,
      Math.PI,
      0
    );
    
    // Calculate the position for the back barge cap
    // It should be at the back edge of the roof along the left rafter
    // Get the back rafter position (last rafter position)
    const backRafterPos = carportConfig.gable_left_rafter_Positions[carportConfig.gable_left_rafter_Positions.length - 1];
    if (!backRafterPos) return () => {}; // Safety check
    
    const x = (span! /2 + 2*overhang!+40) / 1000/2; // Left edge
    const y = backRafterPos.y - 0.03 || length! / 1000; // Back position
    const z = backRafterPos.z +(0.09/Math.cos(pitchRadians)); // Adjusted to match the gable left rafter height
    
    // Create the mesh and add to the scene
    const bargeMaterialWithViewMode = createViewModeMaterial(capMaterial, viewMode, ElementType.BARGE);
    const backBargeLeft = new THREE.Mesh(bargeCapGeometry, bargeMaterialWithViewMode);
    backBargeLeft.position.set(x, y, z);
    
    // Rotate the barge cap to align with the back rafter
    backBargeLeft.rotation.y = -pitchRadians - Math.PI/2;
    backBargeLeft.rotation.z = 0;
    
    group.add(backBargeLeft);
    scene.add(group);
    
    return () => disposeAll(group);
  }, [
    scene,
    span,
    length,
    height,
    pitch,
    pitchRadians,
    overhang,
    viewMode,
    materialsUpdated,
  ]);
  
  // Back Barge Right (along the last rafter at the back)
  useEffect(() => {
    const group = new THREE.Group();
    
    // Barge cap parameters
    const plateWidth = 0.4; // Width for each plate
    const bargeCapThickness = 0.002; // 2mm thickness
    const bargeCapLength = (span! /2+ 2*overhang!+100)/Math.cos(pitchRadians)/ 1000; // Length is the span of the carport
    
    // Create the barge cap shape based on the roof pitch
    const bargeCapShape = createGableRightBargeCapFlashing2DShape(plateWidth, bargeCapThickness, 0);
    
    // Process the geometry
    const bargeCapGeometry = processGeometry(
      new THREE.ExtrudeGeometry(bargeCapShape, {
        depth: bargeCapLength,
        bevelEnabled: false,
      }),
      0,
      0,
      Math.PI
    );
    
    // Calculate the position for the back right barge cap
    // It should be at the back edge of the roof along the right rafter
    // Get the back rafter position (last rafter position)
    const backRafterPos = carportConfig.gable_right_rafter_Positions[carportConfig.gable_right_rafter_Positions.length - 1];
    if (!backRafterPos) return () => {}; // Safety check
    
    const x = (span! /2 + 2*overhang!+40) / 1000/2; // Right edge
    const y = backRafterPos.y - 0.03 || length! / 1000; // Back position
    const z = backRafterPos.z +(0.09/Math.cos(pitchRadians)); // Adjusted to match the gable right rafter height
    
    // Create the mesh and add to the scene
    const bargeMaterialWithViewMode = createViewModeMaterial(capMaterial, viewMode, ElementType.BARGE);
    const backBargeRight = new THREE.Mesh(bargeCapGeometry, bargeMaterialWithViewMode);
    backBargeRight.position.set(span! / 1000 - x, y, z); // Mirror position for right side
    
    // Rotate the barge cap to align with the back rafter
    backBargeRight.rotation.y = pitchRadians + Math.PI/2; // Mirror rotation for right side at back
    backBargeRight.rotation.z = 0;
    
    group.add(backBargeRight);
    scene.add(group);
    
    return () => disposeAll(group);
  }, [
    scene,
    span,
    length,
    height,
    pitch,
    pitchRadians,
    overhang,
    viewMode,
    materialsUpdated,
  ]);
  
  // Left Lean-to Front Barge Cap
  useEffect(() => {
    // Only render if leftLeanTo is enabled
    if (!leftLeanTo) return;
    
    const group = new THREE.Group();
    
    // Calculate the pitch in radians (lean-to uses a fixed 2 degree pitch)
    const leanToPitchRadians = (2 * Math.PI) / 180;
    
    // Barge cap parameters
    const plateWidth = 0.21; // Width for each plate
    const bargeCapThickness = 0.001; // 1mm thickness
    const bargeCapLength = (leftLeanToSpan! + 100)/ Math.cos(leanToPitchRadians) / 1000; // Length is the lean-to span
    
    // Create the barge cap shape based on the roof pitch
    const bargeCapShape = createBargeCapFlashing2DShape(plateWidth, bargeCapThickness, -2); // 2 degree pitch
    
    // Process the geometry
    const bargeCapGeometry = processGeometry(
      new THREE.ExtrudeGeometry(bargeCapShape, {
        depth: bargeCapLength,
        bevelEnabled: false,
      }),
      0,
      0,
      0
    );
    
    // Calculate the position for the front barge cap
    // Get the front lean-to rafter position (first rafter position)
    const frontRafterPos = carportConfig.leanto_rafter_Positions[0];
    if (!frontRafterPos) return () => {}; // Safety check
    
    const x = -(leftLeanToSpan!) / 1000 /2 + overhang!/1000; // Center of lean-to span
    const y = frontRafterPos.y - 0.064 + 0.13; // Front position
    const z = frontRafterPos.z + 0.01; // Same height as the front rafter
    
    // Create the mesh and add to the scene
    const bargeMaterialWithViewMode = createViewModeMaterial(capMaterial, viewMode, ElementType.BARGE);
    const frontBargeLeft = new THREE.Mesh(bargeCapGeometry, bargeMaterialWithViewMode);
    frontBargeLeft.position.set(x, y, z);
    
    // Rotate the barge cap to align with the front rafter
    frontBargeLeft.rotation.y = -leanToPitchRadians + Math.PI/2;
    
    group.add(frontBargeLeft);
    scene.add(group);
    
    return () => disposeAll(group);
  }, [
    scene,
    span,
    length,
    height,
    pitch,
    pitchRadians,
    overhang,
    leftLeanTo,
    leftLeanToSpan,
    leftLeanToDropHeight,
    viewMode,
    materialsUpdated,
  ]);
  
  // Left Lean-to Back Barge Cap
  useEffect(() => {
    // Only render if leftLeanTo is enabled
    if (!leftLeanTo) return;
    
    const group = new THREE.Group();
    
    // Calculate the pitch in radians (lean-to uses a fixed 2 degree pitch)
    const leanToPitchRadians = (2 * Math.PI) / 180;
    
    // Barge cap parameters
    const plateWidth = 0.21; // Width for each plate
    const bargeCapThickness = 0.001; // 1mm thickness
    const bargeCapLength = (leftLeanToSpan! + 100)/ 1000; // Length is the lean-to span
    
    // Create the barge cap shape based on the roof pitch
    const bargeCapShape = createBargeCapFlashing2DShape(plateWidth, bargeCapThickness, -2); // 2 degree pitch
    
    // Process the geometry
    const bargeCapGeometry = processGeometry(
      new THREE.ExtrudeGeometry(bargeCapShape, {
        depth: bargeCapLength,
        bevelEnabled: false,
      }),
      Math.PI,
      0,
      -Math.PI/2
    );
    
    // Calculate the position for the back barge cap
    // Get the back lean-to rafter position (last rafter position)
    const backRafterPos = carportConfig.leanto_rafter_Positions[carportConfig.leanto_rafter_Positions.length - 1];
    if (!backRafterPos) return () => {}; // Safety check
    
    const x = -(leftLeanToSpan!) / 1000 / 2 + overhang!/1000; // Center of lean-to span
    const y = backRafterPos.y - 0.03; // Back position
    const z = backRafterPos.z + 0.01; // Same height as the back rafter
    
    // Create the mesh and add to the scene
    const bargeMaterialWithViewMode = createViewModeMaterial(capMaterial, viewMode, ElementType.BARGE);
    const backBargeLeft = new THREE.Mesh(bargeCapGeometry, bargeMaterialWithViewMode);
    backBargeLeft.position.set(x, y, z);
    
    // Rotate the barge cap to align with the back rafter
    backBargeLeft.rotation.y = -leanToPitchRadians - Math.PI/2;
    
    group.add(backBargeLeft);
    scene.add(group);
    
    return () => disposeAll(group);
  }, [
    scene,
    span,
    length,
    height,
    pitch,
    pitchRadians,
    overhang,
    leftLeanTo,
    leftLeanToSpan,
    leftLeanToDropHeight,
    viewMode,
    materialsUpdated,
  ]);
  
  // Right Lean-to Front Barge Cap
  useEffect(() => {
    // Only render if rightLeanTo is enabled and not attached
    // For attached lean-tos, we don't need barge caps
    // Also don't render if we have a skillion roof (indicated by non-zero pitch)
    if (!rightLeanTo || rightLeanToAttached || (pitch !== undefined && pitch > 0)) return;
    
    const group = new THREE.Group();
    
    // Calculate the pitch in radians (lean-to uses a fixed 2 degree pitch)
    const leanToPitchRadians = (2 * Math.PI) / 180;
    
    // Barge cap parameters
    const plateWidth = 0.21; // Width for each plate
    const bargeCapThickness = 0.001; // 1mm thickness
    const bargeCapLength = (rightLeanToSpan! + 100)/ 1000; // Length is the lean-to span
    
    // Create the barge cap shape based on the roof pitch - use right barge cap for right lean-to
    const bargeCapShape = createRightBargeCapFlashing2DShape(plateWidth, bargeCapThickness, -2, false); // 2 degree pitch
    
    // Process the geometry
    const bargeCapGeometry = processGeometry(
      new THREE.ExtrudeGeometry(bargeCapShape, {
        depth: bargeCapLength,
        bevelEnabled: false,
      }),
      0,
      Math.PI,
      0
    );
    
    // Calculate the position for the front barge cap
    // Get the front lean-to rafter position (first rafter position)
    const frontRafterPos = carportConfig.gable_leanto_rafter_right_Positions[0];
    if (!frontRafterPos) return () => {}; // Safety check
    
    const x = (span! + overhang!) / 1000 + (rightLeanToSpan!) / 1000 / 2; // Center of right lean-to span
    const y = frontRafterPos.y - 0.064 + 0.13; // Front position
    const z = frontRafterPos.z ; // Same height as the front rafter
    
    // Create the mesh and add to the scene
    const bargeMaterialWithViewMode = createViewModeMaterial(capMaterial, viewMode, ElementType.BARGE);
    const frontBargeRight = new THREE.Mesh(bargeCapGeometry, bargeMaterialWithViewMode);
    frontBargeRight.position.set(x, y, z);
    
    // Rotate the barge cap to align with the front rafter
    frontBargeRight.rotation.y = leanToPitchRadians + Math.PI/2;
    
    group.add(frontBargeRight);
    scene.add(group);
    
    return () => disposeAll(group);
  }, [
    scene,
    span,
    length,
    height,
    pitch,
    pitchRadians,
    overhang,
    rightLeanTo,
    rightLeanToSpan,
    rightLeanToDropHeight,
    viewMode,
    materialsUpdated,
  ]);
  
  // Right Lean-to Back Barge Cap
  useEffect(() => {
    // Only render if rightLeanTo is enabled and not attached
    // For attached lean-tos, we don't need barge caps
    // Also don't render if we have a skillion roof (indicated by non-zero pitch)
    if (!rightLeanTo || rightLeanToAttached || (pitch !== undefined && pitch > 0)) return;
    
    const group = new THREE.Group();
    
    // Calculate the pitch in radians (lean-to uses a fixed 2 degree pitch)
    const leanToPitchRadians = (2 * Math.PI) / 180;
    
    // Barge cap parameters
    const plateWidth = 0.21; // Width for each plate
    const bargeCapThickness = 0.001; // 1mm thickness
    const bargeCapLength = (rightLeanToSpan! + 100) / 1000; // Length is the lean-to span
    
    // Create the barge cap shape based on the roof pitch - use right barge cap for right lean-to
    const bargeCapShape = createRightBargeCapFlashing2DShape(plateWidth, bargeCapThickness, -2, false); // 2 degree pitch
    
    // Process the geometry
    const bargeCapGeometry = processGeometry(
      new THREE.ExtrudeGeometry(bargeCapShape, {
        depth: bargeCapLength,
        bevelEnabled: false,
      }),
      0,
      0,
      Math.PI/2 // Positive for right side
    );
    
    // Calculate the position for the back barge cap
    // Get the back lean-to rafter position (last rafter position)
    const backRafterPos = carportConfig.gable_leanto_rafter_right_Positions[carportConfig.gable_leanto_rafter_right_Positions.length - 1];
    if (!backRafterPos) return () => {}; // Safety check
    
    const x = (span! + overhang!) / 1000 + (rightLeanToSpan!) / 1000 / 2; // Center of right lean-to span
    const y = backRafterPos.y - 0.03; // Back position
    const z = backRafterPos.z; // Same height as the back rafter
    
    // Create the mesh and add to the scene
    const bargeMaterialWithViewMode = createViewModeMaterial(capMaterial, viewMode, ElementType.BARGE);
    const backBargeRight = new THREE.Mesh(bargeCapGeometry, bargeMaterialWithViewMode);
    backBargeRight.position.set(x, y, z);
    
    // Rotate the barge cap to align with the back rafter
    backBargeRight.rotation.y = leanToPitchRadians - Math.PI/2;
    
    group.add(backBargeRight);
    scene.add(group);
    
    return () => disposeAll(group);
  }, [
    scene,
    span,
    length,
    height,
    pitch,
    pitchRadians,
    overhang,
    rightLeanTo,
    rightLeanToSpan,
    rightLeanToDropHeight,
    viewMode,
    materialsUpdated,
  ]);

  // Knee Braces
  useEffect(() => {
    // Create a group for all knee braces
    const group = new THREE.Group();

    // Use the same material as rafters for consistent appearance
    // Slightly reduce opacity to avoid clashing
    const kneeMaterial = rafterMaterial.clone();
    kneeMaterial.transparent = true;
    kneeMaterial.opacity = 0.9;

    // Use fixed values for testing to avoid NaN errors
    const webSize = .05; // 50mm
    const flangeSize = 0.102; // 102mm

    // Create a C-section shape for the knee brace with fixed values
    const kneeShape = createCeeSectionShape(webSize, flangeSize, 0.002);

    // Create a simple box geometry as fallback in case of errors
    let leftKneeGeometry;
    let rightKneeGeometry;
    let kneefactor;
    let kneeoffset;


    if (pitch! === 5) {
      kneefactor = -0.3;
      kneeoffset = 150;
    } else if (pitch! === 11) {
      kneefactor = -0.1;
      kneeoffset = 150;
    } else if (pitch! === 15) {
      kneefactor = 0.1;
      kneeoffset = 200;
    } else if (pitch! === 22) {
      kneefactor = 0.2;
      kneeoffset = 240;
    } else {
      kneefactor = 0.5;
      kneeoffset = 370;
    }


    try {
      // Create and process the extruded geometry
      const extrudedGeometry = new THREE.ExtrudeGeometry(kneeShape, {
        depth: 0.6, // 600mm length
        bevelEnabled: false
      });

      // Process the geometry for left knee orientation
      leftKneeGeometry = processGeometry(
        extrudedGeometry.clone(),
        Math.PI / 4, // Rotation around X
        0,        // No rotation around Y
        Math.PI / 4 // Rotation around Z
      );

      // Process the geometry for right knee with different orientation
      rightKneeGeometry = processGeometry(
        extrudedGeometry.clone(),
        -Math.PI / 4,  // Rotation around X
        Math.PI,         // No rotation around Y
        -Math.PI / 4 // Rotation around Z (mirrored)
      );
    } catch (error) {
      console.error('Error creating C-section geometry:', error);
      // Fallback to box geometry if there's an error
      leftKneeGeometry = new THREE.BoxGeometry(0.6, 0.05, 0.05);
      rightKneeGeometry = new THREE.BoxGeometry(0.6, 0.05, 0.05);
    }

    // Main left knee adjustment - same logic as in Carport.tsx
    let main_left_knee_adjustment = 0;
    if (pitch === 5) {

      if (span! < 4800) {
        main_left_knee_adjustment = -.03;
      } else if (span! < 6600) {
        main_left_knee_adjustment = 0;
      } else if (span! < 9000) {
        main_left_knee_adjustment = 0.05;
      } else {
        main_left_knee_adjustment = 0;
      }


    } else if (pitch === 11) {
      if (span! < 4800) {
        main_left_knee_adjustment = 0.02;
      } else if (span! < 6600) {
        main_left_knee_adjustment = 0.03;
      } else if (span! < 9000) {
        main_left_knee_adjustment = 0.05;
      } else {
        main_left_knee_adjustment = 0.02;
      }



    } else if (pitch === 15) {
      if (span! < 4800) {
        main_left_knee_adjustment = 0.04;
      } else if (span! < 6600) {
        main_left_knee_adjustment = 0.05;
      } else if (span! < 9000) {
        main_left_knee_adjustment = 0.07;
      } else {
        main_left_knee_adjustment = 0.08;
      }

    } else if (pitch === 22) {
      if (span! < 4800) {
        main_left_knee_adjustment = 0.07;
      } else if (span! < 6600) {
        main_left_knee_adjustment = 0.075;
      } else if (span! < 9000) {
        main_left_knee_adjustment = 0.085;
      } else {
        main_left_knee_adjustment = 0.1;
      }
    } else if (pitch === 30) {
      if (span! < 4800) {
        main_left_knee_adjustment = 0.12;
      } else if (span! < 6600) {
        main_left_knee_adjustment = 0.13;
      } else if (span! < 9000) {
        main_left_knee_adjustment = 0.15;
      } else {
        main_left_knee_adjustment = 0.15;
      }
    }

    // Main right knee adjustment - following same pattern as left knee
    let main_right_knee_adjustment = 0;
    if (pitch === 5) {
      if (span! < 4800) {
        main_right_knee_adjustment = -0.03;
      } else if (span! < 6600) {
        main_right_knee_adjustment = 0;
      } else if (span! < 9000) {
        main_right_knee_adjustment = 0.05;
      } else {
        main_right_knee_adjustment = 0;
      }
    } else if (pitch === 11) {
      if (span! < 4800) {
        main_right_knee_adjustment = 0.02;
      } else if (span! < 6600) {
        main_right_knee_adjustment = 0.03;
      } else if (span! < 9000) {
        main_right_knee_adjustment = 0.05;
      } else {
        main_right_knee_adjustment = 0.02;
      }
    } else if (pitch === 15) {
      if (span! < 4800) {
        main_right_knee_adjustment = 0.04;
      } else if (span! < 6600) {
        main_right_knee_adjustment = 0.05;
      } else if (span! < 9000) {
        main_right_knee_adjustment = 0.07;
      } else {
        main_right_knee_adjustment = 0.08;
      }
    } else if (pitch === 22) {
      if (span! < 4800) {
        main_right_knee_adjustment = 0.07;
      } else if (span! < 6600) {
        main_right_knee_adjustment = 0.075;
      } else if (span! < 9000) {
        main_right_knee_adjustment = 0.085;
      } else {
        main_right_knee_adjustment = 0.1;
      }
    } else if (pitch === 30) {
      if (span! < 4800) {
        main_right_knee_adjustment = 0.12;
      } else if (span! < 6600) {
        main_right_knee_adjustment = 0.13;
      } else if (span! < 9000) {
        main_right_knee_adjustment = 0.15;
      } else {
        main_right_knee_adjustment = 0.15;
      }
    }

    // For each bay, create left and right knee braces
    for (let i = 0; i <= Math.max(1, Math.floor(length! / 4000)); i++) {
      try {
        // Calculate the positions
        // Start at left post
        const leftPostX = (overhang! + post_size) / 1000 + 0.25;
        // Calculate right post position (span - left post position + adjustments)
        const rightPostX = (span! - overhang! - post_size) / 1000 - 0.25;

        const postY = i === Math.floor(length! / 4000)
          ? (length! - post_size) / 1000
          : (post_size + (i * length!) / Math.max(1, Math.floor(length! / 4000))) / 1000;

        // Calculate dynamic height using the exact same formula as in Carportconfig.ts
        // This ensures the knee brace will always land on the rafter regardless of span
        const span_drop = 0;

        // Use the exact formula from Carportconfig.ts for rafter position
        const rafter_z = (height! + span_drop / 2 - (0.25 * Rafter_flange) / Math.cos(pitchRadians)) / 1000;

        // Position at the post, with adjustment for left knee
        const leftPostZ = rafter_z - 0.4 + main_left_knee_adjustment;

        // Position at the post, with adjustment for right knee
        const rightPostZ = rafter_z - 0.4 + main_right_knee_adjustment;

        // Create the left knee brace mesh
        const leftKneeBrace = new THREE.Mesh(leftKneeGeometry, kneeMaterial);
        leftKneeBrace.position.set(leftPostX, postY, leftPostZ);
        leftKneeBrace.rotation.z = Math.PI / 4;
        group.add(leftKneeBrace);

        //console.log('Added left knee brace at position:', leftKneeBrace.position);

        // Create the right knee brace mesh
        const rightKneeBrace = new THREE.Mesh(rightKneeGeometry, kneeMaterial);
        rightKneeBrace.position.set(rightPostX, postY, rightPostZ);
        // Mirror the rotation for the right side
        rightKneeBrace.rotation.z = -Math.PI / 4;
        group.add(rightKneeBrace);

        //console.log('Added right knee brace at position:', rightKneeBrace.position);
      } catch (error) {
        console.error('Error creating knee brace:', error);
      }
    }

    // Add the group to the scene
    scene.add(group);

    return () => disposeAll(group);
  }, [
    scene,
    span,
    length,
    height,
    pitch,
    post_size,
    overhang,
  ]);

  // Apex Braces (only for gable roof)
  useEffect(() => {
    // Create a group for all apex braces
    const group = new THREE.Group();

    // Use the same material as rafters for consistent appearance
    const apexMaterial = rafterMaterial.clone();
    apexMaterial.transparent = true;
    apexMaterial.opacity = 0.9;

    // Use fixed values from constants
    const webSize = apex_web / 1000; // Convert mm to meters
    const flangeSize = apex_flange / 1000; // Convert mm to meters

    // Create a C-section shape for the apex brace
    const apexShape = createCeeSectionShape(webSize, flangeSize, 0.002);

    // Create a simple box geometry as fallback in case of errors
    let apexGeometry;

    try {
      // Create and process the extruded geometry - horizontal brace
      const extrudedGeometry = new THREE.ExtrudeGeometry(apexShape, {
        depth: span! / 1000 * .2,
        bevelEnabled: false
      });

      // Process the geometry for horizontal orientation
      apexGeometry = processGeometry(
        extrudedGeometry.clone(),
        Math.PI / 2, // No rotation around X
        0, // Rotation around Y to make it horizontal
        Math.PI / 2 // No rotation around Z
      );
    } catch (error) {
      console.error('Error creating apex C-section geometry:', error);
      // Fallback to box geometry if there's an error
      apexGeometry = new THREE.BoxGeometry(span! / 1000 * 0.8, 0.05, 0.05);
    }

    let apexadjust;

    if (pitch! === 5) {

      if (span! < 4800) {
        apexadjust = 0.3;
      } else if (span! < 6600) {
        apexadjust = 0.32;
      } else if (span! < 9000) {
        apexadjust = 0.33;
      } else {
        apexadjust = 0.34;
      }








    } else if (pitch! === 11) {


      if (span! < 4800) {
        apexadjust = 0.35;
      } else if (span! < 6600) {
        apexadjust = 0.38;
      } else if (span! < 9000) {
        apexadjust = 0.42;
      } else {
        apexadjust = 0.46;
      }


    } else if (pitch! === 15) {
      if (span! < 4800) {
        apexadjust = 0.37;
      } else if (span! < 6600) {
        apexadjust = 0.4;
      } else if (span! < 9000) {
        apexadjust = 0.48;
      } else {
        apexadjust = 0.55;
      }





    } else if (pitch! === 22) {
      if (span! < 4800) {
        apexadjust = 0.4;
      } else if (span! < 6600) {
        apexadjust = 0.5;
      } else if (span! < 9000) {
        apexadjust = 0.6;
      } else {
        apexadjust = 0.7;
      }




    } else {
      if (span! < 4800) {
        apexadjust = 0.5;
      } else if (span! < 6600) {
        apexadjust = 0.65;
      } else if (span! < 9000) {
        apexadjust = 0.75;
      } else {
        apexadjust = .92;
      }


    }



    // Create one apex brace per bay
    for (let i = 0; i <= Math.max(1, Math.floor(length! / 4000)); i++) {
      try {
        // Calculate the position
        const postY = i === Math.floor(length! / 4000)
          ? (length! - post_size) / 1000
          : (post_size + (i * length!) / Math.max(1, Math.floor(length! / 4000))) / 1000;

        // Calculate the peak height based on pitch and span
        const pitchRadians = (pitch! * Math.PI) / 180;
        const peakHeight = height! / 1000 + (span! / 2) * Math.tan(pitchRadians) / 1000;

        // Position at the center of the span, at the peak height
        const apexX = span! / 2 / 1000;
        const apexZ = -apexadjust + peakHeight;

        // Create the apex brace mesh
        const apexBrace = new THREE.Mesh(apexGeometry, apexMaterial);
        apexBrace.position.set(apexX, postY, apexZ);
        group.add(apexBrace);

        //console.log('Added apex brace at position:', apexBrace.position);
      } catch (error) {
        console.error('Error creating apex brace:', error);
      }
    }

    // Add the group to the scene
    scene.add(group);

    return () => disposeAll(group);
  }, [
    scene,
    span,
    length,
    height,
    pitch,
  ]);

  // Left Lean-to Components
  useEffect(() => {
    if (leftLeanTo===false) return;

    const group = new THREE.Group();

    // Calculate the adjusted height for lean-to (main height minus drop height)
    const leanToHeight = (height! - leftLeanToDropHeight!) / 1000;
    const leanToPitchRadians = (2 * Math.PI) / 180; // 2-degree pitch for lean-to

    try {
      // Left Lean-to Barge Cap Flashing
      // Barge cap parameters
      const plateWidth = 0.17; // 170mm width for each plate
      const bargeCapThickness = 0.001; // 1mm thickness
      const bargeCapLength = length! / 1000; // Same length as the carport

      // Create the barge cap shape based on the lean-to pitch
      const leanToBargeCapShape = createBargeCapFlashing2DShape(plateWidth, bargeCapThickness, 2); // 2-degree pitch

      // Process the geometry
      const leanToBargeCapGeometry = processGeometry(
        new THREE.ExtrudeGeometry(leanToBargeCapShape, {
          depth: bargeCapLength,
          bevelEnabled: false,
        }),
        Math.PI,
        Math.PI,
        Math.PI / 2
      );

      // Calculate the position for the lean-to barge cap
      // It should be at the left edge of the lean-to roof with no overhang
      const x = (overhang! - leftLeanToSpan!) / 1000 + 0.05; // Left edge of lean-to
      const y = length! / 2 / 1000; // Center position along the length
      // Use the z-position based on the lean-to height
      const z = leanToHeight - 0.07;

      // Create the mesh and add to the scene
      // Create material with view mode applied for lean-to barge cap
      const leanToBargeMaterialWithViewMode = createViewModeMaterial(capMaterial, viewMode, ElementType.BARGE);
      const leanToBargeCap = new THREE.Mesh(leanToBargeCapGeometry, leanToBargeMaterialWithViewMode);
      leanToBargeCap.position.set(x, y, z);

      // Rotate the barge cap to align with the lean-to roof
      leanToBargeCap.rotation.x = Math.PI / 2;

      group.add(leanToBargeCap);

      // Left Lean-to Knee Braces
      // Use the same material as rafters for consistent appearance
      const leanToKneeMaterial = rafterMaterial.clone();
      leanToKneeMaterial.transparent = true;
      leanToKneeMaterial.opacity = 0.8;

      // Use fixed values for knee braces
      const webSize = .05; // 50mm
      const flangeSize = 0.102; // 102mm

      // Create a C-section shape for the knee brace
      const leanToKneeShape = createCeeSectionShape(webSize, flangeSize, 0.002);

      // Variables for knee geometry
      let leanToLeftKneeGeometry;
      const leanToKneeFactor = 0.15; // Same as main knee factor for 2-degree pitch
      const leanToKneeOffset = 105; // Same as main knee offset for 2-degree pitch

      try {
        // Create and process the extruded geometry
        const leanToExtrudedGeometry = new THREE.ExtrudeGeometry(leanToKneeShape, {
          depth: 0.15 * (height! - leftLeanToDropHeight!) / 1000 + Math.sin(leanToPitchRadians) * leanToKneeFactor * (height! - leftLeanToDropHeight!) / 1000,
          bevelEnabled: false
        });

        // Process the geometry for lean-to knee orientation
        leanToLeftKneeGeometry = processGeometry(
          leanToExtrudedGeometry.clone(),
          Math.PI / 4, // Rotation around X
          0,        // No rotation around Y
          Math.PI / 4 // Rotation around Z
        );
      } catch (error) {
        console.error('Error creating lean-to C-section geometry:', error);
        // Fallback to box geometry if there's an error
        leanToLeftKneeGeometry = new THREE.BoxGeometry(0.6, 0.05, 0.05);
      }

      // For each bay, create lean-to knee braces
      for (let i = 0; i <= Math.max(1, Math.floor(length! / 4000)); i++) {
        try {
          // Calculate the positions
          // Position at lean-to post
          const leanToLeftPostX = (overhang! - leftLeanToSpan! + post_size + leanToKneeOffset) / 1000;

          const postY = i === Math.floor(length! / 4000)
            ? (length! - post_size) / 1000
            : (post_size + (i * length!) / Math.max(1, Math.floor(length! / 4000))) / 1000;

          // Calculate dynamic height using similar formula as main knee braces
          const leanToSpanDrop = leftLeanToSpan! * Math.tan(leanToPitchRadians);

          // Calculate rafter position for lean-to
          const leanToRafterZ = ((height! - leftLeanToDropHeight!) + leanToSpanDrop / 2 - (0.25 * 152) / Math.cos(leanToPitchRadians)) / 1000;

          // Position at the post, 90% up from the ground
          const leanToPostZ = leanToRafterZ * 0.9;

          // Create the lean-to knee brace mesh
          const leanToLeftKneeBrace = new THREE.Mesh(leanToLeftKneeGeometry, leanToKneeMaterial);
          leanToLeftKneeBrace.position.set(leanToLeftPostX, postY, leanToPostZ);
          leanToLeftKneeBrace.rotation.z = Math.PI / 4;
          group.add(leanToLeftKneeBrace);
        } catch (error) {
          console.error('Error creating lean-to knee brace:', error);
        }
      }

      // 1. Left Lean-to Posts
      carportConfig.leanto_post_left_Positions.forEach((position, index) => {
        const geo = carportConfig.leanto_post_left_Geo[index];
        const shsShape = createSHSShape(geo.geo_x, geo.geo_y);
        const extrudeSettings = { depth: geo.geo_z, bevelEnabled: false };
        const postGeometry = new THREE.ExtrudeGeometry(shsShape, extrudeSettings);
        const post = new THREE.Mesh(postGeometry, postMaterial);

        // Position the post
        post.position.set(position.x, position.y, position.z);
        group.add(post);
      });

      // 2. Left Lean-to Rafters
      carportConfig.leanto_rafter_Positions.forEach((position, index) => {
        // Create a C-section shape for the rafter
        const rafterShape = createCeeSectionShape(
          carportConfig.leanto_rafter_Geo.geo_x,
          carportConfig.leanto_rafter_Geo.geo_y,
          carportConfig.leanto_rafter_Geo.flange_thickness
        );

        // Process the geometry - using the same rotation logic as main rafters
        const rafterGeometry = processGeometry(
          new THREE.ExtrudeGeometry(rafterShape, {
            depth: carportConfig.leanto_rafter_Geo.geo_z,
            bevelEnabled: false,
          }),
          Math.PI / 2 + (2 * Math.PI) / 180, // Same rotation as main rafters but with 2-degree pitch
          0,
          -Math.PI / 2
        );

        const rafter = new THREE.Mesh(rafterGeometry, rafterMaterial);

        // Position the rafter
        rafter.position.set(position.x, position.y, position.z);

        // Apply the same rotation logic as main rafters for the last rafter
        if (index === carportConfig.leanto_rafter_Positions.length - 1) {
          rafter.rotation.y = (-2 * (2 * Math.PI)) / 180;
          rafter.rotation.z = Math.PI;
        }

        group.add(rafter);
      });

      // 3. Left Lean-to Left EP
      if (carportConfig.leanto_left_EP_Positions.length > 0) {
        // Create the Cee section shape for lean-to left EP
        const leantoLeftEPShape = createCeeSectionShape(
          carportConfig.leanto_left_EP_Geo.geo_x,
          carportConfig.leanto_left_EP_Geo.geo_y,
          carportConfig.leanto_left_EP_Geo.flange_thickness
        );

        // Process the geometry - using the same rotation logic as main left EP
        const leantoLeftEPGeometry = processGeometry(
          new THREE.ExtrudeGeometry(leantoLeftEPShape, {
            depth: carportConfig.leanto_left_EP_Geo.geo_z,
            bevelEnabled: false,
          }),
          Math.PI / 2,
          Math.PI,
          0
        );

        // Create and position the lean-to left EP
        carportConfig.leanto_left_EP_Positions.forEach((position) => {
          const leantoLeftEP = new THREE.Mesh(leantoLeftEPGeometry, rafterMaterial);
          leantoLeftEP.position.set(position.x, position.y, position.z);
          group.add(leantoLeftEP);
        });
      }

      // 4. Left Lean-to Flat Purlin
      if (carportConfig.leanto_flat_purlin_Positions.length > 0) {
        // Create the Cee section shape for lean-to flat purlin
        const leantoFlatPurlinShape = createCeeSectionShape(
          carportConfig.leanto_flat_purlin_Geo.geo_x,
          carportConfig.leanto_flat_purlin_Geo.geo_y,
          0.002
        );

        // Process the geometry - using the same rotation logic as main purlin
        const leantoFlatPurlinGeometry = processGeometry(
          new THREE.ExtrudeGeometry(leantoFlatPurlinShape, {
            depth: carportConfig.leanto_flat_purlin_Geo.geo_z,
            bevelEnabled: false,
          }),
          Math.PI / 2,
          (-2 * Math.PI) / 180, // Use 2-degree pitch
          0
        );

        // Create and position the lean-to flat purlins
        carportConfig.leanto_flat_purlin_Positions.forEach((position) => {
          const leantoFlatPurlin = new THREE.Mesh(leantoFlatPurlinGeometry, rafterMaterial);
          leantoFlatPurlin.position.set(position.x, position.y, position.z);
          group.add(leantoFlatPurlin);
        });
      }

      // 4. Left Lean-to Footings
      if (carportConfig.leanto_footing_Positions.length > 0) {
        carportConfig.leanto_footing_Positions.forEach((position) => {
          const geometry = new THREE.CylinderGeometry(
            carportConfig.footing_Geo.radius,
            carportConfig.footing_Geo.radius,
            carportConfig.footing_Geo.height,
            32
          );
          const FOOTING_GEO = processGeometry(geometry, Math.PI / 2, 0, 0);
          const leantoFooting = new THREE.Mesh(FOOTING_GEO, footingMaterial);
          leantoFooting.position.set(position.x, position.y, position.z);
          group.add(leantoFooting);
        });
      }

      // 5. Left Lean-to Slab
      // Only create and add slab if showSlab is true
      if (showSlabValue) {
        const slabGeometry = new THREE.BoxGeometry(
          (leftLeanToSpan || 1000) / 1000, // Use default if undefined
          carportConfig.leanto_slab_Geo.length,
          carportConfig.leanto_slab_Geo.thickness
        );

        const slab = new THREE.Mesh(slabGeometry, slabMaterial);
        slab.position.set(
          carportConfig.leanto_slab_Position.x,
          carportConfig.leanto_slab_Position.y,
          carportConfig.leanto_slab_Position.z
        );

        group.add(slab);
      }

      // 6. Left Lean-to Roof
      if (carportConfig.leanto_roof_Positions.length > 0) {
        const noSheets = carportConfig.leanto_roof_Geo.length;

        // Create MonoClad section shapes
        const monoPlateLine = createCorro2DSection(
          carportConfig.leanto_roof_Geo[0].height,
          carportConfig.leanto_roof_Geo[0].thickness,
          carportConfig.leanto_roof_Geo[0].singlelength
        );

        const lastMonoPlateLine = noSheets > 1 ? createCorro2DSection(
          carportConfig.leanto_roof_Geo[noSheets - 1].height,
          carportConfig.leanto_roof_Geo[noSheets - 1].thickness,
          carportConfig.leanto_roof_Geo[noSheets - 1].singlelength
        ) : monoPlateLine;

        // Create and position each roof sheet
        carportConfig.leanto_roof_Positions.forEach((position, index) => {
          const geometry = processGeometry(
            new THREE.ExtrudeGeometry(index < noSheets - 1 ? monoPlateLine : lastMonoPlateLine, {
              depth: carportConfig.leanto_roof_Geo[index].length,
              bevelEnabled: false,
            }),
            Math.PI / 2,
            0,
            Math.PI / 2
          );

          // Create material with view mode applied for lean-to roof
          const leanToRoofMaterialWithViewMode = createViewModeMaterial(roofMaterial, viewMode, ElementType.LEAN_TO_ROOF);
          const roof = new THREE.Mesh(geometry, leanToRoofMaterialWithViewMode);
          roof.position.set(position.x, position.y, position.z);

          // Apply the 2-degree pitch rotation (negative to make it lower on left, higher on right)
          roof.rotation.y = -leanToPitchRadians;

          group.add(roof);
        });
      }
      
      // 7. Main Left Drop Sheet
      if (carportConfig.main_left_drop_Positions.length > 0) {
        const noSheets = carportConfig.main_left_drop_Geo.length;
        
        // Create MonoClad section shapes for the drop sheets
        const dropMonoPlateLine = createCorro2DSection(
          carportConfig.main_left_drop_Geo[0].height,
          carportConfig.main_left_drop_Geo[0].thickness,
          carportConfig.main_left_drop_Geo[0].singlelength
        );
        
        const dropLastMonoPlateLine = noSheets > 1 ? createCorro2DSection(
          carportConfig.main_left_drop_Geo[noSheets - 1].height,
          carportConfig.main_left_drop_Geo[noSheets - 1].thickness,
          carportConfig.main_left_drop_Geo[noSheets - 1].singlelength
        ) : dropMonoPlateLine;
        
        // Create and position each drop sheet
        carportConfig.main_left_drop_Positions.forEach((position, index) => {
          // Use the appropriate shape based on whether this is the last sheet
          const shapeToUse = index === noSheets - 1 ? dropLastMonoPlateLine : dropMonoPlateLine;
          
          const geometry = processGeometry(
            new THREE.ExtrudeGeometry(shapeToUse, {
              depth: carportConfig.main_left_drop_Geo[index].length,
              bevelEnabled: false,
            }),
            0,         // No rotation around X (keep vertical)
            Math.PI,   // Rotate 180 degrees around Y to make it vertical
            Math.PI/2  // Rotate 90 degrees around Z
          );
          
          // Create material with view mode applied for main drop sheet
          const mainDropMaterialWithViewMode = createViewModeMaterial(roofMaterial, viewMode, ElementType.MAIN_DROP);
          const dropSheet = new THREE.Mesh(geometry, mainDropMaterialWithViewMode);
          dropSheet.position.set(position.x, position.y, position.z);
          group.add(dropSheet);
        });
      }
    } catch (error) {
      console.error('Error creating left lean-to components:', error);
    }

    scene.add(group);
    return () => disposeAll(group);
  }, [
    scene,
    leftLeanTo,
    leftLeanToSpan,
    leftLeanToDropHeight,
    carportConfig.leanto_post_left_Positions,
    carportConfig.leanto_post_left_Geo,
    carportConfig.leanto_footing_Positions,
    carportConfig.footing_Geo,
    carportConfig.leanto_roof_Positions,
    carportConfig.leanto_roof_Geo,
    carportConfig.main_left_drop_Positions,
    carportConfig.main_left_drop_Geo,
    length,
    height,
    overhang,
    post_size,
    viewMode,
    showSlabValue
  ]);

  // RIGHT LEAN-TO COMPONENTS - Separate useEffect for independence
  useEffect(() => {
    if (rightLeanTo===false) return;
    
    const group = new THREE.Group();
    
    // Calculate the adjusted height for lean-to (main height minus drop height)
    const leanToHeight = (height! - rightLeanToDropHeight!) / 1000;
    const leanToPitchRadians = (2 * Math.PI) / 180; // 2-degree pitch for lean-to
    
    try {
      // 1. Right Lean-to Posts
      carportConfig.gable_leanto_post_right_Positions.forEach((position, index) => {
        const geo = carportConfig.gable_leanto_post_right_Geo[index];
        const shsShape = createSHSShape(geo.geo_x, geo.geo_y);
        const extrudeSettings = { depth: geo.geo_z, bevelEnabled: false };
        const postGeometry = new THREE.ExtrudeGeometry(shsShape, extrudeSettings);
        const post = new THREE.Mesh(postGeometry, postMaterial);

        // Position the post
        post.position.set(position.x, position.y, position.z);
        group.add(post);
      });

      // 2. Right Lean-to Rafters
      carportConfig.gable_leanto_rafter_right_Positions.forEach((position, index) => {
        // Create a C-section shape for the rafter
        const rafterShape = createCeeSectionShape(
          carportConfig.gable_leanto_rafter_right_Geo.geo_x,
          carportConfig.gable_leanto_rafter_right_Geo.geo_y,
          carportConfig.gable_leanto_rafter_right_Geo.flange_thickness
        );

        // Process the geometry - using the same rotation logic as main rafters
        const rafterGeometry = processGeometry(
          new THREE.ExtrudeGeometry(rafterShape, {
            depth: carportConfig.gable_leanto_rafter_right_Geo.geo_z,
            bevelEnabled: false,
          }),
          Math.PI / 2 - (2 * Math.PI) / 180, // Same rotation as main rafters but with 2-degree pitch
          Math.PI,
          Math.PI / 2 // Mirror of left lean-to
        );

        const rafter = new THREE.Mesh(rafterGeometry, rafterMaterial);

        // Position the rafter
        rafter.position.set(position.x, position.y, position.z);

        // Apply rotation logic based on rafter position
        if (index === 0) { // First rafter needs different rotation
          rafter.rotation.y = 0; // Positive 2-degree pitch for right side
          rafter.rotation.z = 0;
        } else if (index === carportConfig.gable_leanto_rafter_right_Positions.length - 1) { // Last rafter
          rafter.rotation.y = 2 * (2 * Math.PI) / 180; // Positive 2-degree pitch for right side
          rafter.rotation.z = Math.PI;
        }

        group.add(rafter);
      });
      // 3. Right Lean-to Right EP
      if (carportConfig.gable_leanto_right_EP_Positions.length > 0) {
        // Create the Cee section shape for lean-to right EP
        const leantoRightEPShape = createCeeSectionShape(
          carportConfig.gable_leanto_right_EP_Geo.geo_x,
          carportConfig.gable_leanto_right_EP_Geo.geo_y,
          carportConfig.gable_leanto_right_EP_Geo.flange_thickness
        );

        // Process the geometry - using the same rotation logic as main left EP
        const gable_right_leanto_EP_Geometry = processGeometry(
          new THREE.ExtrudeGeometry(leantoRightEPShape, {
            depth: carportConfig.gable_leanto_right_EP_Geo.geo_z,
            bevelEnabled: false,
          }),
          Math.PI / 2,
          0,
          0
        );

        // Create and position the lean-to right EP
        carportConfig.gable_leanto_right_EP_Positions.forEach((position) => {
          const leantoRightEP = new THREE.Mesh(gable_right_leanto_EP_Geometry, rafterMaterial);
          leantoRightEP.position.set(position.x, position.y, position.z);
          group.add(leantoRightEP);
        });
      }
      // 3. Right Lean-to Purlins
      if (carportConfig.gable_leanto_right_flat_purlin_Positions.length > 0) {
        // Create the Cee section shape for right lean-to flat purlin
        const leantoRightFlatPurlinShape = createCeeSectionShape(
          carportConfig.gable_leanto_right_flat_purlin_Geo.geo_x,
          carportConfig.gable_leanto_right_flat_purlin_Geo.geo_y,
          0.002
        );

        // Process the geometry - using the same rotation logic as main purlin
        const leantoRightFlatPurlinGeometry = processGeometry(
          new THREE.ExtrudeGeometry(leantoRightFlatPurlinShape, {
            depth: carportConfig.gable_leanto_right_flat_purlin_Geo.geo_z,
            bevelEnabled: false,
          }),
          Math.PI / 2,
          (2 * Math.PI) / 180, // Use 2-degree pitch (positive for right side, opposite of left lean-to)
          0
        );

        // Create and position the right lean-to flat purlins
        carportConfig.gable_leanto_right_flat_purlin_Positions.forEach((position) => {
          const leantoRightFlatPurlin = new THREE.Mesh(leantoRightFlatPurlinGeometry, rafterMaterial);
          leantoRightFlatPurlin.position.set(position.x, position.y, position.z);
          group.add(leantoRightFlatPurlin);
        });
      }

      // 4. Right Lean-to Slab
      // Only create and add slab if showSlab is true
      if (showSlabValue) {
        const rightSlabGeometry = new THREE.BoxGeometry(
          (rightLeanToSpan || 1000) / 1000, // Use default if undefined
          carportConfig.leanto_right_slab_Geo.length,
          carportConfig.leanto_right_slab_Geo.thickness
        );
        
        const rightSlab = new THREE.Mesh(rightSlabGeometry, slabMaterial);
        rightSlab.position.set(
          (overhang! + span! + rightLeanToSpan! / 2) / 1000,
          length! / 2 / 1000,
          -0.1
        );
        
        group.add(rightSlab);
      }

      // 5. Right Lean-to Roof
      if (carportConfig.gable_leanto_right_roof_Positions && carportConfig.gable_leanto_right_roof_Positions.length > 0) {
        const noSheets = carportConfig.gable_leanto_right_roof_Geo.length;

        // Create MonoClad section shapes
        const monoPlateLine = createCorro2DSection(
          carportConfig.gable_leanto_right_roof_Geo[0].height,
          carportConfig.gable_leanto_right_roof_Geo[0].thickness,
          carportConfig.gable_leanto_right_roof_Geo[0].singlelength
        );

        const lastMonoPlateLine = noSheets > 1 ? createCorro2DSection(
          carportConfig.gable_leanto_right_roof_Geo[noSheets - 1].height,
          carportConfig.gable_leanto_right_roof_Geo[noSheets - 1].thickness,
          carportConfig.gable_leanto_right_roof_Geo[noSheets - 1].singlelength
        ) : monoPlateLine;

        // Create and position each roof sheet - exactly like left lean-to
        carportConfig.gable_leanto_right_roof_Positions.forEach((position, index) => {
          const geometry = processGeometry(
            new THREE.ExtrudeGeometry(index < noSheets - 1 ? monoPlateLine : lastMonoPlateLine, {
              depth: carportConfig.gable_leanto_right_roof_Geo[index].length,
              bevelEnabled: false,
            }),
            Math.PI / 2,
            0,
            Math.PI / 2
          );

          // Create material with view mode applied for lean-to roof
          const leanToRoofMaterialWithViewMode = createViewModeMaterial(roofMaterial, viewMode, ElementType.LEAN_TO_ROOF);
          const roof = new THREE.Mesh(geometry, leanToRoofMaterialWithViewMode);
          roof.position.set(position.x, position.y, position.z);

          // Apply the 2-degree pitch rotation (positive for right side, opposite of left lean-to)
          roof.rotation.y = leanToPitchRadians; // Positive rotation for right lean-to

          group.add(roof);
        });
        
        // Right Lean-to Barge Cap Flashing
        // Barge cap parameters
        const plateWidth = 0.17; // 170mm width for each plate
        const bargeCapThickness = 0.001; // 1mm thickness
        const bargeCapLength = length! / 1000; // Same length as the carport
        
        // Create the barge cap shape based on the lean-to pitch
        const rightLeanToBargeCapShape = createRightBargeCapFlashing2DShape(plateWidth, bargeCapThickness, 2); // 2-degree pitch
        
        // Process the geometry
        const rightLeanToBargeCapGeometry = processGeometry(
          new THREE.ExtrudeGeometry(rightLeanToBargeCapShape, {
            depth: bargeCapLength,
            bevelEnabled: false,
          }),
          Math.PI,
          Math.PI,
          -Math.PI / 2 // Opposite rotation for right side
        );
        
        // Calculate the position for the right lean-to barge cap
        // It should be at the right edge of the lean-to roof with no overhang
        const x = (overhang! + span! + rightLeanToSpan!) / 1000 - 0.05; // Right edge of lean-to
        const y = length! / 2 / 1000; // Center position along the length
        // Use the z-position based on the lean-to height
        const z = leanToHeight - 0.07;
        
        // Create the mesh and add to the scene
        // Create material with view mode applied for right lean-to barge cap
        const rightLeanToBargeMaterialWithViewMode = createViewModeMaterial(capMaterial, viewMode, ElementType.BARGE);
        const rightLeanToBargeCap = new THREE.Mesh(rightLeanToBargeCapGeometry, rightLeanToBargeMaterialWithViewMode);
        rightLeanToBargeCap.position.set(x, y, z);
        
        // Rotate the barge cap to align with the lean-to roof
        rightLeanToBargeCap.rotation.x = Math.PI / 2;
        
        group.add(rightLeanToBargeCap);
      }

      // 6. Right Lean-to Knee Braces
      // Use the same material as rafters for consistent appearance
      const rightLeanToKneeMaterial = rafterMaterial.clone();
      rightLeanToKneeMaterial.transparent = true;
      rightLeanToKneeMaterial.opacity = 0.8;

      // Use fixed values for knee braces - same as left lean-to
      const rightWebSize = .05; // 50mm
      const rightFlangeSize = 0.102; // 102mm

      // Create a C-section shape for the knee brace
      const rightLeanToKneeShape = createCeeSectionShape(rightWebSize, rightFlangeSize, 0.002);

      // Variables for knee geometry - same as left lean-to
      let rightLeanToKneeGeometry;
      const rightLeanToKneeFactor = 0.15; // Same as main knee factor for 2-degree pitch
      const rightLeanToKneeOffset = 105; // Same as main knee offset for 2-degree pitch
      const rightLeanToPitchRadians = (2 * Math.PI) / 180; // 2-degree pitch in radians

      try {
        // Create and process the extruded geometry
        const rightLeanToExtrudedGeometry = new THREE.ExtrudeGeometry(rightLeanToKneeShape, {
          depth: 0.15 * (height! - rightLeanToDropHeight!) / 1000 + Math.sin(rightLeanToPitchRadians) * rightLeanToKneeFactor * (height! - rightLeanToDropHeight!) / 1000,
          bevelEnabled: false
        });

        // Process the geometry for right lean-to knee orientation
        rightLeanToKneeGeometry = processGeometry(
          rightLeanToExtrudedGeometry.clone(),
          -Math.PI / 4, // Rotation around X
          0,        // No rotation around Y
          Math.PI / 4 // Rotation around Z
        );
      } catch (error) {
        console.error('Error creating right lean-to C-section geometry:', error);
        // Fallback to box geometry if there's an error - same as left lean-to
        rightLeanToKneeGeometry = new THREE.BoxGeometry(0.6, 0.05, 0.05);
      }

      // For each bay, create right lean-to knee braces
      for (let i = 0; i <= Math.max(1, Math.floor(length! / 4000)); i++) {
        try {
          // Calculate the positions
          // Position at right lean-to post
          const rightLeanToPostX = (overhang! + span! + rightLeanToKneeOffset+rightLeanToSpan!-300) / 1000;

          const postY = i === Math.floor(length! / 4000)
            ? (length! - post_size) / 1000
            : (post_size + (i * length!) / Math.max(1, Math.floor(length! / 4000))) / 1000;

          // Calculate dynamic height using similar formula as main knee braces
          const rightLeanToSpanDrop = rightLeanToSpan! * Math.tan(rightLeanToPitchRadians);

          // Calculate rafter position for right lean-to
          // Use 152 for Rafter_web value as defined in CarportParams.ts - same as left lean-to
          const rightLeanToRafterZ = ((height! - rightLeanToDropHeight!) + rightLeanToSpanDrop / 2 - (0.25 * 152) / Math.cos(rightLeanToPitchRadians)) / 1000;

          // Position at the post, 90% up from the ground
          const rightLeanToPostZ = rightLeanToRafterZ * 0.92;

          // Create the right lean-to knee brace mesh
          const rightLeanToKneeBrace = new THREE.Mesh(rightLeanToKneeGeometry, rightLeanToKneeMaterial);
          rightLeanToKneeBrace.position.set(rightLeanToPostX, postY, rightLeanToPostZ);
          rightLeanToKneeBrace.rotation.z = Math.PI / 4;
          group.add(rightLeanToKneeBrace);
        } catch (error) {
          console.error('Error creating right lean-to knee brace:', error);
        }
      }
    } catch (error) {
      console.error('Error creating right lean-to components:', error);
    }
    if (carportConfig.gable_leanto_right_footing_Positions && carportConfig.gable_leanto_right_footing_Positions.length > 0) {
      carportConfig.gable_leanto_right_footing_Positions.forEach((position) => {
        const geometry = new THREE.CylinderGeometry(
          carportConfig.gable_leanto_right_footing_Geo.radius,
          carportConfig.gable_leanto_right_footing_Geo.radius,
          carportConfig.gable_leanto_right_footing_Geo.height,
          32
        );
        const FOOTING_GEO = processGeometry(geometry, Math.PI / 2, 0, 0);
        const leantoRightFooting = new THREE.Mesh(FOOTING_GEO, footingMaterial);
        leantoRightFooting.position.set(position.x, position.y, position.z);
        group.add(leantoRightFooting);
      });
    }
    
    // 6. Right Main Drop Sheets
    if (carportConfig.gable_main_right_drop_Positions && carportConfig.gable_main_right_drop_Positions.length > 0) {
      const noSheets = carportConfig.gable_main_right_drop_Geo.length;

      // Create Corro section shapes for the drop sheets
      const dropMonoPlateLine = createCorro2DSection(
        carportConfig.gable_main_right_drop_Geo[0].height,
        carportConfig.gable_main_right_drop_Geo[0].thickness,
        carportConfig.gable_main_right_drop_Geo[0].singlelength
      );

      const dropLastMonoPlateLine = noSheets > 1 ? createCorro2DSection(
        carportConfig.gable_main_right_drop_Geo[noSheets - 1].height,
        carportConfig.gable_main_right_drop_Geo[noSheets - 1].thickness,
        carportConfig.gable_main_right_drop_Geo[noSheets - 1].singlelength
      ) : dropMonoPlateLine;

      // Create and position each drop sheet
      carportConfig.gable_main_right_drop_Positions.forEach((position, index) => {
        // Use the appropriate shape based on whether this is the last sheet
        const shapeToUse = index === noSheets - 1 ? dropLastMonoPlateLine : dropMonoPlateLine;
        
        const geometry = processGeometry(
          new THREE.ExtrudeGeometry(shapeToUse, {
            depth: carportConfig.gable_main_right_drop_Geo[index].length,
            bevelEnabled: false,
          }),
          0,         // No rotation around X (keep vertical)
          Math.PI,   // Rotate 180 degrees around Y to make it vertical
          Math.PI / 2  // Rotate 90 degrees around Z
        );

        // Create material with view mode applied for main drop sheet
        const mainDropMaterialWithViewMode = createViewModeMaterial(roofMaterial, viewMode, ElementType.MAIN_DROP);
        const dropSheet = new THREE.Mesh(geometry, mainDropMaterialWithViewMode);
        dropSheet.position.set(position.x, position.y, position.z);
        group.add(dropSheet);
      });
    }
    
    scene.add(group);
    return () => disposeAll(group);
  }, [
    scene,
    rightLeanTo,
    rightLeanToSpan,
    rightLeanToDropHeight,
    carportConfig.gable_leanto_post_right_Positions,
    carportConfig.gable_leanto_post_right_Geo,
    carportConfig.gable_leanto_rafter_right_Positions,
    carportConfig.gable_leanto_rafter_right_Geo,
    carportConfig.gable_leanto_right_footing_Positions,
    carportConfig.gable_leanto_right_footing_Geo,
    carportConfig.gable_main_right_drop_Positions,
    carportConfig.gable_main_right_drop_Geo,
    length,
    height,
    overhang,
    post_size,
    viewMode,
    showSlabValue
  ]);

  useEffect(() => {
    const slabVisible = showSlabValue !== undefined ? showSlabValue : true;
    // Use scene.traverse to find and update the slab visibility
    scene.traverse((child) => {
      if (child instanceof THREE.Mesh && child.userData.type === 'slab') {
        child.visible = slabVisible;
      }
    });
  }, [
    scene,
    showSlabValue
  ]);
  
  // Using same approach for girt materials as in Carport.tsx
  
  // Render Main Left Wall Girts
  useEffect(() => {
    // Debugging: log whether we're trying to render girts for this wall
    
    // Skip if no girts to render
    if (!girtConfig.mainLeftGirt?.geometries || girtConfig.mainLeftGirt.geometries.length === 0) {
      return;
    }
    
    const group = new THREE.Group();
    
    // Create a material for all girts in this wall - using same approach as in Carport.tsx
    const girtMaterial = createViewModeMaterial(rafterMaterial, currentViewMode, ElementType.KNEE_BRACE);
    
    // Render each girt - exactly like in Carport.tsx
    girtConfig.mainLeftGirt.geometries.forEach((geo, index) => {
      // Create Z section shape for girt using standard Z-section helper function
      const girtShape = createStandardZeeSectionShape(
        Girt_web/1000,       // Web width
        Girt_flangeE/1000,   // Flange E dimension
        Girt_flangeF/1000,   // Flange F dimension
        Girt_lip/1000,       // Lip dimension
        Girt_thickness/1000  // Material thickness
      );
      
      // Process the geometry with proper orientation
      // Scale down by 1000 since we're working in meters in the scene
      const girtGeometry = processGeometry(
        new THREE.ExtrudeGeometry(girtShape, {
          depth: geo.geo_z,
          bevelEnabled: false
        }),
        Math.PI / 2, // Rotate 90 degrees around X
        0,           // No rotation around Y
        0            // No rotation around Z
      );
      
      // Create mesh and position it
      const position = girtConfig.mainLeftGirt.positions[index];
      const girt = new THREE.Mesh(girtGeometry, girtMaterial);
      girt.position.set(position.x, position.y, position.z);
      
      // Apply rotation if specified
      if (position.rotation) {
        girt.rotation.set(position.rotation.x, position.rotation.y, position.rotation.z);
      }
      
      group.add(girt);
    });
    
    scene.add(group);
    
    return () => {
      scene.remove(group);
      disposeAll(group);
    };
  }, [girtConfig.mainLeftGirt, currentViewMode, scene]);
  
  // Render Main Right Wall Girts
  useEffect(() => {
    // Skip if no girts to render
    if (!girtConfig.mainRightGirt?.geometries || girtConfig.mainRightGirt.geometries.length === 0) {
      return;
    }
    
    const group = new THREE.Group();
    
    // Create a material for all girts in this wall - using same approach as in Carport.tsx
    const girtMaterial = createViewModeMaterial(rafterMaterial, currentViewMode, ElementType.KNEE_BRACE);
    
    // Render each girt - exactly like in Carport.tsx
    girtConfig.mainRightGirt.geometries.forEach((geo, index) => {
      // Create Z section shape for girt using standard Z-section helper function
      const girtShape = createStandardZeeSectionShape(
        Girt_web/1000,       // Web width
        Girt_flangeE/1000,   // Flange E dimension
        Girt_flangeF/1000,   // Flange F dimension
        Girt_lip/1000,       // Lip dimension
        Girt_thickness/1000  // Material thickness
      );
      
      // Process the geometry with proper orientation
      const girtGeometry = processGeometry(
        new THREE.ExtrudeGeometry(girtShape, {
          depth: geo.geo_z,
          bevelEnabled: false
        }),
        Math.PI / 2, // Rotate 90 degrees around X
        0,           // No rotation around Y
        0            // No rotation around Z
      );
      
      // Create mesh and position it
      const position = girtConfig.mainRightGirt.positions[index];
      const girt = new THREE.Mesh(girtGeometry, girtMaterial);
      girt.position.set(position.x, position.y, position.z);
      
      // Apply rotation if specified
      if (position.rotation) {
        girt.rotation.set(position.rotation.x, position.rotation.y, position.rotation.z);
      }
      
      group.add(girt);
    });
    
    scene.add(group);
    
    return () => {
      scene.remove(group);
      disposeAll(group);
    };
  }, [girtConfig.mainRightGirt, currentViewMode, scene]);
  
  // Render Front Wall Girts
  useEffect(() => {
    // Skip if no girts to render
    if (!girtConfig.mainFrontGirt?.geometries || girtConfig.mainFrontGirt.geometries.length === 0) {
      return;
    }
    
    const group = new THREE.Group();
    
    // Create a material for all girts in this wall - using same approach as in Carport.tsx
    const girtMaterial = createViewModeMaterial(rafterMaterial, currentViewMode, ElementType.KNEE_BRACE);
    
    // Render each girt - exactly like in Carport.tsx
    girtConfig.mainFrontGirt.geometries.forEach((geo, index) => {
      // Create Z section shape for girt using standard Z-section helper function
      const girtShape = createStandardZeeSectionShape(
        Girt_web/1000,       // Web width
        Girt_flangeE/1000,   // Flange E dimension
        Girt_flangeF/1000,   // Flange F dimension
        Girt_lip/1000,       // Lip dimension
        Girt_thickness/1000  // Material thickness
      );
      
      // Process the geometry with proper orientation
      const girtGeometry = processGeometry(
        new THREE.ExtrudeGeometry(girtShape, {
          depth: geo.geo_z,
          bevelEnabled: false
        }),
        Math.PI/2 , // Rotate 90 degrees around X
        0,           // No rotation around Y
        Math.PI/2            
      );
      
      // Create mesh and position it
      const position = girtConfig.mainFrontGirt.positions[index];
      const girt = new THREE.Mesh(girtGeometry, girtMaterial);
      girt.position.set(position.x, position.y, position.z);
      
      // Apply rotation if specified
      if (position.rotation) {
        girt.rotation.set(position.rotation.x, position.rotation.y, position.rotation.z);
      }
      
      group.add(girt);
    });
    
    scene.add(group);
    
    return () => {
      scene.remove(group);
      disposeAll(group);
    };
  }, [girtConfig.mainFrontGirt, currentViewMode, scene]);
  
  // Render Back Wall Girts
  useEffect(() => {
    // Skip if no girts to render
    if (!girtConfig.mainBackGirt?.geometries || girtConfig.mainBackGirt.geometries.length === 0) {
      return;
    }
    
    const group = new THREE.Group();
    
    // Create a material for all girts in this wall - using same approach as in Carport.tsx
    const girtMaterial = createViewModeMaterial(rafterMaterial, currentViewMode, ElementType.KNEE_BRACE);
    
    // Render each girt - exactly like in Carport.tsx
    girtConfig.mainBackGirt.geometries.forEach((geo, index) => {
      // Create Z section shape for girt using standard Z-section helper function
      const girtShape = createStandardZeeSectionShape(
        Girt_web/1000,       // Web width
        Girt_flangeE/1000,   // Flange E dimension
        Girt_flangeF/1000,   // Flange F dimension
        Girt_lip/1000,       // Lip dimension
        Girt_thickness/1000  // Material thickness
      );
      
      // Process the geometry with proper orientation
      const girtGeometry = processGeometry(
        new THREE.ExtrudeGeometry(girtShape, {
          depth: geo.geo_z,
          bevelEnabled: false
        }),
        Math.PI / 2, // Rotate 90 degrees around X
        0,           // No rotation around Y
        Math.PI/2            // No rotation around Z
      );
      
      // Create mesh and position it
      const position = girtConfig.mainBackGirt.positions[index];
      const girt = new THREE.Mesh(girtGeometry, girtMaterial);
      girt.position.set(position.x, position.y, position.z);
      
      // Apply rotation if specified
      if (position.rotation) {
        girt.rotation.set(position.rotation.x, position.rotation.y, position.rotation.z);
      }
      
      group.add(girt);
    });
    
    scene.add(group);
    
    return () => {
      scene.remove(group);
      disposeAll(group);
    };
  }, [girtConfig.mainBackGirt, currentViewMode, scene]);
  
  // Render Divider Wall Girts
  useEffect(() => {
    // Skip if no girts to render
    if (!girtConfig.mainDividerGirt?.geometries || girtConfig.mainDividerGirt.geometries.length === 0) {
      return;
    }
    
    const group = new THREE.Group();
    
    // Create a material for all girts in this wall - using same approach as in Carport.tsx
    const girtMaterial = createViewModeMaterial(rafterMaterial, currentViewMode, ElementType.KNEE_BRACE);
    
    // Render each girt - exactly like in Carport.tsx
    girtConfig.mainDividerGirt.geometries.forEach((geo, index) => {
      // Create Z section shape for girt using standard Z-section helper function
      const girtShape = createStandardZeeSectionShape(
        Girt_web/1000,       // Web width
        Girt_flangeE/1000,   // Flange E dimension
        Girt_flangeF/1000,   // Flange F dimension
        Girt_lip/1000,       // Lip dimension
        Girt_thickness/1000  // Material thickness
      );
      
      // Process the geometry with proper orientation
      const girtGeometry = processGeometry(
        new THREE.ExtrudeGeometry(girtShape, {
          depth: geo.geo_z,
          bevelEnabled: false
        }),
        Math.PI / 2, // Rotate 90 degrees around X
        0,           // No rotation around Y
        Math.PI/2            // No rotation around Z
      );
      
      // Create mesh and position it
      const position = girtConfig.mainDividerGirt.positions[index];
      const girt = new THREE.Mesh(girtGeometry, girtMaterial);
      girt.position.set(position.x, position.y, position.z);
      
      // Apply rotation if specified
      if (position.rotation) {
        girt.rotation.set(position.rotation.x, position.rotation.y, position.rotation.z);
      }
      
      group.add(girt);
    });
    
    scene.add(group);
    
    return () => {
      scene.remove(group);
      disposeAll(group);
    };
  }, [girtConfig.mainDividerGirt, currentViewMode, scene]);

  // Render Left Lean-to Wall Girts
  useEffect(() => {
    // Skip if no girts to render or lean-to is disabled
    if (!leftLeanTo || !girtConfig.leftLeanToGirt?.geometries || girtConfig.leftLeanToGirt.geometries.length === 0) {
      return;
    }
    
    
    const group = new THREE.Group();
    
    // Create a material for all girts in this wall
    const girtMaterial = createViewModeMaterial(rafterMaterial, currentViewMode, ElementType.KNEE_BRACE);
    
    // Render each girt
    girtConfig.leftLeanToGirt.geometries.forEach((geo, index) => {
      // Create Z section shape for girt using standard Z-section helper function
      const girtShape = createStandardZeeSectionShape(
        Girt_web/1000,       // Web width
        Girt_flangeE/1000,   // Flange E dimension
        Girt_flangeF/1000,   // Flange F dimension
        Girt_lip/1000,       // Lip dimension
        Girt_thickness/1000  // Material thickness
      );
      
      // Process the geometry with proper orientation
      const girtGeometry = processGeometry(
        new THREE.ExtrudeGeometry(girtShape, {
          depth: geo.geo_z,
          bevelEnabled: false
        }),
        0,           // No rotation around X
        -Math.PI/2,  // Rotate 90 degrees around Y
        Math.PI      // Rotate 180 degrees around Z
      );
      
      // Create mesh and position it
      const position = girtConfig.leftLeanToGirt.positions[index];
      const girt = new THREE.Mesh(girtGeometry, girtMaterial);
      girt.position.set(position.x, position.y, position.z);
      
      // Apply rotation if specified
      if (position.rotation) {
        girt.rotation.set(position.rotation.x, position.rotation.y, position.rotation.z);
      }
      
      group.add(girt);
    });
    
    scene.add(group);
    
    return () => {
      scene.remove(group);
      disposeAll(group);
    };
  }, [girtConfig.leftLeanToGirt, currentViewMode, scene, leftLeanTo]);
  
  // Render Right Lean-to Wall Girts
  useEffect(() => {
    // Skip if no girts to render or lean-to is disabled
    if (!rightLeanTo || !girtConfig.rightLeanToGirt?.geometries || girtConfig.rightLeanToGirt.geometries.length === 0) {
      return;
    }
    
    
    const group = new THREE.Group();
    
    // Create a material for all girts in this wall
    const girtMaterial = createViewModeMaterial(rafterMaterial, currentViewMode, ElementType.KNEE_BRACE);
    
    // Render each girt
    girtConfig.rightLeanToGirt.geometries.forEach((geo, index) => {
      // Create Z section shape for girt using standard Z-section helper function
      const girtShape = createStandardZeeSectionShape(
        Girt_web/1000,       // Web width
        Girt_flangeE/1000,   // Flange E dimension
        Girt_flangeF/1000,   // Flange F dimension
        Girt_lip/1000,       // Lip dimension
        Girt_thickness/1000  // Material thickness
      );
      
      // Process the geometry with proper orientation
      const girtGeometry = processGeometry(
        new THREE.ExtrudeGeometry(girtShape, {
          depth: geo.geo_z,
          bevelEnabled: false
        }),
        0,          // No rotation around X
        Math.PI/2,  // Rotate 90 degrees around Y
        0           // No rotation around Z
      );
      
      // Create mesh and position it
      const position = girtConfig.rightLeanToGirt.positions[index];
      const girt = new THREE.Mesh(girtGeometry, girtMaterial);
      girt.position.set(position.x, position.y, position.z);
      
      // Apply rotation if specified
      if (position.rotation) {
        girt.rotation.set(position.rotation.x, position.rotation.y, position.rotation.z);
      }
      
      group.add(girt);
    });
    
    scene.add(group);
    
    return () => {
      scene.remove(group);
      disposeAll(group);
    };
  }, [girtConfig.rightLeanToGirt, currentViewMode, scene, rightLeanTo]);

  // Render Left Lean-to Front Wall Girts
  useEffect(() => {
    // Skip if no girts to render or lean-to is disabled
    if (!leftLeanTo || !girtConfig.leftLeanToFrontGirt?.geometries || girtConfig.leftLeanToFrontGirt.geometries.length === 0) {
      return;
    }
    
    
    const group = new THREE.Group();
    
    // Create a material for all girts in this wall
    const girtMaterial = createViewModeMaterial(rafterMaterial, currentViewMode, ElementType.KNEE_BRACE);
    
    // Render each girt
    girtConfig.leftLeanToFrontGirt.geometries.forEach((geo, index) => {
      // Create Z section shape for girt
      const girtShape = createStandardZeeSectionShape(
        Girt_web/1000,       // Web width
        Girt_flangeE/1000,   // Flange E dimension
        Girt_flangeF/1000,   // Flange F dimension
        Girt_lip/1000,       // Lip dimension
        Girt_thickness/1000  // Material thickness
      );
      
      // Process the geometry with proper orientation
      const girtGeometry = processGeometry(
        new THREE.ExtrudeGeometry(girtShape, {
          depth: geo.geo_z,
          bevelEnabled: false
        }),
        Math.PI/2,  // Rotate 90 degrees around X
        0,          // No rotation around Y
        Math.PI/2   // Rotate 90 degrees around Z
      );
      
      // Create mesh and position it
      const position = girtConfig.leftLeanToFrontGirt.positions[index];
      const girt = new THREE.Mesh(girtGeometry, girtMaterial);
      girt.position.set(position.x, position.y, position.z);
      
      // Apply rotation if specified
      if (position.rotation) {
        girt.rotation.set(position.rotation.x, position.rotation.y, position.rotation.z);
      }
      
      group.add(girt);
    });
    
    scene.add(group);
    
    return () => {
      scene.remove(group);
      disposeAll(group);
    };
  }, [girtConfig.leftLeanToFrontGirt, currentViewMode, scene, leftLeanTo]);
  
  // Render Left Lean-to Back Wall Girts
  useEffect(() => {
    // Skip if no girts to render or lean-to is disabled
    if (!leftLeanTo || !girtConfig.leftLeanToBackGirt?.geometries || girtConfig.leftLeanToBackGirt.geometries.length === 0) {
      return;
    }
    
    
    const group = new THREE.Group();
    
    // Create a material for all girts in this wall
    const girtMaterial = createViewModeMaterial(rafterMaterial, currentViewMode, ElementType.KNEE_BRACE);
    
    // Render each girt
    girtConfig.leftLeanToBackGirt.geometries.forEach((geo, index) => {
      // Create Z section shape for girt
      const girtShape = createStandardZeeSectionShape(
        Girt_web/1000,       // Web width
        Girt_flangeE/1000,   // Flange E dimension
        Girt_flangeF/1000,   // Flange F dimension
        Girt_lip/1000,       // Lip dimension
        Girt_thickness/1000  // Material thickness
      );
      
      // Process the geometry with proper orientation
      const girtGeometry = processGeometry(
        new THREE.ExtrudeGeometry(girtShape, {
          depth: geo.geo_z,
          bevelEnabled: false
        }),
        Math.PI/2,  // Rotate 90 degrees around X
        0,          // No rotation around Y
        Math.PI/2   // Rotate 90 degrees around Z
      );
      
      // Create mesh and position it
      const position = girtConfig.leftLeanToBackGirt.positions[index];
      const girt = new THREE.Mesh(girtGeometry, girtMaterial);
      girt.position.set(position.x, position.y, position.z);
      
      // Apply rotation if specified
      if (position.rotation) {
        girt.rotation.set(position.rotation.x, position.rotation.y, position.rotation.z);
      }
      
      group.add(girt);
    });
    
    scene.add(group);
    
    return () => {
      scene.remove(group);
      disposeAll(group);
    };
  }, [girtConfig.leftLeanToBackGirt, currentViewMode, scene, leftLeanTo]);
  
  // Render Left Lean-to Divider Wall Girts
  useEffect(() => {
    // Skip if no girts to render or lean-to is disabled
    if (!leftLeanTo || !girtConfig.leftLeanToDividerGirt?.geometries || girtConfig.leftLeanToDividerGirt.geometries.length === 0) {
      return;
    }
    
    
    const group = new THREE.Group();
    
    // Create a material for all girts in this wall
    const girtMaterial = createViewModeMaterial(rafterMaterial, currentViewMode, ElementType.KNEE_BRACE);
    
    // Render each girt
    girtConfig.leftLeanToDividerGirt.geometries.forEach((geo, index) => {
      // Create Z section shape for girt
      const girtShape = createStandardZeeSectionShape(
        Girt_web/1000,       // Web width
        Girt_flangeE/1000,   // Flange E dimension
        Girt_flangeF/1000,   // Flange F dimension
        Girt_lip/1000,       // Lip dimension
        Girt_thickness/1000  // Material thickness
      );
      
      // Process the geometry with proper orientation
      const girtGeometry = processGeometry(
        new THREE.ExtrudeGeometry(girtShape, {
          depth: geo.geo_z,
          bevelEnabled: false
        }),
        Math.PI/2,  // Rotate 90 degrees around X
        0,          // No rotation around Y
        Math.PI/2   // Rotate 90 degrees around Z
      );
      
      // Create mesh and position it
      const position = girtConfig.leftLeanToDividerGirt.positions[index];
      const girt = new THREE.Mesh(girtGeometry, girtMaterial);
      girt.position.set(position.x, position.y, position.z);
      
      // Apply rotation if specified
      if (position.rotation) {
        girt.rotation.set(position.rotation.x, position.rotation.y, position.rotation.z);
      }
      
      group.add(girt);
    });
    
    scene.add(group);
    
    return () => {
      scene.remove(group);
      disposeAll(group);
    };
  }, [girtConfig.leftLeanToDividerGirt, currentViewMode, scene, leftLeanTo]);
  
  // Render Right Lean-to Front Wall Girts
  useEffect(() => {
    // Skip if no girts to render or lean-to is disabled
    if (!rightLeanTo || !girtConfig.rightLeanToFrontGirt?.geometries || girtConfig.rightLeanToFrontGirt.geometries.length === 0) {
      return;
    }
    
    
    const group = new THREE.Group();
    
    // Create a material for all girts in this wall
    const girtMaterial = createViewModeMaterial(rafterMaterial, currentViewMode, ElementType.KNEE_BRACE);
    
    // Render each girt
    girtConfig.rightLeanToFrontGirt.geometries.forEach((geo, index) => {
      // Create Z section shape for girt
      const girtShape = createStandardZeeSectionShape(
        Girt_web/1000,       // Web width
        Girt_flangeE/1000,   // Flange E dimension
        Girt_flangeF/1000,   // Flange F dimension
        Girt_lip/1000,       // Lip dimension
        Girt_thickness/1000  // Material thickness
      );
      
      // Process the geometry with proper orientation
      const girtGeometry = processGeometry(
        new THREE.ExtrudeGeometry(girtShape, {
          depth: geo.geo_z,
          bevelEnabled: false
        }),
        Math.PI/2,  // Rotate 90 degrees around X
        0,          // No rotation around Y
        Math.PI/2   // Rotate 90 degrees around Z
      );
      
      // Create mesh and position it
      const position = girtConfig.rightLeanToFrontGirt.positions[index];
      const girt = new THREE.Mesh(girtGeometry, girtMaterial);
      girt.position.set(position.x, position.y, position.z);
      
      // Apply rotation if specified
      if (position.rotation) {
        girt.rotation.set(position.rotation.x, position.rotation.y, position.rotation.z);
      }
      
      group.add(girt);
    });
    
    scene.add(group);
    
    return () => {
      scene.remove(group);
      disposeAll(group);
    };
  }, [girtConfig.rightLeanToFrontGirt, currentViewMode, scene, rightLeanTo]);
  
  // Render Right Lean-to Back Wall Girts
  useEffect(() => {
    // Skip if no girts to render or lean-to is disabled
    if (!rightLeanTo || !girtConfig.rightLeanToBackGirt?.geometries || girtConfig.rightLeanToBackGirt.geometries.length === 0) {
      return;
    }
    
    
    const group = new THREE.Group();
    
    // Create a material for all girts in this wall
    const girtMaterial = createViewModeMaterial(rafterMaterial, currentViewMode, ElementType.KNEE_BRACE);
    
    // Render each girt
    girtConfig.rightLeanToBackGirt.geometries.forEach((geo, index) => {
      // Create Z section shape for girt
      const girtShape = createStandardZeeSectionShape(
        Girt_web/1000,       // Web width
        Girt_flangeE/1000,   // Flange E dimension
        Girt_flangeF/1000,   // Flange F dimension
        Girt_lip/1000,       // Lip dimension
        Girt_thickness/1000  // Material thickness
      );
      
      // Process the geometry with proper orientation
      const girtGeometry = processGeometry(
        new THREE.ExtrudeGeometry(girtShape, {
          depth: geo.geo_z,
          bevelEnabled: false
        }),
        Math.PI/2,  // Rotate 90 degrees around X
        0,          // No rotation around Y
        Math.PI/2   // Rotate 90 degrees around Z
      );
      
      // Create mesh and position it
      const position = girtConfig.rightLeanToBackGirt.positions[index];
      const girt = new THREE.Mesh(girtGeometry, girtMaterial);
      girt.position.set(position.x, position.y, position.z);
      
      // Apply rotation if specified
      if (position.rotation) {
        girt.rotation.set(position.rotation.x, position.rotation.y, position.rotation.z);
      }
      
      group.add(girt);
    });
    
    scene.add(group);
    
    return () => {
      scene.remove(group);
      disposeAll(group);
    };
  }, [girtConfig.rightLeanToBackGirt, currentViewMode, scene, rightLeanTo]);
  
  // Render Right Lean-to Divider Wall Girts
  useEffect(() => {
    // Skip if no girts to render or lean-to is disabled
    if (!rightLeanTo || !girtConfig.rightLeanToDividerGirt?.geometries || girtConfig.rightLeanToDividerGirt.geometries.length === 0) {
      return;
    }
    
    
    const group = new THREE.Group();
    
    // Create a material for all girts in this wall
    const girtMaterial = createViewModeMaterial(rafterMaterial, currentViewMode, ElementType.KNEE_BRACE);
    
    // Render each girt
    girtConfig.rightLeanToDividerGirt.geometries.forEach((geo, index) => {
      // Create Z section shape for girt
      const girtShape = createStandardZeeSectionShape(
        Girt_web/1000,       // Web width
        Girt_flangeE/1000,   // Flange E dimension
        Girt_flangeF/1000,   // Flange F dimension
        Girt_lip/1000,       // Lip dimension
        Girt_thickness/1000  // Material thickness
      );
      
      // Process the geometry with proper orientation
      const girtGeometry = processGeometry(
        new THREE.ExtrudeGeometry(girtShape, {
          depth: geo.geo_z,
          bevelEnabled: false
        }),
        Math.PI/2,  // Rotate 90 degrees around X
        0,          // No rotation around Y
        Math.PI/2   // Rotate 90 degrees around Z
      );
      
      // Create mesh and position it
      const position = girtConfig.rightLeanToDividerGirt.positions[index];
      const girt = new THREE.Mesh(girtGeometry, girtMaterial);
      girt.position.set(position.x, position.y, position.z);
      
      // Apply rotation if specified
      if (position.rotation) {
        girt.rotation.set(position.rotation.x, position.rotation.y, position.rotation.z);
      }
      
      group.add(girt);
    });
    
    scene.add(group);
    
    return () => {
      scene.remove(group);
      disposeAll(group);
    };
  }, [girtConfig.rightLeanToDividerGirt, currentViewMode, scene, rightLeanTo]);

  // Wall Sheet Rendering
  // ====================
  
  // Render Main Left Wall Sheets
  useEffect(() => {
    
    // Skip if no wall sheets to render
    if (!wallSheetConfig.mainLeftWallSheet?.geometries || wallSheetConfig.mainLeftWallSheet.geometries.length === 0) {
      return;
    }
    
    const group = new THREE.Group();
    
    // Render each wall sheet
    wallSheetConfig.mainLeftWallSheet.geometries.forEach((geo, index) => {
      // Get the appropriate material, use the one specified in the geometry or default to roof material
      const material = geo.material 
        ? createViewModeMaterial(geo.material, currentViewMode, ElementType.MAIN_DROP)
        : createViewModeMaterial(roofMaterial, currentViewMode, ElementType.MAIN_DROP);
      
      // Use simple BoxGeometry for better performance and to avoid freezing
      const wallGeometry = new THREE.BoxGeometry(
        geo.geo_x,  // Width (thickness of wall)
        geo.geo_y,  // Height
        geo.geo_z   // Length
      );
      
      // Create mesh and position it
      const position = wallSheetConfig.mainLeftWallSheet.positions[index];
      
      const wallSheet = new THREE.Mesh(wallGeometry, material);
      wallSheet.position.set(position.x, position.y, position.z);
      
      // Apply rotation if specified
      if (position.rotation) {
        wallSheet.rotation.set(position.rotation.x, position.rotation.y, position.rotation.z);
      }
      
      group.add(wallSheet);
    });
    
    scene.add(group);
    return () => disposeAll(group);
  }, [wallSheetConfig.mainLeftWallSheet, currentViewMode, scene]);
  
  // Render Main Right Wall Sheets
  useEffect(() => {
    
    // Skip if no wall sheets to render
    if (!wallSheetConfig.mainRightWallSheet?.geometries || wallSheetConfig.mainRightWallSheet.geometries.length === 0) {
      return;
    }
    
    const group = new THREE.Group();
    
    // Render each wall sheet
    wallSheetConfig.mainRightWallSheet.geometries.forEach((geo, index) => {
      // Get the appropriate material, use the one specified in the geometry or default to brick wall
      const material = geo.material 
        ? createViewModeMaterial(geo.material, currentViewMode, ElementType.MAIN_DROP)
        : createViewModeMaterial(roofMaterial, currentViewMode, ElementType.MAIN_DROP);
      
      // Use simple BoxGeometry for better performance and to avoid freezing
      const wallGeometry = new THREE.BoxGeometry(
        geo.geo_x,  // Width (thickness of wall)
        geo.geo_y,  // Height
        geo.geo_z   // Length
      );
      
      // Create mesh and position it
      const position = wallSheetConfig.mainRightWallSheet.positions[index];
      const wallSheet = new THREE.Mesh(wallGeometry, material);
      wallSheet.position.set(position.x, position.y, position.z);
      
      // Apply rotation if specified
      if (position.rotation) {
        wallSheet.rotation.set(position.rotation.x, position.rotation.y, position.rotation.z);
      }
      
      group.add(wallSheet);
    });
    
    scene.add(group);
    return () => disposeAll(group);
  }, [wallSheetConfig.mainRightWallSheet, currentViewMode, scene]);
  
  // Render Front Wall Sheets
  useEffect(() => {
    
    // Skip if no wall sheets to render
    if (!wallSheetConfig.mainFrontWallSheet?.geometries || wallSheetConfig.mainFrontWallSheet.geometries.length === 0) {
      return;
    }
    
    const group = new THREE.Group();
    
    // Create materials with view mode applied
    const wallMaterial = createViewModeMaterial(roofMaterial, currentViewMode, ElementType.MAIN_DROP);
    
    // Render each wall sheet
    wallSheetConfig.mainFrontWallSheet.geometries.forEach((geo, index) => {
      let wallGeometry; // Declare geometry variable outside if/else
      
      // Check if we have the top edge data for corrugated profile
      if (geo.topEdge && geo.topEdge.length > 0) {
        // Create a custom shape for the corrugated wall using provided topEdge
        const shape = new THREE.Shape();
        
        // Add the topEdge points to the shape to create the profile
        const startPoint = geo.topEdge[0];
        shape.moveTo(startPoint[0], startPoint[1]);
        
        for (let i = 1; i < geo.topEdge.length; i++) {
          shape.lineTo(geo.topEdge[i][0], geo.topEdge[i][1]);
        }
        
        // Close the shape by going back to the first point
        shape.lineTo(startPoint[0], startPoint[1]);
        
        // Process the geometry with proper orientation
        wallGeometry = processGeometry(
          new THREE.ExtrudeGeometry(shape, {
            depth: geo.geo_x,
            bevelEnabled: false
          }),
          Math.PI/2,  // Rotate 90 degrees around X
          0,          // No rotation around Y
          Math.PI/2   // Rotate 90 degrees around Z
        );
      } else {
        // Fall back to the old method if no topEdge is provided
        const wallShape = createCorro2DSection(
          geo.geo_y * 1000, // Height in mm
          geo.thickness * 1000, // Thickness in mm
          geo.geo_z * 1000 // Length in mm
        );
        
        // Process the geometry with proper orientation
        wallGeometry = processGeometry(
          new THREE.ExtrudeGeometry(wallShape, {
            depth: geo.geo_x,
            bevelEnabled: false
          }),
          Math.PI/2,  // Rotate 90 degrees around X
          0,          // No rotation around Y
          Math.PI/2   // Rotate 90 degrees around Z
        );
      }
      
      // Create mesh and position it
      const position = wallSheetConfig.mainFrontWallSheet.positions[index];
      
      // Use material provided in geo if available, otherwise use default
      const material = geo.material ? geo.material : wallMaterial;
      const materialWithViewMode = createViewModeMaterial(material, currentViewMode, ElementType.MAIN_DROP);
      
      const wallSheet = new THREE.Mesh(wallGeometry, materialWithViewMode);
      wallSheet.position.set(position.x, position.y, position.z);
      
      // Apply rotation if specified
      if (position.rotation) {
        wallSheet.rotation.set(position.rotation.x, position.rotation.y, position.rotation.z);
      }
      
      group.add(wallSheet);
    });
    
    scene.add(group);
    return () => disposeAll(group);
  }, [wallSheetConfig.mainFrontWallSheet, currentViewMode, scene]);
  
  // Render Back Wall Sheets
  useEffect(() => {
    
    // Skip if no wall sheets to render
    if (!wallSheetConfig.mainBackWallSheet?.geometries || wallSheetConfig.mainBackWallSheet.geometries.length === 0) {
      return;
    }
    
    const group = new THREE.Group();
    
    // Create materials with view mode applied
    const wallMaterial = createViewModeMaterial(roofMaterial, currentViewMode, ElementType.MAIN_DROP);
    
    // Render each wall sheet
    wallSheetConfig.mainBackWallSheet.geometries.forEach((geo, index) => {
      let wallGeometry; // Declare geometry variable outside if/else
      
      // Check if we have the top edge data for gable trimming
      if (geo.topEdge && geo.topEdge.length > 0) {
        // Create a custom shape for the corrugated wall that follows the gable roof
        const shape = new THREE.Shape();
        
        // Calculate sheet dimensions
        const sheetWidth = geo.geo_z ; // Convert to mm
        const baseHeight = height || 2.700; // Base wall height with fallback
        const corrugationWidth = .050; // Standard corrugation width in mm
        
        // Create a custom shape for the sheet with trimmed top following the gable
        // Start at bottom left corner
        const startX = 0;
        const startY = 0;
        shape.moveTo(startX, startY);
        
        // Draw bottom edge (straight line)
        shape.lineTo(sheetWidth, startY);
        
        // Draw right edge upward (straight line)
        shape.lineTo(sheetWidth, baseHeight / 1000);
        
        // Draw the top edge following the gable shape (use the pre-calculated points)
        // Convert the topEdge points to match our coordinate system
        const topPoints = geo.topEdge.map(point => [point[0]  - (index * sheetWidth), point[1] ]);
        
        // Add points from right to left along the gable
        for (let i = topPoints.length - 1; i >= 0; i--) {
          const [x, y] = topPoints[i];
          shape.lineTo(x, y);
        }
        
        // Close the shape back to start
        shape.lineTo(startX, startY);
        
        // Create corrugated section for the wall material
        const corroTemplate = createCorro2DSection(
          .02, // Standard corrugation height
          geo.thickness, // Material thickness
          corrugationWidth // Corrugation width/wavelength
        );
        
        // Process the geometry
        wallGeometry = processGeometry(
          new THREE.ExtrudeGeometry(shape, {
            depth: .001, // A small depth for the corrugated effect
            bevelEnabled: false
          }),
          Math.PI/2,  // Rotate 90 degrees around X 
          0,          // No rotation around Y
          Math.PI/2   // Rotate 90 degrees around Z
        );
      } else {
        // Fallback to standard rectangular sheet if no top edge data
        const sheetWidth = geo.geo_z ; // Convert back to mm for creating shape
        const sheetHeight = geo.geo_y ; // Convert back to mm for creating shape
        
        // Create corrugated section for the wall sheet
        const wallShape = createCorro2DSection(
          20, // Standard corrugation height
          geo.thickness, // Material thickness
          50  // Standard corrugation length (wavelength)
        );
        
        // Process the geometry with proper orientation
        wallGeometry = processGeometry(
          new THREE.ExtrudeGeometry(wallShape, {
            depth: sheetWidth, // Extrude along the width of the sheet
            bevelEnabled: false
          }),
          0,          // No rotation around X initially
          0,          // No rotation around Y
          Math.PI/2   // Rotate 90 degrees around Z to make vertical
        );
        
        // Scale the geometry to match the desired height
        wallGeometry.scale(1, sheetHeight/20, 1);
      }
      
      // Create mesh and position it (using the geometry created in either branch)
      const position = wallSheetConfig.mainBackWallSheet.positions[index];
      const wallSheet = new THREE.Mesh(wallGeometry, wallMaterial);
      wallSheet.position.set(position.x, position.y, position.z);
      
      // Apply rotation if specified
      if (position.rotation) {
        wallSheet.rotation.set(position.rotation.x, position.rotation.y, position.rotation.z);
      }
      
      group.add(wallSheet);
    });
    
    scene.add(group);
    return () => disposeAll(group);
  }, [wallSheetConfig.mainBackWallSheet, currentViewMode, scene]);
  
  // Render Divider Wall Sheets
  useEffect(() => {
    
    // Skip if no wall sheets to render
    if (!wallSheetConfig.mainDividerWallSheet?.geometries || wallSheetConfig.mainDividerWallSheet.geometries.length === 0) {
      return;
    }
    
    const group = new THREE.Group();
    
    // Create materials with view mode applied
    const wallMaterial = createViewModeMaterial(brickWallMaterial, currentViewMode, ElementType.MAIN_DROP);
    
    // Render each wall sheet
    wallSheetConfig.mainDividerWallSheet.geometries.forEach((geo, index) => {
      let wallGeometry; // Declare geometry variable outside if/else
      
      // Check if we have the top edge data for corrugated profile
      if (geo.topEdge && geo.topEdge.length > 0) {
        // Create a custom shape for the corrugated wall using provided topEdge
        const shape = new THREE.Shape();
        
        // Add the topEdge points to the shape to create the profile
        const startPoint = geo.topEdge[0];
        shape.moveTo(startPoint[0], startPoint[1]);
        
        for (let i = 1; i < geo.topEdge.length; i++) {
          shape.lineTo(geo.topEdge[i][0], geo.topEdge[i][1]);
        }
        
        // Close the shape by going back to the first point
        shape.lineTo(startPoint[0], startPoint[1]);
        
        // Process the geometry with proper orientation
        wallGeometry = processGeometry(
          new THREE.ExtrudeGeometry(shape, {
            depth: geo.geo_x,
            bevelEnabled: false
          }),
          Math.PI/2,  // Rotate 90 degrees around X
          0,          // No rotation around Y
          Math.PI/2   // Rotate 90 degrees around Z
        );
      } else {
        // Fall back to the old method if no topEdge is provided
        const wallShape = createCorro2DSection(
          geo.geo_y * 1000, // Height in mm
          geo.thickness * 1000, // Thickness in mm
          geo.geo_z * 1000 // Length in mm
        );
        
        // Process the geometry with proper orientation
        wallGeometry = processGeometry(
          new THREE.ExtrudeGeometry(wallShape, {
            depth: geo.geo_x,
            bevelEnabled: false
          }),
          Math.PI/2,  // Rotate 90 degrees around X
          0,          // No rotation around Y
          Math.PI/2   // Rotate 90 degrees around Z
        );
      }
      
      // Create mesh and position it
      const position = wallSheetConfig.mainDividerWallSheet.positions[index];
      
      // Use material provided in geo if available, otherwise use default
      const material = geo.material ? geo.material : wallMaterial;
      const materialWithViewMode = createViewModeMaterial(material, currentViewMode, ElementType.MAIN_DROP);
      
      const wallSheet = new THREE.Mesh(wallGeometry, materialWithViewMode);
      wallSheet.position.set(position.x, position.y, position.z);
      
      // Apply rotation if specified
      if (position.rotation) {
        wallSheet.rotation.set(position.rotation.x, position.rotation.y, position.rotation.z);
      }
      
      group.add(wallSheet);
    });
    
    scene.add(group);
    return () => disposeAll(group);
  }, [wallSheetConfig.mainDividerWallSheet, currentViewMode, scene]);

  // Render Left Lean-to Wall Sheets
  useEffect(() => {
    
    // Skip if no wall sheets to render or lean-to is disabled
    if (!leftLeanTo || !wallSheetConfig.leftLeanToWallSheet?.geometries || wallSheetConfig.leftLeanToWallSheet.geometries.length === 0) {
      return;
    }
    
    const group = new THREE.Group();
    
    // Create materials with view mode applied
    const wallMaterial = createViewModeMaterial(roofMaterial, currentViewMode, ElementType.MAIN_DROP);
    
    // Render each wall sheet
    wallSheetConfig.leftLeanToWallSheet.geometries.forEach((geo, index) => {
      // Create a corrugated wall sheet
      let wallGeometry;
      
      // Create corrugated section for the wall sheet
      const wallShape = createCorro2DSection(
        geo.geo_y , // Height in mm
        geo.thickness || 1, // Thickness in mm
        geo.geo_z // Length in mm
      );
      
      // Process the geometry with proper orientation
      wallGeometry = processGeometry(
        new THREE.ExtrudeGeometry(wallShape, {
          depth: geo.geo_y ,
          bevelEnabled: false
        }),
        Math.PI/2,  // Rotate 90 degrees around X
        0,          // No rotation around Y
        Math.PI/2   // Rotate 90 degrees around Z
      );
      
      // Create mesh and position it
      const position = wallSheetConfig.leftLeanToWallSheet.positions[index];
      const material = geo.material ? geo.material : wallMaterial;
      const materialWithViewMode = createViewModeMaterial(material, currentViewMode, ElementType.MAIN_DROP);
      
      const wallSheet = new THREE.Mesh(wallGeometry, materialWithViewMode);
      wallSheet.position.set(position.x, position.y, position.z);
      
      // Apply rotation if specified
      if (position.rotation) {
        wallSheet.rotation.set(position.rotation.x, position.rotation.y, position.rotation.z);
      }
      
      group.add(wallSheet);
    });
    
    scene.add(group);
    return () => disposeAll(group);
  }, [wallSheetConfig.leftLeanToWallSheet, currentViewMode, scene, leftLeanTo]);
  
  // Render Left Lean-to Front Wall Sheet
  useEffect(() => {
    
    // Skip if no wall sheets to render or lean-to is disabled
    if (!leftLeanTo || !wallSheetConfig.leftLeanToFrontWallSheet?.geometries || wallSheetConfig.leftLeanToFrontWallSheet.geometries.length === 0) {
      return;
    }
    
    const group = new THREE.Group();
    
    // Create materials with view mode applied
    const wallMaterial = createViewModeMaterial(roofMaterial, currentViewMode, ElementType.MAIN_DROP);
    
    // Render each wall sheet
    wallSheetConfig.leftLeanToFrontWallSheet.geometries.forEach((geo, index) => {
      // Create a corrugated wall sheet
      let wallGeometry;
      
      // Create corrugated section for the wall sheet
      const wallShape = createCorro2DSection(
        1, // Height in mm
        geo.thickness , // Thickness in mm
        geo.geo_z // Length in mm
      );
      
      // Process the geometry with proper orientation
      wallGeometry = processGeometry(
        new THREE.ExtrudeGeometry(wallShape, {
          depth: geo.geo_y || 1,
          bevelEnabled: false
        }),
        0,  // Rotate 90 degrees around X
        0,          // No rotation around Y
        0           // No rotation around Z
      );
      
      // Create mesh and position it
      const position = wallSheetConfig.leftLeanToFrontWallSheet.positions[index];
      const material = geo.material ? geo.material : wallMaterial;
      const materialWithViewMode = createViewModeMaterial(material, currentViewMode, ElementType.MAIN_DROP);
      
      const wallSheet = new THREE.Mesh(wallGeometry, materialWithViewMode);
      wallSheet.position.set(position.x, position.y, position.z);
      
      // Apply rotation if specified
      if (position.rotation) {
        wallSheet.rotation.set(position.rotation.x, position.rotation.y, position.rotation.z);
      }
      
      group.add(wallSheet);
    });
    
    scene.add(group);
    return () => disposeAll(group);
  }, [wallSheetConfig.leftLeanToFrontWallSheet, currentViewMode, scene, leftLeanTo]);
  
  // Render Left Lean-to Back Wall Sheet
  useEffect(() => {
    
    // Skip if no wall sheets to render or lean-to is disabled
    if (!leftLeanTo || !wallSheetConfig.leftLeanToBackWallSheet?.geometries || wallSheetConfig.leftLeanToBackWallSheet.geometries.length === 0) {
      return;
    }
    
    const group = new THREE.Group();
    
    // Create materials with view mode applied
    const wallMaterial = createViewModeMaterial(roofMaterial, currentViewMode, ElementType.MAIN_DROP);
    
    // Render each wall sheet
    wallSheetConfig.leftLeanToBackWallSheet.geometries.forEach((geo, index) => {
      // Create a corrugated wall sheet
      let wallGeometry;
      
      // Create corrugated section for the wall sheet
      const wallShape = createCorro2DSection(
        1 , // Height in mm
        geo.thickness , // Thickness in mm
        geo.geo_z // Length in mm
      );
      
      // Process the geometry with proper orientation
      wallGeometry = processGeometry(
        new THREE.ExtrudeGeometry(wallShape, {
          depth: geo.geo_y ,
          bevelEnabled: false
        }),
        0,  // Rotate 90 degrees around X
        0,          // No rotation around Y
        0           // No rotation around Z
      );
      
      // Create mesh and position it
      const position = wallSheetConfig.leftLeanToBackWallSheet.positions[index];
      const material = geo.material ? geo.material : wallMaterial;
      const materialWithViewMode = createViewModeMaterial(material, currentViewMode, ElementType.MAIN_DROP);
      
      const wallSheet = new THREE.Mesh(wallGeometry, materialWithViewMode);
      wallSheet.position.set(position.x, position.y, position.z);
      
      // Apply rotation if specified
      if (position.rotation) {
        wallSheet.rotation.set(position.rotation.x, position.rotation.y, position.rotation.z);
      }
      
      group.add(wallSheet);
    });
    
    scene.add(group);
    return () => disposeAll(group);
  }, [wallSheetConfig.leftLeanToBackWallSheet, currentViewMode, scene, leftLeanTo]);
  
  // Render Right Lean-to Wall Sheets
  useEffect(() => {
    
    // Skip if no wall sheets to render or lean-to is disabled
    if (!rightLeanTo || !wallSheetConfig.rightLeanToWallSheet?.geometries || wallSheetConfig.rightLeanToWallSheet.geometries.length === 0) {
      return;
    }
    
    const group = new THREE.Group();
    
    // Create materials with view mode applied
    const wallMaterial = createViewModeMaterial(roofMaterial, currentViewMode, ElementType.MAIN_DROP);
    
    // Render each wall sheet
    wallSheetConfig.rightLeanToWallSheet.geometries.forEach((geo, index) => {
      // Create a corrugated wall sheet
      let wallGeometry;
      
      // Create corrugated section for the wall sheet
      const wallShape = createCorro2DSection(
        0.001, // Height in mm
        geo.thickness, // Thickness in mm
        geo.geo_z  // Length in mm
      );
      
      // Process the geometry with proper orientation
      wallGeometry = processGeometry(
        new THREE.ExtrudeGeometry(wallShape, {
          depth: geo.geo_y,
          bevelEnabled: false
        }),
        Math.PI/2,  // Rotate 90 degrees around X
        0,          // No rotation around Y
        Math.PI/2   // Rotate 90 degrees around Z
      );
      
      // Create mesh and position it
      const position = wallSheetConfig.rightLeanToWallSheet.positions[index];
      const material = geo.material ? geo.material : wallMaterial;
      const materialWithViewMode = createViewModeMaterial(material, currentViewMode, ElementType.MAIN_DROP);
      
      const wallSheet = new THREE.Mesh(wallGeometry, materialWithViewMode);
      wallSheet.position.set(position.x, position.y, position.z);
      
      // Apply rotation if specified
      if (position.rotation) {
        wallSheet.rotation.set(position.rotation.x, position.rotation.y, position.rotation.z);
      }
      
      group.add(wallSheet);
    });
    
    scene.add(group);
    return () => disposeAll(group);
  }, [wallSheetConfig.rightLeanToWallSheet, currentViewMode, scene, rightLeanTo]);
  
  // Render Right Lean-to Front Wall Sheet
  useEffect(() => {
    
    // Skip if no wall sheets to render or lean-to is disabled
    if (!rightLeanTo || !wallSheetConfig.rightLeanToFrontWallSheet?.geometries || wallSheetConfig.rightLeanToFrontWallSheet.geometries.length === 0) {
      return;
    }
    
    const group = new THREE.Group();
    
    // Create materials with view mode applied
    const wallMaterial = createViewModeMaterial(roofMaterial, currentViewMode, ElementType.MAIN_DROP);
    
    // Render each wall sheet
    wallSheetConfig.rightLeanToFrontWallSheet.geometries.forEach((geo, index) => {
      // Create a corrugated wall sheet
      let wallGeometry;
      
      // Create corrugated section for the wall sheet
      const wallShape = createCorro2DSection(
        0.001, // Height in mm
        geo.thickness , // Thickness in mm
        geo.geo_z // Length in mm
      );
      
      // Process the geometry with proper orientation
      wallGeometry = processGeometry(
        new THREE.ExtrudeGeometry(wallShape, {
          depth: geo.geo_y,
          bevelEnabled: false
        }),
        0,  // Rotate 90 degrees around X
        0,          // No rotation around Y
        0           // No rotation around Z
      );
      
      // Create mesh and position it
      const position = wallSheetConfig.rightLeanToFrontWallSheet.positions[index];
      const material = geo.material ? geo.material : wallMaterial;
      const materialWithViewMode = createViewModeMaterial(material, currentViewMode, ElementType.MAIN_DROP);
      
      const wallSheet = new THREE.Mesh(wallGeometry, materialWithViewMode);
      wallSheet.position.set(position.x, position.y, position.z);
      
      // Apply rotation if specified
      if (position.rotation) {
        wallSheet.rotation.set(position.rotation.x, position.rotation.y, position.rotation.z);
      }
      
      group.add(wallSheet);
    });
    
    scene.add(group);
    return () => disposeAll(group);
  }, [wallSheetConfig.rightLeanToFrontWallSheet, currentViewMode, scene, rightLeanTo]);
  
  // Render Right Lean-to Back Wall Sheet
  useEffect(() => {
    
    // Skip if no wall sheets to render or lean-to is disabled
    if (!rightLeanTo || !wallSheetConfig.rightLeanToBackWallSheet?.geometries || wallSheetConfig.rightLeanToBackWallSheet.geometries.length === 0) {
      return;
    }
    
    const group = new THREE.Group();
    
    // Create materials with view mode applied
    const wallMaterial = createViewModeMaterial(roofMaterial, currentViewMode, ElementType.MAIN_DROP);
    
    // Render each wall sheet
    wallSheetConfig.rightLeanToBackWallSheet.geometries.forEach((geo, index) => {
      // Create a corrugated wall sheet
      let wallGeometry;
      
      // Create corrugated section for the wall sheet
      const wallShape = createCorro2DSection(
        0.001, // Height in mm
        geo.thickness, // Thickness in mm
        geo.geo_z // Length in mm
      );
      
      // Process the geometry with proper orientation
      wallGeometry = processGeometry(
        new THREE.ExtrudeGeometry(wallShape, {
          depth: geo.geo_y,
          bevelEnabled: false
        }),
        0,  // No rotation around X
        0,  // No rotation around Y
        0   // No rotation around Z
      );
      
      // Create mesh and position it
      const position = wallSheetConfig.rightLeanToBackWallSheet.positions[index];
      const material = geo.material ? geo.material : wallMaterial;
      const materialWithViewMode = createViewModeMaterial(material, currentViewMode, ElementType.MAIN_DROP);
      
      const wallSheet = new THREE.Mesh(wallGeometry, materialWithViewMode);
      wallSheet.position.set(position.x, position.y, position.z);
      
      // Apply rotation if specified
      if (position.rotation) {
        wallSheet.rotation.set(position.rotation.x, position.rotation.y, position.rotation.z);
      }
      
      group.add(wallSheet);
    });
    
    scene.add(group);
    return () => disposeAll(group);
  }, [wallSheetConfig.rightLeanToBackWallSheet, currentViewMode, scene, rightLeanTo]);
  
  // Render Left Lean-to Divider Wall Sheets
  useEffect(() => {
    
    // Skip if no wall sheets to render or lean-to is disabled
    if (!leftLeanTo || !wallSheetConfig.leftLeanToDividerWallSheet?.geometries || wallSheetConfig.leftLeanToDividerWallSheet.geometries.length === 0) {
      return;
    }
    
    const group = new THREE.Group();
    
    // Create materials with view mode applied
    const wallMaterial = createViewModeMaterial(roofMaterial, currentViewMode, ElementType.MAIN_DROP);
    
    // Render each wall sheet
    wallSheetConfig.leftLeanToDividerWallSheet.geometries.forEach((geo, index) => {
      // Create a corrugated wall sheet
      let wallGeometry;
      
      // Create corrugated section for the wall sheet
      const wallShape = createCorro2DSection(
        1, // Height in mm
        geo.thickness, // Thickness in mm
        geo.geo_z // Length in mm
      );
      
      // Process the geometry with proper orientation
      wallGeometry = processGeometry(
        new THREE.ExtrudeGeometry(wallShape, {
          depth: geo.geo_y,
          bevelEnabled: false
        }),
        0,  // No rotation around X
        0,  // No rotation around Y
        0   // No rotation around Z
      );
      
      // Create mesh and position it
      const position = wallSheetConfig.leftLeanToDividerWallSheet.positions[index];
      const material = geo.material ? geo.material : wallMaterial;
      const materialWithViewMode = createViewModeMaterial(material, currentViewMode, ElementType.MAIN_DROP);
      
      const wallSheet = new THREE.Mesh(wallGeometry, materialWithViewMode);
      wallSheet.position.set(position.x, position.y, position.z);
      
      // Apply rotation if specified
      if (position.rotation) {
        wallSheet.rotation.set(position.rotation.x, position.rotation.y, position.rotation.z);
      }
      
      group.add(wallSheet);
    });
    
    scene.add(group);
    return () => disposeAll(group);
  }, [wallSheetConfig.leftLeanToDividerWallSheet, currentViewMode, scene, leftLeanTo]);
  
  // Render Right Lean-to Divider Wall Sheets
  useEffect(() => {
    
    // Skip if no wall sheets to render or lean-to is disabled
    if (!rightLeanTo || !wallSheetConfig.rightLeanToDividerWallSheet?.geometries || wallSheetConfig.rightLeanToDividerWallSheet.geometries.length === 0) {
      return;
    }
    
    const group = new THREE.Group();
    
    // Create materials with view mode applied
    const wallMaterial = createViewModeMaterial(roofMaterial, currentViewMode, ElementType.MAIN_DROP);
    
    // Render each wall sheet
    wallSheetConfig.rightLeanToDividerWallSheet.geometries.forEach((geo, index) => {
      // Create a corrugated wall sheet
      let wallGeometry;
      
      // Create corrugated section for the wall sheet
      const wallShape = createCorro2DSection(
        1, // Height in mm
        geo.thickness, // Thickness in mm
        geo.geo_z // Length in mm
      );
      
      // Process the geometry with proper orientation
      wallGeometry = processGeometry(
        new THREE.ExtrudeGeometry(wallShape, {
          depth: geo.geo_y,
          bevelEnabled: false
        }),
        0,  // No rotation around X
        0,  // No rotation around Y
        0   // No rotation around Z
      );
      
      // Create mesh and position it
      const position = wallSheetConfig.rightLeanToDividerWallSheet.positions[index];
      const material = geo.material ? geo.material : wallMaterial;
      const materialWithViewMode = createViewModeMaterial(material, currentViewMode, ElementType.MAIN_DROP);
      
      const wallSheet = new THREE.Mesh(wallGeometry, materialWithViewMode);
      wallSheet.position.set(position.x, position.y, position.z);
      
      // Apply rotation if specified
      if (position.rotation) {
        wallSheet.rotation.set(position.rotation.x, position.rotation.y, position.rotation.z);
      }
      
      group.add(wallSheet);
    });
    
    scene.add(group);
    return () => disposeAll(group);
  }, [wallSheetConfig.rightLeanToDividerWallSheet, currentViewMode, scene, rightLeanTo]);
  
  return <primitive object={scene} />;


}

export default GableCarport;
