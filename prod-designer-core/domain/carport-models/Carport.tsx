import { useEffect, useMemo } from 'react';
import { useSceneContext } from '@/redux/context';
import { THREE } from 'expo-three';
import { createCeeSectionShape, createMonoClad2DSection, createSHSShape, createCorro2DSection, createBargeCapFlashing2DShape, createRightBargeCapFlashing2DShape, createZeeSectionShape, createStandardZeeSectionShape } from '@/domain/helpers/SectionHelper';
import { knee_web, knee_flange, post_size, Rafter_flange, Rafter_web, Girt_web, Girt_flangeE, Girt_flangeF, Girt_lip, Girt_thickness } from '@/constants/carport/CarportParams';
import { processGeometry } from '@/domain/helpers/Geofunctions';
import calculateGeometriesAndPositions from '@/domain/config/Carportconfig';
import calculateGirtGeometriesAndPositions, { WallMatrixType } from '@/domain/config/girt.config';
import calculateWallSheetGeometriesAndPositions from '@/domain/config/flat.wallsheet.config';
import { applyViewMode, createViewModeMaterial, ElementType } from '@/domain/materials/materialViewModes';
import { materialsUpdated } from '@/domain/materials/materials-pool';
import {
  footingMaterial,
  postMaterial,
  rafterMaterial,
  roofMaterial,
  slabMaterial,
  brickWallMaterial,
  zincFinishMaterial,
  capMaterial, // Import the cap material for barge caps and ridge caps
} from '@/domain/materials/materials-pool';

function disposeAll(obj: THREE.Object3D) {
  for (const c of obj.children) {
    disposeAll(c);
  }
  if (obj instanceof THREE.Mesh && obj.isMesh) {
    obj.geometry.dispose();
  }
  obj.parent?.remove(obj);
}

function Carport({
  length,
  span,
  height,
  pitch,
  overhang,
  viewMode = 'normal', // Provide a default value for viewMode
  showSlab,
  rightLeanToAttached,
  wallMatrix, // Add wall matrix for girt configuration
}: {
  length?: number;
  span?: number;
  height?: number;
  pitch?: number;
  overhang?: number;
  viewMode?: 'normal' | 'transparent' | 'frame';
  showSlab?: boolean;
  rightLeanToAttached?: boolean;
  wallMatrix?: WallMatrixType; // Optional wall matrix parameter
}) {
  const { dimensions, sceneSettings } = useSceneContext();
  const { roofType } = dimensions;
  const { 
    leftLeanTo, leftLeanToDropHeight = 500, leftLeanToSpan = 1000,
    rightLeanTo, rightLeanToDropHeight = 500, rightLeanToSpan = 1000
  } = sceneSettings;
  
  // Create a default wall matrix if none provided
  const defaultWallMatrix: WallMatrixType = {
    // Main carport walls - Set all walls to false by default so UI controls work correctly
    mainLeftWall: Array(Math.max(1, Math.floor((length || 12000) / 4000))).fill(false),
    mainRightWall: Array(Math.max(1, Math.floor((length || 12000) / 4000))).fill(false),
    mainDividerWall: Array(Math.max(1, Math.floor((length || 12000) / 4000) - 1)).fill(false),
    mainFrontWall: false,
    mainBackWall: false,
    // Lean-to walls
    leftLeanToWall: leftLeanTo ? Array(Math.max(1, Math.floor((length || 12000) / 4000))).fill(false) : [],
    rightLeanToWall: rightLeanTo ? Array(Math.max(1, Math.floor((length || 12000) / 4000))).fill(false) : [],
    leftLeanToDividerWall: leftLeanTo ? Array(Math.max(1, Math.floor((length || 12000) / 4000) - 1)).fill(false) : [],
    rightLeanToDividerWall: rightLeanTo ? Array(Math.max(1, Math.floor((length || 12000) / 4000) - 1)).fill(false) : [],
    // Front/back walls for lean-tos
    leftLeanToFrontWall: leftLeanTo ? false : null,
    leftLeanToBackWall: leftLeanTo ? false : null,
    rightLeanToFrontWall: rightLeanTo ? false : null,
    rightLeanToBackWall: rightLeanTo ? false : null
  };
  
  // Get the wall matrix from scene context if available, or from props, or use default
  const contextWallMatrix = sceneSettings.wallMatrix;
  const currentWallMatrix = contextWallMatrix || wallMatrix || defaultWallMatrix;
  
  // Create a stringified version of the wall matrix to ensure React detects changes properly
  // This is necessary because React might not detect nested object changes in the dependency array
  const wallMatrixString = JSON.stringify(currentWallMatrix);
  
  // Log when the wall matrix changes from scene context
  useEffect(() => {
    if (contextWallMatrix) {
    }
  }, [contextWallMatrix, wallMatrix, currentWallMatrix, wallMatrixString]);
  
  // Use the provided viewMode prop or fall back to sceneSettings
  const currentViewMode = viewMode || sceneSettings.viewMode || 'normal';
  const showSlabValue = showSlab !== undefined ? showSlab : sceneSettings.showSlab;
  const scene = useMemo(() => {
    const s = new THREE.Scene();
    // Rotate and place
    s.rotation.x = -Math.PI / 2;
    return s;
  }, []);

  // For left lean-to, we use a fixed pitch of 2 degrees
  const leanToPitch = 2;
  
  const carportConfig = useMemo(
    () => calculateGeometriesAndPositions(
      span, 
      pitch, 
      height, 
      overhang, 
      length, 
      leftLeanTo, 
      leftLeanToSpan, 
      leftLeanToDropHeight,
      rightLeanTo,
      rightLeanToSpan,
      rightLeanToDropHeight
    ),
    [span, height, length, pitch, overhang, leftLeanTo, leftLeanToSpan, leftLeanToDropHeight, rightLeanTo, rightLeanToSpan, rightLeanToDropHeight],
  );
  
  // Calculate girt geometries and positions based on wall matrix
  const girtConfig = useMemo(
    () => {
      
      const result = calculateGirtGeometriesAndPositions(
        span, 
        pitch, 
        height, 
        overhang, 
        length, 
        leftLeanTo, 
        leftLeanToSpan, 
        leftLeanToDropHeight,
        rightLeanTo,
        rightLeanToSpan,
        rightLeanToDropHeight,
        currentWallMatrix,
        roofType
      );
      
      
      return result;
    },
    [span, height, length, pitch, overhang, leftLeanTo, leftLeanToSpan, leftLeanToDropHeight, 
     rightLeanTo, rightLeanToSpan, rightLeanToDropHeight, wallMatrixString], // use string version to detect all changes
  );
  
  // Calculate wall sheet geometries and positions based on wall matrix
  const wallSheetConfig = useMemo(
    () => {
      
      const result = calculateWallSheetGeometriesAndPositions(
        length, 
        span, 
        height, 
        leftLeanToSpan, 
        leftLeanToDropHeight,
        rightLeanToSpan,
        rightLeanToDropHeight,
        overhang,
        pitch, // Pass actual pitch value from UI
        currentWallMatrix
      );
      
      
      return result;
    },
    [span, height, overhang, length, pitch, leftLeanToSpan, leftLeanToDropHeight, rightLeanToSpan, rightLeanToDropHeight, currentWallMatrix]
  );
  
  // Log the wall matrix, girt configuration, and wall sheet configuration for debugging
  useEffect(() => {
    console.log('Current Wall Matrix:', currentWallMatrix);
    console.log('Girt Configuration:', girtConfig);
    console.log('Wall Sheet Configuration:', wallSheetConfig);
  }, [currentWallMatrix, girtConfig, wallSheetConfig]);
  
  // Render Main Left Wall Girts
  useEffect(() => {
    // Debugging: log whether we're trying to render girts for this wall
    
    // Skip if no girts to render
    if (girtConfig.mainLeftGirt.geometries.length === 0) {
      return;
    }
    
    const group = new THREE.Group();
    
    // Create materials with view mode applied
    const girtMaterial = createViewModeMaterial(rafterMaterial, currentViewMode, ElementType.KNEE_BRACE);
    
    // Render each girt
    girtConfig.mainLeftGirt.geometries.forEach((geo, index) => {
      // Create Z section shape for girt using standard Z-section helper function
      const girtShape = createStandardZeeSectionShape(
        Girt_web/1000,       // Web width
        Girt_flangeE/1000,   // Flange E dimension
        Girt_flangeF/1000,   // Flange F dimension
        Girt_lip/1000,       // Lip dimension
        Girt_thickness/1000  // Material thickness
      );
      
      // Process the geometry with proper orientation
      // Scale down by 1000 since we're working in meters in the scene
      const girtGeometry = processGeometry(
        new THREE.ExtrudeGeometry(girtShape, {
          depth: geo.geo_z,
          bevelEnabled: false
        }),
        Math.PI/2, // Rotate 90 degrees around X
        0,           // No rotation around Y
        0            // No rotation around Z
      );
      
      // Create mesh and position it
      const position = girtConfig.mainLeftGirt.positions[index];
      const girt = new THREE.Mesh(girtGeometry, girtMaterial);
      girt.position.set(position.x, position.y, position.z);
      
      // Apply rotation if specified
      if (position.rotation) {
        girt.rotation.set(position.rotation.x, position.rotation.y, position.rotation.z);
      }
      
      group.add(girt);
    });
    
    scene.add(group);
    return () => disposeAll(group);
  }, [scene, girtConfig.mainLeftGirt, currentViewMode]);
  
  // Render Main Left Wall Sheets
  useEffect(() => {
    // Debugging: log whether we're trying to render wall sheets for this wall
    
    // Skip if no wall sheets to render
    if (!wallSheetConfig.mainLeftWallSheet?.geometries || wallSheetConfig.mainLeftWallSheet.geometries.length === 0) {
      return;
    }
    
    const group = new THREE.Group();
    
    // Create materials with view mode applied
    const material = createViewModeMaterial(roofMaterial, currentViewMode, ElementType.MAIN_DROP);
    
    // Render each wall sheet
    wallSheetConfig.mainLeftWallSheet.geometries.forEach((geo, index) => {
      // Create a simple box geometry for the wall sheet
      const wallGeometry = new THREE.BoxGeometry(
        geo.geo_x,    // Thickness (x-dimension)
        geo.geo_z,    // Length (y-dimension, along the wall)
        geo.geo_y     // Height (z-dimension)
      );
      
      const position = wallSheetConfig.mainLeftWallSheet.positions[index];
      
      const wallSheet = new THREE.Mesh(wallGeometry, material);
      wallSheet.position.set(position.x, position.y, position.z);
      
      // Apply rotation if specified
      if (position.rotation) {
        wallSheet.rotation.set(position.rotation.x, position.rotation.y, position.rotation.z);
      }
      
      group.add(wallSheet);
    });
    
    scene.add(group);
    return () => disposeAll(group);
  }, [scene, wallSheetConfig.mainLeftWallSheet, currentViewMode, currentWallMatrix]);

// Log the wall matrix, girt configuration, and wall sheet configuration for debugging
useEffect(() => {
console.log('Current Wall Matrix:', currentWallMatrix);
console.log('Girt Configuration:', girtConfig);
console.log('Wall Sheet Configuration:', wallSheetConfig);
}, [currentWallMatrix, girtConfig, wallSheetConfig]);

// Render Main Left Wall Girts
useEffect(() => {
// Debugging: log whether we're trying to render girts for this wall

// Skip if no girts to render
if (girtConfig.mainLeftGirt.geometries.length === 0) {
  return;
}

const group = new THREE.Group();

// Create materials with view mode applied
const girtMaterial = createViewModeMaterial(rafterMaterial, currentViewMode, ElementType.KNEE_BRACE);

// Render each girt
girtConfig.mainLeftGirt.geometries.forEach((geo, index) => {
  // Create Z section shape for girt using standard Z-section helper function
  const girtShape = createStandardZeeSectionShape(
    Girt_web/1000,       // Web width
    Girt_flangeE/1000,   // Flange E dimension
    Girt_flangeF/1000,   // Flange F dimension
    Girt_lip/1000,       // Lip dimension
    Girt_thickness/1000  // Material thickness
  );

  // Process the geometry with proper orientation
  // Scale down by 1000 since we're working in meters in the scene
  const girtGeometry = processGeometry(
    new THREE.ExtrudeGeometry(girtShape, {
      depth: geo.geo_z,
      bevelEnabled: false
    }),
    Math.PI/2, // Rotate 90 degrees around X
    0,           // No rotation around Y
    0            // No rotation around Z
  );

  // Create mesh and position it
  const position = girtConfig.mainLeftGirt.positions[index];
  const girt = new THREE.Mesh(girtGeometry, girtMaterial);
  girt.position.set(position.x, position.y, position.z);

  // Apply rotation if specified
  if (position.rotation) {
    girt.rotation.set(position.rotation.x, position.rotation.y, position.rotation.z);
  }

  group.add(girt);
});

scene.add(group);
return () => disposeAll(group);
}, [scene, girtConfig.mainLeftGirt, currentViewMode]);

// Render Main Left Wall Sheets
useEffect(() => {
// Debugging: log whether we're trying to render wall sheets for this wall

// Skip if no wall sheets to render
if (!wallSheetConfig.mainLeftWallSheet?.geometries || wallSheetConfig.mainLeftWallSheet.geometries.length === 0) {
  return;
}

const group = new THREE.Group();

// Create materials with view mode applied
const material = createViewModeMaterial(roofMaterial, currentViewMode, ElementType.MAIN_DROP);

// Render each wall sheet
wallSheetConfig.mainLeftWallSheet.geometries.forEach((geo, index) => {
  // Create a simple box geometry for the wall sheet
  const wallGeometry = new THREE.BoxGeometry(
    geo.geo_x,    // Thickness (x-dimension)
    geo.geo_z,    // Length (y-dimension, along the wall)
    geo.geo_y     // Height (z-dimension)
  );

  const position = wallSheetConfig.mainLeftWallSheet.positions[index];

  const wallSheet = new THREE.Mesh(wallGeometry, material);
  wallSheet.position.set(position.x, position.y, position.z);

  // Apply rotation if specified
  if (position.rotation) {
    wallSheet.rotation.set(position.rotation.x, position.rotation.y, position.rotation.z);
  }

  group.add(wallSheet);
});

scene.add(group);
return () => disposeAll(group);
}, [wallSheetConfig.mainLeftWallSheet, currentViewMode, scene]);

// Render Main Right Wall Girts
useEffect(() => {
// Skip if no girts to render
if (girtConfig.mainRightGirt.geometries.length === 0) return;

const group = new THREE.Group();

// Create materials with view mode applied
const girtMaterial = createViewModeMaterial(rafterMaterial, currentViewMode, ElementType.KNEE_BRACE);

// Render each girt
girtConfig.mainRightGirt.geometries.forEach((geo, index) => {
  // Create Z section shape for girt using helper function
  const girtShape = createStandardZeeSectionShape(
    Girt_web/1000,       // Web width
    Girt_flangeE/1000,   // Flange E dimension
    Girt_flangeF/1000,   // Flange F dimension
    Girt_lip/1000,       // Lip dimension
    Girt_thickness/1000  // Material thickness
  );

  // Process the geometry with proper orientation (mirror of left side)
  const girtGeometry = processGeometry(
    new THREE.ExtrudeGeometry(girtShape, {
      depth: geo.geo_z,
      bevelEnabled: false
    }),
    -Math.PI / 2, // Mirror rotation (negative) 90 degrees around X
    0,            // No rotation around Y
    Math.PI       // 180 degrees around Z to flip
  );

  // Create mesh and position it
  const position = girtConfig.mainRightGirt.positions[index];
  const girt = new THREE.Mesh(girtGeometry, girtMaterial);
  girt.position.set(position.x, position.y, position.z);

  // Apply rotation if specified
  if (position.rotation) {
    girt.rotation.set(position.rotation.x, position.rotation.y, position.rotation.z);
  }

  group.add(girt);
});

scene.add(group);
return () => disposeAll(group);
}, [scene, girtConfig.mainRightGirt, currentViewMode]);

// Render Main Right Wall Sheets
useEffect(() => {
// Debugging: log whether we're trying to render wall sheets for this wall

// Skip if no wall sheets to render
if (!wallSheetConfig.mainRightWallSheet?.geometries || wallSheetConfig.mainRightWallSheet.geometries.length === 0) {
  return;
}

const group = new THREE.Group();

// Create materials with view mode applied
const material = createViewModeMaterial(roofMaterial, currentViewMode, ElementType.MAIN_DROP);

// Render each wall sheet
wallSheetConfig.mainRightWallSheet.geometries.forEach((geo, index) => {
  // Create a simple box geometry for the wall sheet
  const wallGeometry = new THREE.BoxGeometry(
    geo.geo_x,    // Thickness (x-dimension)
    geo.geo_z,    // Length (y-dimension, along the wall)
    geo.geo_y     // Height (z-dimension)
  );

  const position = wallSheetConfig.mainRightWallSheet.positions[index];

  const wallSheet = new THREE.Mesh(wallGeometry, material);
  wallSheet.position.set(position.x, position.y, position.z);

  // Apply rotation if specified
  if (position.rotation) {
    wallSheet.rotation.set(position.rotation.x, position.rotation.y, position.rotation.z);
  }

  group.add(wallSheet);
});

scene.add(group);
return () => disposeAll(group);
}, [wallSheetConfig.mainRightWallSheet, currentViewMode, scene]);

// Render Main Front Wall Girts
useEffect(() => {
// Skip if no girts to render
if (girtConfig.mainFrontGirt.geometries.length === 0) return;

const group = new THREE.Group();

// Create materials with view mode applied
const girtMaterial = createViewModeMaterial(rafterMaterial, currentViewMode, ElementType.KNEE_BRACE);

// Render each girt
girtConfig.mainFrontGirt.geometries.forEach((geo, index) => {
  // Create Z section shape for girt using helper function
  const girtShape = createStandardZeeSectionShape(
    Girt_web/1000,       // Web width
    Girt_flangeE/1000,   // Flange E dimension
    Girt_flangeF/1000,   // Flange F dimension
    Girt_lip/1000,       // Lip dimension
    Girt_thickness/1000  // Material thickness
  );

  // Process the geometry with proper orientation for front wall
  const girtGeometry = processGeometry(
    new THREE.ExtrudeGeometry(girtShape, {
      depth: geo.geo_z,
      bevelEnabled: false
    }),
    0,            // No rotation around X
    Math.PI / 2,  // 90 degrees around Y
    0             // No rotation around Z
  );

  // Create mesh and position it
  const position = girtConfig.mainFrontGirt.positions[index];
  const girt = new THREE.Mesh(girtGeometry, girtMaterial);
  girt.position.set(position.x, position.y, position.z);

  // Apply rotation if specified
  if (position.rotation) {
    girt.rotation.set(position.rotation.x, position.rotation.y, position.rotation.z);
  }

  group.add(girt);
});

scene.add(group);
return () => disposeAll(group);
}, [scene, girtConfig.mainFrontGirt, currentViewMode]);

// Render Main Front Wall Sheets
useEffect(() => {
// Debugging: log whether we're trying to render wall sheets for this wall

// Skip if no wall sheets to render
if (!wallSheetConfig.mainFrontWallSheet?.geometries || wallSheetConfig.mainFrontWallSheet.geometries.length === 0) {
  return;
}

const group = new THREE.Group();

// Create materials with view mode applied
const material = createViewModeMaterial(roofMaterial, currentViewMode, ElementType.MAIN_DROP);

// Render each wall sheet
wallSheetConfig.mainFrontWallSheet.geometries.forEach((geo, index) => {
  // For the front wall, check if we have a custom shape via topEdge
  let wallGeometry;

  if (geo.topEdge) {
    // Create a custom shape using the provided top edge points
    const shape = new THREE.Shape();
    
    // Start at bottom-left corner
    shape.moveTo(0, 0);
    
    // Bottom edge (from left to right)
    shape.lineTo(geo.geo_x, 0);
    
    // Right edge (from bottom to top)
    shape.lineTo(geo.geo_x, geo.geo_y);
    
    // Top edge (defined by the custom points, from right to left)
    // For front wall, use the points in order from right to left
    for (let i = geo.topEdge.length - 1; i >= 0; i--) {
      const point = geo.topEdge[i];
      shape.lineTo(point[0], point[1]);
    }
    
    // Close the shape
    shape.lineTo(0, 0);
    
    // Create and orient the geometry
    wallGeometry = processGeometry(
      new THREE.ExtrudeGeometry(shape, {
        depth: geo.geo_z || 0.001, // Provide a minimum depth if not specified
        bevelEnabled: false
      }),
      0,             // No rotation around X
      Math.PI / 2,   // 90 degrees around Y for front wall
      0              // No rotation around Z
    );
  } else {
    // Fall back to the corrugated section if no topEdge is provided (match gable approach)
    const wallShape = createCorro2DSection(
      geo.geo_y * 1000, // Height in mm
      geo.thickness * 1000 || 1, // Thickness in mm (default to 1mm if not provided)
      geo.geo_z * 1000 // Length in mm
    );
    
    // Process the geometry with proper orientation
    wallGeometry = processGeometry(
      new THREE.ExtrudeGeometry(wallShape, {
        depth: geo.geo_x,
        bevelEnabled: false
      }),
      Math.PI/2,  // Rotate 90 degrees around X
      0,          // No rotation around Y
      Math.PI/2   // Rotate 90 degrees around Z
    );
  }
  
  // Create mesh and position it
  const position = wallSheetConfig.mainFrontWallSheet.positions[index];
  const wallSheet = new THREE.Mesh(wallGeometry, material);
  wallSheet.position.set(position.x, position.y, position.z);
  
  // Apply rotation if specified
  if (position.rotation) {
    wallSheet.rotation.set(position.rotation.x, position.rotation.y, position.rotation.z);
  }
  
  group.add(wallSheet);
});

scene.add(group);
return () => {
  disposeAll(group);
  scene.remove(group);
};
}, [wallSheetConfig.mainBackWallSheet, currentViewMode, scene, currentWallMatrix.mainBackWall]);

  // Render Main Divider Wall Sheets
  useEffect(() => {
    // Debugging: log whether we're trying to render wall sheets for this wall
    
    // Skip if no wall sheets to render
    if (!wallSheetConfig.mainDividerWallSheet?.geometries || wallSheetConfig.mainDividerWallSheet.geometries.length === 0) {
      return;
    }
    
    const group = new THREE.Group();
    
    // Create materials with view mode applied
    const wallSheetMaterial = createViewModeMaterial(roofMaterial, currentViewMode, ElementType.MAIN_DROP);
    
    // Render each divider wall sheet
    wallSheetConfig.mainDividerWallSheet.geometries.forEach((geo, index) => {
      // For the divider wall, check if we have a custom shape via topEdge
      let wallGeometry;
      
      if (geo.topEdge) {
        // Create a custom shape using the provided top edge points
        const shape = new THREE.Shape();
        
        // Start at bottom-left corner
        shape.moveTo(0, 0);
        
        // Bottom edge (from left to right)
        shape.lineTo(geo.geo_x, 0);
        
        // Right edge (from bottom to top)
        shape.lineTo(geo.geo_x, geo.geo_y);
        
        // Top edge (defined by the custom points, from right to left)
        // Reverse the points to go from right to left
        const topEdgePoints = [...geo.topEdge].reverse();
        
        for (const point of topEdgePoints) {
          shape.lineTo(point[0], point[1]);
        }
        
        // Close the shape
        shape.lineTo(0, 0);
        
        // Create and orient the geometry
        wallGeometry = processGeometry(
          new THREE.ExtrudeGeometry(shape, {
            depth: geo.geo_z || 0.001, // Provide a minimum depth if not specified
            bevelEnabled: false
          }),
          0,              // No rotation around X
          -Math.PI / 2,   // -90 degrees around Y (similar to front/back walls)
          0               // No rotation around Z
        );
      } else {
        // Fallback to corrugated section if no top edge data
        const sheetWidth = geo.geo_z; // Width
        
        // Create corrugated section for the wall sheet
        const wallShape = createCorro2DSection(
          20, // Standard corrugation height
          geo.thickness || 0.001, // Material thickness with fallback
          50  // Standard corrugation length (wavelength)
        );
        
        // Process the geometry with proper orientation
        wallGeometry = processGeometry(
          new THREE.ExtrudeGeometry(wallShape, {
            depth: sheetWidth, // Extrude along the width of the sheet
            bevelEnabled: false
          }),
          0,              // No rotation around X
          -Math.PI / 2,   // -90 degrees around Y
          Math.PI/2       // Rotate 90 degrees around Z to make vertical
        );
        
        // Scale the geometry to match the desired height if needed
        if (geo.geo_y) {
          wallGeometry.scale(1, geo.geo_y/20, 1);
        }
      }
      
      // Create the mesh and apply position/rotation
      const position = wallSheetConfig.mainDividerWallSheet.positions[index];
      const wallSheet = new THREE.Mesh(wallGeometry, wallSheetMaterial);
      
      // Position the wall sheet
      wallSheet.position.set(position.x, position.y, position.z);
      
      // Apply rotation if specified
      if (position.rotation) {
        wallSheet.rotation.set(position.rotation.x, position.rotation.y, position.rotation.z);
      }
      
      group.add(wallSheet);
    });
    
    scene.add(group);
    
    return () => disposeAll(group);
  }, [wallSheetConfig.mainDividerWallSheet, currentViewMode, scene, currentWallMatrix.mainDividerWall]);

  // Render Main Divider Wall Girts
  useEffect(() => {
    // Skip if no girts to render
    if (!girtConfig.mainDividerGirt?.geometries || girtConfig.mainDividerGirt.geometries.length === 0) {
      return;
    }
    
    
    const group = new THREE.Group();
    
    // Create materials with view mode applied
    const girtMaterial = createViewModeMaterial(rafterMaterial, currentViewMode, ElementType.KNEE_BRACE);
    
    // Render each girt
    girtConfig.mainDividerGirt.geometries.forEach((geo, index) => {
      // Create Z section shape for girt using helper function
      const girtShape = createStandardZeeSectionShape(
        Girt_web/1000,       // Web width
        Girt_flangeE/1000,   // Flange E dimension
        Girt_flangeF/1000,   // Flange F dimension
        Girt_lip/1000,       // Lip dimension
        Girt_thickness/1000  // Material thickness
      );
      
      // Process the geometry with proper orientation for divider wall
      const girtGeometry = processGeometry(
        new THREE.ExtrudeGeometry(girtShape, {
          depth: geo.geo_z,
          bevelEnabled: false
        }),
        -Math.PI/2,       // -90 degrees around X to align horizontally
        0,                // No rotation around Y
        Math.PI           // 180 degrees around Z to face properly
      );
      
      // Create mesh and position it
      const position = girtConfig.mainDividerGirt.positions[index];
      const girt = new THREE.Mesh(girtGeometry, girtMaterial);
      girt.position.set(position.x, position.y, position.z);
      
      // Apply rotation if specified
      if (position.rotation) {
        girt.rotation.set(position.rotation.x, position.rotation.y, position.rotation.z);
      }
      
      group.add(girt);
    });
    
    scene.add(group);
    return () => disposeAll(group);
  }, [girtConfig.mainDividerGirt, currentViewMode, scene, currentWallMatrix.mainDividerWall]);

  // Render Main Right Wall Girts
  useEffect(() => {
// Skip if no girts to render
if (girtConfig.mainRightGirt.geometries.length === 0) return;

const group = new THREE.Group();

// Create materials with view mode applied
const girtMaterial = createViewModeMaterial(rafterMaterial, currentViewMode, ElementType.KNEE_BRACE);

// Render each girt
girtConfig.mainRightGirt.geometries.forEach((geo, index) => {
  // Create Z section shape for girt using helper function
  const girtShape = createStandardZeeSectionShape(
    Girt_web/1000,       // Web width
    Girt_flangeE/1000,   // Flange E dimension
    Girt_flangeF/1000,   // Flange F dimension
    Girt_lip/1000,       // Lip dimension
    Girt_thickness/1000  // Material thickness
  );
  
  // Process the geometry with proper orientation (mirror of left side)
  const girtGeometry = processGeometry(
    new THREE.ExtrudeGeometry(girtShape, {
      depth: geo.geo_z,
      bevelEnabled: false
    }),
    -Math.PI / 2, // Mirror rotation (negative) 90 degrees around X
    0,            // No rotation around Y
    Math.PI       // 180 degrees around Z to flip
  );
  
  // Create mesh and position it
  const position = girtConfig.mainRightGirt.positions[index];
  const girt = new THREE.Mesh(girtGeometry, girtMaterial);
  girt.position.set(position.x, position.y, position.z);
  
  // Apply rotation if specified
  if (position.rotation) {
    girt.rotation.set(position.rotation.x, position.rotation.y, position.rotation.z);
  }
  
  group.add(girt);
});

scene.add(group);
return () => disposeAll(group);
}, [scene, girtConfig.mainRightGirt, currentViewMode]);

// Render Main Right Wall Sheets
  useEffect(() => {
    // Debugging: log whether we're trying to render wall sheets for this wall
    
    // Skip if no wall sheets to render
    if (!wallSheetConfig.mainRightWallSheet?.geometries || wallSheetConfig.mainRightWallSheet.geometries.length === 0) {
      return;
    }
    
    const group = new THREE.Group();
    
    // Create materials with view mode applied
    const material = createViewModeMaterial(roofMaterial, currentViewMode, ElementType.MAIN_DROP);
    
    // Render each wall sheet
    wallSheetConfig.mainRightWallSheet.geometries.forEach((geo, index) => {
      // Create a simple box geometry for the wall sheet
      const wallGeometry = new THREE.BoxGeometry(
        geo.geo_x,    // Thickness (x-dimension)
        geo.geo_z,    // Length (y-dimension, along the wall)
        geo.geo_y     // Height (z-dimension)
      );
      
      const position = wallSheetConfig.mainRightWallSheet.positions[index];
      
      const wallSheet = new THREE.Mesh(wallGeometry, material);
      wallSheet.position.set(position.x, position.y, position.z);
      
      // Apply rotation if specified
      if (position.rotation) {
        wallSheet.rotation.set(position.rotation.x, position.rotation.y, position.rotation.z);
      }
      
      group.add(wallSheet);
    });
    
    scene.add(group);
    return () => disposeAll(group);
  }, [wallSheetConfig.mainRightWallSheet, currentViewMode, scene]);
  
  // Render Main Front Wall Girts
  useEffect(() => {
    // Skip if no girts to render
    if (girtConfig.mainFrontGirt.geometries.length === 0) return;
    
    const group = new THREE.Group();
    
    // Create materials with view mode applied
    const girtMaterial = createViewModeMaterial(rafterMaterial, currentViewMode, ElementType.KNEE_BRACE);
    
    // Render each girt
    girtConfig.mainFrontGirt.geometries.forEach((geo, index) => {
      // Create Z section shape for girt using helper function
      const girtShape = createStandardZeeSectionShape(
        Girt_web/1000,       // Web width
        Girt_flangeE/1000,   // Flange E dimension
        Girt_flangeF/1000,   // Flange F dimension
        Girt_lip/1000,       // Lip dimension
        Girt_thickness/1000  // Material thickness
      );
      
      // Process the geometry with proper orientation for front wall
      const girtGeometry = processGeometry(
        new THREE.ExtrudeGeometry(girtShape, {
          depth: geo.geo_z,
          bevelEnabled: false
        }),
        0,            // No rotation around X
        Math.PI / 2,  // 90 degrees around Y
        0             // No rotation around Z
      );
      
      // Create mesh and position it
      const position = girtConfig.mainFrontGirt.positions[index];
      const girt = new THREE.Mesh(girtGeometry, girtMaterial);
      girt.position.set(position.x, position.y, position.z);
      
      // Apply rotation if specified
      if (position.rotation) {
        girt.rotation.set(position.rotation.x, position.rotation.y, position.rotation.z);
      }
      
      group.add(girt);
    });
    
    scene.add(group);
    return () => disposeAll(group);
  }, [scene, girtConfig.mainFrontGirt, currentViewMode]);
  
  // Render Main Front Wall Sheets
  useEffect(() => {
    // Debugging: log whether we're trying to render wall sheets for this wall
    
    // Skip if no wall sheets to render
    if (!wallSheetConfig.mainFrontWallSheet?.geometries || wallSheetConfig.mainFrontWallSheet.geometries.length === 0) {
      return;
    }
    
    const group = new THREE.Group();
    
    // Create materials with view mode applied
    const material = createViewModeMaterial(roofMaterial, currentViewMode, ElementType.MAIN_DROP);
    
    // Render each wall sheet
    wallSheetConfig.mainFrontWallSheet.geometries.forEach((geo, index) => {
      // For the front wall, check if we have a custom shape via topEdge
      let wallGeometry;
      
      if (geo.topEdge) {
        // Create a custom shape using the provided top edge points
        const shape = new THREE.Shape();
        
        // Start at bottom-left corner
        shape.moveTo(0, 0);
        
        // Bottom edge (from left to right)
        shape.lineTo(geo.geo_x, 0);
        
        // Right edge (from bottom to top)
        shape.lineTo(geo.geo_x, geo.geo_y);
        
        // Top edge (defined by the custom points, from right to left)
        // Reverse the points to go from right to left
        const topEdgePoints = [...geo.topEdge].reverse();
        
        for (const point of topEdgePoints) {
          shape.lineTo(point[0], point[1]);
        }
        
        // Close the shape
        shape.lineTo(0, 0);
        
        // Create and orient the geometry
        wallGeometry = processGeometry(
          new THREE.ExtrudeGeometry(shape, {
            depth: geo.geo_z || 0.001, // Provide a minimum depth if not specified
            bevelEnabled: false
          }),
          0,              // No rotation around X
          Math.PI / 2,    // 90 degrees around Y (opposite of back wall)
          0               // No rotation around Z
        );
      } else {
        // Fallback to corrugated section if no top edge data (match gable approach)
        const sheetWidth = geo.geo_z; // Width
        
        // Create corrugated section for the wall sheet
        const wallShape = createCorro2DSection(
          20, // Standard corrugation height
          geo.thickness || 0.001, // Material thickness with fallback
          50  // Standard corrugation length (wavelength)
        );
        
        // Process the geometry with proper orientation
        wallGeometry = processGeometry(
          new THREE.ExtrudeGeometry(wallShape, {
            depth: sheetWidth, // Extrude along the width of the sheet
            bevelEnabled: false
          }),
          0,              // No rotation around X
          Math.PI / 2,    // 90 degrees around Y (opposite of back wall)
          Math.PI/2       // Rotate 90 degrees around Z to make vertical
        );
        
        // Scale the geometry to match the desired height if needed
        if (geo.geo_y) {
          wallGeometry.scale(1, geo.geo_y/20, 1);
        }
      }
      
      const position = wallSheetConfig.mainFrontWallSheet.positions[index];
      
      const wallSheet = new THREE.Mesh(wallGeometry, material);
      wallSheet.position.set(position.x, position.y, position.z);
      
      // Apply rotation if specified
      if (position.rotation) {
        wallSheet.rotation.set(position.rotation.x, position.rotation.y, position.rotation.z);
      }
      
      group.add(wallSheet);
    });
    
    scene.add(group);
    return () => disposeAll(group);
  }, [wallSheetConfig.mainFrontWallSheet, currentViewMode, scene]);
  
  // Render Main Back Wall Girts
  useEffect(() => {
    // Skip if no girts to render
    if (girtConfig.mainBackGirt.geometries.length === 0) return;
    
    const group = new THREE.Group();
    
    // Create materials with view mode applied
    const girtMaterial = createViewModeMaterial(rafterMaterial, currentViewMode, ElementType.KNEE_BRACE);
    
    // Render each girt
    girtConfig.mainBackGirt.geometries.forEach((geo, index) => {
      // Create Z section shape for girt using helper function
      const girtShape = createStandardZeeSectionShape(
        Girt_web/1000,       // Web width
        Girt_flangeE/1000,   // Flange E dimension
        Girt_flangeF/1000,   // Flange F dimension
        Girt_lip/1000,       // Lip dimension
        Girt_thickness/1000  // Material thickness
      );
      
      // Process the geometry with proper orientation for back wall (opposite of front)
      const girtGeometry = processGeometry(
        new THREE.ExtrudeGeometry(girtShape, {
          depth: geo.geo_z,
          bevelEnabled: false
        }),
        0,              // No rotation around X
        -Math.PI / 2,   // -90 degrees around Y (opposite of front wall)
        Math.PI         // 180 degrees around Z to flip
      );
      
      // Create mesh and position it
      const position = girtConfig.mainBackGirt.positions[index];
      const girt = new THREE.Mesh(girtGeometry, girtMaterial);
      girt.position.set(position.x, position.y, position.z);
      
      // Apply rotation if specified
      if (position.rotation) {
        girt.rotation.set(position.rotation.x, position.rotation.y, position.rotation.z);
      }
      
      group.add(girt);
    });
    
    scene.add(group);
    return () => disposeAll(group);
  }, [scene, girtConfig.mainBackGirt, currentViewMode]);

  // Render Main Back Wall Sheets
  useEffect(() => {
    // Debugging: log whether we're trying to render wall sheets for this wall
    
    // Skip if no wall sheets to render
    if (!wallSheetConfig.mainBackWallSheet?.geometries || wallSheetConfig.mainBackWallSheet.geometries.length === 0) {
      return;
    }
    
    const group = new THREE.Group();
    
    // Create materials with view mode applied - using MAIN_DROP type for now
    const wallSheetMaterial = createViewModeMaterial(roofMaterial, currentViewMode, ElementType.MAIN_DROP);
    
    // Render each wall sheet
    wallSheetConfig.mainBackWallSheet.geometries.forEach((geo, index) => {
      // For the back wall, check if we have a custom shape via topEdge
      let wallGeometry;
      
      if (geo.topEdge) {
        // Create a custom shape using the provided top edge points
        const shape = new THREE.Shape();
        
        // Start at bottom-left corner
        shape.moveTo(0, 0);
        
        // Bottom edge (from left to right)
        shape.lineTo(geo.geo_x, 0);
        
        // Right edge (from bottom to top)
        shape.lineTo(geo.geo_x, geo.geo_y);
        
        // Top edge (defined by the custom points, from right to left)
        // Reverse the points to go from right to left
        const topEdgePoints = [...geo.topEdge].reverse();
        
        for (const point of topEdgePoints) {
          shape.lineTo(point[0], point[1]);
        }
        
        // Close the shape
        shape.lineTo(0, 0);
        
        // Create and orient the geometry
        wallGeometry = processGeometry(
          new THREE.ExtrudeGeometry(shape, {
            depth: geo.geo_z || 0.001, // Provide a minimum depth if not specified
            bevelEnabled: false
          }),
          0,              // No rotation around X
          -Math.PI / 2,   // -90 degrees around Y (opposite of front wall)
          0               // No rotation around Z
        );
      } else {
        // Fallback to corrugated section if no top edge data (match gable approach)
        const sheetWidth = geo.geo_z; // Width
        
        // Create corrugated section for the wall sheet
        const wallShape = createCorro2DSection(
          20, // Standard corrugation height
          geo.thickness || 0.001, // Material thickness with fallback
          50  // Standard corrugation length (wavelength)
        );
        
        // Process the geometry with proper orientation
        wallGeometry = processGeometry(
          new THREE.ExtrudeGeometry(wallShape, {
            depth: sheetWidth, // Extrude along the width of the sheet
            bevelEnabled: false
          }),
          0,              // No rotation around X
          -Math.PI / 2,   // -90 degrees around Y (opposite of front wall)
          Math.PI/2       // Rotate 90 degrees around Z to make vertical
        );
        
        // Scale the geometry to match the desired height if needed
        if (geo.geo_y) {
          wallGeometry.scale(1, geo.geo_y/20, 1);
        }
      }
      
      // Create mesh and position it
      const position = wallSheetConfig.mainBackWallSheet.positions[index];
      const wallSheet = new THREE.Mesh(wallGeometry, wallSheetMaterial);
      wallSheet.position.set(position.x, position.y, position.z);
      
      // Apply rotation if specified
      if (position.rotation) {
        wallSheet.rotation.set(position.rotation.x, position.rotation.y, position.rotation.z);
      }
      
      group.add(wallSheet);
    });
    
    scene.add(group);
    
    return () => {
      disposeAll(group);
      scene.remove(group);
    };
  }, [scene, wallSheetConfig.mainBackWallSheet, currentViewMode, currentWallMatrix]);

  useEffect(() => {
    const group = new THREE.Group();
    
    // Create materials with view mode applied based on element types
    const postMaterialWithViewMode = createViewModeMaterial(postMaterial, currentViewMode, ElementType.POST);
    const rafterMaterialWithViewMode = createViewModeMaterial(rafterMaterial, currentViewMode, ElementType.KNEE_BRACE); // Treating rafters as structural (knee braces)
    const slabMaterialWithViewMode = createViewModeMaterial(slabMaterial, currentViewMode, ElementType.OTHER);
    const footingMaterialWithViewMode = createViewModeMaterial(footingMaterial, currentViewMode, ElementType.FOOTING);
    const zincFinishMaterialWithViewMode = createViewModeMaterial(zincFinishMaterial, currentViewMode, ElementType.OTHER);
    
    // Left Posts
    carportConfig.post_left_Positions.forEach((position, index) => {
      const geo = carportConfig.post_left_Geo[index];
      const shsShape = createSHSShape(geo.geo_x, geo.geo_y);
      const extrudeSettings = { depth: geo.geo_z, bevelEnabled: false };
      const postGeometry = new THREE.ExtrudeGeometry(shsShape, extrudeSettings);
      const post = new THREE.Mesh(postGeometry, postMaterialWithViewMode);
      post.position.set(position.x, position.y, position.z);
      group.add(post);
    });

    scene.add(group);
    return () => disposeAll(group);
  }, [
    scene, 
    carportConfig.post_left_Positions, 
    carportConfig.post_left_Geo,
    currentViewMode // Update viewMode to currentViewMode
  ]);

  // Separate useEffect for the wall when roof type is Skillion Attached
  useEffect(() => {
    if (roofType === 'Skillion Attached') {
      // Create a wall at the right edge of the carport where it should be for Skillion Attached
      const wallGeometry = new THREE.BoxGeometry(
        0.2, // 200mm thickness
        (length || 6000) / 1000 +1, // Full length
        1.5*(height || 3600) / 1000  // 2x height
      );
      
      // Create a custom material with realistic terracotta color
      const wallMaterial = new THREE.MeshStandardMaterial({
        color: 0xd35400, // Authentic terracotta color (more orange-brown)
        emissive: 0x783104, // Subtle warm glow
        metalness: 0.0,
        roughness: 0.85, // More roughness for clay-like appearance
        side: THREE.DoubleSide, // Render both sides
      });
      
      // Apply view mode to wall material
      applyViewMode(wallMaterial, currentViewMode, ElementType.ATTACHED_WALL); // Attached wall is structural
      
      // Force material update
      wallMaterial.needsUpdate = true;
      const wall = new THREE.Mesh(wallGeometry, wallMaterial);
      
      // Position the wall at the right edge of the carport
      wall.position.set(
        (span || 3000) / 1000 , // Right edge
        (length || 6000) / 1000 / 2, // Center along length
        (height || 3600) / 2000 // Ground level
      );
      
      // Apply rotation to match scene orientation
      wall.rotation.x = Math.PI ;
      // Add the wall directly to the scene
      scene.add(wall);
      
      // Log the wall position for debugging
      //console.log('Wall position:', wall.position);
      //console.log('Slab position:', carportConfig.slab_Position);
      
      // Return cleanup function
      return () => disposeAll(wall);
    }
    // No cleanup needed if no wall was created
    return undefined;
  }, [scene, roofType, height, length, span, currentViewMode]);
  
  useEffect(() => {
    const group = new THREE.Group();
    
    // Create material with view mode applied
    const postMaterialWithViewMode = createViewModeMaterial(postMaterial, currentViewMode, ElementType.POST);
    
    if (roofType !== 'Skillion Attached') {
      // Regular right posts for other roof types
      carportConfig.post_right_Positions.forEach((position, index) => {
        const geo = carportConfig.post_right_Geo[index];
        const shsShape = createSHSShape(geo.geo_x, geo.geo_y);
        const extrudeSettings = { depth: geo.geo_z, bevelEnabled: false };
        const postGeometry = new THREE.ExtrudeGeometry(shsShape, extrudeSettings);
        const post = new THREE.Mesh(postGeometry, postMaterialWithViewMode);
        post.position.set(position.x, position.y, position.z);
        group.add(post);
      });
    }

    scene.add(group);
    return () => disposeAll(group);
  }, [scene, carportConfig.post_right_Geo, carportConfig.post_right_Positions, roofType, height, length, span, currentViewMode]);

  useEffect(() => {
    const group = new THREE.Group();
    
    // Create material with view mode applied
    const rafterMaterialWithViewMode = createViewModeMaterial(rafterMaterial, currentViewMode, ElementType.KNEE_BRACE);
    
    // Create the Cee section shape using the helper function
    const ceeShape = createCeeSectionShape(
      carportConfig.rafter_Geo.geo_x,
      carportConfig.rafter_Geo.geo_y,
      carportConfig.rafter_Geo.flange_thickness,
    );
    // Process the geometry
    const rafterGeometry = processGeometry(
      new THREE.ExtrudeGeometry(ceeShape, {
        depth: carportConfig.rafter_Geo.geo_z,
        bevelEnabled: false,
      }),
      Math.PI / 2 + (carportConfig.pitch * Math.PI) / 180,
      0,
      -Math.PI / 2,
    );

    carportConfig.rafter_Positions.forEach((position, index) => {
      const rafter = new THREE.Mesh(rafterGeometry, rafterMaterialWithViewMode);
      rafter.position.set(position.x, position.y, position.z);
      if (index === carportConfig.rafter_Positions.length - 1) {
        rafter.rotation.y = (-2 * (carportConfig.pitch * Math.PI)) / 180;
        rafter.rotation.z = Math.PI;
      }
      group.add(rafter);
    });

    scene.add(group);
    return () => disposeAll(group);
  }, [
    scene,
    carportConfig.pitch,
    carportConfig.rafter_Geo.flange_thickness,
    carportConfig.rafter_Geo.geo_x,
    carportConfig.rafter_Geo.geo_y,
    carportConfig.rafter_Geo.geo_z,
    carportConfig.rafter_Positions,
    currentViewMode, // Update viewMode to currentViewMode
  ]);

  // Flat Purlin //////////////
  useEffect(() => {
    const group = new THREE.Group();
    const ceeShape = createCeeSectionShape(carportConfig.flat_purlin_Geo.geo_x, carportConfig.flat_purlin_Geo.geo_y, 0.002);
    // Process the geometry
    const purlinGeometry = processGeometry(
      new THREE.ExtrudeGeometry(ceeShape, {
        depth: carportConfig.flat_purlin_Geo.geo_z,
        bevelEnabled: false,
      }),
      Math.PI / 2,
      (-carportConfig.pitch * Math.PI) / 180,
      0
    );

    // Use one purlins for each row as approximate
    carportConfig.flat_purlin_Positions.forEach((position) => {
      const purlin = new THREE.Mesh(purlinGeometry, rafterMaterial);
      purlin.position.set(position.x, position.y, position.z);
      group.add(purlin);
    });

    scene.add(group);
    return () => disposeAll(group);
  }, [
    scene,
    carportConfig.flat_purlin_Geo,
    carportConfig.flat_purlin_Positions,
    carportConfig.pitch,
  ]);

  // Left EP //////////////////////////////////////////////
  // Create the Cee section shape using the helper function
  const ceeShape_EP = createCeeSectionShape(
    carportConfig.left_EP_Geo.geo_x,
    carportConfig.left_EP_Geo.geo_y,
    carportConfig.left_EP_Geo.flange_thickness,
  );

  useEffect(() => {
    const group = new THREE.Group();
    // Process the geometry
    const EP_LEFT_Geometry = processGeometry(
      new THREE.ExtrudeGeometry(ceeShape_EP, {
        depth: carportConfig.left_EP_Geo.geo_z,
        bevelEnabled: false,
      }),
      Math.PI / 2,
      Math.PI,
      0,
    );

    carportConfig.left_EP_Positions.forEach((position) => {
      const LEFT_EP = new THREE.Mesh(EP_LEFT_Geometry, rafterMaterial);
      LEFT_EP.position.set(position.x, position.y, position.z);
      group.add(LEFT_EP);
    });

    scene.add(group);
    return () => disposeAll(group);
  }, [scene, carportConfig.left_EP_Geo.geo_z, carportConfig.left_EP_Positions, ceeShape_EP]);

  useEffect(() => {
    const group = new THREE.Group();
    // Right EP
    // Create the Cee section shape using the helper function
    const ceeShape_EP_R = createCeeSectionShape(
      carportConfig.right_EP_Geo.geo_x,
      carportConfig.right_EP_Geo.geo_y,
      carportConfig.right_EP_Geo.flange_thickness,
    );

    // Process the geometry
    const EP_RIGHT_Geometry = processGeometry(
      new THREE.ExtrudeGeometry(ceeShape_EP_R, {
        depth: carportConfig.right_EP_Geo.geo_z,
        bevelEnabled: false,
      }),
      -Math.PI / 2,
      Math.PI,
      Math.PI,
    );

    carportConfig.right_EP_Positions.forEach((position) => {
      const RIGHT_EP = new THREE.Mesh(EP_RIGHT_Geometry, rafterMaterial);
      RIGHT_EP.position.set(position.x, position.y, position.z);
      group.add(RIGHT_EP);
    });

    scene.add(group);
    return () => disposeAll(group);
  }, [
    scene,
    carportConfig.right_EP_Geo.flange_thickness,
    carportConfig.right_EP_Geo.geo_x,
    carportConfig.right_EP_Geo.geo_y,
    carportConfig.right_EP_Geo.geo_z,
    carportConfig.right_EP_Positions,
  ]);

  useEffect(() => {
    const group = new THREE.Group();
    // Footings //////////////////////////////////////////////
    carportConfig.footing_Positions.forEach((position) => {
      const geometry = new THREE.CylinderGeometry(
        carportConfig.footing_Geo.radius,
        carportConfig.footing_Geo.radius,
        carportConfig.footing_Geo.height,
        32,
      );

      const FOOTING_GEO = processGeometry(geometry, Math.PI / 2, 0, 0);
      // Create a material with view mode applied
      const material = createViewModeMaterial(footingMaterial, currentViewMode, ElementType.FOOTING);
      const footing = new THREE.Mesh(FOOTING_GEO, material);
      footing.position.set(position.x, position.y, position.z);
      group.add(footing);
    });

    scene.add(group);
    return () => disposeAll(group);
  }, [
    scene,
    carportConfig.footing_Geo.height,
    carportConfig.footing_Geo.radius,
    carportConfig.footing_Positions,
    currentViewMode, // Update viewMode to currentViewMode
  ]);

  useEffect(() => {
    // Slab //////////////////////////////////////////////
    // Only create and add slab if showSlab is true
    if (showSlabValue) {
      const slab_GEO = processGeometry(
        new THREE.BoxGeometry(
          carportConfig.slab_Geo.width,
          carportConfig.slab_Geo.thickness,
          carportConfig.slab_Geo.length,
        ),
        Math.PI / 2,
        0,
        0,
      );
      const slab = new THREE.Mesh(slab_GEO, slabMaterial);
      slab.position.set(
        carportConfig.slab_Position.x,
        carportConfig.slab_Position.y,
        carportConfig.slab_Position.z,
      );

      scene.add(slab);
      return () => disposeAll(slab);
    }
    
    // Return empty cleanup function if no slab was created
    return () => {};
  }, [
    scene,
    sceneSettings.showSlab, // Add showSlab to dependencies
    carportConfig.slab_Geo.width,
    carportConfig.slab_Geo.thickness,
    carportConfig.slab_Geo.length,
    carportConfig.slab_Position.x,
    carportConfig.slab_Position.y,
    carportConfig.slab_Position.z,
  ]);

  useEffect(() => {
    // Roofs - MonoClad Type (for Flat, Awning, Attached) //////////////////////////////////////////////
    const group = new THREE.Group();
    const noPlates = carportConfig.roof_Geo.length;
    const monoPlateLine = createMonoClad2DSection(
      carportConfig.roof_Geo[0].height,
      carportConfig.roof_Geo[0].thickness,
      carportConfig.roof_Geo[0].singlelength,
    );
    const lastMonoPlateLine = createMonoClad2DSection(
      carportConfig.roof_Geo[noPlates - 1].height,
      carportConfig.roof_Geo[noPlates - 1].thickness,
      carportConfig.roof_Geo[noPlates - 1].singlelength,
    );
    carportConfig.roof_Positions.forEach((position, index) => {
      const geometry = processGeometry(
        new THREE.ExtrudeGeometry(index < noPlates - 1 ? monoPlateLine : lastMonoPlateLine, {
          depth: carportConfig.roof_Geo[index].length,
          bevelEnabled: false,
        }),
        Math.PI / 2 - carportConfig.pitch * Math.PI / 180,
        0,
        Math.PI / 2,
      );
      // Create material with view mode applied for roof sheets
      const roofMaterialWithViewMode = createViewModeMaterial(roofMaterial, viewMode, ElementType.ROOF_SHEET);
      const roof = new THREE.Mesh(geometry, roofMaterialWithViewMode);
      roof.position.set(position.x, position.y, position.z);
      group.add(roof);
    });
    scene.add(group);
    return () => disposeAll(group);
  }, [
    scene,
    carportConfig.roof_Positions,
    carportConfig.roof_Geo,
    carportConfig.pitch,
    viewMode,
    materialsUpdated, // Add materialsUpdated to trigger re-renders when colors change
  ]);

  // Left Barge Cap Flashing
  useEffect(() => {
    const group = new THREE.Group();
    
    // Calculate the pitch in radians
    const pitchRadians = (pitch! * Math.PI) / 180;
    
    // Barge cap parameters
    const plateWidth = 0.17; // 170mm width for each plate
    const bargeCapThickness = 0.001; // 1mm thickness
    const bargeCapLength = (length!+30)/ 1000; // Same length as the carport
    
    // Create the barge cap shape based on the roof pitch
    const bargeCapShape = createBargeCapFlashing2DShape(plateWidth, bargeCapThickness, pitch!);
    
    // Process the geometry
    const bargeCapGeometry = processGeometry(
      new THREE.ExtrudeGeometry(bargeCapShape, {
        depth: bargeCapLength,
        bevelEnabled: false,
      }),
      Math.PI ,
      Math.PI ,
      Math.PI / 2
    );
    
    // Calculate the position for the barge cap
    // It should be at the left edge of the roof
    const x = 0.06; // Left edge, slightly offset from edge
    const y = (length! ) / 2 / 1000-.02; // Center position along the length
    // Use the same z-value as the left EP
    const z = carportConfig.left_EP_Positions[0]?.z || height! / 1000; // Base height matching left EP
    
    // Create the mesh and add to the scene
    // Create material with view mode applied for barge cap (using capMaterial for trim color control)
    const bargeMaterialWithViewMode = createViewModeMaterial(capMaterial, viewMode, ElementType.BARGE);
    const bargeCap = new THREE.Mesh(bargeCapGeometry, bargeMaterialWithViewMode);
    bargeCap.position.set(x, y, z);
    
    // Rotate the barge cap to align with the roof
    bargeCap.rotation.x = Math.PI / 2;
    
    group.add(bargeCap);
    scene.add(group);
    
    return () => disposeAll(group);
  }, [
    scene,
    span,
    length,
    height,
    pitch,
    overhang,
    roofType,
    carportConfig,
    viewMode, // Add viewMode to dependency array
    materialsUpdated, // Add materialsUpdated to trigger re-renders when colors change
  ]);

  // Front Barge Left (along the front rafter)
  useEffect(() => {
    const group = new THREE.Group();
    
    // Calculate the pitch in radians
    const pitchRadians = (pitch! * Math.PI) / 180;
    
    // Barge cap parameters
    const plateWidth = 0.21; // 170mm width for each plate
    const bargeCapThickness = 0.001; // 1mm thickness
    const bargeCapLength = (span! + 2*overhang!)/Math.cos(pitchRadians)/ 1000; // Length is the span of the carport
    
    // Create the barge cap shape based on the roof pitch
    const bargeCapShape = createBargeCapFlashing2DShape(plateWidth, bargeCapThickness, -pitch!);
    
    // Process the geometry
    const bargeCapGeometry = processGeometry(
      new THREE.ExtrudeGeometry(bargeCapShape, {
        depth: bargeCapLength,
        bevelEnabled: false,
      }),
      0 ,
      0,
      0
    );
    
    // Calculate the position for the front barge cap
    // It should be at the front edge of the roof along the left rafter
    // Get the front rafter position (first rafter position)
    const frontRafterPos = carportConfig.rafter_Positions[0];
    const x = (span! + 2*overhang!)/Math.cos(pitchRadians) / 1000/2; // Left edge
    const y = frontRafterPos?.y -0.064+.13|| 0.2; // Front position
    const z = frontRafterPos?.z +.03|| height! / 1000; // Same height as the front rafter
    
    // Create the mesh and add to the scene
    const bargeMaterialWithViewMode = createViewModeMaterial(capMaterial, viewMode, ElementType.BARGE);
    const frontBargeLeft = new THREE.Mesh(bargeCapGeometry, bargeMaterialWithViewMode);
    frontBargeLeft.position.set(x, y, z);
    
    // Rotate the barge cap to align with the front rafter
    frontBargeLeft.rotation.y = -pitchRadians+Math.PI/2;
    frontBargeLeft.rotation.z = 0 ;
    group.add(frontBargeLeft);
    scene.add(group);
    
    return () => disposeAll(group);
  }, [
    scene,
    span,
    length,
    height,
    pitch,
    overhang,
    roofType,
    carportConfig,
    viewMode,
    materialsUpdated,
  ]);

  // Back Barge Left (along the last rafter at the back)
  useEffect(() => {
    const group = new THREE.Group();
    
    // Calculate the pitch in radians
    const pitchRadians = (pitch! * Math.PI) / 180;
    
    // Barge cap parameters
    const plateWidth = 0.21; // Width for each plate
    const bargeCapThickness = 0.001; // 1mm thickness
    const bargeCapLength = (span! + 2*overhang!)/Math.cos(pitchRadians)/ 1000; // Length is the span of the carport
    
    // Create the barge cap shape based on the roof pitch
    const bargeCapShape = createBargeCapFlashing2DShape(plateWidth, bargeCapThickness, -pitch!);
    
    // Process the geometry
    const bargeCapGeometry = processGeometry(
      new THREE.ExtrudeGeometry(bargeCapShape, {
        depth: bargeCapLength,
        bevelEnabled: false,
      }),
      Math.PI,
      0,
      -Math.PI/2
    );
    
    // Calculate the position for the back barge cap
    // It should be at the back edge of the roof along the left rafter
    // Get the back rafter position (last rafter position)
    const backRafterPos = carportConfig.rafter_Positions[carportConfig.rafter_Positions.length - 1];
    const x = (span! + 2*overhang!)/Math.cos(pitchRadians) / 1000/2; // Left edge
    const y = backRafterPos?.y -.03 || length! / 1000; // Back position
    const z = backRafterPos?.z +.03 || height! / 1000; // Same height as the back rafter
    
    // Create the mesh and add to the scene
    const bargeMaterialWithViewMode = createViewModeMaterial(capMaterial, viewMode, ElementType.BARGE);
    const backBargeLeft = new THREE.Mesh(bargeCapGeometry, bargeMaterialWithViewMode);
    backBargeLeft.position.set(x, y, z);
    
    // Rotate the barge cap to align with the back rafter
    backBargeLeft.rotation.y = -pitchRadians-Math.PI/2; // Opposite rotation from front
    backBargeLeft.rotation.z = 0;
    
    group.add(backBargeLeft);
    scene.add(group);
    
    return () => disposeAll(group);
  }, [
    scene,
    span,
    length,
    height,
    pitch,
    overhang,
    roofType,
    carportConfig,
    viewMode,
    materialsUpdated,
  ]);

  // Right Barge Cap Flashing
  useEffect(() => {
    const group = new THREE.Group();
    
    // Calculate the pitch in radians
    const pitchRadians = (pitch! * Math.PI) / 180;
    
    // Barge cap parameters
    const plateWidth = 0.17; // 170mm width for each plate
    const bargeCapThickness = 0.001; // 1mm thickness
    const bargeCapLength = (length! + 65)/ 1000; // Same length as the carport
    
    // Create the right barge cap shape based on the roof pitch
    // For non-gable roofs, the angle is 90 degrees minus pitch
    const bargeCapShape = createRightBargeCapFlashing2DShape(plateWidth, bargeCapThickness, pitch!, false);
    
    // Process the geometry
    const bargeCapGeometry = processGeometry(
      new THREE.ExtrudeGeometry(bargeCapShape, {
        depth: bargeCapLength,
        bevelEnabled: false,
      }),
      Math.PI,
      Math.PI,
      -Math.PI / 2
    );
    
    // Calculate the position for the barge cap
    // It should be at the right edge of the roof
    const x = (span! + 2*overhang!)/ 1000 - 0.06; // Right edge (span minus offset)
    const y = (length! + 65) / 2 / 1000-.03; // Center position along the length
    // Use the same z-value as the right EP
    const z = carportConfig.right_EP_Positions[0]?.z + .032 || height! / 1000; // Base height matching right EP
    
    // Create the mesh and add to the scene
    // Create material with view mode applied for barge cap (using capMaterial for trim color control)
    const bargeMaterialWithViewMode = createViewModeMaterial(capMaterial, viewMode, ElementType.BARGE);
    const bargeCap = new THREE.Mesh(bargeCapGeometry, bargeMaterialWithViewMode);
    bargeCap.position.set(x, y, z);
    
    // Rotate the barge cap to align with the roof
    bargeCap.rotation.x = Math.PI / 2;
    
    group.add(bargeCap);
    scene.add(group);
    
    return () => disposeAll(group);
  }, [
    scene,
    span,
    length,
    height,
    pitch,
    overhang,
    roofType,
    carportConfig,
    viewMode, // Add viewMode to dependency array
    materialsUpdated, // Add materialsUpdated to trigger re-renders when colors change
  ]);

  // Left Lean-to Front Barge Cap
  useEffect(() => {
    // Only render if leftLeanTo is enabled
    if (!leftLeanTo) return;
    
    const group = new THREE.Group();
    
    // Calculate the pitch in radians (lean-to uses a fixed 2 degree pitch)
    const leanToPitchRadians = (2 * Math.PI) / 180;
    
    // Barge cap parameters
    const plateWidth = 0.21; // Width for each plate
    const bargeCapThickness = 0.001; // 1mm thickness
    const bargeCapLength = (leftLeanToSpan! + 100)/ Math.cos(leanToPitchRadians) / 1000; // Length is the lean-to span
    
    // Create the barge cap shape based on the roof pitch
    const bargeCapShape = createBargeCapFlashing2DShape(plateWidth, bargeCapThickness, -2); // 2 degree pitch
    
    // Process the geometry
    const bargeCapGeometry = processGeometry(
      new THREE.ExtrudeGeometry(bargeCapShape, {
        depth: bargeCapLength,
        bevelEnabled: false,
      }),
      0,
      0,
      0
    );
    
    // Calculate the position for the front barge cap
    // Get the front lean-to rafter position (first rafter position)
    const frontRafterPos = carportConfig.leanto_rafter_Positions[0];
    if (!frontRafterPos) return () => {}; // Safety check
    
    const x = -(leftLeanToSpan! ) / 1000 /2+overhang!/1000; // Center of lean-to span
    const y = frontRafterPos.y - 0.064 + 0.13; // Front position
    const z = frontRafterPos.z + 0.01; // Same height as the front rafter
    
    // Create the mesh and add to the scene
    const bargeMaterialWithViewMode = createViewModeMaterial(capMaterial, viewMode, ElementType.BARGE);
    const frontBargeLeft = new THREE.Mesh(bargeCapGeometry, bargeMaterialWithViewMode);
    frontBargeLeft.position.set(x, y, z);
    
    // Rotate the barge cap to align with the front rafter
    frontBargeLeft.rotation.y = -leanToPitchRadians + Math.PI/2;
    
    group.add(frontBargeLeft);
    scene.add(group);
    
    return () => disposeAll(group);
  }, [
    scene,
    span,
    length,
    height,
    pitch,
    overhang,
    leftLeanTo,
    leftLeanToSpan,
    leftLeanToDropHeight,
    carportConfig,
    viewMode,
    materialsUpdated,
  ]);
  
  // Left Lean-to Back Barge Cap
  useEffect(() => {
    // Only render if leftLeanTo is enabled
    if (!leftLeanTo) return;
    
    const group = new THREE.Group();
    
    // Calculate the pitch in radians (lean-to uses a fixed 2 degree pitch)
    const leanToPitchRadians = (2 * Math.PI) / 180;
    
    // Barge cap parameters
    const plateWidth = 0.21; // Width for each plate
    const bargeCapThickness = 0.001; // 1mm thickness
    const bargeCapLength = (leftLeanToSpan! + 100)/ 1000; // Length is the lean-to span
    
    // Create the barge cap shape based on the roof pitch
    const bargeCapShape = createBargeCapFlashing2DShape(plateWidth, bargeCapThickness, -2); // 2 degree pitch
    
    // Process the geometry
    const bargeCapGeometry = processGeometry(
      new THREE.ExtrudeGeometry(bargeCapShape, {
        depth: bargeCapLength,
        bevelEnabled: false,
      }),
      Math.PI,
      0,
      -Math.PI/2
    );
    
    // Calculate the position for the back barge cap
    // Get the back lean-to rafter position (last rafter position)
    const backRafterPos = carportConfig.leanto_rafter_Positions[carportConfig.leanto_rafter_Positions.length - 1];
    if (!backRafterPos) return () => {}; // Safety check
    
    const x = -(leftLeanToSpan! ) / 1000 / 2+overhang!/1000; // Center of lean-to span
    const y = backRafterPos.y - 0.03; // Back position
    const z = backRafterPos.z + 0.01; // Same height as the back rafter
    
    // Create the mesh and add to the scene
    const bargeMaterialWithViewMode = createViewModeMaterial(capMaterial, viewMode, ElementType.BARGE);
    const backBargeLeft = new THREE.Mesh(bargeCapGeometry, bargeMaterialWithViewMode);
    backBargeLeft.position.set(x, y, z);
    
    // Rotate the barge cap to align with the back rafter
    backBargeLeft.rotation.y = -leanToPitchRadians - Math.PI/2;
    
    group.add(backBargeLeft);
    scene.add(group);
    
    return () => disposeAll(group);
  }, [
    scene,
    span,
    length,
    height,
    pitch,
    overhang,
    leftLeanTo,
    leftLeanToSpan,
    leftLeanToDropHeight,
    carportConfig,
    viewMode,
    materialsUpdated,
  ]);
  
  // Right Lean-to Front Barge Cap
  useEffect(() => {
    // Only render if rightLeanTo is enabled and not attached
    // For attached lean-tos, we don't need barge caps
    // Also don't render if we have a skillion roof (indicated by non-zero pitch)
    if (!rightLeanTo || rightLeanToAttached || (pitch !== undefined && pitch > 0)) return;
    
    const group = new THREE.Group();
    
    // Calculate the pitch in radians (lean-to uses a fixed 2 degree pitch)
    const leanToPitchRadians = (2 * Math.PI) / 180;
    
    // Barge cap parameters
    const plateWidth = 0.21; // Width for each plate
    const bargeCapThickness = 0.001; // 1mm thickness
    const bargeCapLength = (rightLeanToSpan! + 100)/ 1000; // Length is the lean-to span
    
    // Create the barge cap shape based on the roof pitch - use right barge cap for right lean-to
    const bargeCapShape = createRightBargeCapFlashing2DShape(plateWidth, bargeCapThickness, -2, false); // 2 degree pitch
    
    // Process the geometry
    const bargeCapGeometry = processGeometry(
      new THREE.ExtrudeGeometry(bargeCapShape, {
        depth: bargeCapLength,
        bevelEnabled: false,
      }),
      0,
      Math.PI,
      0
    );
    
    // Calculate the position for the front barge cap
    // Get the front lean-to rafter position (first rafter position)
    const frontRafterPos = carportConfig.leanto_rafter_right_Positions[0];
    if (!frontRafterPos) return () => {}; // Safety check
    
    const x = (span! + overhang!) / 1000 + (rightLeanToSpan! ) / 1000 / 2; // Center of right lean-to span
    const y = frontRafterPos.y - 0.064 + 0.13; // Front position
    const z = frontRafterPos.z + 0.01; // Same height as the front rafter
    
    // Create the mesh and add to the scene
    const bargeMaterialWithViewMode = createViewModeMaterial(capMaterial, viewMode, ElementType.BARGE);
    const frontBargeRight = new THREE.Mesh(bargeCapGeometry, bargeMaterialWithViewMode);
    frontBargeRight.position.set(x, y, z);
    
    // Rotate the barge cap to align with the front rafter
    frontBargeRight.rotation.y = leanToPitchRadians + Math.PI/2;
    
    group.add(frontBargeRight);
    scene.add(group);
    
    return () => disposeAll(group);
  }, [
    scene,
    span,
    length,
    height,
    pitch,
    overhang,
    rightLeanTo,
    rightLeanToSpan,
    rightLeanToDropHeight,
    carportConfig,
    viewMode,
    materialsUpdated,
  ]);
  
  // Right Lean-to Back Barge Cap
  useEffect(() => {
    // Only render if rightLeanTo is enabled and not attached
    // For attached lean-tos, we don't need barge caps
    // Also don't render if we have a skillion roof (indicated by non-zero pitch)
    if (!rightLeanTo || rightLeanToAttached || (pitch !== undefined && pitch > 0)) return;
    
    const group = new THREE.Group();
    
    // Calculate the pitch in radians (lean-to uses a fixed 2 degree pitch)
    const leanToPitchRadians = (2 * Math.PI) / 180;
    
    // Barge cap parameters
    const plateWidth = 0.21; // Width for each plate
    const bargeCapThickness = 0.001; // 1mm thickness
    const bargeCapLength = (rightLeanToSpan! + 100) / 1000; // Length is the lean-to span
    
    // Create the barge cap shape based on the roof pitch - use right barge cap for right lean-to
    const bargeCapShape = createRightBargeCapFlashing2DShape(plateWidth, bargeCapThickness, -2, false); // 2 degree pitch
    
    // Process the geometry
    const bargeCapGeometry = processGeometry(
      new THREE.ExtrudeGeometry(bargeCapShape, {
        depth: bargeCapLength,
        bevelEnabled: false,
      }),
      0,
      0,
      Math.PI/2 // Positive for right side
    );
    
    // Calculate the position for the back barge cap
    // Get the back lean-to rafter position (last rafter position)
    const backRafterPos = carportConfig.leanto_rafter_right_Positions[carportConfig.leanto_rafter_right_Positions.length - 1];
    if (!backRafterPos) return () => {}; // Safety check
    
    const x = (span! + overhang!) / 1000 + (rightLeanToSpan! ) / 1000 / 2; // Center of right lean-to span
    const y = backRafterPos.y - 0.03; // Back position
    const z = backRafterPos.z + 0.01; // Same height as the back rafter
    
    // Create the mesh and add to the scene
    const bargeMaterialWithViewMode = createViewModeMaterial(capMaterial, viewMode, ElementType.BARGE);
    const backBargeRight = new THREE.Mesh(bargeCapGeometry, bargeMaterialWithViewMode);
    backBargeRight.position.set(x, y, z);
    
    // Rotate the barge cap to align with the back rafter
    backBargeRight.rotation.y = leanToPitchRadians - Math.PI/2;
    
    group.add(backBargeRight);
    scene.add(group);
    
    return () => disposeAll(group);
  }, [
    scene,
    span,
    length,
    height,
    pitch,
    overhang,
    rightLeanTo,
    rightLeanToSpan,
    rightLeanToDropHeight,
    carportConfig,
    viewMode,
    materialsUpdated,
  ]);

  // Knee Braces
  useEffect(() => {
    // Create a group for all knee braces
    const group = new THREE.Group();
    
    // Use the same material as rafters for consistent appearance
    // Slightly reduce opacity to avoid clashing
    // Use zinc finish material with texture for knee braces
    const kneeMaterial = zincFinishMaterial.clone();
    // Ensure the texture is properly applied
    kneeMaterial.needsUpdate = true;
    kneeMaterial.transparent = false; // Full opacity for better texture visibility
    kneeMaterial.opacity = 1.0;
    
    // Use fixed values for testing to avoid NaN errors
    const webSize = .05; // 50mm
    const flangeSize = 0.102; // 102mm
    
    // Create a C-section shape for the knee brace with fixed values
    const kneeShape = createCeeSectionShape(webSize, flangeSize, 0.002);
    
    // Create a simple box geometry as fallback in case of errors
    let leftKneeGeometry;
    let rightKneeGeometry;
    let kneefactor;
    let kneeoffset;

    // Calculate pitch in radians
    const pitchRadians = (pitch! * Math.PI) / 180;
    
    // Set knee factors based on pitch
    if (pitch! === 2 ) {
      kneefactor = 0.15;
      kneeoffset = 175;
    } else if (pitch! === 5) {
      kneefactor = -0.5;
      kneeoffset = 125;
    } else {
      // Default values for other pitch values
      kneefactor = 0.1;
      kneeoffset = 200;
    }
    
    try {
      // Create and process the extruded geometry
      const extrudedGeometry = new THREE.ExtrudeGeometry(kneeShape, {
        depth: 0.6,
        bevelEnabled: false
      });
      
      // Process the geometry for left knee orientation
      leftKneeGeometry = processGeometry(
        extrudedGeometry.clone(),
        Math.PI/4, // Rotation around X
        0,        // No rotation around Y
        Math.PI/4 // Rotation around Z
      );
      
      // Process the geometry for right knee with different orientation
      rightKneeGeometry = processGeometry(
        extrudedGeometry.clone(),
        -Math.PI/4,  // Rotation around X
        Math.PI,     // Rotation around Y
        -Math.PI/4   // Rotation around Z (mirrored)
      );
    } catch (error) {
      console.error('Error creating C-section geometry:', error);
      // Fallback to box geometry if there's an error
      leftKneeGeometry = new THREE.BoxGeometry(0.6, 0.05, 0.05);
      rightKneeGeometry = new THREE.BoxGeometry(0.6, 0.05, 0.05);
    }
    
    // main left knee adjustment
    let main_left_knee_adjustment = 0;
    if (pitch === 5) {
      if  (span! <4800) {
        main_left_knee_adjustment = 0.1;
      } else if (span! >9000) {
        main_left_knee_adjustment = -.25;
      } else if (span! >6500) {
        main_left_knee_adjustment = -.15;
      } else {
        main_left_knee_adjustment = 0;
      }
    } else if (pitch === 2) {
      if  (span! <4800) {
        main_left_knee_adjustment = 0.15;
      } else if (span! >9000) {
        main_left_knee_adjustment = 0;
      } else if (span! >6500) {
        main_left_knee_adjustment = .1;
      } else {
        main_left_knee_adjustment = .1;
      }
    }

// main right knee adjustment
let main_right_knee_adjustment = 0;
if (pitch === 5) {
  if  (span! <4800) {
    main_right_knee_adjustment = -.08;
  } else if (span! >9000) {
    main_right_knee_adjustment = -.08;
  } else if (span! >6500) {
    main_right_knee_adjustment = -.05;
  } else {
    main_right_knee_adjustment = -.05;
  }
} else if (pitch === 2) {
  if  (span! <4800) {
    main_right_knee_adjustment = -.05;
  } else if (span! >9000) {
    main_right_knee_adjustment = 0;
  } else if (span! >6500) {
    main_right_knee_adjustment = -.05;
  } else {
    main_left_knee_adjustment = .1;
  }
}

    // For each bay, create left and right knee braces
    for (let i = 0; i <= Math.max(1, Math.floor(length! / 4000)); i++) {
      try {
        // Calculate the positions
        // Start at left post
        const leftPostX = (overhang! + post_size + 250) / 1000;
        // Calculate right post position (span - left post position + adjustments)
        const rightPostX = (span! + overhang! - post_size - 250) / 1000;
        
        const postY = i === Math.floor(length! / 4000)
          ? (length! - post_size) / 1000
          : (post_size + (i * length!) / Math.max(1, Math.floor(length! / 4000))) / 1000;
        
        // Calculate dynamic height using the exact same formula as in Carportconfig.ts
        // This ensures the knee brace will always land on the rafter regardless of span
        const span_drop = (span! - Rafter_flange * 2 - post_size) * Math.tan(pitchRadians);
        
        // Use the exact formula from Carportconfig.ts
        const rafter_z = (height! + span_drop / 2 - (0.25 * Rafter_web) / Math.cos(pitchRadians)) / 1000;
        
        // Position at the post, 90% up from the ground
        const postZ = rafter_z - 0.4+main_left_knee_adjustment;
        
        // Create the left knee brace mesh
        const leftKneeBrace = new THREE.Mesh(leftKneeGeometry, kneeMaterial);
        leftKneeBrace.position.set(leftPostX, postY, postZ);
        leftKneeBrace.rotation.z = Math.PI / 4;
        group.add(leftKneeBrace);
        
        //console.log('Added left knee brace at position:', leftKneeBrace.position);
        
        let roofheightadjustment;
        if (pitch === 5) {
          roofheightadjustment = 0.95;
        } else {
          roofheightadjustment = 1;
        }
        if (roofType !== 'Skillion Attached') {
          // Create the right knee brace mesh
          const rightKneeBrace = new THREE.Mesh(rightKneeGeometry, kneeMaterial);
          
          // Calculate height adjustment based on pitch and span
          // For right side, we need to adjust the height based on the roof slope
          const heightAdjustment = (span! - Rafter_flange * 2 - post_size) * Math.tan(pitchRadians) / 1000;
          const rightPostZ = postZ + heightAdjustment+main_right_knee_adjustment;
          
          rightKneeBrace.position.set(rightPostX, postY, rightPostZ);
          // Mirror the rotation for the right side
          rightKneeBrace.rotation.z = -Math.PI / 4;
          group.add(rightKneeBrace);
          
          //console.log('Added right knee brace at position:', rightKneeBrace.position);
        }
      } catch (error) {
        console.error('Error creating knee brace:', error);
      }
    }
    
    // Add the group to the scene
    scene.add(group);
    
    return () => disposeAll(group);
  }, [
    scene,
    span,
    length,
    height,
    pitch,
    post_size,
    overhang,
    knee_web,
    knee_flange
  ]);

  // Main Left Drop Sheets
  useEffect(() => {
    if (!leftLeanTo) return;
    
    try {
      const group = new THREE.Group();
      
      if (carportConfig.main_left_drop_Positions.length > 0) {
        const noSheets = carportConfig.main_left_drop_Geo.length;
        
        // Create MonoClad section shapes for the drop sheets
        const dropMonoPlateLine = createMonoClad2DSection(
          carportConfig.main_left_drop_Geo[0].height,
          carportConfig.main_left_drop_Geo[0].thickness,
          carportConfig.main_left_drop_Geo[0].singlelength
        );
        
        const dropLastMonoPlateLine = noSheets > 1 ? createMonoClad2DSection(
          carportConfig.main_left_drop_Geo[noSheets - 1].height,
          carportConfig.main_left_drop_Geo[noSheets - 1].thickness,
          carportConfig.main_left_drop_Geo[noSheets - 1].singlelength
        ) : dropMonoPlateLine;
        
        // Create and position each drop sheet
        carportConfig.main_left_drop_Positions.forEach((position, index) => {
          const geometry = processGeometry(
            new THREE.ExtrudeGeometry(index < noSheets - 1 ? dropMonoPlateLine : dropLastMonoPlateLine, {
              depth: carportConfig.main_left_drop_Geo[index].length,
              bevelEnabled: false,
            }),
            0,         // No rotation around X (keep vertical)
            Math.PI, // Rotate 90 degrees around Y to make it vertical
            Math.PI/2          // No rotation around Z
          );
          
          // Create material with view mode applied for main drop sheet
          const mainDropMaterialWithViewMode = createViewModeMaterial(roofMaterial, viewMode, ElementType.MAIN_DROP);
          const dropSheet = new THREE.Mesh(geometry, mainDropMaterialWithViewMode);
          dropSheet.position.set(position.x, position.y, position.z);
          group.add(dropSheet);
        });
      }
      
      scene.add(group);
      return () => disposeAll(group);
    } catch (error) {
      console.error('Error creating main left drop sheets:', error);
    }
  }, [
    scene,
    leftLeanTo,
    carportConfig.main_left_drop_Positions,
    carportConfig.main_left_drop_Geo,
    leftLeanToDropHeight,
    viewMode // Add viewMode to dependency array to ensure it updates when view mode changes
  ]);
  
  // Main Right Drop Sheets
  useEffect(() => {
    if (!rightLeanTo) return;
    
    try {
      const group = new THREE.Group();
      
      if (carportConfig.main_right_drop_Positions.length > 0) {
        const noSheets = carportConfig.main_right_drop_Geo.length;
        
        // Calculate the height difference based on span and pitch
        const rightLeanToSpanDrop = (span!) * Math.tan((pitch! * Math.PI) / 180); // Using lean-to pitch
        
        // Create MonoClad section shapes for the drop sheets
        const dropMonoPlateLine = createMonoClad2DSection(
          carportConfig.main_right_drop_Geo[0].height,
          carportConfig.main_right_drop_Geo[0].thickness,
          carportConfig.main_right_drop_Geo[0].singlelength
        );
        
        const dropLastMonoPlateLine = noSheets > 1 ? createMonoClad2DSection(
          carportConfig.main_right_drop_Geo[noSheets - 1].height,
          carportConfig.main_right_drop_Geo[noSheets - 1].thickness,
          carportConfig.main_right_drop_Geo[noSheets - 1].singlelength
        ) : dropMonoPlateLine;
        
        // Create and position each drop sheet
        carportConfig.main_right_drop_Positions.forEach((position, index) => {
          const geometry = processGeometry(
            new THREE.ExtrudeGeometry(index < noSheets - 1 ? dropMonoPlateLine : dropLastMonoPlateLine, {
              depth: carportConfig.main_right_drop_Geo[index].length,
              bevelEnabled: false,
            }),
            Math.PI,         // No rotation around X (keep vertical)
            0,         // No rotation around Y for right side (opposite of left side)
            Math.PI/2  // Rotation around Z
          );
          
          // Create material with view mode applied for main drop sheet
          const mainDropMaterialWithViewMode = createViewModeMaterial(roofMaterial, viewMode, ElementType.MAIN_DROP);
          const dropSheet = new THREE.Mesh(geometry, mainDropMaterialWithViewMode);
          
          // Adjust the z position to account for the height difference due to span and pitch
          const adjustedPosition = {
            x: position.x,
            y: position.y,
            z: position.z + rightLeanToSpanDrop / 1000 // Add half the height difference (converted to meters)
          };
          
          dropSheet.position.set(adjustedPosition.x, adjustedPosition.y, adjustedPosition.z);
          group.add(dropSheet);
        });
      }
      
      scene.add(group);
      return () => disposeAll(group);
    } catch (error) {
      console.error('Error creating main right drop sheets:', error);
    }
  }, [
    scene,
    rightLeanTo,
    rightLeanToSpan,
    leanToPitch,
    carportConfig.main_right_drop_Positions,
    carportConfig.main_right_drop_Geo,
    rightLeanToDropHeight,
    viewMode // Add viewMode to dependency array to ensure it updates when view mode changes
  ]);

  // Left Lean-to Components
  useEffect(() => {
    if (!leftLeanTo) return;
    
    const group = new THREE.Group();
    
    // Calculate the adjusted height for lean-to (main height minus drop height)
    const leanToHeight = (height! - leftLeanToDropHeight) / 1000;
    const leanToPitchRadians = (leanToPitch * Math.PI) / 180;
    
    try {
      // Left Lean-to Barge Cap Flashing
      // Barge cap parameters
      const plateWidth = 0.17; // 170mm width for each plate
      const bargeCapThickness = 0.001; // 1mm thickness
      const bargeCapLength = length! / 1000; // Same length as the carport
      
      // Create the barge cap shape based on the lean-to pitch
      const leanToBargeCapShape = createBargeCapFlashing2DShape(plateWidth, bargeCapThickness, leanToPitch);
      
      // Process the geometry
      const leanToBargeCapGeometry = processGeometry(
        new THREE.ExtrudeGeometry(leanToBargeCapShape, {
          depth: bargeCapLength,
          bevelEnabled: false,
        }),
        Math.PI,
        Math.PI,
        Math.PI / 2
      );
      
      // Calculate the position for the lean-to barge cap
      // It should be at the left edge of the lean-to roof with no overhang
      const x = (overhang! - leftLeanToSpan ) / 1000+0.05; // Left edge of lean-to
      const y = length! / 2 / 1000; // Center position along the length
      // Use the z-position based on the lean-to height
      const z = leanToHeight-0.07;
      
      // Create material with view mode applied for lean-to barge cap (using capMaterial for trim color control)
      const leanToBargeMaterialWithViewMode = createViewModeMaterial(capMaterial, viewMode, ElementType.BARGE);
      // Create the mesh and add to the scene
      const leanToBargeCap = new THREE.Mesh(leanToBargeCapGeometry, leanToBargeMaterialWithViewMode);
      leanToBargeCap.position.set(x, y, z);
      
      // Rotate the barge cap to align with the lean-to roof
      leanToBargeCap.rotation.x = Math.PI / 2;
      
      group.add(leanToBargeCap);
      
      // Left Lean-to Knee Braces
      // Use zinc finish material with texture for lean-to knee braces
      const leanToKneeMaterial = zincFinishMaterial.clone();
      // Ensure the texture is properly applied
      leanToKneeMaterial.needsUpdate = true;
      leanToKneeMaterial.transparent = false; // Full opacity for better texture visibility
      leanToKneeMaterial.opacity = 1.0;
      
      // Use fixed values for knee braces - same as main knee
      const webSize = .05; // 50mm
      const flangeSize = 0.102; // 102mm
      
      // Create a C-section shape for the knee brace
      const leanToKneeShape = createCeeSectionShape(webSize, flangeSize, 0.002);
      
      // Variables for knee geometry
      let leanToLeftKneeGeometry;
      let leanToKneeFactor;
      let leanToKneeOffset;
      
      // Set knee factors based on lean-to span - similar to main knee logic
      if (leftLeanToSpan < 2000) {
        leanToKneeFactor = 0.15;
        leanToKneeOffset = 175;
      } else if (leftLeanToSpan > 3500) {
        leanToKneeFactor = 0.1;
        leanToKneeOffset = 150;
      } else {
        leanToKneeFactor = 0.15;
        leanToKneeOffset = 175;
      }
      
      try {
        // Create and process the extruded geometry
        const leanToExtrudedGeometry = new THREE.ExtrudeGeometry(leanToKneeShape, {
          depth: 0.6, // Same as main knee
          bevelEnabled: false
        });
        
        // Process the geometry for lean-to knee orientation - same as main left knee
        leanToLeftKneeGeometry = processGeometry(
          leanToExtrudedGeometry.clone(),
          Math.PI/4, // Rotation around X
          0,        // No rotation around Y
          Math.PI/4 // Rotation around Z
        );
      } catch (error) {
        console.error('Error creating lean-to C-section geometry:', error);
        // Fallback to box geometry if there's an error
        leanToLeftKneeGeometry = new THREE.BoxGeometry(0.6, 0.05, 0.05);
      }
      
      // Calculate lean-to knee adjustment based on span - similar to main knee adjustment
      let leanTo_left_knee_adjustment = 0;
      if (leftLeanToSpan < 2000) {
        leanTo_left_knee_adjustment = 0.15;
      } else if (leftLeanToSpan > 3500) {
        leanTo_left_knee_adjustment = 0.1;
      } else {
        leanTo_left_knee_adjustment = 0.1;
      }
      
      // For each bay, create lean-to knee braces
      for (let i = 0; i <= Math.max(1, Math.floor(length! / 4000)); i++) {
        try {
          // Calculate the positions
          // Position at lean-to post - similar to main left post calculation
          const leanToLeftPostX = (overhang! - leftLeanToSpan + post_size + 250) / 1000;
          
          const postY = i === Math.floor(length! / 4000)
            ? (length! - post_size) / 1000
            : (post_size + (i * length!) / Math.max(1, Math.floor(length! / 4000))) / 1000;
          
          // Calculate dynamic height using similar formula as main knee braces
          const leanToSpanDrop = leftLeanToSpan * Math.tan(leanToPitchRadians);
          
          // Calculate rafter position for lean-to
          const leanToRafterZ = ((height! - leftLeanToDropHeight) + leanToSpanDrop / 2 - (0.25 * Rafter_web) / Math.cos(leanToPitchRadians)) / 1000;
          
          // Position at the post with adjustment - similar to main left knee
          const leanToPostZ = leanToRafterZ - 0.4 + leanTo_left_knee_adjustment;
          
          // Create the lean-to knee brace mesh
          const leanToLeftKneeBrace = new THREE.Mesh(leanToLeftKneeGeometry, leanToKneeMaterial);
          leanToLeftKneeBrace.position.set(leanToLeftPostX, postY, leanToPostZ);
          leanToLeftKneeBrace.rotation.z = Math.PI / 4;
          group.add(leanToLeftKneeBrace);
        } catch (error) {
          console.error('Error creating lean-to knee brace:', error);
        }
      }
      
      // 1. Left Lean-to Posts
      carportConfig.leanto_post_left_Positions.forEach((position, index) => {
        const geo = carportConfig.leanto_post_left_Geo[index];
        const shsShape = createSHSShape(geo.geo_x, geo.geo_y);
        const extrudeSettings = { depth: geo.geo_z, bevelEnabled: false };
        const postGeometry = new THREE.ExtrudeGeometry(shsShape, extrudeSettings);
        const post = new THREE.Mesh(postGeometry, postMaterial);
        
        // Position the post
        post.position.set(position.x, position.y, position.z);
        group.add(post);
      });
      
      // 2. Left Lean-to Rafters
      carportConfig.leanto_rafter_Positions.forEach((position, index) => {
        // Create a C-section shape for the rafter
        const rafterShape = createCeeSectionShape(
          carportConfig.leanto_rafter_Geo.geo_x,
          carportConfig.leanto_rafter_Geo.geo_y,
          carportConfig.leanto_rafter_Geo.flange_thickness
        );
        
        // Process the geometry - using the same rotation logic as main rafters
        const rafterGeometry = processGeometry(
          new THREE.ExtrudeGeometry(rafterShape, {
            depth: carportConfig.leanto_rafter_Geo.geo_z,
            bevelEnabled: false,
          }),
          Math.PI / 2 + (leanToPitch * Math.PI) / 180, // Same rotation as main rafters but with lean-to pitch
          0,
          -Math.PI / 2
        );
        
        const rafter = new THREE.Mesh(rafterGeometry, rafterMaterial);
        
        // Position the rafter
        rafter.position.set(position.x, position.y, position.z);
        
        // Apply the same rotation logic as main rafters for the last rafter
        if (index === carportConfig.leanto_rafter_Positions.length - 1) {
          rafter.rotation.y = (-2 * (leanToPitch * Math.PI)) / 180;
          rafter.rotation.z = Math.PI;
        }
        
        group.add(rafter);
      });
      
      // 3. Left Lean-to Left EP
      if (carportConfig.leanto_left_EP_Positions.length > 0) {
        // Create the Cee section shape for lean-to left EP
        const leantoLeftEPShape = createCeeSectionShape(
          carportConfig.leanto_left_EP_Geo.geo_x,
          carportConfig.leanto_left_EP_Geo.geo_y,
          carportConfig.leanto_left_EP_Geo.flange_thickness
        );
        
        // Process the geometry - using the same rotation logic as main left EP
        const leantoLeftEPGeometry = processGeometry(
          new THREE.ExtrudeGeometry(leantoLeftEPShape, {
            depth: carportConfig.leanto_left_EP_Geo.geo_z,
            bevelEnabled: false,
          }),
          Math.PI / 2,
          Math.PI,
          0
        );
        
        // Create and position the lean-to left EP
        carportConfig.leanto_left_EP_Positions.forEach((position) => {
          const leantoLeftEP = new THREE.Mesh(leantoLeftEPGeometry, rafterMaterial);
          leantoLeftEP.position.set(position.x, position.y, position.z);
          group.add(leantoLeftEP);
        });
      }
      
      // 4. Left Lean-to Flat Purlin
      if (carportConfig.leanto_flat_purlin_Positions.length > 0) {
        // Create the Cee section shape for lean-to flat purlin
        const leantoFlatPurlinShape = createCeeSectionShape(
          carportConfig.leanto_flat_purlin_Geo.geo_x, 
          carportConfig.leanto_flat_purlin_Geo.geo_y, 
          0.002
        );
        
        // Process the geometry - using the same rotation logic as main purlin
        const leantoFlatPurlinGeometry = processGeometry(
          new THREE.ExtrudeGeometry(leantoFlatPurlinShape, {
            depth: carportConfig.leanto_flat_purlin_Geo.geo_z,
            bevelEnabled: false,
          }),
          Math.PI / 2,
          (-leanToPitch * Math.PI) / 180, // Use lean-to pitch (2 degrees)
          0
        );
        
        // Create and position the lean-to flat purlins
        carportConfig.leanto_flat_purlin_Positions.forEach((position) => {
          const leantoFlatPurlin = new THREE.Mesh(leantoFlatPurlinGeometry, rafterMaterial);
          leantoFlatPurlin.position.set(position.x, position.y, position.z);
          group.add(leantoFlatPurlin);
        });
      }
      
      // 4. Left Lean-to Footings
      if (carportConfig.leanto_footing_Positions.length > 0) {
        carportConfig.leanto_footing_Positions.forEach((position) => {
          const geometry = new THREE.CylinderGeometry(
            carportConfig.leanto_footing_Geo.radius,
            carportConfig.leanto_footing_Geo.radius,
            carportConfig.leanto_footing_Geo.height,
            32
          );
          const FOOTING_GEO = processGeometry(geometry, Math.PI / 2, 0, 0);
          const material = createViewModeMaterial(footingMaterial, currentViewMode, ElementType.FOOTING);
          const leantoFooting = new THREE.Mesh(FOOTING_GEO, material);
          leantoFooting.position.set(position.x, position.y, position.z);
          group.add(leantoFooting);
        });
      }
      
      // 5. Left Lean-to Slab
      // Only create and add slab if showSlab is true
      if (sceneSettings.showSlab) {
        const slabGeometry = new THREE.BoxGeometry(
          leftLeanToSpan / 1000,
          carportConfig.leanto_slab_Geo.length,
          carportConfig.leanto_slab_Geo.thickness
        );
        
        const slab = new THREE.Mesh(slabGeometry, slabMaterial);
        slab.position.set(
          carportConfig.leanto_slab_Position.x,
          carportConfig.leanto_slab_Position.y,
          carportConfig.leanto_slab_Position.z
        );
        
        group.add(slab);
      }
      
      // 4. Left Lean-to Roof
      if (carportConfig.leanto_roof_Positions.length > 0) {
        const noPlates = carportConfig.leanto_roof_Geo.length;
        
        // Create MonoClad section shapes for the roof sheets
        const leantoMonoPlateLine = createMonoClad2DSection(
          carportConfig.leanto_roof_Geo[0].height,
          carportConfig.leanto_roof_Geo[0].thickness,
          carportConfig.leanto_roof_Geo[0].singlelength
        );
        
        const leantoLastMonoPlateLine = noPlates > 1 ? createMonoClad2DSection(
          carportConfig.leanto_roof_Geo[noPlates - 1].height,
          carportConfig.leanto_roof_Geo[noPlates - 1].thickness,
          carportConfig.leanto_roof_Geo[noPlates - 1].singlelength
        ) : leantoMonoPlateLine;
        
        // Create and position each roof sheet
        carportConfig.leanto_roof_Positions.forEach((position, index) => {
          const geometry = processGeometry(
            new THREE.ExtrudeGeometry(index < noPlates - 1 ? leantoMonoPlateLine : leantoLastMonoPlateLine, {
              depth: carportConfig.leanto_roof_Geo[index].length,
              bevelEnabled: false,
            }),
            Math.PI / 2 - leanToPitch * Math.PI / 180, // Apply lean-to pitch (2 degrees)
            0,
            Math.PI / 2
          );
          
          // Create material with view mode applied for lean-to roof
          const leanToRoofMaterialWithViewMode = createViewModeMaterial(roofMaterial, viewMode, ElementType.LEAN_TO_ROOF);
          const leantoRoof = new THREE.Mesh(geometry, leanToRoofMaterialWithViewMode);
          leantoRoof.position.set(position.x, position.y, position.z);
          group.add(leantoRoof);
        });
      }
    } catch (error) {
      console.error('Error creating lean-to components:', error);
    }
    
    scene.add(group);
    return () => disposeAll(group);
  }, [
    scene,
    sceneSettings,
    sceneSettings.showSlab, // Explicitly add showSlab to dependencies
    leftLeanTo,
    leftLeanToSpan,
    rightLeanTo,
    rightLeanToSpan,
    span,
    length,
    height,
    overhang,
    carportConfig,
    roofMaterial,
    rafterMaterial,
    slabMaterial,
    footingMaterial,
    postMaterial,
    post_size,
    leanToPitch,
    carportConfig.leanto_post_left_Positions, 
    carportConfig.leanto_post_left_Geo,
    carportConfig.leanto_rafter_Positions, 
    carportConfig.leanto_rafter_Geo,
    carportConfig.leanto_left_EP_Positions,
    carportConfig.leanto_left_EP_Geo,
    carportConfig.leanto_flat_purlin_Positions,
    carportConfig.leanto_flat_purlin_Geo,
    carportConfig.leanto_footing_Positions,
    carportConfig.leanto_footing_Geo,
    carportConfig.leanto_roof_Positions,
    carportConfig.leanto_roof_Geo,
    carportConfig.leanto_slab_Geo, 
    carportConfig.leanto_slab_Position,
    viewMode // Add viewMode to dependency array to ensure it updates when view mode changes
  ]);
  
  // Right Lean-to Components
  useEffect(() => {
    // Only create right lean-to if it's enabled AND roof type is not skillion variants
    if (!rightLeanTo ||  roofType === 'Skillion Attached') return;
    
    const group = new THREE.Group();
    
    // Calculate the adjusted height for right lean-to (main height minus drop height)
    // Calculate the pitch-related height increase (same as span_drop in Carportconfig.ts)
    const pitchHeightIncrease = span! * Math.tan((pitch! * Math.PI) / 180);
    const rightLeanToHeight = (height! + pitchHeightIncrease - rightLeanToDropHeight) / 1000;
    const leanToPitchRadians = (leanToPitch * Math.PI) / 180;
    
    try {
      // Right Lean-to Barge Cap Flashing
      // Barge cap parameters
      const plateWidth = 0.17; // 170mm width for each plate
      const bargeCapThickness = 0.001; // 1mm thickness
      const bargeCapLength = length! / 1000; // Same length as the carport
      
      // Create the barge cap shape based on the lean-to pitch
      const rightLeanToBargeCapShape = createRightBargeCapFlashing2DShape(plateWidth, bargeCapThickness, leanToPitch);
      
      // Process the geometry
      const rightLeanToBargeCapGeometry = processGeometry(
        new THREE.ExtrudeGeometry(rightLeanToBargeCapShape, {
          depth: bargeCapLength,
          bevelEnabled: false,
        }),
        Math.PI,
        Math.PI,
        -Math.PI / 2
      );
      
      // Calculate the position for the right lean-to barge cap
      const x = (overhang! + span! + rightLeanToSpan) / 1000 - 0.05; // Right edge of lean-to
      const y = length! / 2 / 1000; // Center position along the length
      const z = rightLeanToHeight - 0.09;
      
      // Create material with view mode applied for right lean-to barge cap (using capMaterial for trim color control)
      const rightLeanToBargeMaterialWithViewMode = createViewModeMaterial(capMaterial, viewMode, ElementType.BARGE);
      // Create the mesh and add to the scene
      const rightLeanToBargeCap = new THREE.Mesh(rightLeanToBargeCapGeometry, rightLeanToBargeMaterialWithViewMode);
      rightLeanToBargeCap.position.set(x, y, z);
      
      // Rotate the barge cap to align with the lean-to roof
      rightLeanToBargeCap.rotation.x = Math.PI / 2;
      
      group.add(rightLeanToBargeCap);
      
      // Right Lean-to Knee Braces
      // Use zinc finish material with texture for right lean-to knee braces
      const rightLeanToKneeMaterial = zincFinishMaterial.clone();
      // Ensure the texture is properly applied
      rightLeanToKneeMaterial.needsUpdate = true;
      rightLeanToKneeMaterial.transparent = false; // Full opacity for better texture visibility
      rightLeanToKneeMaterial.opacity = 1.0;
      
      // Use fixed values for knee braces - same as main knee
      const webSize = .05; // 50mm
      const flangeSize = 0.102; // 102mm
      
      // Create a C-section shape for the knee brace
      const rightLeanToKneeShape = createCeeSectionShape(webSize, flangeSize, 0.002);
      
      // Variables for knee geometry
      let rightLeanToKneeGeometry;
      let rightLeanToKneeFactor;
      let rightLeanToKneeOffset;
      
      // Set knee factors based on right lean-to span - similar to main knee logic
      if (rightLeanToSpan < 2000) {
        rightLeanToKneeFactor = 0.15;
        rightLeanToKneeOffset = 175;
      } else if (rightLeanToSpan > 3500) {
        rightLeanToKneeFactor = 0.1;
        rightLeanToKneeOffset = 150;
      } else {
        rightLeanToKneeFactor = 0.15;
        rightLeanToKneeOffset = 175;
      }
      
      try {
        // Create and process the extruded geometry
        const rightLeanToExtrudedGeometry = new THREE.ExtrudeGeometry(rightLeanToKneeShape, {
          depth: 0.6, // Same as main knee
          bevelEnabled: false
        });
        
        // Process the geometry for right lean-to knee orientation - similar to main right knee
        rightLeanToKneeGeometry = processGeometry(
          rightLeanToExtrudedGeometry.clone(),
          -Math.PI/4,  // Rotation around X
          Math.PI,     // Rotation around Y
          -Math.PI/4   // Rotation around Z (mirrored)
        );
      } catch (error) {
        console.error('Error creating right lean-to C-section geometry:', error);
        // Fallback to box geometry if there's an error
        rightLeanToKneeGeometry = new THREE.BoxGeometry(0.6, 0.05, 0.05);
      }
      
      // Calculate right lean-to knee adjustment based on span - similar to main knee adjustment
      let rightLeanTo_knee_adjustment = 0;
      if (rightLeanToSpan < 2000) {
        rightLeanTo_knee_adjustment = -0.05;
      } else if (rightLeanToSpan > 3500) {
        rightLeanTo_knee_adjustment = -0.08;
      } else {
        rightLeanTo_knee_adjustment = -0.05;
      }
      
      // For each bay, create right lean-to knee braces
      for (let i = 0; i <= Math.max(1, Math.floor(length! / 4000)); i++) {
        try {
          // Calculate the positions
          // Position at right lean-to post - similar to main right post calculation
          const rightLeanToPostX = (overhang! + span! + rightLeanToSpan - post_size - 250) / 1000;
          
          const postY = i === Math.floor(length! / 4000)
            ? (length! - post_size) / 1000
            : (post_size + (i * length!) / Math.max(1, Math.floor(length! / 4000))) / 1000;
          
          // Calculate dynamic height using similar formula as main knee braces
          const rightLeanToSpanDrop = rightLeanToSpan * Math.tan(leanToPitchRadians);
          
          // Calculate rafter position for right lean-to
          const rightLeanToRafterZ = ((height! + pitchHeightIncrease - rightLeanToDropHeight) + rightLeanToSpanDrop / 2 - (0.25 * Rafter_web) / Math.cos(leanToPitchRadians)) / 1000;
          
          // Position at the post with adjustment - similar to main right knee
          const rightLeanToPostZ = rightLeanToRafterZ - 0.2 + rightLeanTo_knee_adjustment;
          
          // Create the right lean-to knee brace mesh
          const rightLeanToKneeBrace = new THREE.Mesh(rightLeanToKneeGeometry, rightLeanToKneeMaterial);
          rightLeanToKneeBrace.position.set(rightLeanToPostX, postY, rightLeanToPostZ);
          rightLeanToKneeBrace.rotation.z = -Math.PI / 4; // Negative for right side
          group.add(rightLeanToKneeBrace);
        } catch (error) {
          console.error('Error creating right lean-to knee brace:', error);
        }
      }
      
      // 1. Right Lean-to Posts
      carportConfig.leanto_post_right_Positions.forEach((position, index) => {
        const geo = carportConfig.leanto_post_right_Geo[index];
        const shsShape = createSHSShape(geo.geo_x, geo.geo_y);
        const extrudeSettings = { depth: geo.geo_z, bevelEnabled: false };
        const postGeometry = new THREE.ExtrudeGeometry(shsShape, extrudeSettings);
        const post = new THREE.Mesh(postGeometry, postMaterial);
        
        // Position the post
        post.position.set(position.x, position.y, position.z);
        group.add(post);
      });
      
      // 2. Right Lean-to Rafters
      carportConfig.leanto_rafter_right_Positions.forEach((position, index) => {
        // Create a C-section shape for the rafter
        const rafterShape = createCeeSectionShape(
          carportConfig.leanto_rafter_right_Geo.geo_x,
          carportConfig.leanto_rafter_right_Geo.geo_y,
          carportConfig.leanto_rafter_right_Geo.flange_thickness
        );
        
        // Process the geometry - using the same rotation logic as main rafters but mirrored for right side
        const rafterGeometry = processGeometry(
          new THREE.ExtrudeGeometry(rafterShape, {
            depth: carportConfig.leanto_rafter_right_Geo.geo_z,
            bevelEnabled: false,
          }),
          Math.PI / 2 + (leanToPitch * Math.PI) / 180, // Same rotation as main rafters but with lean-to pitch
          0,
          Math.PI / 2 // Positive for right side
        );
        
        const rafter = new THREE.Mesh(rafterGeometry, rafterMaterial);
        
        // Position the rafter
        rafter.position.set(position.x, position.y, position.z);
        
        // Apply the same rotation logic as main rafters for the last rafter
        if (index === carportConfig.leanto_rafter_right_Positions.length - 1) {
          rafter.rotation.y = 0; // Positive for right side - consistent with other rafters
          rafter.rotation.z = 0; // Different for right side
        }
        
        group.add(rafter);
      });
      
      // 3. Right Lean-to Right EP
      if (carportConfig.leanto_right_EP_Positions.length > 0) {
        // Create the Cee section shape for right lean-to right EP
        const leantoRightEPShape = createCeeSectionShape(
          carportConfig.leanto_right_EP_Geo.geo_x,
          carportConfig.leanto_right_EP_Geo.geo_y,
          carportConfig.leanto_right_EP_Geo.flange_thickness
        );
        
        // Process the geometry - using the same rotation logic as main right EP
        const leantoRightEPGeometry = processGeometry(
          new THREE.ExtrudeGeometry(leantoRightEPShape, {
            depth: carportConfig.leanto_right_EP_Geo.geo_z,
            bevelEnabled: false,
          }),
          Math.PI / 2,
          0, // Different for right side
          0
        );
        
        // Create and position the right lean-to right EP
        carportConfig.leanto_right_EP_Positions.forEach((position) => {
          const leantoRightEP = new THREE.Mesh(leantoRightEPGeometry, rafterMaterial);
          leantoRightEP.position.set(position.x, position.y, position.z);
          group.add(leantoRightEP);
        });
      }
      
      // 4. Right Lean-to Flat Purlin
      if (carportConfig.leanto_right_flat_purlin_Positions.length > 0) {
        // Create the Cee section shape for right lean-to flat purlin
        const leantoRightFlatPurlinShape = createCeeSectionShape(
          carportConfig.leanto_right_flat_purlin_Geo.geo_x, 
          carportConfig.leanto_right_flat_purlin_Geo.geo_y, 
          0.002
        );
        
        // Process the geometry - using the same rotation logic as main purlin but mirrored for right side
        const leantoRightFlatPurlinGeometry = processGeometry(
          new THREE.ExtrudeGeometry(leantoRightFlatPurlinShape, {
            depth: carportConfig.leanto_right_flat_purlin_Geo.geo_z,
            bevelEnabled: false,
          }),
          Math.PI / 2,
          (leanToPitch * Math.PI) / 180, // Positive for right side
          0
        );
        
        // Create and position the right lean-to flat purlins
        carportConfig.leanto_right_flat_purlin_Positions.forEach((position) => {
          const leantoRightFlatPurlin = new THREE.Mesh(leantoRightFlatPurlinGeometry, rafterMaterial);
          leantoRightFlatPurlin.position.set(position.x, position.y, position.z);
          group.add(leantoRightFlatPurlin);
        });
      }
      
      // 5. Right Lean-to Footings
      if (carportConfig.leanto_post_right_Positions.length > 0) {
        // Use the right lean-to footing geometry
        carportConfig.leanto_post_right_Positions.forEach((position) => {
          const geometry = new THREE.CylinderGeometry(
            carportConfig.leanto_right_footing_Geo.radius,
            carportConfig.leanto_right_footing_Geo.radius,
            carportConfig.leanto_right_footing_Geo.height,
            32
          );
          const FOOTING_GEO = processGeometry(geometry, Math.PI / 2, 0, 0);
          const material = createViewModeMaterial(footingMaterial, currentViewMode, ElementType.FOOTING);
          const leantoRightFooting = new THREE.Mesh(FOOTING_GEO, material);
          leantoRightFooting.position.set(
            position.x + post_size / 2 / 1000,
            position.y + post_size / 2 / 1000,
            position.z - carportConfig.leanto_right_footing_Geo.height / 2
          );
          group.add(leantoRightFooting);
        });
      }
      
      // 6. Right Lean-to Slab
      // Only create and add slab if showSlab is true
      if (sceneSettings.showSlab) {
        const rightSlabGeometry = new THREE.BoxGeometry(
          rightLeanToSpan / 1000,
          carportConfig.leanto_right_slab_Geo.length,
          carportConfig.leanto_right_slab_Geo.thickness
        );
        
        const rightSlab = new THREE.Mesh(rightSlabGeometry, slabMaterial);
        rightSlab.position.set(
          carportConfig.leanto_right_slab_Position.x,
          carportConfig.leanto_right_slab_Position.y,
          carportConfig.leanto_right_slab_Position.z
        );
        
        group.add(rightSlab);
      }
      
      // 7. Right Lean-to Roof
      if (carportConfig.leanto_right_roof_Positions && carportConfig.leanto_right_roof_Positions.length > 0) {
        const noPlates = carportConfig.leanto_right_roof_Geo.length;
        
        // Create MonoClad section shapes for the roof sheets
        const leantoRightMonoPlateLine = createMonoClad2DSection(
          carportConfig.leanto_right_roof_Geo[0].height,
          carportConfig.leanto_right_roof_Geo[0].thickness,
          carportConfig.leanto_right_roof_Geo[0].singlelength
        );
        
        const leantoRightLastMonoPlateLine = noPlates > 1 ? createMonoClad2DSection(
          carportConfig.leanto_right_roof_Geo[noPlates - 1].height,
          carportConfig.leanto_right_roof_Geo[noPlates - 1].thickness,
          carportConfig.leanto_right_roof_Geo[noPlates - 1].singlelength
        ) : leantoRightMonoPlateLine;
        
        // Create and position each roof sheet for right lean-to
        carportConfig.leanto_right_roof_Positions.forEach((position, index) => {
          const geometry = processGeometry(
            new THREE.ExtrudeGeometry(index < noPlates - 1 ? leantoRightMonoPlateLine : leantoRightLastMonoPlateLine, {
              depth: carportConfig.leanto_right_roof_Geo[index].length,
              bevelEnabled: false,
            }),
            Math.PI / 2 , // Apply lean-to pitch (2 degrees) - same as left lean-to
            0,
            Math.PI / 2
          );
          
          // Create material with view mode applied for right lean-to roof
          const leanToRightRoofMaterialWithViewMode = createViewModeMaterial(roofMaterial, viewMode, ElementType.LEAN_TO_ROOF);
          const leantoRightRoof = new THREE.Mesh(geometry, leanToRightRoofMaterialWithViewMode);
          leantoRightRoof.position.set(position.x, position.y, position.z);
          if (position.rot_y !== undefined) {
            leantoRightRoof.rotation.y = position.rot_y;
          }
          group.add(leantoRightRoof);
        });
      }
    } catch (error) {
      console.error('Error creating right lean-to components:', error);
    }
    
    scene.add(group);
    return () => disposeAll(group);
  }, [
    scene, 
    sceneSettings,
    sceneSettings.showSlab, // Explicitly add showSlab to dependencies
    rightLeanTo, 
    rightLeanToSpan, 
    rightLeanToDropHeight,
    height,
    length,
    span,
    leanToPitch,
    roofType,
    carportConfig.leanto_post_right_Positions, 
    carportConfig.leanto_post_right_Geo,
    carportConfig.leanto_rafter_right_Positions, 
    carportConfig.leanto_rafter_right_Geo,
    carportConfig.leanto_right_EP_Positions,
    carportConfig.leanto_right_EP_Geo,
    carportConfig.leanto_right_flat_purlin_Positions,
    carportConfig.leanto_right_flat_purlin_Geo,
    carportConfig.leanto_right_footing_Geo,
    carportConfig.leanto_right_roof_Positions,
    carportConfig.leanto_right_roof_Geo,
    carportConfig.leanto_right_slab_Geo, 
    carportConfig.leanto_right_slab_Position,
    carportConfig.main_right_drop_Positions,
    carportConfig.main_right_drop_Geo,
    leftLeanToSpan,
    overhang,
    viewMode // Add viewMode to dependency array to ensure it updates when view mode changes
  ]);
  
  // Render Left Lean-to Wall Girts
  useEffect(() => {
    // Skip if no girts to render or lean-to is disabled
    if (!leftLeanTo || !girtConfig.leftLeanToGirt?.geometries || girtConfig.leftLeanToGirt.geometries.length === 0) {
      return;
    }
    
    
    const group = new THREE.Group();
    
    // Create a material for all girts in this wall
    const girtMaterial = createViewModeMaterial(rafterMaterial, currentViewMode, ElementType.KNEE_BRACE);
    
    // Render each girt
    girtConfig.leftLeanToGirt.geometries.forEach((geo, index) => {
      // Create Z section shape for girt using standard Z-section helper function
      const girtShape = createStandardZeeSectionShape(
        Girt_web/1000,       // Web width
        Girt_flangeE/1000,   // Flange E dimension
        Girt_flangeF/1000,   // Flange F dimension
        Girt_lip/1000,       // Lip dimension
        Girt_thickness/1000  // Material thickness
      );
      
      // Process the geometry with proper orientation
      const girtGeometry = processGeometry(
        new THREE.ExtrudeGeometry(girtShape, {
          depth: geo.geo_z,
          bevelEnabled: false
        }),
        0,           // No rotation around X
        -Math.PI/2,  // Rotate 90 degrees around Y
        Math.PI      // Rotate 180 degrees around Z
      );
      
      // Create mesh and position it
      const position = girtConfig.leftLeanToGirt.positions[index];
      const girt = new THREE.Mesh(girtGeometry, girtMaterial);
      girt.position.set(position.x, position.y, position.z);
      
      // Apply rotation if specified
      if (position.rotation) {
        girt.rotation.set(position.rotation.x, position.rotation.y, position.rotation.z);
      }
      
      group.add(girt);
    });
    
    scene.add(group);
    
    return () => disposeAll(group);
  }, [girtConfig.leftLeanToGirt, currentViewMode, scene, leftLeanTo]);
  
  // Render Left Lean-to Divider Wall Girts
  useEffect(() => {
    // Skip if no girts to render or lean-to is disabled
    if (!leftLeanTo || !girtConfig.leftLeanToDividerGirt?.geometries || girtConfig.leftLeanToDividerGirt.geometries.length === 0) {
      return;
    }
    
    
    const group = new THREE.Group();
    
    // Create a material for all girts in this wall
    const girtMaterial = createViewModeMaterial(rafterMaterial, currentViewMode, ElementType.KNEE_BRACE);
    
    // Render each girt
    girtConfig.leftLeanToDividerGirt.geometries.forEach((geo, index) => {
      // Create Z section shape for girt
      const girtShape = createStandardZeeSectionShape(
        Girt_web/1000,       // Web width
        Girt_flangeE/1000,   // Flange E dimension
        Girt_flangeF/1000,   // Flange F dimension
        Girt_lip/1000,       // Lip dimension
        Girt_thickness/1000  // Material thickness
      );
      
      // Process the geometry with proper orientation
      const girtGeometry = processGeometry(
        new THREE.ExtrudeGeometry(girtShape, {
          depth: geo.geo_z,
          bevelEnabled: false
        }),
        Math.PI/2,  // Rotate 90 degrees around X
        0,          // No rotation around Y
        Math.PI/2   // Rotate 90 degrees around Z
      );
      
      // Create mesh and position it
      const position = girtConfig.leftLeanToDividerGirt.positions[index];
      const girt = new THREE.Mesh(girtGeometry, girtMaterial);
      girt.position.set(position.x, position.y, position.z);
      
      // Apply rotation if specified
      if (position.rotation) {
        girt.rotation.set(position.rotation.x, position.rotation.y, position.rotation.z);
      }
      
      group.add(girt);
    });
    
    scene.add(group);
    
    return () => disposeAll(group);
  }, [girtConfig.leftLeanToDividerGirt, currentViewMode, scene, leftLeanTo]);
  
  // Render Left Lean-to Front Wall Girts
  useEffect(() => {
    // Skip if no girts to render or lean-to is disabled
    if (!leftLeanTo || !girtConfig.leftLeanToFrontGirt?.geometries || girtConfig.leftLeanToFrontGirt.geometries.length === 0) {
      return;
    }
    
    
    const group = new THREE.Group();
    
    // Create a material for all girts in this wall
    const girtMaterial = createViewModeMaterial(rafterMaterial, currentViewMode, ElementType.KNEE_BRACE);
    
    // Render each girt
    girtConfig.leftLeanToFrontGirt.geometries.forEach((geo, index) => {
      // Create Z section shape for girt
      const girtShape = createStandardZeeSectionShape(
        Girt_web/1000,       // Web width
        Girt_flangeE/1000,   // Flange E dimension
        Girt_flangeF/1000,   // Flange F dimension
        Girt_lip/1000,       // Lip dimension
        Girt_thickness/1000  // Material thickness
      );
      
      // Process the geometry with proper orientation
      const girtGeometry = processGeometry(
        new THREE.ExtrudeGeometry(girtShape, {
          depth: geo.geo_z,
          bevelEnabled: false
        }),
        Math.PI/2,  // Rotate 90 degrees around X
        0,          // No rotation around Y
        Math.PI/2   // Rotate 90 degrees around Z
      );
      
      // Create mesh and position it
      const position = girtConfig.leftLeanToFrontGirt.positions[index];
      const girt = new THREE.Mesh(girtGeometry, girtMaterial);
      girt.position.set(position.x, position.y, position.z);
      
      // Apply rotation if specified
      if (position.rotation) {
        girt.rotation.set(position.rotation.x, position.rotation.y, position.rotation.z);
      }
      
      group.add(girt);
    });
    
    scene.add(group);
    
    return () => disposeAll(group);
  }, [girtConfig.leftLeanToFrontGirt, currentViewMode, scene, leftLeanTo]);
  
  // Render Left Lean-to Back Wall Girts
  useEffect(() => {
    // Skip if no girts to render or lean-to is disabled
    if (!leftLeanTo || !girtConfig.leftLeanToBackGirt?.geometries || girtConfig.leftLeanToBackGirt.geometries.length === 0) {
      return;
    }
    
    
    const group = new THREE.Group();
    
    // Create a material for all girts in this wall
    const girtMaterial = createViewModeMaterial(rafterMaterial, currentViewMode, ElementType.KNEE_BRACE);
    
    // Render each girt
    girtConfig.leftLeanToBackGirt.geometries.forEach((geo, index) => {
      // Create Z section shape for girt
      const girtShape = createStandardZeeSectionShape(
        Girt_web/1000,       // Web width
        Girt_flangeE/1000,   // Flange E dimension
        Girt_flangeF/1000,   // Flange F dimension
        Girt_lip/1000,       // Lip dimension
        Girt_thickness/1000  // Material thickness
      );
      
      // Process the geometry with proper orientation
      const girtGeometry = processGeometry(
        new THREE.ExtrudeGeometry(girtShape, {
          depth: geo.geo_z,
          bevelEnabled: false
        }),
        Math.PI/2,  // Rotate 90 degrees around X
        0,          // No rotation around Y
        Math.PI/2   // Rotate 90 degrees around Z
      );
      
      // Create mesh and position it
      const position = girtConfig.leftLeanToBackGirt.positions[index];
      const girt = new THREE.Mesh(girtGeometry, girtMaterial);
      girt.position.set(position.x, position.y, position.z);
      
      // Apply rotation if specified
      if (position.rotation) {
        girt.rotation.set(position.rotation.x, position.rotation.y, position.rotation.z);
      }
      
      group.add(girt);
    });
    
    scene.add(group);
    
    return () => disposeAll(group);
  }, [girtConfig.leftLeanToBackGirt, currentViewMode, scene, leftLeanTo]);
  
  // Render Right Lean-to Wall Girts
  useEffect(() => {
    // Skip if no girts to render or lean-to is disabled
    if (!rightLeanTo || !girtConfig.rightLeanToGirt?.geometries || girtConfig.rightLeanToGirt.geometries.length === 0) {
      return;
    }
    
    
    const group = new THREE.Group();
    
    // Create a material for all girts in this wall
    const girtMaterial = createViewModeMaterial(rafterMaterial, currentViewMode, ElementType.KNEE_BRACE);
    
    // Render each girt
    girtConfig.rightLeanToGirt.geometries.forEach((geo, index) => {
      // Create Z section shape for girt using standard Z-section helper function
      const girtShape = createStandardZeeSectionShape(
        Girt_web/1000,       // Web width
        Girt_flangeE/1000,   // Flange E dimension
        Girt_flangeF/1000,   // Flange F dimension
        Girt_lip/1000,       // Lip dimension
        Girt_thickness/1000  // Material thickness
      );
      
      // Process the geometry with proper orientation
      const girtGeometry = processGeometry(
        new THREE.ExtrudeGeometry(girtShape, {
          depth: geo.geo_z,
          bevelEnabled: false
        }),
        0,          // No rotation around X
        Math.PI/2,  // Rotate 90 degrees around Y
        0           // No rotation around Z
      );
      
      // Create mesh and position it
      const position = girtConfig.rightLeanToGirt.positions[index];
      const girt = new THREE.Mesh(girtGeometry, girtMaterial);
      girt.position.set(position.x, position.y, position.z);
      
      // Apply rotation if specified
      if (position.rotation) {
        girt.rotation.set(position.rotation.x, position.rotation.y, position.rotation.z);
      }
      
      group.add(girt);
    });
    
    scene.add(group);
    
    return () => disposeAll(group);
  }, [girtConfig.rightLeanToGirt, currentViewMode, scene, rightLeanTo]);
  
  // Render Right Lean-to Divider Wall Girts
  useEffect(() => {
    // Skip if no girts to render or lean-to is disabled
    if (!rightLeanTo || !girtConfig.rightLeanToDividerGirt?.geometries || girtConfig.rightLeanToDividerGirt.geometries.length === 0) {
      return;
    }
    
    
    const group = new THREE.Group();
    
    // Create a material for all girts in this wall
    const girtMaterial = createViewModeMaterial(rafterMaterial, currentViewMode, ElementType.KNEE_BRACE);
    
    // Render each girt
    girtConfig.rightLeanToDividerGirt.geometries.forEach((geo, index) => {
      // Create Z section shape for girt
      const girtShape = createStandardZeeSectionShape(
        Girt_web/1000,       // Web width
        Girt_flangeE/1000,   // Flange E dimension
        Girt_flangeF/1000,   // Flange F dimension
        Girt_lip/1000,       // Lip dimension
        Girt_thickness/1000  // Material thickness
      );
      
      // Process the geometry with proper orientation
      const girtGeometry = processGeometry(
        new THREE.ExtrudeGeometry(girtShape, {
          depth: geo.geo_z,
          bevelEnabled: false
        }),
        Math.PI/2,  // Rotate 90 degrees around X
        0,          // No rotation around Y
        Math.PI/2   // Rotate 90 degrees around Z
      );
      
      // Create mesh and position it
      const position = girtConfig.rightLeanToDividerGirt.positions[index];
      const girt = new THREE.Mesh(girtGeometry, girtMaterial);
      girt.position.set(position.x, position.y, position.z);
      
      // Apply rotation if specified
      if (position.rotation) {
        girt.rotation.set(position.rotation.x, position.rotation.y, position.rotation.z);
      }
      
      group.add(girt);
    });
    
    scene.add(group);
    
    return () => disposeAll(group);
  }, [girtConfig.rightLeanToDividerGirt, currentViewMode, scene, rightLeanTo]);
  
  // Render Right Lean-to Front Wall Girts
  useEffect(() => {
    // Skip if no girts to render or lean-to is disabled
    if (!rightLeanTo || !girtConfig.rightLeanToFrontGirt?.geometries || girtConfig.rightLeanToFrontGirt.geometries.length === 0) {
      return;
    }
    
    
    const group = new THREE.Group();
    
    // Create a material for all girts in this wall
    const girtMaterial = createViewModeMaterial(rafterMaterial, currentViewMode, ElementType.KNEE_BRACE);
    
    // Render each girt
    girtConfig.rightLeanToFrontGirt.geometries.forEach((geo, index) => {
      // Create Z section shape for girt
      const girtShape = createStandardZeeSectionShape(
        Girt_web/1000,       // Web width
        Girt_flangeE/1000,   // Flange E dimension
        Girt_flangeF/1000,   // Flange F dimension
        Girt_lip/1000,       // Lip dimension
        Girt_thickness/1000  // Material thickness
      );
      
      // Process the geometry with proper orientation
      const girtGeometry = processGeometry(
        new THREE.ExtrudeGeometry(girtShape, {
          depth: geo.geo_z,
          bevelEnabled: false
        }),
        Math.PI/2,  // Rotate 90 degrees around X
        0,          // No rotation around Y
        Math.PI/2   // Rotate 90 degrees around Z
      );
      
      // Create mesh and position it
      const position = girtConfig.rightLeanToFrontGirt.positions[index];
      const girt = new THREE.Mesh(girtGeometry, girtMaterial);
      girt.position.set(position.x, position.y, position.z);
      
      // Apply rotation if specified
      if (position.rotation) {
        girt.rotation.set(position.rotation.x, position.rotation.y, position.rotation.z);
      }
      
      group.add(girt);
    });
    
    scene.add(group);
    
    return () => disposeAll(group);
  }, [girtConfig.rightLeanToFrontGirt, currentViewMode, scene, rightLeanTo]);
  
  // Render Right Lean-to Back Wall Girts
  useEffect(() => {
    // Skip if no girts to render or lean-to is disabled
    if (!rightLeanTo || !girtConfig.rightLeanToBackGirt?.geometries || girtConfig.rightLeanToBackGirt.geometries.length === 0) {
      return;
    }
    
    
    const group = new THREE.Group();
    
    // Create a material for all girts in this wall
    const girtMaterial = createViewModeMaterial(rafterMaterial, currentViewMode, ElementType.KNEE_BRACE);
    
    // Render each girt
    girtConfig.rightLeanToBackGirt.geometries.forEach((geo, index) => {
      // Create Z section shape for girt
      const girtShape = createStandardZeeSectionShape(
        Girt_web/1000,       // Web width
        Girt_flangeE/1000,   // Flange E dimension
        Girt_flangeF/1000,   // Flange F dimension
        Girt_lip/1000,       // Lip dimension
        Girt_thickness/1000  // Material thickness
      );
      
      // Process the geometry with proper orientation
      const girtGeometry = processGeometry(
        new THREE.ExtrudeGeometry(girtShape, {
          depth: geo.geo_z,
          bevelEnabled: false
        }),
        Math.PI/2,  // Rotate 90 degrees around X
        0,          // No rotation around Y
        Math.PI/2   // Rotate 90 degrees around Z
      );
      
      // Create mesh and position it
      const position = girtConfig.rightLeanToBackGirt.positions[index];
      const girt = new THREE.Mesh(girtGeometry, girtMaterial);
      girt.position.set(position.x, position.y, position.z);
      
      // Apply rotation if specified
      if (position.rotation) {
        girt.rotation.set(position.rotation.x, position.rotation.y, position.rotation.z);
      }
      
      group.add(girt);
    });
    
    scene.add(group);
    
    return () => disposeAll(group);
  }, [girtConfig.rightLeanToBackGirt, currentViewMode, scene, rightLeanTo]);
  
  // Render Left Lean-to Wall Sheets
  useEffect(() => {
    
    // Skip if no wall sheets to render or lean-to is disabled
    if (!leftLeanTo || !wallSheetConfig.leftLeanToWallSheet?.geometries || wallSheetConfig.leftLeanToWallSheet.geometries.length === 0) {
      return;
    }
    
    const group = new THREE.Group();
    
    // Create materials with view mode applied
    const wallMaterial = createViewModeMaterial(roofMaterial, currentViewMode, ElementType.MAIN_DROP);
    
    // Render each wall sheet
    wallSheetConfig.leftLeanToWallSheet.geometries.forEach((geo, index) => {
      // Create a corrugated wall sheet
      let wallGeometry;
      
      // Create corrugated section for the wall sheet
      const wallShape = createCorro2DSection(
        0.001, // Height in mm
        geo.thickness, // Thickness in mm
        geo.geo_z // Length in mm
      );
      
      // Process the geometry with proper orientation
      wallGeometry = processGeometry(
        new THREE.ExtrudeGeometry(wallShape, {
          depth: geo.geo_y,
          bevelEnabled: false
        }),
        0,  // No rotation around X
        0,  // No rotation around Y
        0   // No rotation around Z
      );
      
      // Create mesh and position it
      const position = wallSheetConfig.leftLeanToWallSheet.positions[index];
      const material = geo.material ? geo.material : wallMaterial;
      const materialWithViewMode = createViewModeMaterial(material, currentViewMode, ElementType.MAIN_DROP);
      
      const wallSheet = new THREE.Mesh(wallGeometry, materialWithViewMode);
      wallSheet.position.set(position.x, position.y, position.z);
      
      // Apply rotation if specified
      if (position.rotation) {
        wallSheet.rotation.set(position.rotation.x, position.rotation.y, position.rotation.z);
      }
      
      group.add(wallSheet);
    });
    
    scene.add(group);
    return () => disposeAll(group);
  }, [wallSheetConfig.leftLeanToWallSheet, currentViewMode, scene, leftLeanTo]);
  
  // Render Left Lean-to Front Wall Sheet
  useEffect(() => {
    
    // Skip if no wall sheets to render or lean-to is disabled
    if (!leftLeanTo || !wallSheetConfig.leftLeanToFrontWallSheet?.geometries || wallSheetConfig.leftLeanToFrontWallSheet.geometries.length === 0) {
      return;
    }
    
    const group = new THREE.Group();
    
    // Create materials with view mode applied
    const wallMaterial = createViewModeMaterial(roofMaterial, currentViewMode, ElementType.MAIN_DROP);
    
    // Render each wall sheet
    wallSheetConfig.leftLeanToFrontWallSheet.geometries.forEach((geo, index) => {
      // Create a corrugated wall sheet
      let wallGeometry;
      
      // Create corrugated section for the wall sheet
      const wallShape = createCorro2DSection(
        0.001, // Height in mm
        geo.thickness, // Thickness in mm
        geo.geo_z // Length in mm
      );
      
      // Process the geometry with proper orientation
      wallGeometry = processGeometry(
        new THREE.ExtrudeGeometry(wallShape, {
          depth: geo.geo_y,
          bevelEnabled: false
        }),
        0,  // No rotation around X
        0,  // No rotation around Y
        0   // No rotation around Z
      );
      
      // Create mesh and position it
      const position = wallSheetConfig.leftLeanToFrontWallSheet.positions[index];
      const material = geo.material ? geo.material : wallMaterial;
      const materialWithViewMode = createViewModeMaterial(material, currentViewMode, ElementType.MAIN_DROP);
      
      const wallSheet = new THREE.Mesh(wallGeometry, materialWithViewMode);
      wallSheet.position.set(position.x, position.y, position.z);
      
      // Apply rotation if specified
      if (position.rotation) {
        wallSheet.rotation.set(position.rotation.x, position.rotation.y, position.rotation.z);
      }
      
      group.add(wallSheet);
    });
    
    scene.add(group);
    return () => disposeAll(group);
  }, [wallSheetConfig.leftLeanToFrontWallSheet, currentViewMode, scene, leftLeanTo]);
  
  // Render Left Lean-to Back Wall Sheet
  useEffect(() => {
    
    // Skip if no wall sheets to render or lean-to is disabled
    if (!leftLeanTo || !wallSheetConfig.leftLeanToBackWallSheet?.geometries || wallSheetConfig.leftLeanToBackWallSheet.geometries.length === 0) {
      return;
    }
    
    const group = new THREE.Group();
    
    // Create materials with view mode applied
    const wallMaterial = createViewModeMaterial(roofMaterial, currentViewMode, ElementType.MAIN_DROP);
    
    // Render each wall sheet
    wallSheetConfig.leftLeanToBackWallSheet.geometries.forEach((geo, index) => {
      // Create a corrugated wall sheet
      let wallGeometry;
      
      // Create corrugated section for the wall sheet
      const wallShape = createCorro2DSection(
        0.001, // Height in mm
        geo.thickness, // Thickness in mm
        geo.geo_z // Length in mm
      );
      
      // Process the geometry with proper orientation
      wallGeometry = processGeometry(
        new THREE.ExtrudeGeometry(wallShape, {
          depth: geo.geo_y,
          bevelEnabled: false
        }),
        0,  // No rotation around X
        0,  // No rotation around Y
        0   // No rotation around Z
      );
      
      // Create mesh and position it
      const position = wallSheetConfig.leftLeanToBackWallSheet.positions[index];
      const material = geo.material ? geo.material : wallMaterial;
      const materialWithViewMode = createViewModeMaterial(material, currentViewMode, ElementType.MAIN_DROP);
      
      const wallSheet = new THREE.Mesh(wallGeometry, materialWithViewMode);
      wallSheet.position.set(position.x, position.y, position.z);
      
      // Apply rotation if specified
      if (position.rotation) {
        wallSheet.rotation.set(position.rotation.x, position.rotation.y, position.rotation.z);
      }
      
      group.add(wallSheet);
    });
    
    scene.add(group);
    return () => disposeAll(group);
  }, [wallSheetConfig.leftLeanToBackWallSheet, currentViewMode, scene, leftLeanTo]);
  
  // Render Left Lean-to Divider Wall Sheets
  useEffect(() => {
    
    // Skip if no wall sheets to render or lean-to is disabled
    if (!leftLeanTo || !wallSheetConfig.leftLeanToDividerWallSheet?.geometries || wallSheetConfig.leftLeanToDividerWallSheet.geometries.length === 0) {
      return;
    }
    
    const group = new THREE.Group();
    
    // Create materials with view mode applied
    const wallMaterial = createViewModeMaterial(roofMaterial, currentViewMode, ElementType.MAIN_DROP);
    
    // Render each wall sheet
    wallSheetConfig.leftLeanToDividerWallSheet.geometries.forEach((geo, index) => {
      // Create a corrugated wall sheet
      let wallGeometry;
      
      // Create corrugated section for the wall sheet
      const wallShape = createCorro2DSection(
        1, // Height in mm
        geo.thickness, // Thickness in mm
        geo.geo_z // Length in mm
      );
      
      // Process the geometry with proper orientation
      wallGeometry = processGeometry(
        new THREE.ExtrudeGeometry(wallShape, {
          depth: geo.geo_y,
          bevelEnabled: false
        }),
        0,  // No rotation around X
        0,  // No rotation around Y
        0   // No rotation around Z
      );
      
      // Create mesh and position it
      const position = wallSheetConfig.leftLeanToDividerWallSheet.positions[index];
      const material = geo.material ? geo.material : wallMaterial;
      const materialWithViewMode = createViewModeMaterial(material, currentViewMode, ElementType.MAIN_DROP);
      
      const wallSheet = new THREE.Mesh(wallGeometry, materialWithViewMode);
      wallSheet.position.set(position.x, position.y, position.z);
      
      // Apply rotation if specified
      if (position.rotation) {
        wallSheet.rotation.set(position.rotation.x, position.rotation.y, position.rotation.z);
      }
      
      group.add(wallSheet);
    });
    
    scene.add(group);
    return () => disposeAll(group);
  }, [wallSheetConfig.leftLeanToDividerWallSheet, currentViewMode, scene, leftLeanTo]);
  
  // Render Right Lean-to Wall Sheets
  useEffect(() => {
    
    // Skip if no wall sheets to render or lean-to is disabled
    if (!rightLeanTo || !wallSheetConfig.rightLeanToWallSheet?.geometries || wallSheetConfig.rightLeanToWallSheet.geometries.length === 0) {
      return;
    }
    
    const group = new THREE.Group();
    
    // Create materials with view mode applied
    const wallMaterial = createViewModeMaterial(roofMaterial, currentViewMode, ElementType.MAIN_DROP);
    
    // Render each wall sheet
    wallSheetConfig.rightLeanToWallSheet.geometries.forEach((geo, index) => {
      // Create a corrugated wall sheet
      let wallGeometry;
      
      // Create corrugated section for the wall sheet
      const wallShape = createCorro2DSection(
        0.001, // Height in mm
        geo.thickness, // Thickness in mm
        geo.geo_z // Length in mm
      );
      
      // Process the geometry with proper orientation
      wallGeometry = processGeometry(
        new THREE.ExtrudeGeometry(wallShape, {
          depth: geo.geo_y,
          bevelEnabled: false
        }),
        0,  // No rotation around X
        0,  // No rotation around Y
        0   // No rotation around Z
      );
      
      // Create mesh and position it
      const position = wallSheetConfig.rightLeanToWallSheet.positions[index];
      const material = geo.material ? geo.material : wallMaterial;
      const materialWithViewMode = createViewModeMaterial(material, currentViewMode, ElementType.MAIN_DROP);
      
      const wallSheet = new THREE.Mesh(wallGeometry, materialWithViewMode);
      wallSheet.position.set(position.x, position.y, position.z);
      
      // Apply rotation if specified
      if (position.rotation) {
        wallSheet.rotation.set(position.rotation.x, position.rotation.y, position.rotation.z);
      }
      
      group.add(wallSheet);
    });
    
    scene.add(group);
    return () => disposeAll(group);
  }, [wallSheetConfig.rightLeanToWallSheet, currentViewMode, scene, rightLeanTo]);
  
  // Render Right Lean-to Front Wall Sheet
  useEffect(() => {
    
    // Skip if no wall sheets to render or lean-to is disabled
    if (!rightLeanTo || !wallSheetConfig.rightLeanToFrontWallSheet?.geometries || wallSheetConfig.rightLeanToFrontWallSheet.geometries.length === 0) {
      return;
    }
    
    const group = new THREE.Group();
    
    // Create materials with view mode applied
    const wallMaterial = createViewModeMaterial(roofMaterial, currentViewMode, ElementType.MAIN_DROP);
    
    // Render each wall sheet
    wallSheetConfig.rightLeanToFrontWallSheet.geometries.forEach((geo, index) => {
      // Create a corrugated wall sheet
      let wallGeometry;
      
      // Create corrugated section for the wall sheet
      const wallShape = createCorro2DSection(
        0.001, // Height in mm
        geo.thickness, // Thickness in mm
        geo.geo_z // Length in mm
      );
      
      // Process the geometry with proper orientation
      wallGeometry = processGeometry(
        new THREE.ExtrudeGeometry(wallShape, {
          depth: geo.geo_y,
          bevelEnabled: false
        }),
        0,  // No rotation around X
        0,  // No rotation around Y
        0   // No rotation around Z
      );
      
      // Create mesh and position it
      const position = wallSheetConfig.rightLeanToFrontWallSheet.positions[index];
      const material = geo.material ? geo.material : wallMaterial;
      const materialWithViewMode = createViewModeMaterial(material, currentViewMode, ElementType.MAIN_DROP);
      
      const wallSheet = new THREE.Mesh(wallGeometry, materialWithViewMode);
      wallSheet.position.set(position.x, position.y, position.z);
      
      // Apply rotation if specified
      if (position.rotation) {
        wallSheet.rotation.set(position.rotation.x, position.rotation.y, position.rotation.z);
      }
      
      group.add(wallSheet);
    });
    
    scene.add(group);
    return () => disposeAll(group);
  }, [wallSheetConfig.rightLeanToFrontWallSheet, currentViewMode, scene, rightLeanTo]);
  
  // Render Right Lean-to Back Wall Sheet
  useEffect(() => {
    
    // Skip if no wall sheets to render or lean-to is disabled
    if (!rightLeanTo || !wallSheetConfig.rightLeanToBackWallSheet?.geometries || wallSheetConfig.rightLeanToBackWallSheet.geometries.length === 0) {
      return;
    }
    
    const group = new THREE.Group();
    
    // Create materials with view mode applied
    const wallMaterial = createViewModeMaterial(roofMaterial, currentViewMode, ElementType.MAIN_DROP);
    
    // Render each wall sheet
    wallSheetConfig.rightLeanToBackWallSheet.geometries.forEach((geo, index) => {
      // Create a corrugated wall sheet
      let wallGeometry;
      
      // Create corrugated section for the wall sheet
      const wallShape = createCorro2DSection(
        0.001, // Height in mm
        geo.thickness, // Thickness in mm
        geo.geo_z // Length in mm
      );
      
      // Process the geometry with proper orientation
      wallGeometry = processGeometry(
        new THREE.ExtrudeGeometry(wallShape, {
          depth: geo.geo_y,
          bevelEnabled: false
        }),
        0,  // No rotation around X
        0,  // No rotation around Y
        0   // No rotation around Z
      );
      
      // Create mesh and position it
      const position = wallSheetConfig.rightLeanToBackWallSheet.positions[index];
      const material = geo.material ? geo.material : wallMaterial;
      const materialWithViewMode = createViewModeMaterial(material, currentViewMode, ElementType.MAIN_DROP);
      
      const wallSheet = new THREE.Mesh(wallGeometry, materialWithViewMode);
      wallSheet.position.set(position.x, position.y, position.z);
      
      // Apply rotation if specified
      if (position.rotation) {
        wallSheet.rotation.set(position.rotation.x, position.rotation.y, position.rotation.z);
      }
      
      group.add(wallSheet);
    });
    
    scene.add(group);
    return () => disposeAll(group);
  }, [wallSheetConfig.rightLeanToBackWallSheet, currentViewMode, scene, rightLeanTo]);
  
  // Render Right Lean-to Divider Wall Sheets
  useEffect(() => {
    
    // Skip if no wall sheets to render or lean-to is disabled
    if (!rightLeanTo || !wallSheetConfig.rightLeanToDividerWallSheet?.geometries || wallSheetConfig.rightLeanToDividerWallSheet.geometries.length === 0) {
      return;
    }
    
    const group = new THREE.Group();
    
    // Create materials with view mode applied
    const wallMaterial = createViewModeMaterial(roofMaterial, currentViewMode, ElementType.MAIN_DROP);
    
    // Render each wall sheet
    wallSheetConfig.rightLeanToDividerWallSheet.geometries.forEach((geo, index) => {
      // Create a corrugated wall sheet
      let wallGeometry;
      
      // Create corrugated section for the wall sheet
      const wallShape = createCorro2DSection(
        1, // Height in mm
        geo.thickness, // Thickness in mm
        geo.geo_z // Length in mm
      );
      
      // Process the geometry with proper orientation
      wallGeometry = processGeometry(
        new THREE.ExtrudeGeometry(wallShape, {
          depth: geo.geo_y,
          bevelEnabled: false
        }),
        0,  // No rotation around X
        0,  // No rotation around Y
        0   // No rotation around Z
      );
      
      // Create mesh and position it
      const position = wallSheetConfig.rightLeanToDividerWallSheet.positions[index];
      const material = geo.material ? geo.material : wallMaterial;
      const materialWithViewMode = createViewModeMaterial(material, currentViewMode, ElementType.MAIN_DROP);
      
      const wallSheet = new THREE.Mesh(wallGeometry, materialWithViewMode);
      wallSheet.position.set(position.x, position.y, position.z);
      
      // Apply rotation if specified
      if (position.rotation) {
        wallSheet.rotation.set(position.rotation.x, position.rotation.y, position.rotation.z);
      }
      
      group.add(wallSheet);
    });
    
    scene.add(group);
    return () => disposeAll(group);
  }, [wallSheetConfig.rightLeanToDividerWallSheet, currentViewMode, scene, rightLeanTo]);
  
  return <primitive object={scene} />;
}

export default Carport;
