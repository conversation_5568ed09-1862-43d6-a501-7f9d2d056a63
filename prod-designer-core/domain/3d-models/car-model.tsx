import React, { useState } from 'react';
import { THREE } from 'expo-three';
import { Clone, useGLTF } from '@react-three/drei/native';
import { ThreeEvent } from '@react-three/fiber/native';
import modelPath from '@/assets/models/car.glb';
import { useSceneContext } from '@/redux/context';

type CarModelProps = {
  visible?: boolean;
};

function Cars({ visible = true }: CarModelProps) {
  const obj = useGLTF(modelPath);

  const { sceneSettings, setSceneSettings } = useSceneContext();

  const [cars, setCars] = useState<THREE.Vector3[]>([]);

  function onPlaneClick(e: ThreeEvent<MouseEvent>) {
    if (sceneSettings.isAddingCar && e.delta < 5) {
      addCars(e.point);
      setSceneSettings((p) => ({ ...p, isAddingCar: false }));
    }
  }

  function addCars(point: THREE.Vector3) {
    setCars((p) => [...p, point]);
  }

  function onCarClick(e: ThreeEvent<MouseEvent>, object: THREE.Vector3) {
    if (sceneSettings.isAddingCar && e.delta < 5) {
      e.stopPropagation();
      setCars((p) => p.filter((e) => e !== object));
      setSceneSettings((p) => ({ ...p, isAddingCar: false }));
    }
  }

  return (
    <group dispose={null} visible={visible} position={[0, 0.25, 0]}>
      <mesh visible={false} onClick={onPlaneClick} rotation={[-Math.PI / 2, 0, 0]}>
        <planeGeometry args={[200, 200]} />
        <meshBasicMaterial />
      </mesh>
      {cars.map((object) => (
        <Clone
          key={object
            .toArray()
            .map((x) => Math.floor(x * 1000000))
            .join()}
          object={obj.scene}
          onClick={(e) => onCarClick(e, object)}
          scale={0.075}
          rotation={[-Math.PI / 2, 0, -Math.PI / 2]}
          position={object}
        />
      ))}
    </group>
  );
}

export default Cars;
