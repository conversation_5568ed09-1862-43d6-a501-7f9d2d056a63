import { THREE } from 'expo-three';

/**
 * Generate a procedural zinc texture that mimics the crystalline pattern of galvanized zinc
 * @param width Width of the texture (default: 512)
 * @param height Height of the texture (default: 512)
 * @returns A THREE.Texture with the zinc pattern
 */
export function generateZincTexture(width = 512, height = 512): THREE.Texture {
  // Create a canvas to draw the texture
  const canvas = document.createElement('canvas');
  canvas.width = width;
  canvas.height = height;
  const context = canvas.getContext('2d');
  
  if (!context) {
    throw new Error('Could not get canvas context');
  }
  
  // Fill background with base zinc color
  context.fillStyle = '#b8c0c8';
  context.fillRect(0, 0, width, height);
  
  // Add noise patterns to simulate zinc crystalline structure
  addZincPattern(context, width, height);
  
  // Create texture from canvas
  const texture = new THREE.CanvasTexture(canvas);
  texture.wrapS = THREE.RepeatWrapping;
  texture.wrapT = THREE.RepeatWrapping;
  
  return texture;
}

/**
 * Add zinc crystalline pattern to the canvas
 * @param context Canvas 2D context
 * @param width Canvas width
 * @param height Canvas height
 */
function addZincPattern(
  context: CanvasRenderingContext2D,
  width: number,
  height: number
): void {
  // Draw large crystalline shapes
  drawCrystallineShapes(context, width, height, 30, 100, 0.2);
  
  // Draw medium crystalline shapes
  drawCrystallineShapes(context, width, height, 20, 50, 0.3);
  
  // Draw small crystalline shapes
  drawCrystallineShapes(context, width, height, 10, 30, 0.4);
  
  // Add subtle noise texture
  addNoiseTexture(context, width, height, 0.05);
}

/**
 * Draw crystalline shapes to simulate zinc pattern
 * @param context Canvas 2D context
 * @param width Canvas width
 * @param height Canvas height
 * @param minSize Minimum size of shapes
 * @param maxSize Maximum size of shapes
 * @param opacity Opacity of shapes
 */
function drawCrystallineShapes(
  context: CanvasRenderingContext2D,
  width: number,
  height: number,
  minSize: number,
  maxSize: number,
  opacity: number
): void {
  const numShapes = Math.floor(width * height / (maxSize * maxSize) * 2);
  
  for (let i = 0; i < numShapes; i++) {
    const x = Math.random() * width;
    const y = Math.random() * height;
    const size = minSize + Math.random() * (maxSize - minSize);
    
    // Randomize between lighter and darker shapes
    const colorValue = Math.random() > 0.5 
      ? Math.floor(200 + Math.random() * 55) 
      : Math.floor(150 + Math.random() * 50);
    
    const color = `rgb(${colorValue}, ${colorValue}, ${colorValue + Math.random() * 10})`;
    
    context.save();
    context.translate(x, y);
    context.rotate(Math.random() * Math.PI * 2);
    context.globalAlpha = opacity;
    context.fillStyle = color;
    
    // Draw a polygon shape
    context.beginPath();
    const points = 3 + Math.floor(Math.random() * 4); // 3 to 6 points
    for (let j = 0; j < points; j++) {
      const angle = (j / points) * Math.PI * 2;
      const radius = size * (0.7 + Math.random() * 0.3);
      const px = Math.cos(angle) * radius;
      const py = Math.sin(angle) * radius;
      
      if (j === 0) {
        context.moveTo(px, py);
      } else {
        context.lineTo(px, py);
      }
    }
    context.closePath();
    context.fill();
    context.restore();
  }
}

/**
 * Add noise texture to the canvas
 * @param context Canvas 2D context
 * @param width Canvas width
 * @param height Canvas height
 * @param opacity Opacity of noise
 */
function addNoiseTexture(
  context: CanvasRenderingContext2D,
  width: number,
  height: number,
  opacity: number
): void {
  context.save();
  context.globalAlpha = opacity;
  
  const imageData = context.getImageData(0, 0, width, height);
  const data = imageData.data;
  
  for (let i = 0; i < data.length; i += 4) {
    const noise = Math.random() * 50 - 25;
    data[i] = Math.max(0, Math.min(255, data[i] + noise));
    data[i + 1] = Math.max(0, Math.min(255, data[i + 1] + noise));
    data[i + 2] = Math.max(0, Math.min(255, data[i + 2] + noise));
  }
  
  context.putImageData(imageData, 0, 0);
  context.restore();
}
