import { THREE } from 'expo-three';
import { CarportMaterialType, MaterialsList } from '@/constants/materials/Material';
import { colorbondColors } from '@/constants/colors/colorbondColors';
import { createZincTexture } from './zincTexture';
import { createConcreteTexture, createFootingConcreteTexture } from './concreteTexture';
import { updateCachedMaterials } from './materialViewModes';

// Create materials with default colors
const materials = {
  [CarportMaterialType.Post]: new THREE.MeshStandardMaterial({ 
    ...MaterialsList[CarportMaterialType.Post]
  }),
  [CarportMaterialType.Roof]: new THREE.MeshStandardMaterial({ 
    ...MaterialsList[CarportMaterialType.Roof]
  }),
  [CarportMaterialType.Rafter]: new THREE.MeshStandardMaterial({ 
    ...MaterialsList[CarportMaterialType.Rafter]
  }),
  [CarportMaterialType.Footing]: new THREE.MeshStandardMaterial({ 
    ...MaterialsList[CarportMaterialType.Footing],
    map: createFootingConcreteTexture(),
    bumpMap: createFootingConcreteTexture(),
    bumpScale: 0.03, // Increased bump scale for more visible texture
    displacementMap: createFootingConcreteTexture(),
    displacementScale: 0.008, // Slightly increased displacement for more depth
    normalScale: new THREE.Vector2(1, 1) // Add normal scaling
  }),
  [CarportMaterialType.Slab]: new THREE.MeshStandardMaterial({ 
    ...MaterialsList[CarportMaterialType.Slab],
    map: createConcreteTexture(),
    bumpMap: createConcreteTexture(),
    bumpScale: 0.02, // Increased bump scale for more visible texture
    displacementMap: createConcreteTexture(),
    displacementScale: 0.004, // Increased displacement for more depth
    normalScale: new THREE.Vector2(1, 1) // Add normal scaling
  }),
  [CarportMaterialType.BrickWall]: new THREE.MeshStandardMaterial({ 
    ...MaterialsList[CarportMaterialType.BrickWall]
  }),
  [CarportMaterialType.ZincFinish]: new THREE.MeshStandardMaterial({ 
    ...MaterialsList[CarportMaterialType.ZincFinish],
    map: createZincTexture(),
    bumpMap: createZincTexture(), // Use same texture for bump mapping
    bumpScale: 0.015, // More subtle bump effect for smoother reflection
    roughnessMap: createZincTexture(), // Use same texture for roughness
    roughness: 0.4, // Lower roughness for more reflective appearance
    metalness: 0.9, // Higher metalness for stronger reflections
    envMapIntensity: 1.5, // Enhance environment reflections
  }),
  [CarportMaterialType.Cap]: new THREE.MeshStandardMaterial({ 
    ...MaterialsList[CarportMaterialType.Cap],
    map: createZincTexture(), // Use zinc texture for caps
    bumpMap: createZincTexture(),
    bumpScale: 0.01, // Subtle bump effect
    roughnessMap: createZincTexture(),
    roughness: 0.3, // Smoother for caps
    metalness: 0.8, // High metalness for ridge caps and barge caps
    envMapIntensity: 1.2, // Enhance environment reflections
  }),
};

// Convert hex string to number
const hexToNumber = (hex: string): number => {
  return parseInt(hex.replace('#', ''), 16);
};

// Global flag to track when materials have been updated
export let materialsUpdated = false;

// Update material color
export const updateMaterialColor = (materialType: CarportMaterialType, colorName: string): void => {
  const colorObj = colorbondColors.find(color => color.value === colorName);
  if (colorObj && materials[materialType]) {
    // Update the base material color
    const newColor = hexToNumber(colorObj.hex);
    materials[materialType].color.set(newColor);
    materials[materialType].needsUpdate = true;
    
    // Update all cached materials derived from this base material
    updateCachedMaterials(materials[materialType]);
    
    // Set the global flag to true to trigger re-renders
    materialsUpdated = !materialsUpdated;
  }
};

// Update multiple material types at once with the same color
export const updateMultipleMaterialColors = (materialTypes: CarportMaterialType[], colorName: string): void => {
  materialTypes.forEach(materialType => {
    updateMaterialColor(materialType, colorName);
  });
};

// Get material
export const getMaterial = (materialType: CarportMaterialType): THREE.MeshStandardMaterial => {
  return materials[materialType];
};

export const postMaterial = materials[CarportMaterialType.ZincFinish]; // Changed to zinc finish
export const roofMaterial = materials[CarportMaterialType.Roof];
export const rafterMaterial = materials[CarportMaterialType.ZincFinish]; // Changed to zinc finish
export const footingMaterial = materials[CarportMaterialType.Footing];
export const slabMaterial = materials[CarportMaterialType.Slab];
export const brickWallMaterial = materials[CarportMaterialType.BrickWall];
export const zincFinishMaterial = materials[CarportMaterialType.ZincFinish];
export const capMaterial = materials[CarportMaterialType.Cap]; // Material for ridge caps and barge caps
