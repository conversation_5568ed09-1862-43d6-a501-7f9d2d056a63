import { THREE } from 'expo-three';
import { Asset } from 'expo-asset';

// Texture cache to avoid reloading textures
const textureCache: { [key: string]: THREE.Texture } = {};

/**
 * Load a texture from a local asset
 * @param assetModule The asset module to load
 * @returns A promise that resolves to a THREE.Texture
 */
export async function loadTexture(assetModule: number): Promise<THREE.Texture> {
  const cacheKey = assetModule.toString();
  
  // Return cached texture if available
  if (textureCache[cacheKey]) {
    return textureCache[cacheKey];
  }
  
  try {
    // Load the asset
    const asset = Asset.fromModule(assetModule);
    await asset.downloadAsync();
    
    // Create texture from asset
    const texture = new THREE.TextureLoader().load(asset.uri);
    
    // Configure texture
    texture.wrapS = THREE.RepeatWrapping;
    texture.wrapT = THREE.RepeatWrapping;
    texture.repeat.set(2, 2); // Repeat the texture
    
    // Cache the texture
    textureCache[cacheKey] = texture;
    
    return texture;
  } catch (error) {
    console.error('Error loading texture:', error);
    throw error;
  }
}

/**
 * Create a textured material with the specified texture and base material properties
 * @param texture The texture to apply
 * @param baseMaterial The base material properties
 * @returns A THREE.MeshStandardMaterial with the texture applied
 */
export function createTexturedMaterial(
  texture: THREE.Texture, 
  baseMaterial: THREE.MeshStandardMaterial
): THREE.MeshStandardMaterial {
  const material = baseMaterial.clone();
  material.map = texture;
  material.needsUpdate = true;
  return material;
}

/**
 * Apply a texture to an existing material
 * @param material The material to apply the texture to
 * @param texture The texture to apply
 */
export function applyTextureToMaterial(
  material: THREE.MeshStandardMaterial,
  texture: THREE.Texture
): void {
  material.map = texture;
  material.needsUpdate = true;
}
