import { THREE } from 'expo-three';

/**
 * Creates a realistic concrete texture with subtle variations and imperfections
 * using a DataTexture approach compatible with React Native
 * @param resolution The resolution of the texture (default: 512)
 * @param color The base color of the concrete (default: light gray)
 * @param noiseScale The scale of the noise pattern (default: 0.05)
 * @returns A THREE.js texture for concrete
 */
export function createConcreteTexture(
  resolution = 512,
  color = 0xb0b0b0, // Darker gray for more realistic concrete
  noiseScale = 0.08 // Increased noise scale for more visible pattern
): THREE.Texture {
  // Create data array for texture (RGBA format)
  const size = resolution * resolution;
  const data = new Uint8Array(4 * size);
  
  // Convert hex color to RGB
  const baseColor = new THREE.Color(color);
  const baseR = Math.floor(baseColor.r * 255);
  const baseG = Math.floor(baseColor.g * 255);
  const baseB = Math.floor(baseColor.b * 255);
  
  // Generate concrete texture pattern
  for (let i = 0; i < size; i++) {
    const stride = i * 4;
    const x = i % resolution;
    const y = Math.floor(i / resolution);
    
    // Add noise to create concrete texture
    const noise = (Math.random() - 0.5) * 60 * noiseScale; // Increased noise range
    
    // Create small cracks and imperfections
    let crackEffect = 0;
    if (Math.random() < 0.003) { // More frequent darker spots for cracks
      crackEffect = -40 - Math.random() * 30; // Darker cracks
    }
    
    // Add some larger dark spots for aggregate in concrete
    if (Math.random() < 0.005) {
      crackEffect = -35 - Math.random() * 25;
    }
    
    // Create more pronounced color variations
    const variation = (simplex2(x * 0.02, y * 0.02) * 15); // Higher frequency and amplitude
    
    // Apply all effects to RGB channels
    data[stride] = Math.max(0, Math.min(255, baseR + noise + crackEffect + variation));
    data[stride + 1] = Math.max(0, Math.min(255, baseG + noise + crackEffect + variation));
    data[stride + 2] = Math.max(0, Math.min(255, baseB + noise + crackEffect + variation));
    data[stride + 3] = 255; // Alpha channel (fully opaque)
  }
  
  // Create texture from data
  const texture = new THREE.DataTexture(data, resolution, resolution, THREE.RGBAFormat);
  texture.needsUpdate = true;
  
  return texture;
}

/**
 * Creates a realistic concrete texture specifically for footings
 * with a rougher appearance and darker color
 */
export function createFootingConcreteTexture(
  resolution = 512
): THREE.Texture {
  // Even darker gray for footings with more pronounced pattern
  return createConcreteTexture(resolution, 0x909090, 0.12);
}

/**
 * Simple 2D simplex noise function
 * This is a basic implementation for texture generation
 */
function simplex2(x: number, y: number): number {
  // Constants for simplex grid
  const F2 = 0.5 * (Math.sqrt(3) - 1);
  const G2 = (3 - Math.sqrt(3)) / 6;
  
  // Skew input space to determine simplex cell
  const s = (x + y) * F2;
  const i = Math.floor(x + s);
  const j = Math.floor(y + s);
  
  // Unskew back to get distance from cell origin
  const t = (i + j) * G2;
  const X0 = i - t;
  const Y0 = j - t;
  const x0 = x - X0;
  const y0 = y - Y0;
  
  // Determine which simplex we're in
  let i1, j1;
  if (x0 > y0) {
    i1 = 1;
    j1 = 0;
  } else {
    i1 = 0;
    j1 = 1;
  }
  
  // Offsets for corners
  const x1 = x0 - i1 + G2;
  const y1 = y0 - j1 + G2;
  const x2 = x0 - 1 + 2 * G2;
  const y2 = y0 - 1 + 2 * G2;
  
  // Calculate contribution from each corner
  const n0 = getCornerNoise(x0, y0);
  const n1 = getCornerNoise(x1, y1);
  const n2 = getCornerNoise(x2, y2);
  
  // Sum contributions (scaled to [-1, 1])
  return 70 * (n0 + n1 + n2);
}

/**
 * Helper function to calculate noise contribution from a corner
 */
function getCornerNoise(x: number, y: number): number {
  // Calculate falloff based on distance
  const t = 0.5 - x * x - y * y;
  
  if (t < 0) return 0;
  
  // Use dot product of gradient with distance vector
  const h = hash(x, y);
  const grad = getGradient(h);
  
  return t * t * t * t * (grad.x * x + grad.y * y);
}

/**
 * Simple hash function for 2D coordinates
 */
function hash(x: number, y: number): number {
  return Math.abs(Math.sin(x * 12.9898 + y * 78.233) * 43758.5453) % 1;
}

/**
 * Get gradient vector based on hash
 */
function getGradient(h: number): { x: number, y: number } {
  const angle = h * Math.PI * 2;
  return {
    x: Math.cos(angle),
    y: Math.sin(angle)
  };
}
