import { THREE } from 'expo-three';

/**
 * Creates a procedural zinc texture pattern using Three.js DataTexture
 * This approach is compatible with React Native and Expo
 * @returns A THREE.Texture with zinc pattern
 */
export function createZincTexture(): THREE.Texture {
  // Larger texture size for more detailed, less concentrated pattern
  const width = 512;
  const height = 512;
  const size = width * height;
  
  // Create data array for texture (RGBA format)
  const data = new Uint8Array(4 * size);
  
  // Generate zinc pattern
  for (let i = 0; i < size; i++) {
    const stride = i * 4;
    
    // Base zinc color (more silver with slight blue tint)
    let baseR = 210; // Increased R value for brighter zinc
    let baseG = 215; // Increased G value for brighter zinc
    let baseB = 220; // Increased B value for brighter zinc
    
    // Calculate position
    const x = i % width;
    const y = Math.floor(i / width);
    
    // Add noise pattern with much lower frequency for more spread-out pattern
    const noise1 = simplex2(x * 0.003, y * 0.003) * 15; // Much lower frequency
    const noise2 = simplex2(x * 0.01, y * 0.01) * 10;    // Lower frequency
    const noise3 = simplex2(x * 0.05, y * 0.05) * 8;     // Lower amplitude
    
    // Add sparse highlights to simulate reflective properties
    // Use very low frequency to create larger, more spread out highlight areas
    const highlight = Math.pow(simplex2((x + 30) * 0.005, (y + 50) * 0.005), 3) * 30;
    
    // Combined noise for crystalline effect with highlights
    const combinedNoise = noise1 + noise2 + noise3 + highlight;
    
    // Apply noise to base color
    data[stride] = Math.max(0, Math.min(255, baseR + combinedNoise));     // R
    data[stride + 1] = Math.max(0, Math.min(255, baseG + combinedNoise)); // G
    data[stride + 2] = Math.max(0, Math.min(255, baseB + combinedNoise)); // B
    data[stride + 3] = 255; // Alpha (fully opaque)
  }
  
  // Create texture
  const texture = new THREE.DataTexture(data, width, height, THREE.RGBAFormat);
  texture.wrapS = THREE.RepeatWrapping;
  texture.wrapT = THREE.RepeatWrapping;
  texture.repeat.set(1, 1); // No repetition for more natural appearance
  texture.needsUpdate = true;
  
  return texture;
}

/**
 * Simple implementation of 2D simplex noise
 * This is a simplified version for demonstration
 */
function simplex2(x: number, y: number): number {
  // Simple noise function based on sin/cos
  const noise = Math.sin(x * 10) * Math.cos(y * 10) * 0.5 +
                Math.sin(x * 20 + y * 10) * 0.25 +
                Math.cos(x * 5 - y * 15) * 0.25;
  
  return noise;
}
