import { Material, MeshStandardMaterial } from 'three';

/**
 * Element type enumeration for different carport components
 */
export enum ElementType {
  // Structural elements - visible in frame mode
  POST = 'post',                // Main posts
  LEAN_TO_POST = 'leanToPost',  // Lean-to posts
  KNEE_BRACE = 'kneeBrace',     // Knee braces
  APEX_BRACE = 'apexBrace',     // Apex braces
  ATTACHED_WALL = 'attachedWall', // Attached wall in skillion attached roof
  FOOTING = 'footing',          // Footings/foundations
  GIRT = 'girt',                // Wall girts/purlins
  
  // Sheet elements - semi-transparent in transparent mode
  ROOF_SHEET = 'roofSheet',     // Main roof sheets
  LEAN_TO_ROOF = 'leanToRoof',  // Lean-to roof sheets
  MAIN_DROP = 'mainDrop',       // Main drop sheets
  BARGE = 'barge',              // Barge caps
  RIDGE = 'ridge',              // Ridge caps
  
  // Other elements - default behavior
  OTHER = 'other'               // Any other element
}

/**
 * Applies the appropriate material properties based on the view mode and element type
 * @param material The material to modify
 * @param viewMode The current view mode ('normal', 'transparent', or 'frame')
 * @param elementType The type of element this material is for
 */
export function applyViewMode(
  material: Material, 
  viewMode: 'normal' | 'transparent' | 'frame' | undefined,
  elementType: ElementType = ElementType.OTHER
): void {
  // Default to 'normal' if viewMode is undefined
  const mode = viewMode || 'normal';
  if (!(material instanceof MeshStandardMaterial)) {
    return;
  }

  // Reset material properties
  material.transparent = false;
  material.opacity = 1.0;
  material.wireframe = false;
  material.visible = true;

  // Check if element is structural (visible in frame mode)
  const isStructural = [
    ElementType.POST,
    ElementType.LEAN_TO_POST,
    ElementType.KNEE_BRACE,
    ElementType.APEX_BRACE,
    ElementType.ATTACHED_WALL,
    ElementType.FOOTING
  ].includes(elementType);
  
  // Check if element is a sheet element (semi-transparent in transparent mode)
  const isSheetElement = [
    ElementType.ROOF_SHEET,
    ElementType.LEAN_TO_ROOF,
    ElementType.MAIN_DROP,
    ElementType.BARGE,
    ElementType.RIDGE
  ].includes(elementType);

  switch (viewMode) {
    case 'normal':
      // Default view - no changes needed
      break;
      
    case 'transparent':
      // Only make sheet elements semi-transparent
      if (isSheetElement) {
        material.transparent = true;
        material.opacity = 0.5;
      }
      break;
      
    case 'frame':
      // Show only structural elements
      if (!isStructural) {
        // Non-structural elements are fully transparent
        material.transparent = true;
        material.opacity = 0.0; // Completely transparent
      } else {
        // Structural elements remain fully visible
        material.transparent = false;
        material.opacity = 1.0;
      }
      break;
  }
}

// Keep track of all created materials for each base material
const materialCache: Record<string, Material[]> = {};

/**
 * Updates all cached materials derived from a base material
 * This ensures color changes are immediately reflected
 * @param baseMaterial The base material that was updated
 */
export function updateCachedMaterials(baseMaterial: Material): void {
  // Go through all cached materials
  Object.keys(materialCache).forEach(key => {
    if (key.startsWith(`${baseMaterial.uuid}-`)) {
      // Update all cached materials that were derived from this base material
      materialCache[key].forEach(derivedMaterial => {
        // Copy color from base material if both are MeshStandardMaterial
        if (baseMaterial instanceof MeshStandardMaterial && derivedMaterial instanceof MeshStandardMaterial) {
          derivedMaterial.color.copy(baseMaterial.color);
          derivedMaterial.needsUpdate = true;
        }
      });
    }
  });
}

/**
 * Creates a clone of a material with view mode properties applied
 * @param material The base material to clone
 * @param viewMode The current view mode
 * @param elementType The type of element this material is for
 * @returns A new material with view mode properties applied
 */
export function createViewModeMaterial(
  material: Material,
  viewMode: 'normal' | 'transparent' | 'frame' | undefined,
  elementType: ElementType = ElementType.OTHER
): Material {
  // Default to 'normal' if viewMode is undefined
  const mode = viewMode || 'normal';
  // Clone the material and apply view mode
  const newMaterial = material.clone();
  applyViewMode(newMaterial, mode, elementType);
  
  // Store the original material reference for color updates
  (newMaterial as any).baseMaterial = material;
  
  // Create a unique ID for this material based on the material and element type
  const materialId = `${material.uuid}-${elementType}`;
  
  // Initialize the cache for this material if it doesn't exist
  if (!materialCache[materialId]) {
    materialCache[materialId] = [];
  }
  
  // Add to cache for future updates
  materialCache[materialId].push(newMaterial);
  
  return newMaterial;
}
