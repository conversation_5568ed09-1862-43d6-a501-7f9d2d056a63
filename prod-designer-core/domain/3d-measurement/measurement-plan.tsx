import React, { useRef, useMemo } from 'react';
import { useSceneContext } from '@/redux/context';
import * as THREE from 'three';
import { Line } from '@react-three/drei/native';
import { Platform } from 'react-native';

// Conditionally import Text component which may use browser APIs
let Text;
try {
  Text = require('@react-three/drei/native').Text;
} catch (error) {
  // Fallback for environments where Text is not available
  Text = ({ children, ...props }: { children: React.ReactNode; [key: string]: any }) => null;
}

// Define types for measurement orientations
type Orientation = 'front' | 'back' | 'left' | 'right';

interface MeasurementPlanProps {
  orientation: Orientation;
}

/**
 * MeasurementPlan component that renders measurement lines and text
 * for different orthographic views of the carport
 */
export const MeasurementPlan = ({ orientation }: MeasurementPlanProps) => {
  const { sceneSettings } = useSceneContext();
  const { span, height, length, leftLeanTo, rightLeanTo, leftLeanToSpan, rightLeanToSpan } = sceneSettings;
  
  // Convert dimensions from mm to meters for Three.js
  const spanM = (span || 3000) / 1000;
  const heightM = (height || 2400) / 1000;
  const lengthM = (length || 6000) / 1000;
  const leftLeanToSpanM = leftLeanTo ? (leftLeanToSpan || 1500) / 1000 : 0;
  const rightLeanToSpanM = rightLeanTo ? (rightLeanToSpan || 1500) / 1000 : 0;
  const totalSpanM = spanM + leftLeanToSpanM + rightLeanToSpanM;
  
  // Calculate positions for measurement lines based on orientation
  const measurements = useMemo(() => {
    switch (orientation) {
      case 'front':
      case 'back':
        return {
          // Width measurements (horizontal)
          width: [
            // Total width
            {
              start: [-totalSpanM/2, -heightM/2, 0] as [number, number, number],
              end: [totalSpanM/2, -heightM/2, 0] as [number, number, number],
              label: `${totalSpanM * 1000}mm`,
              labelPos: [0, -heightM/2 - 0.2, 0] as [number, number, number],
            },
            // Main span
            {
              start: [-spanM/2, -heightM/2 + 0.4, 0] as [number, number, number],
              end: [spanM/2, -heightM/2 + 0.4, 0] as [number, number, number],
              label: `${spanM * 1000}mm`,
              labelPos: [0, -heightM/2 + 0.2, 0] as [number, number, number],
            },
            // Left lean-to (if present)
            ...(leftLeanToSpanM > 0 ? [{
              start: [-totalSpanM/2, -heightM/2 + 0.8, 0] as [number, number, number],
              end: [-spanM/2, -heightM/2 + 0.8, 0] as [number, number, number],
              label: `${leftLeanToSpanM * 1000}mm`,
              labelPos: [-spanM/2 - leftLeanToSpanM/2, -heightM/2 + 0.6, 0] as [number, number, number],
            }] : []),
            // Right lean-to (if present)
            ...(rightLeanToSpanM > 0 ? [{
              start: [spanM/2, -heightM/2 + 0.8, 0] as [number, number, number],
              end: [totalSpanM/2, -heightM/2 + 0.8, 0] as [number, number, number],
              label: `${rightLeanToSpanM * 1000}mm`,
              labelPos: [spanM/2 + rightLeanToSpanM/2, -heightM/2 + 0.6, 0] as [number, number, number],
            }] : []),
          ],
          // Height measurements (vertical)
          height: [
            // Total height
            {
              start: [-totalSpanM/2 - 0.2, -heightM/2, 0] as [number, number, number],
              end: [-totalSpanM/2 - 0.2, heightM/2, 0] as [number, number, number],
              label: `${heightM * 1000}mm`,
              labelPos: [-totalSpanM/2 - 0.5, 0, 0] as [number, number, number],
            },
          ],
        };
      
      case 'left':
      case 'right':
        return {
          // Length measurements (horizontal)
          length: [
            // Total length
            {
              start: [-lengthM/2, -heightM/2, 0] as [number, number, number],
              end: [lengthM/2, -heightM/2, 0] as [number, number, number],
              label: `${lengthM * 1000}mm`,
              labelPos: [0, -heightM/2 - 0.2, 0] as [number, number, number],
            },
          ],
          // Height measurements (vertical)
          height: [
            // Total height
            {
              start: [-lengthM/2 - 0.2, -heightM/2, 0] as [number, number, number],
              end: [-lengthM/2 - 0.2, heightM/2, 0] as [number, number, number],
              label: `${heightM * 1000}mm`,
              labelPos: [-lengthM/2 - 0.5, 0, 0] as [number, number, number],
            },
          ],
          // Depth measurements (only shown in side views)
          depth: [
            // Main span
            {
              start: [-lengthM/2 + 0.5, heightM/2, -spanM/2] as [number, number, number],
              end: [-lengthM/2 + 0.5, heightM/2, spanM/2] as [number, number, number],
              label: `${spanM * 1000}mm`,
              labelPos: [-lengthM/2 + 0.5, heightM/2 + 0.2, 0] as [number, number, number],
            },
            // Total span (if lean-tos present)
            ...(leftLeanToSpanM > 0 || rightLeanToSpanM > 0 ? [{
              start: [-lengthM/2 + 1.0, heightM/2, -totalSpanM/2] as [number, number, number],
              end: [-lengthM/2 + 1.0, heightM/2, totalSpanM/2] as [number, number, number],
              label: `${totalSpanM * 1000}mm`,
              labelPos: [-lengthM/2 + 1.0, heightM/2 + 0.4, 0] as [number, number, number],
            }] : []),
          ],
        };
      
      default:
        return {};
    }
  }, [orientation, spanM, heightM, lengthM, leftLeanToSpanM, rightLeanToSpanM, totalSpanM]);
  
  // Render the measurement lines and labels
  return (
    <group>
      {/* Render width/length measurement lines */}
      {(measurements.width || measurements.length || []).map((measure, index) => (
        <group key={`width-${index}`}>
          <Line
            points={[measure.start, measure.end]}
            color="black"
            lineWidth={1}
          />
          {/* Vertical end markers */}
          <Line
            points={[
              [measure.start[0], measure.start[1] - 0.1, measure.start[2]] as [number, number, number],
              [measure.start[0], measure.start[1] + 0.1, measure.start[2]] as [number, number, number]
            ]}
            color="black"
            lineWidth={1}
          />
          <Line
            points={[
              [measure.end[0], measure.end[1] - 0.1, measure.end[2]] as [number, number, number],
              [measure.end[0], measure.end[1] + 0.1, measure.end[2]] as [number, number, number]
            ]}
            color="black"
            lineWidth={1}
          />
          {/* Measurement text */}
          <Text
            position={measure.labelPos}
            color="black"
            fontSize={0.15}
            anchorX="center"
            anchorY="middle"
          >
            {measure.label}
          </Text>
        </group>
      ))}
      
      {/* Render height measurement lines */}
      {(measurements.height || []).map((measure, index) => (
        <group key={`height-${index}`}>
          <Line
            points={[measure.start, measure.end]}
            color="black"
            lineWidth={1}
          />
          {/* Horizontal end markers */}
          <Line
            points={[
              [measure.start[0] - 0.1, measure.start[1], measure.start[2]] as [number, number, number],
              [measure.start[0] + 0.1, measure.start[1], measure.start[2]] as [number, number, number]
            ]}
            color="black"
            lineWidth={1}
          />
          <Line
            points={[
              [measure.end[0] - 0.1, measure.end[1], measure.end[2]] as [number, number, number],
              [measure.end[0] + 0.1, measure.end[1], measure.end[2]] as [number, number, number]
            ]}
            color="black"
            lineWidth={1}
          />
          {/* Measurement text */}
          <Text
            position={measure.labelPos}
            color="black"
            fontSize={0.15}
            anchorX="center"
            anchorY="middle"
            rotation={[0, 0, Math.PI/2]}
          >
            {measure.label}
          </Text>
        </group>
      ))}
      
      {/* Render depth measurement lines (only in side views) */}
      {(measurements.depth || []).map((measure, index) => (
        <group key={`depth-${index}`}>
          <Line
            points={[measure.start, measure.end]}
            color="black"
            lineWidth={1}
          />
          {/* Vertical end markers */}
          <Line
            points={[
              [measure.start[0], measure.start[1], measure.start[2]],
              [measure.start[0], measure.start[1] - 0.1, measure.start[2]]
            ]}
            color="black"
            lineWidth={1}
          />
          <Line
            points={[
              [measure.end[0], measure.end[1], measure.end[2]],
              [measure.end[0], measure.end[1] - 0.1, measure.end[2]]
            ]}
            color="black"
            lineWidth={1}
          />
          {/* Measurement text */}
          <Text
            position={measure.labelPos}
            color="black"
            fontSize={0.15}
            anchorX="center"
            anchorY="middle"
          >
            {measure.label}
          </Text>
        </group>
      ))}
    </group>
  );
}

// Export is already handled with named export
