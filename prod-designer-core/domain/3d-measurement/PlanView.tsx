import React, { useRef, useEffect } from 'react';
import { StyleSheet, View, Text } from 'react-native';
import { Canvas, useFrame, useThree } from '@react-three/fiber/native';
// Import only the components that are safe for React Native
import { OrthographicCamera } from '@react-three/drei/native';
import { Platform } from 'react-native';
import { useSceneContext } from '@/redux/context';
import * as THREE from 'three';

// Import the carport models
import Carport from '@/domain/carport-models/Carport';
import GableCarport from '@/domain/carport-models/gable_Carport';
// Import the measurement plan component
// Conditionally import the measurement plan component
const MeasurementPlan = Platform.OS === 'web' ? null : require('./measurement-plan').MeasurementPlan;

/**
 * PlanView component that displays four orthographic views of the carport
 * (front, back, left, right) in a grid layout
 */
function PlanView() {
  const { sceneSettings, dimensions } = useSceneContext();
  
  // Ensure we have the required dimensions
  const { span, height, length, pitch, overhang, roofType } = dimensions;
  
  return (
    <View style={styles.container}>
      {/* Front View (Top-Left Quadrant) */}
      <View style={styles.quadrant}>
        <Canvas style={styles.canvas}>
          <ambientLight intensity={0.8} />
          <directionalLight position={[0, 5, 5]} intensity={1} />
          <FrontView />
        </Canvas>
        <View style={styles.viewLabel}>
          <View style={styles.labelBackground}>
            <Text style={styles.labelText}>Front View</Text>
          </View>
        </View>
      </View>

      {/* Right View (Top-Right Quadrant) */}
      <View style={styles.quadrant}>
        <Canvas style={styles.canvas}>
          <ambientLight intensity={0.8} />
          <directionalLight position={[5, 5, 0]} intensity={1} />
          <RightView />
        </Canvas>
        <View style={styles.viewLabel}>
          <View style={styles.labelBackground}>
            <Text style={styles.labelText}>Right View</Text>
          </View>
        </View>
      </View>

      {/* Left View (Bottom-Left Quadrant) */}
      <View style={styles.quadrant}>
        <Canvas style={styles.canvas}>
          <ambientLight intensity={0.8} />
          <directionalLight position={[-5, 5, 0]} intensity={1} />
          <LeftView />
        </Canvas>
        <View style={styles.viewLabel}>
          <View style={styles.labelBackground}>
            <Text style={styles.labelText}>Left View</Text>
          </View>
        </View>
      </View>

      {/* Back View (Bottom-Right Quadrant) */}
      <View style={styles.quadrant}>
        <Canvas style={styles.canvas}>
          <ambientLight intensity={0.8} />
          <directionalLight position={[0, 5, -5]} intensity={1} />
          <BackView />
        </Canvas>
        <View style={styles.viewLabel}>
          <View style={styles.labelBackground}>
            <Text style={styles.labelText}>Back View</Text>
          </View>
        </View>
      </View>
    </View>
  );
}

// Front View Component (Z-axis view)
function FrontView() {
  const { sceneSettings } = useSceneContext();
  const { size, camera } = useThree();
  const groupRef = useRef<THREE.Group>(null);
  
  useEffect(() => {
    if (camera && groupRef.current) {
      // Set up orthographic camera for front view
      camera.position.set(0, 0, 10);
      camera.lookAt(0, 0, 0);
      
      // Center and scale the model appropriately
      const modelScale = 0.015;
      groupRef.current.scale.set(modelScale, modelScale, modelScale);
      
      // Adjust position to center the model
      const { span, height, length } = sceneSettings;
      const adjustedSpan = span || 3000;
      const adjustedHeight = height || 2400;
      groupRef.current.position.set(0, -adjustedHeight/200000, 0);
    }
  }, [camera, sceneSettings]);
  
  return (
    <group ref={groupRef}>
      {sceneSettings.roofType === 'gable' ? (
        <GableCarport viewMode="normal" showSlab={sceneSettings.showSlab} />
      ) : (
        <Carport viewMode="normal" showSlab={sceneSettings.showSlab} />
      )}
      {MeasurementPlan && <MeasurementPlan orientation="front" />}
    </group>
  );
}

// Right View Component (X-axis view)
function RightView() {
  const { sceneSettings } = useSceneContext();
  const { camera } = useThree();
  const groupRef = useRef<THREE.Group>(null);
  
  useEffect(() => {
    if (camera && groupRef.current) {
      // Set up orthographic camera for right view
      camera.position.set(10, 0, 0);
      camera.lookAt(0, 0, 0);
      
      // Center and scale the model appropriately
      const modelScale = 0.015;
      groupRef.current.scale.set(modelScale, modelScale, modelScale);
      
      // Adjust position to center the model
      const { span, height, length } = sceneSettings;
      const adjustedHeight = height || 2400;
      const adjustedLength = length || 6000;
      groupRef.current.position.set(0, -adjustedHeight/200000, 0);
      
      // Rotate to show right side
      groupRef.current.rotation.y = Math.PI / 2;
    }
  }, [camera, sceneSettings]);
  
  return (
    <group ref={groupRef}>
      {sceneSettings.roofType === 'gable' ? (
        <GableCarport viewMode="normal" showSlab={sceneSettings.showSlab} />
      ) : (
        <Carport viewMode="normal" showSlab={sceneSettings.showSlab} />
      )}
      {MeasurementPlan && <MeasurementPlan orientation="right" />}
    </group>
  );
}

// Left View Component (X-axis view from opposite side)
function LeftView() {
  const { sceneSettings } = useSceneContext();
  const { camera } = useThree();
  const groupRef = useRef<THREE.Group>(null);
  
  useEffect(() => {
    if (camera && groupRef.current) {
      // Set up orthographic camera for left view
      camera.position.set(-10, 0, 0);
      camera.lookAt(0, 0, 0);
      
      // Center and scale the model appropriately
      const modelScale = 0.015;
      groupRef.current.scale.set(modelScale, modelScale, modelScale);
      
      // Adjust position to center the model
      const { span, height, length } = sceneSettings;
      const adjustedHeight = height || 2400;
      const adjustedLength = length || 6000;
      groupRef.current.position.set(0, -adjustedHeight/200000, 0);
      
      // Rotate to show left side
      groupRef.current.rotation.y = -Math.PI / 2;
    }
  }, [camera, sceneSettings]);
  
  return (
    <group ref={groupRef}>
      {sceneSettings.roofType === 'gable' ? (
        <GableCarport viewMode="normal" showSlab={sceneSettings.showSlab} />
      ) : (
        <Carport viewMode="normal" showSlab={sceneSettings.showSlab} />
      )}
      {MeasurementPlan && <MeasurementPlan orientation="left" />}
    </group>
  );
}

// Back View Component (Z-axis view from opposite side)
function BackView() {
  const { sceneSettings } = useSceneContext();
  const { camera } = useThree();
  const groupRef = useRef<THREE.Group>(null);
  
  useEffect(() => {
    if (camera && groupRef.current) {
      // Set up orthographic camera for back view
      camera.position.set(0, 0, -10);
      camera.lookAt(0, 0, 0);
      
      // Center and scale the model appropriately
      const modelScale = 0.015;
      groupRef.current.scale.set(modelScale, modelScale, modelScale);
      
      // Adjust position to center the model
      const { span, height, length } = sceneSettings;
      const adjustedHeight = height || 2400;
      groupRef.current.position.set(0, -adjustedHeight/200000, 0);
      
      // Rotate to show back side
      groupRef.current.rotation.y = Math.PI;
    }
  }, [camera, sceneSettings]);
  
  return (
    <group ref={groupRef}>
      {sceneSettings.roofType === 'gable' ? (
        <GableCarport viewMode="normal" showSlab={sceneSettings.showSlab} />
      ) : (
        <Carport viewMode="normal" showSlab={sceneSettings.showSlab} />
      )}
      {MeasurementPlan && <MeasurementPlan orientation="back" />}
    </group>
  );
}

export default PlanView;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'row',
    flexWrap: 'wrap',
    backgroundColor: '#f5f5f5',
  },
  quadrant: {
    width: '50%',
    height: '50%',
    position: 'relative',
    borderWidth: 1,
    borderColor: '#ddd',
  },
  canvas: {
    flex: 1,
  },
  viewLabel: {
    position: 'absolute',
    top: 10,
    left: 10,
    backgroundColor: 'rgba(255, 255, 255, 0.7)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  labelBackground: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  labelContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  labelIcon: {
    width: 16,
    height: 16,
    marginRight: 4,
  },
  labelText: {
    fontWeight: 'bold' as 'bold',
    fontSize: 12,
    color: '#333',
  },
});
