import { post_size } from '@/constants/carport/CarportParams';
import { createCorro2DSection } from '@/domain/helpers/SectionHelper';
import { WallMatrixType } from './girt.config';
import { CarportMaterialType } from '@/constants/materials/Material';
import { brickWallMaterial, roofMaterial,getMaterial } from '@/domain/materials/colorManager';
import { THREE } from 'expo-three';

// Define interfaces for wall sheet geometries and positions
interface WallSheetGeometry {
  geo_x: number;  // width of sheet
  geo_y: number;  // height of sheet
  geo_z: number;  // length of sheet
  thickness: number;  // thickness of the sheet material
  topEdge?: Array<[number, number]>;  // optional array of [x,y] points defining the top edge for gable shape
  material?: THREE.MeshStandardMaterial; // Material for sheet color
}

interface WallSheetPosition {
  x: number;  // x position in scene
  y: number;  // y position in scene
  z: number;  // z position in scene
  rotation?: {  // optional rotation values
    x: number;
    y: number;
    z: number;
  };
}

interface WallSheetReturn {
  // Main carport wall sheets
  mainLeftWallSheet: {
    geometries: WallSheetGeometry[];
    positions: WallSheetPosition[];
  };
  mainRightWallSheet: {
    geometries: WallSheetGeometry[];
    positions: WallSheetPosition[];
  };
  mainFrontWallSheet: {
    geometries: WallSheetGeometry[];
    positions: WallSheetPosition[];
  };
  mainBackWallSheet: {
    geometries: WallSheetGeometry[];
    positions: WallSheetPosition[];
  };
  mainDividerWallSheet: {
    geometries: WallSheetGeometry[];
    positions: WallSheetPosition[];
  };
  
  // Left lean-to wall sheets
  leftLeanToWallSheet: {
    geometries: WallSheetGeometry[];
    positions: WallSheetPosition[];
  };
  leftLeanToFrontWallSheet: {
    geometries: WallSheetGeometry[];
    positions: WallSheetPosition[];
  };
  leftLeanToBackWallSheet: {
    geometries: WallSheetGeometry[];
    positions: WallSheetPosition[];
  };
  leftLeanToDividerWallSheet: {
    geometries: WallSheetGeometry[];
    positions: WallSheetPosition[];
  };
  
  // Right lean-to wall sheets
  rightLeanToWallSheet: {
    geometries: WallSheetGeometry[];
    positions: WallSheetPosition[];
  };
  rightLeanToFrontWallSheet: {
    geometries: WallSheetGeometry[];
    positions: WallSheetPosition[];
  };
  rightLeanToBackWallSheet: {
    geometries: WallSheetGeometry[];
    positions: WallSheetPosition[];
  };
  rightLeanToDividerWallSheet: {
    geometries: WallSheetGeometry[];
    positions: WallSheetPosition[];
  };
}

// Helper function to get wall sheet dimensions
function getWallSheetDimensions() {
  // Standard dimensions for corrugated wall sheet
  return {
    width: 50,         // width of a single sheet in mm
    height: 20,        // height of the profile in mm
    thickness: 0.42,   // thickness of sheet in mm
  };
}

/**
 * Helper function to calculate wall height with gable trim at a specific position
 * @param horizontalPosition - Position along the span (mm)
 * @param span - Total span width (mm)
 * @param height - Wall height (mm)
 * @param pitch - Roof pitch (degrees)
 * @returns The height of the wall at this horizontal position (mm)
 */
function calculateWallHeightWithGableTrim(horizontalPosition: number, span: number, height: number, pitch: number): number {
  // Calculate the gable rise based on span and pitch
  const pitchRadians = (pitch * Math.PI) / 180;
  const gableRise = (span / 2) * Math.tan(pitchRadians);
  
  // Calculate distance from center of span
  const centerPosition = span / 2;
  const distanceFromCenter = Math.abs(horizontalPosition - centerPosition);
  
  // Calculate the height at this position
  // At the center, height is maximum (baseHeight + gableRise)
  // At the edges, height is just the base height
  const heightAtPosition = height + gableRise - (gableRise * (distanceFromCenter / (span / 2)));
  
  return heightAtPosition;
}

/**
 * Helper function to calculate top edge vertices for a gable wall sheet
 * @param startX - Start X position in mm
 * @param endX - End X position in mm
 * @param numPoints - Number of points to calculate along the edge
 * @param span - Total span width (mm)
 * @param height - Wall height (mm)
 * @param pitch - Roof pitch (degrees)
 * @returns Array of [x, y] positions defining the top edge
 */
function calculateGableTopEdge(startX: number, endX: number, numPoints: number, span: number, height: number, pitch: number): Array<[number, number]> {
  const points: Array<[number, number]> = [];
  
  for (let i = 0; i <= numPoints; i++) {
    const x = startX + (i * (endX - startX) / numPoints);
    const heightAtPoint = calculateWallHeightWithGableTrim(x, span, height, pitch);
    points.push([x, heightAtPoint]);
  }
  
  return points;
}

// Main function to calculate wall sheet geometries and positions
export default function calculateWallSheetGeometriesAndPositions(
  length: number = 12000,
  span: number = 6000,
  height: number = 2700,
  leftLeanToSpan: number = 0,
  leftLeanToDropHeight: number = 0,
  rightLeanToSpan: number = 0,
  rightLeanToDropHeight: number = 0,
  overhang: number = 0,
  pitch: number = 15, // Add pitch parameter with default value
  wallMatrix: WallMatrixType = {
    mainLeftWall: [],
    mainRightWall: [],
    mainDividerWall: [],
    mainFrontWall: false,
    mainBackWall: false,
    leftLeanToWall: [],
    leftLeanToFrontWall: null,
    leftLeanToBackWall: null,
    leftLeanToDividerWall: [],
    rightLeanToWall: [],
    rightLeanToFrontWall: null,
    rightLeanToBackWall: null,
    rightLeanToDividerWall: [],
  }
): WallSheetReturn {
  // Initialize arrays to store geometries and positions
  const mainLeftWallSheet_Geo: WallSheetGeometry[] = [];
  const mainLeftWallSheet_Positions: WallSheetPosition[] = [];
  
  const mainRightWallSheet_Geo: WallSheetGeometry[] = [];
  const mainRightWallSheet_Positions: WallSheetPosition[] = [];
  
  const mainFrontWallSheet_Geo: WallSheetGeometry[] = [];
  const mainFrontWallSheet_Positions: WallSheetPosition[] = [];
  
  const mainBackWallSheet_Geo: WallSheetGeometry[] = [];
  const mainBackWallSheet_Positions: WallSheetPosition[] = [];
  
  const mainDividerWallSheet_Geo: WallSheetGeometry[] = [];
  const mainDividerWallSheet_Positions: WallSheetPosition[] = [];
  
  // Left lean-to sheet arrays
  const leftLeanToWallSheet_Geo: WallSheetGeometry[] = [];
  const leftLeanToWallSheet_Positions: WallSheetPosition[] = [];
  
  const leftLeanToFrontWallSheet_Geo: WallSheetGeometry[] = [];
  const leftLeanToFrontWallSheet_Positions: WallSheetPosition[] = [];
  
  const leftLeanToBackWallSheet_Geo: WallSheetGeometry[] = [];
  const leftLeanToBackWallSheet_Positions: WallSheetPosition[] = [];
  
  const leftLeanToDividerWallSheet_Geo: WallSheetGeometry[] = [];
  const leftLeanToDividerWallSheet_Positions: WallSheetPosition[] = [];
  
  // Right lean-to sheet arrays
  const rightLeanToWallSheet_Geo: WallSheetGeometry[] = [];
  const rightLeanToWallSheet_Positions: WallSheetPosition[] = [];
  
  const rightLeanToFrontWallSheet_Geo: WallSheetGeometry[] = [];
  const rightLeanToFrontWallSheet_Positions: WallSheetPosition[] = [];
  
  const rightLeanToBackWallSheet_Geo: WallSheetGeometry[] = [];
  const rightLeanToBackWallSheet_Positions: WallSheetPosition[] = [];
  
  const rightLeanToDividerWallSheet_Geo: WallSheetGeometry[] = [];
  const rightLeanToDividerWallSheet_Positions: WallSheetPosition[] = [];
  
  // Calculate number of bays based on length
  const bay_no = Math.max(1, Math.ceil(length / 4000));
  const sectionLength = length / bay_no / 1000; // Convert to meters
  
  // Wall sheet standard dimensions
  const sheetWidth = 840; // Standard sheet width in mm
  const overlap = 76; // Overlap between sheets in mm
  
  /**
   * Process Main Left Wall Sheets
   */
  for (let i = 0; i < bay_no; i++) {
    // Only add wall sheets for sections that are enabled in the wall matrix
    if (wallMatrix.mainLeftWall && wallMatrix.mainLeftWall[i]) {
      // Get dimensions for the wall sheet
      const { width, height: sheetHeight, thickness } = getWallSheetDimensions();
      
      // Use a single sheet for the full height of the wall in this bay
      const fullHeight = height;
      
      // Using simple geometry for left wall to match right wall implementation
      
      // Add single wall sheet that spans full height for this bay with corrugation
      mainLeftWallSheet_Geo.push({
        geo_x: thickness / 1000,      // Thickness in meters
        geo_y: fullHeight / 1000,    // Full height in meters (up to eave height)
        geo_z: sectionLength,        // Length of the section (bay length)
        thickness: 0.001,            // Material thickness
        material: roofMaterial,      // Use roof material to match other walls
      });
      
      // Calculate position for this wall section
      // x: left side of the structure, accounting for posts
      // y: center of the bay section
      // z: position at the base (no overlap handling needed for single sheet)
      const x = post_size / 2 / 1000; // Left side with post offset
      const y = (i * length / bay_no) / 1000 + sectionLength / 2; // Center of bay section
      const z = 0; // Position at base level
      
      // Add position data
      mainLeftWallSheet_Positions.push({
        x: x,
        y: y,
        z: z,
        rotation: { x: 0, y: -Math.PI/2, z: 0 } // Rotation for left side wall
      });
    }
  }
  
  /**
   * Process Main Right Wall Sheets
   */
  for (let i = 0; i < bay_no; i++) {
    // Only add wall sheets for sections that are enabled in the wall matrix
    if (wallMatrix.mainRightWall && wallMatrix.mainRightWall[i]) {
      // Get dimensions for the wall sheet
      const { width, height: sheetHeight, thickness } = getWallSheetDimensions();
      
      // Use a single sheet for the full height of the wall in this bay
      const fullHeight = height;
      
      // Add single wall sheet that spans full height for this bay
      mainRightWallSheet_Geo.push({
        geo_x: thickness / 1000,      // Thickness in meters
        geo_y: fullHeight / 1000,    // Full height in meters (up to eave height)
        geo_z: sectionLength,        // Length of the section (bay length)
        thickness: 0.001,            // Material thickness
        material: roofMaterial,      // Use roof material to match other walls
      });
      
      // Calculate position for this wall section
      // x: right side of the structure (span width), accounting for posts
      // y: center of the bay section
      // z: position at the base (no overlap handling needed for single sheet)
      const x = (span / 1000) - (post_size / 2 / 1000); // Right side with post offset
      const y = (i * length / bay_no) / 1000 + sectionLength / 2; // Center of bay section
      const z = 0; // Position at base level
      
      // Add position data
      mainRightWallSheet_Positions.push({
        x: x,
        y: y,
        z: z,
        rotation: { x: -Math.PI/2, y: 0, z: Math.PI } // Rotation for right side wall
      });
    }
  }
  
  /**
   * Process Front Wall Sheet (if enabled)
   */
  if (wallMatrix.mainFrontWall) {
    // Calculate the standard sheet dimensions
    const { width, height: sheetHeight, thickness } = getWallSheetDimensions();
    
    // Calculate the gable peak height
    const pitchRadians = (pitch * Math.PI) / 180;
    const gableRise = (span / 2) * Math.tan(pitchRadians);
    const peakHeight = height + gableRise;
    
    // Create a single drop sheet for the front wall following the gable outline
    // This matches the same approach used for the back wall
    
    // Import the THREE.js for the corrugation pattern
    const { THREE } = require('expo-three');
    
    // Standard corrugated steel dimensions
    const corrugationDepth = 16; // Depth of corrugation in mm
    const corrugationWidth = 76; // Width of one corrugation wave in mm
    
    // Define the gable outline shape (points defining the entire wall)
    const gableOutline: Array<[number, number]> = [
      [0, 0],                // Bottom left corner
      [span, 0],             // Bottom right corner
      [span, height],        // Top right corner at eave height
      [span/2, peakHeight],  // Gable peak
      [0, height],           // Top left corner at eave height
    ];
    
    // Create a shape to hold the corrugated gable profile
    const wallShape = new THREE.Shape();
    // Start from the first point of the gable outline
    wallShape.moveTo(gableOutline[0][0] / 1000, gableOutline[0][1] / 1000);
    
    // Add corrugation along the bottom edge
    const numWaves = Math.floor(span / corrugationWidth);
    const actualWaveWidth = span / numWaves;
    
    // Start at bottom left corner
    wallShape.lineTo(0, 0);
    
    // Add corrugation waves along the bottom (alternating up and down)
    for (let i = 0; i < numWaves; i++) {
      const waveStartX = i * actualWaveWidth / 1000;
      const wavePeakX = (i * actualWaveWidth + actualWaveWidth / 2) / 1000;
      const waveEndX = ((i + 1) * actualWaveWidth) / 1000;
      
      // Valley to peak
      wallShape.lineTo(wavePeakX, corrugationDepth / 1000);
      // Peak to valley
      wallShape.lineTo(waveEndX, 0);
    }
    
    // Continue with the right edge, top edge and left edge of gable
    wallShape.lineTo(gableOutline[1][0] / 1000, gableOutline[1][1] / 1000); // Bottom right
    wallShape.lineTo(gableOutline[2][0] / 1000, gableOutline[2][1] / 1000); // Top right
    wallShape.lineTo(gableOutline[3][0] / 1000, gableOutline[3][1] / 1000); // Peak
    wallShape.lineTo(gableOutline[4][0] / 1000, gableOutline[4][1] / 1000); // Top left
    wallShape.lineTo(gableOutline[0][0] / 1000, gableOutline[0][1] / 1000); // Back to start
    
    // Extract points from the shape for rendering
    const extractedPoints = wallShape.extractPoints(12); // Higher resolution
    const corrugatedGablePoints = extractedPoints.shape.map((point: { x: number; y: number }) => [
      point.x, // Already in meters
      point.y  // Already in meters
    ] as [number, number]);
    
    // Add the front wall as a single sheet with the gable profile and corrugation pattern
    mainFrontWallSheet_Geo.push({
      geo_x: thickness / 1000,  // Thickness in meters
      geo_y: peakHeight / 1000, // Total height including gable in meters
      geo_z: span / 1000,       // Width of the entire wall in meters
      thickness: 0.001,         // Material thickness in meters
      material: roofMaterial,  // Use the roof material from material manager
      topEdge: corrugatedGablePoints  // The corrugated gable outline defines the shape
    });
    
    // Position the sheet at the front edge of the carport
    const x = span / 2000;               // Center horizontally
    const y = post_size / 2 / 1000-.038;      // Front edge with post offset
    const z = peakHeight / 2000;         // Center vertically
    
    // Add position data
    mainFrontWallSheet_Positions.push({
      x: x,
      y: y,
      z: z,
      rotation: { x: 0, y: 0, z: -Math.PI/2 } // Rotation for front wall
    });
  }
  
  /**
   * Process Back Wall Sheet (if enabled)
   */
  if (wallMatrix.mainBackWall) {
    // Calculate the standard sheet dimensions
    const { width, height: sheetHeight, thickness } = getWallSheetDimensions();
    
    // Calculate the gable peak height
    const pitchRadians = (pitch * Math.PI) / 180;
    const gableRise = (span / 2) * Math.tan(pitchRadians);
    const peakHeight = height + gableRise;
    
    // Create a single drop sheet for the back wall following the gable outline
    // Similar to the main drop sheet in a lean-to carport but with corrugation pattern
    
    // Import the THREE.js for the corrugation pattern
    const { THREE } = require('expo-three');
    
    // Standard corrugated steel dimensions
    const corrugationDepth = 16; // Depth of corrugation in mm
    const corrugationWidth = 76; // Width of one corrugation wave in mm
    
    // Define the gable outline shape (points defining the entire wall)
    const gableOutline: Array<[number, number]> = [
      [0, 0],                // Bottom left corner
      [span, 0],             // Bottom right corner
      [span, height],        // Top right corner at eave height
      [span/2, peakHeight],  // Gable peak
      [0, height],           // Top left corner at eave height
    ];
    
    // Create a shape to hold the corrugated gable profile
    const wallShape = new THREE.Shape();
    // Start from the first point of the gable outline
    wallShape.moveTo(gableOutline[0][0] / 1000, gableOutline[0][1] / 1000);
    
    // Add corrugation along the bottom edge
    const numWaves = Math.floor(span / corrugationWidth);
    const actualWaveWidth = span / numWaves;
    
    // Start at bottom left corner
    wallShape.lineTo(0, 0);
    
    // Add corrugation waves along the bottom (alternating up and down)
    for (let i = 0; i < numWaves; i++) {
      const waveStartX = i * actualWaveWidth / 1000;
      const wavePeakX = (i * actualWaveWidth + actualWaveWidth / 2) / 1000;
      const waveEndX = ((i + 1) * actualWaveWidth) / 1000;
      
      // Valley to peak
      wallShape.lineTo(wavePeakX, corrugationDepth / 1000);
      // Peak to valley
      wallShape.lineTo(waveEndX, 0);
    }
    
    // Continue with the right edge, top edge and left edge of gable
    wallShape.lineTo(gableOutline[1][0] / 1000, gableOutline[1][1] / 1000); // Bottom right
    wallShape.lineTo(gableOutline[2][0] / 1000, gableOutline[2][1] / 1000); // Top right
    wallShape.lineTo(gableOutline[3][0] / 1000, gableOutline[3][1] / 1000); // Peak
    wallShape.lineTo(gableOutline[4][0] / 1000, gableOutline[4][1] / 1000); // Top left
    wallShape.lineTo(gableOutline[0][0] / 1000, gableOutline[0][1] / 1000); // Back to start
    
    // Extract points from the shape for rendering
    const extractedPoints = wallShape.extractPoints(12); // Higher resolution
    const corrugatedGablePoints = extractedPoints.shape.map((point: { x: number; y: number }) => [
      point.x, // Already in meters
      point.y  // Already in meters
    ] as [number, number]);
    
    // Add the back wall as a single sheet with the gable profile and corrugation pattern
    mainBackWallSheet_Geo.push({
      geo_x: thickness / 1000,  // Thickness in meters
      geo_y: peakHeight / 1000, // Total height including gable in meters
      geo_z: span / 1000-.13,       // Width of the entire wall in meters
      thickness: 0.001,         // Material thickness in meters
      topEdge: corrugatedGablePoints,  // The corrugated gable outline defines the shape
      material: roofMaterial  // Use the wall material from material manager
    });
    
    // Position the sheet at the back edge of the carport
    const x = span / 2000;               // Center horizontally
    const y = (length / 1000) - (post_size / 2 / 1000); // Back edge with post offset
    const z = peakHeight / 2000;         // Center vertically
    
    // Add position data
    mainBackWallSheet_Positions.push({
      x: x,
      y: y+.038,
      z: z,
      rotation: { x: 0, y: 0, z: -Math.PI/2 } // Rotation for back wall
    });
  }
  
  /**
   * Process Divider Wall Sheets
   */
  if (wallMatrix.mainDividerWall && wallMatrix.mainDividerWall.length > 0) {
    // Process each divider wall
    for (let i = 0; i < wallMatrix.mainDividerWall.length; i++) {
      if (wallMatrix.mainDividerWall[i]) {
        // Calculate the standard sheet dimensions
        const { width, height: sheetHeight, thickness } = getWallSheetDimensions();
        
        // Calculate the gable peak height
        const pitchRadians = (pitch * Math.PI) / 180;
        const gableRise = (span / 2) * Math.tan(pitchRadians);
        const peakHeight = height + gableRise;
        
        // Create a single drop sheet for the divider wall following the gable outline
        // This matches the same approach used for the back wall
        
        // Import the THREE.js for the corrugation pattern
        const { THREE } = require('expo-three');
        
        // Standard corrugated steel dimensions
        const corrugationDepth = 16; // Depth of corrugation in mm
        const corrugationWidth = 76; // Width of one corrugation wave in mm
        
        // Define the gable outline shape (points defining the entire wall)
        const gableOutline: Array<[number, number]> = [
          [0, 0],                // Bottom left corner
          [span, 0],             // Bottom right corner
          [span, height],        // Top right corner at eave height
          [span/2, peakHeight],  // Gable peak
          [0, height],           // Top left corner at eave height
        ];
        
        // Create a shape to hold the corrugated gable profile
        const wallShape = new THREE.Shape();
        // Start from the first point of the gable outline
        wallShape.moveTo(gableOutline[0][0] / 1000, gableOutline[0][1] / 1000);
        
        // Add corrugation along the bottom edge
        const numWaves = Math.floor(span / corrugationWidth);
        const actualWaveWidth = span / numWaves;
        
        // Start at bottom left corner
        wallShape.lineTo(0, 0);
        
        // Add corrugation waves along the bottom (alternating up and down)
        for (let j = 0; j < numWaves; j++) {
          const waveStartX = j * actualWaveWidth / 1000;
          const wavePeakX = (j * actualWaveWidth + actualWaveWidth / 2) / 1000;
          const waveEndX = ((j + 1) * actualWaveWidth) / 1000;
          
          // Valley to peak
          wallShape.lineTo(wavePeakX, corrugationDepth / 1000);
          // Peak to valley
          wallShape.lineTo(waveEndX, 0);
        }
        
        // Continue with the right edge, top edge and left edge of gable
        wallShape.lineTo(gableOutline[1][0] / 1000, gableOutline[1][1] / 1000); // Bottom right
        wallShape.lineTo(gableOutline[2][0] / 1000, gableOutline[2][1] / 1000); // Top right
        wallShape.lineTo(gableOutline[3][0] / 1000, gableOutline[3][1] / 1000); // Peak
        wallShape.lineTo(gableOutline[4][0] / 1000, gableOutline[4][1] / 1000); // Top left
        wallShape.lineTo(gableOutline[0][0] / 1000, gableOutline[0][1] / 1000); // Back to start
        
        // Extract points from the shape for rendering
        const extractedPoints = wallShape.extractPoints(12); // Higher resolution
        const corrugatedGablePoints = extractedPoints.shape.map((point: { x: number; y: number }) => [
          point.x, // Already in meters
          point.y  // Already in meters
        ] as [number, number]);
        
        // Add the divider wall as a single sheet with the gable profile and corrugation pattern
        mainDividerWallSheet_Geo.push({
          geo_x: thickness / 1000,  // Thickness in meters
          geo_y: peakHeight / 1000, // Total height including gable in meters
          geo_z: span / 1000,       // Width of the entire wall in meters
          thickness: 0.001,         // Material thickness in meters
          material: roofMaterial,  // Use the roof material from material manager
          topEdge: corrugatedGablePoints  // The corrugated gable outline defines the shape
        });
        
        // Position the sheet at the divider location with the girt.config offset
        const x = span / 2000;                   // Center horizontally
        const y = length /1000 -((i + 1) * length / bay_no) / 1000 -.07; // Divider position with offset to match girt.config
        const z = peakHeight / 2000;             // Center vertically
        
        // Add position data
        mainDividerWallSheet_Positions.push({
          x: x,
          y: y,
          z: z,
          rotation: { x: 0, y: 0, z: -Math.PI/2 } // Rotation for divider wall
        });
      }
    }
  }
  
  // Combine all wall sheet data for return
  return {
    // Main carport wall sheets
    mainLeftWallSheet: {
      geometries: mainLeftWallSheet_Geo,
      positions: mainLeftWallSheet_Positions,
    },
    mainRightWallSheet: {
      geometries: mainRightWallSheet_Geo,
      positions: mainRightWallSheet_Positions,
    },
    mainFrontWallSheet: {
      geometries: mainFrontWallSheet_Geo,
      positions: mainFrontWallSheet_Positions,
    },
    mainBackWallSheet: {
      geometries: mainBackWallSheet_Geo,
      positions: mainBackWallSheet_Positions,
    },
    mainDividerWallSheet: {
      geometries: mainDividerWallSheet_Geo,
      positions: mainDividerWallSheet_Positions,
    },
    
    // Left lean-to wall sheets
    leftLeanToWallSheet: {
      geometries: leftLeanToWallSheet_Geo,
      positions: leftLeanToWallSheet_Positions,
    },
    leftLeanToFrontWallSheet: {
      geometries: leftLeanToFrontWallSheet_Geo,
      positions: leftLeanToFrontWallSheet_Positions,
    },
    leftLeanToBackWallSheet: {
      geometries: leftLeanToBackWallSheet_Geo,
      positions: leftLeanToBackWallSheet_Positions,
    },
    leftLeanToDividerWallSheet: {
      geometries: leftLeanToDividerWallSheet_Geo,
      positions: leftLeanToDividerWallSheet_Positions,
    },
    
    // Right lean-to wall sheets
    rightLeanToWallSheet: {
      geometries: rightLeanToWallSheet_Geo,
      positions: rightLeanToWallSheet_Positions,
    },
    rightLeanToFrontWallSheet: {
      geometries: rightLeanToFrontWallSheet_Geo,
      positions: rightLeanToFrontWallSheet_Positions,
    },
    rightLeanToBackWallSheet: {
      geometries: rightLeanToBackWallSheet_Geo,
      positions: rightLeanToBackWallSheet_Positions,
    },
    rightLeanToDividerWallSheet: {
      geometries: rightLeanToDividerWallSheet_Geo,
      positions: rightLeanToDividerWallSheet_Positions,
    },
  };
}
