import {
  post_size,
  EP_web,
  EP_flange,
  Rafter_web,
  Rafter_flange,
  Footing_depth,
  Footing_size,
  Roof_monoclad_height,
  Roof_monoclad_thickness,
  Gutter_offset,
  Roof_sheet_width,
  Purlin_row_n,
  Gable_purlin_row_n,
  knee_web,
  knee_flange,
} from '@/constants/carport/CarportParams';

function calculateGeometriesAndPositions(
  span: number = 6000,
  pitch: number = 15,
  eaveheight: number = 2400,
  overhang: number = 1000,
  length: number = 12000,
  leftLeanTo: boolean = false,
  leftLeanToSpan: number = 1000,
  leftLeanToDropHeight: number = 500,
  rightLeanTo: boolean = false,
  rightLeanToSpan: number = 1000,
  rightLeanToDropHeight: number = 500,
  // bay_no: number = 5,
) {
  const bay_no = Math.max(1, Math.floor(length / 4000));

  // Calculations

  const span_drop = (span - EP_flange * 2 - post_size) * Math.tan((pitch * Math.PI) / 180);
  // const span_drop = 200;

  // Post
  const post_left_Positions = [];
  const post_left_Geo = [];
  const post_right_Positions = [];
  const post_right_Geo = [];

  // Wall for Skillion Attached
  const wall_Geo: { geo_x: number; geo_y: number; geo_z: number }[] = [];
  const wall_Positions: { x: number; y: number; z: number }[] = [];

  // Left Lean-to Post
  const leanto_post_left_Positions = [];
  const leanto_post_left_Geo = [];
  
  // Right Lean-to Post
  const leanto_post_right_Positions = [];
  const leanto_post_right_Geo = [];

  const gable_post_left_Positions = [];
  const gable_post_left_Geo = [];
  const gable_post_right_Positions = [];
  const gable_post_right_Geo = [];
  
  // Gable Lean-to Right Post
  const gable_leanto_post_right_Positions = [];
  const gable_leanto_post_right_Geo = [];
  

  // Gable Lean-to Right Rafter
  const gable_leanto_rafter_right_Positions = [];
  const gable_leanto_rafter_right_Geo = {
    geo_x: Rafter_flange / 1000,
    geo_y: Rafter_web / 1000,
    geo_z: 0, // Will be calculated later if rightLeanTo is enabled
    flange_thickness: 0.002,
    web_thickness: 0.002,
  };
  
  // Gable Lean-to Right Purlin
  const gable_leanto_right_flat_purlin_Positions = [];
  const gable_leanto_right_flat_purlin_Geo = {
    geo_x: 0, // Will be set later if rightLeanTo is enabled
    geo_y: 0, // Will be set later if rightLeanTo is enabled
    geo_z: 0, // Will be set later if rightLeanTo is enabled
    flange_thickness: 0.002,
  };
  
  // Gable Lean-to Right Roof
  const gable_leanto_right_roof_Positions = [];
  const gable_leanto_right_roof_Geo = [];

  // Left Post
  for (let i = 0; i <= bay_no; i++) {
    const geo_x = post_size / 1000;
    const geo_y = post_size / 1000;
    const geo_z = (eaveheight + EP_flange * Math.tan((pitch * Math.PI) / 180)) / 1000;
    post_left_Geo.push({ geo_x, geo_y, geo_z });

    const x = (overhang + post_size / 2 + Rafter_flange) / 1000 - post_size / 2 / 1000;
    const y =
      i === bay_no
        ? (length - EP_flange - post_size / 2) / 1000 - post_size / 2 / 1000
        : (post_size / 2 + EP_flange / 2 + (i * length) / bay_no) / 1000 - post_size / 2 / 1000;
    const z = 0;
    post_left_Positions.push({ x, y, z });
  }

  // Garble Left Post
  for (let i = 0; i <= bay_no; i++) {
    const geo_x = post_size / 1000;
    const geo_y = post_size / 1000;
    const geo_z = (eaveheight + EP_flange * Math.tan((pitch * Math.PI) / 180)) / 1000;
    gable_post_left_Geo.push({ geo_x, geo_y, geo_z });

    const x = (overhang + post_size / 2 + Rafter_flange) / 1000 - post_size / 2 / 1000;
    const y =
      i === bay_no
        ? (length - post_size / 2) / 1000 - post_size / 2 / 1000
        : (post_size / 2 + (i * length) / bay_no) / 1000 - post_size / 2 / 1000;
    const z = 0;
    gable_post_left_Positions.push({ x, y, z });
  }

  // Right Post
  for (let i = 0; i <= bay_no; i++) {
    const geo_x = post_size / 1000;
    const geo_y = post_size / 1000;
    const geo_z =
      (eaveheight + (span - EP_flange - post_size) * Math.tan((pitch * Math.PI) / 180)) / 1000;
    post_right_Geo.push({ geo_x, geo_y, geo_z });

    const x = (overhang + span - Rafter_flange - post_size / 2) / 1000 - post_size / 2 / 1000;
    const y =
      i === bay_no
        ? (length - EP_flange - post_size / 2) / 1000 - post_size / 2 / 1000
        : (post_size / 2 + EP_flange / 2 + (i * length) / bay_no) / 1000 - post_size / 2 / 1000;
    const z = 0;
    post_right_Positions.push({ x, y, z });
  }
  
  // Wall for Skillion Attached
  const wallThickness = 200 / 1000; // 200mm converted to meters
  const wallHeight = (eaveheight * 1.5) / 1000; // 1.5 times the eave height, converted to meters
  const wallLength = length / 1000; // Full length of carport, converted to meters
  wall_Geo.push({ geo_x: wallThickness, geo_y: wallHeight, geo_z: wallLength });
  
  // Position the wall at the right edge of the carport
  const wallX = (overhang + span - Rafter_flange - post_size / 2) / 1000 - post_size / 2 / 1000; // Same as right posts
  const wallY = length / 2000; // Center of the carport length
  const wallZ = wallHeight / 2; // Half height to center it vertically
  wall_Positions.push({ x: wallX, y: wallY, z: wallZ });

  // Gable Right Post
  for (let i = 0; i <= bay_no; i++) {
    const geo_x = post_size / 1000;
    const geo_y = post_size / 1000;
    const geo_z = (eaveheight + EP_flange * Math.tan((pitch * Math.PI) / 180)) / 1000;
    gable_post_right_Geo.push({ geo_x, geo_y, geo_z });

    const x = (overhang + span - Rafter_flange - post_size / 2) / 1000 - post_size / 2 / 1000;
    const y =
      i === bay_no
        ? (length - post_size / 2) / 1000 - post_size / 2 / 1000
        : (post_size / 2 + (i * length) / bay_no) / 1000 - post_size / 2 / 1000;
    const z = 0;
    gable_post_right_Positions.push({ x, y, z });
  }
  
  // Left Lean-to Post - only create if leftLeanTo is enabled
  if (leftLeanTo) {
    for (let i = 0; i <= bay_no; i++) {
      const geo_x = post_size / 1000;
      const geo_y = post_size / 1000;
      // Lean-to posts height based on main height minus drop height
      const geo_z = (eaveheight - leftLeanToDropHeight) / 1000;
      leanto_post_left_Geo.push({ geo_x, geo_y, geo_z });

      // Position posts to the left of the main structure based on leftLeanToSpan
      const x = (overhang - leftLeanToSpan) / 1000 + post_size / 2 / 1000;
      const y =
        i === bay_no
          ? (length - post_size / 2) / 1000 - post_size / 2 / 1000
          : (post_size / 2 + (i * length) / bay_no) / 1000 - post_size / 2 / 1000;
      const z = 0;
      leanto_post_left_Positions.push({ x, y, z });
    }
  }
  
  // Right Lean-to Posts
  if (rightLeanTo) {
    for (let i = 0; i <= bay_no; i++) {
      const geo_x = post_size / 1000;
      const geo_y = post_size / 1000;
      // Lean-to posts height based on main height minus drop height plus pitch-related height increase
      const geo_z = (eaveheight - rightLeanToDropHeight + (span ) * Math.tan((pitch * Math.PI) / 180)) / 1000;
      leanto_post_right_Geo.push({ geo_x, geo_y, geo_z });

      // Position posts to the right of the main structure based on rightLeanToSpan
      const x = (overhang + span + rightLeanToSpan - post_size) / 1000 - post_size / 2 / 1000;
      const y =
        i === bay_no
          ? (length - post_size / 2) / 1000 - post_size / 2 / 1000
          : (post_size / 2 + (i * length) / bay_no) / 1000 - post_size / 2 / 1000;
      const z = 0;
      leanto_post_right_Positions.push({ x, y, z });
      
      // Gable Lean-to Right Posts
      const gable_geo_z = (eaveheight - rightLeanToDropHeight) / 1000;
      gable_leanto_post_right_Geo.push({ geo_x, geo_y, geo_z: gable_geo_z });
      
      // Use the same x, y positions as regular lean-to posts
      gable_leanto_post_right_Positions.push({ x, y, z });
      
      // Gable Lean-to Right Rafter calculations
      if (i === 0) {
        // Only need to set this once
        gable_leanto_rafter_right_Geo.geo_z = (rightLeanToSpan - post_size) / 1000 / Math.cos((2 * Math.PI) / 180); // Using lean-to pitch of 2 degrees
        
        // Set the purlin geometry to match the left lean-to purlin sections
        gable_leanto_right_flat_purlin_Geo.geo_x = Rafter_flange / 1000; // Same as left lean-to
        gable_leanto_right_flat_purlin_Geo.geo_y = Rafter_web / 1000; // Same as left lean-to
        gable_leanto_right_flat_purlin_Geo.geo_z = length / 1000 - (1.5 * Rafter_flange) / 1000; // Same as left lean-to
      }
      
      // Calculate rafter position for gable lean-to right
      // Account for the gable pitch height increase
      const pitchHeightIncrease = 0;
      const leanto_right_span_drop = (rightLeanToSpan - post_size) * Math.tan((2 * Math.PI) / 180); // Using 2 degree pitch
      // Adjust the z-position to account for the gable pitch height increase
      const gable_leanto_right_z = ((eaveheight - rightLeanToDropHeight ) + leanto_right_span_drop / 2 - (0.4 * Rafter_web) / Math.cos((2 * Math.PI) / 180)) / 1000;
      
      // Add the rafter position
      gable_leanto_rafter_right_Positions.push({ x: x-rightLeanToSpan/2000+post_size/2000, 
        y: y+32/1000, 
        z: gable_leanto_right_z });
    }
  }

  // Rafter geometry and positions
  const rafter_Geo = {
    geo_x: Rafter_flange / 1000,
    geo_y: Rafter_web / 1000,
    geo_z: (span + 2 * overhang) / 1000 / Math.cos((pitch * Math.PI) / 180),
    flange_thickness: 0.002,
    web_thickness: 0.002,
  };

  // Left Lean-to Rafter geometry - use same structure as main rafter
  const leanto_rafter_Geo = {
    geo_x: Rafter_flange / 1000,
    geo_y: Rafter_web / 1000,
    geo_z: (leftLeanToSpan-post_size) / 1000 / Math.cos((2 * Math.PI) / 180), // Using lean-to pitch of 2 degrees
    flange_thickness: 0.002,
    web_thickness: 0.002,
  };
  
  // Right Lean-to Rafter geometry - use same structure as main rafter
  const leanto_rafter_right_Geo = {
    geo_x: Rafter_flange / 1000,
    geo_y: Rafter_web / 1000,
    geo_z: (rightLeanToSpan-post_size) / 1000 / Math.cos((2 * Math.PI) / 180), // Using lean-to pitch of 2 degrees
    flange_thickness: 0.002,
    web_thickness: 0.002,
  };

  const rafter_Positions = [];
  const leanto_rafter_Positions = [];
  const leanto_rafter_right_Positions = [];

  for (let i = 0; i <= bay_no; i++) {
    const x = (span + 2 * overhang) / 1000 / 2;
    const y =
      i === bay_no
        ? (length - Rafter_flange * 0.5) / 1000
        : i === 0
          ? 0
          : (i * (length / bay_no)) / 1000;
    const z =
      (eaveheight + span_drop / 2 - (0.25 * Rafter_web) / Math.cos((pitch * Math.PI) / 180)) / 1000;
    rafter_Positions.push({ x, y, z });
    
    // Left Lean-to Rafter positions - only add if leftLeanTo is enabled
    if (leftLeanTo) {
      // Calculate lean-to position based on the leftLeanToSpan parameter
      const leanto_x = (overhang - leftLeanToSpan/2+post_size) / 1000; // Position at the left edge of lean-to span
      const leanto_y = y; // Same y-position as main rafters
      // Calculate lean-to height using the same formula as main rafter, but with height minus drop
      const leanto_span_drop = (leftLeanToSpan - post_size) * Math.tan((2 * Math.PI) / 180); // Using 2 degree pitch
      const leanto_z = ((eaveheight - leftLeanToDropHeight) + leanto_span_drop / 2 - (0.4 * Rafter_web) / Math.cos((2 * Math.PI) / 180)) / 1000;
      leanto_rafter_Positions.push({ x: leanto_x, y: leanto_y, z: leanto_z });
    }
    
    // Right Lean-to Rafter positions - only add if rightLeanTo is enabled
    if (rightLeanTo) {
      // Calculate lean-to position based on the rightLeanToSpan parameter
      const leanto_right_x = (overhang + span + rightLeanToSpan/2-post_size) / 1000; // Position at the right edge of lean-to span
      const leanto_right_y = y; // Same y-position as main rafters
      // Calculate lean-to height using the same formula as main rafter, but with height minus drop plus pitch-related height increase
      const leanto_right_span_drop = (rightLeanToSpan - post_size) * Math.tan((2 * Math.PI) / 180); // Using 2 degree pitch
      const leanto_right_z = ((eaveheight - rightLeanToDropHeight+span_drop ) + leanto_right_span_drop / 2 - (0.4 * Rafter_web) / Math.cos((2 * Math.PI) / 180)) / 1000;
      leanto_rafter_right_Positions.push({ x: leanto_right_x, y: leanto_right_y, z: leanto_right_z });
    }
  }

  // Flat purlin geometry and positions
  // Dynamic number of purlin rows based on span
  let purlin_count;
  
  // For span = 4m, use 6 purlins
  // For every 1m increase, add 1 more purlin
  if (span <= 3000) {
    purlin_count = Purlin_row_n-4; // 6 purlins for span <= 4m
  } else if (span <= 4000) {
    purlin_count = Purlin_row_n -3; // 7 purlins for span <= 5m
  }else if (span <= 5000) {
    purlin_count = Purlin_row_n -2; // 7 purlins for span <= 5m
  }
  
  
  
  else if (span <= 6000) {
    purlin_count = Purlin_row_n -1; // 7 purlins for span <= 5m
  } else if (span <= 8000) {
    purlin_count = Purlin_row_n; // 7 purlins for span <= 5m
  }else if (span <= 10000) {
    purlin_count = Purlin_row_n + 1; // 7 purlins for span <= 5m
  }
  else if (span <= 12000) {
    purlin_count = Purlin_row_n + 2; // 8 purlins for span <= 6m
  } else {
    purlin_count = Purlin_row_n + 3; // 9 purlins for span > 6m
  }
  const x_dif = (span - EP_flange)/(purlin_count-1);
  const z_dif = (span - EP_flange) * (Math.tan(pitch * Math.PI/180))/(purlin_count-1);

  const flat_purlin_Positions = [];
  const flat_purlin_Geo = {
    geo_x : Rafter_flange / 1000,
    geo_y : Rafter_web / 1000,
    geo_z : length / 1000 - (1.5 * Rafter_flange) / 1000
  }

  for (let j = 0; j < purlin_count; j++) {
    const x = (overhang + x_dif*j + 0.5 * Rafter_flange)/1000;
    const y = (length/1000)/2 - 0.25*EP_flange/1000;
    const z = (eaveheight + z_dif*j - 0.3*EP_web/Math.cos(pitch * Math.PI/180))/1000
    flat_purlin_Positions.push({ x, y, z });
  }
  
  // Gable Left Rafter geometry and positions

  const gable_rafter_length = (0.5*span - EP_flange - post_size - Rafter_web * Math.sin(pitch*Math.PI/180)) / Math.cos(pitch*Math.PI/180) / 1000;
  const gable_left_rafter_Geo = {
    geo_x: Rafter_flange / 1000,
    geo_y: Rafter_web / 1000,
    geo_z: gable_rafter_length,
    flange_thickness: 0.002,
    web_thickness: 0.002,
  };
  const gable_left_rafter_Positions = [];
  
  for (let i = 0; i <= bay_no; i++) {
    const x = ( 0.5*span + EP_flange + post_size - Rafter_web * Math.sin(pitch*Math.PI/180)) / 1000/2 + 0.5*Rafter_web*Math.sin(pitch*Math.PI/180)/1000; 
    // same with gable left post
    const y =
      i === bay_no
        ? (length - post_size / 2) / 1000 
        : (post_size / 2 + (i * length) / bay_no) / 1000;
    const z =
      (eaveheight 
        + (post_size + EP_flange ) * Math.tan(pitch*Math.PI/180)
        - (0.5 * Rafter_web + EP_web) / Math.cos((pitch * Math.PI) / 180)
        + 0.5 * gable_rafter_length * Math.sin(pitch*Math.PI/180) * 1000
        
      ) / 1000;
    gable_left_rafter_Positions.push({ x, y, z });
  }


  // Gable Right Rafter geometry and positions
  const gable_right_rafter_Geo = gable_left_rafter_Geo;
  const gable_right_rafter_Positions = [];
  for (let i = 0; i <= bay_no; i++) {
    const x = span / 1000 - gable_left_rafter_Positions[i].x;
    const y = gable_left_rafter_Positions[i].y;
    const z = gable_left_rafter_Positions[i].z;
    gable_right_rafter_Positions.push({ x, y, z });
  }

  // Gable purlin left geometry and positions

  // Dynamic number of gable purlins based on span
  let gable_purlin_count;
  
  // For span = 4m, use 6 purlins
  // For every 1m increase, add 1 more purlin
  if (span <= 3000) {
    gable_purlin_count = Math.max(1, Gable_purlin_row_n-3); // Ensure at least 1 purlin
  } else if (span <= 4000) {
    gable_purlin_count = Math.max(1, Gable_purlin_row_n-2);
  } else if (span <= 5000) {
    gable_purlin_count = Math.max(1, Gable_purlin_row_n-1);
  } else if (span <= 6000) {
    gable_purlin_count = Gable_purlin_row_n;
  } else if (span <= 8000) {
    gable_purlin_count = Gable_purlin_row_n+1;
  } else if (span <= 10000) {
    gable_purlin_count = Gable_purlin_row_n+2;
  } else if (span <= 12000) {
    gable_purlin_count = Gable_purlin_row_n+3;
  } else {
    gable_purlin_count = Gable_purlin_row_n+4;
  }
  
  // Debug log to check the calculated purlin count
  //console.log('Span:', span, 'Gable purlin count:', gable_purlin_count);

  const gable_left_purlin_Positions = [];
  const gable_left_purlin_Geo = {
    web: 180/1000,
    flangeE: 64/1000,
    flangeF: 60/1000,
    lip: 12.5/1000,
    thickness: 3/1000,
    length: length / 1000 - (1.5 * Rafter_flange) / 1000,
  }
  // Ensure we have a valid divisor to prevent NaN
  const gable_x_dif = (0.5*span - EP_flange - post_size - Rafter_web * Math.sin(pitch*Math.PI/180))/(gable_purlin_count || 1)/1000;
  const gable_z_dif = gable_x_dif*Math.tan(pitch*Math.PI/180);
  const gable_z_start = (eaveheight 
                          //+ (post_size + EP_flange)* Math.tan(pitch*Math.PI/180) 
                          - 0.5* EP_web/Math.cos(pitch*Math.PI/180) 
                          //- 0.5*Rafter_web*Math.cos(pitch*Math.PI/180)
                        )/1000;
  for (let i = 0; i < gable_purlin_count; i++) {
    const x = overhang/1000 + (i+1)*gable_x_dif;
    const y = length/2/1000;
    const z = gable_z_start + (i+1)*gable_z_dif - (pitch*2-6)/1000+30/1000;  // very custom adjustment
    gable_left_purlin_Positions.push({ x, y, z });
  }

  // Gable purlin right geometry and positions

  const gable_right_purlin_Positions = [];
  const gable_right_purlin_Geo = gable_left_purlin_Geo;

  for (let i = 0; i < gable_purlin_count; i++) {
    const x = (overhang+span)/1000 - (i+1)*gable_x_dif;
    const y = length/2/1000;
    const z = gable_z_start + (i+1)*gable_z_dif - (pitch*2-6)/1000+30/1000;  // very custom adjustment
    gable_right_purlin_Positions.push({ x, y, z });
  }


  // Left EP geometry and positions

  const left_EP_Geo = {
    geo_x: EP_flange / 1000,
    geo_y: EP_web / 1000,
    geo_z: length / 1000 - (1.5 * Rafter_flange) / 1000,
    flange_thickness: 0.002,
    web_thickness: 0.002,
  };
  const left_EP_Positions = [];

  for (let i = 0; i <= 0; i++) {
    const x = (0.5 * EP_flange) / 1000;
    const y = length / 2 / 1000 - (0.25 * Rafter_flange) / 1000;
    //const z = (eaveheight - EP_web) / 1000;
    const z =
      (eaveheight -
        overhang * Math.tan((pitch * Math.PI) / 180) -
        (0.25 * Rafter_web) / Math.cos((pitch * Math.PI) / 180)) /
      1000;
    left_EP_Positions.push({ x, y, z });
  }

  // Right EP geometry and positions

  const right_EP_Geo = {
    geo_x: EP_flange / 1000,
    geo_y: EP_web / 1000,
    geo_z: length / 1000 - (1.5 * Rafter_flange) / 1000,
    flange_thickness: 0.002,
    web_thickness: 0.002,
  };
  const right_EP_Positions = [];

  for (let i = 0; i <= 0; i++) {
    const x = (overhang * 2 + span - 0.5 * EP_flange) / 1000;
    const y = length / 2 / 1000 - (0.25 * Rafter_flange) / 1000;
    const z =
      (eaveheight +
        (overhang + span) * Math.tan((pitch * Math.PI) / 180) -
        (0.25 * Rafter_web) / Math.cos((pitch * Math.PI) / 180)) /
      1000;
    right_EP_Positions.push({ x, y, z });
  }


  // Gable Left EP geometry and positions
  const gable_left_EP_Geo = {
    geo_x: EP_flange / 1000,
    geo_y: EP_web / 1000,
    geo_z: length / 1000,
    flange_thickness: 0.002,
    web_thickness: 0.002,
  };
  const gable_left_EP_Positions = [];
  for (let i = 0; i <= 0; i++) {
    const x = 0.5 * EP_flange / 1000;
    const y = length / 2 / 1000;
    const z =  (eaveheight + EP_flange * Math.tan((pitch * Math.PI) / 180) - EP_web * 0.5) / 1000;
    gable_left_EP_Positions.push({ x, y, z });
  }

  // Gable Right EP geometry and positions
  const gable_right_EP_Geo = gable_left_EP_Geo;
  const gable_right_EP_Positions = [];
  for (let i = 0; i <= 0; i++) {
    const x = (overhang * 2 + span - 0.5 * EP_flange) / 1000;
    const y = length / 2 / 1000;
    const z =  (eaveheight + EP_flange * Math.tan((pitch * Math.PI) / 180) - EP_web * 0.5) / 1000;
    gable_right_EP_Positions.push({ x, y, z });
  }

  // Flat Footing geometry and positions

  const footing_Geo = {
    radius: Footing_size / 1000 / 2,
    height: Footing_depth / 1000,
  };

  const footing_Positions = [...post_left_Positions, ...post_right_Positions].map((position) => ({
    x: position.x + post_size / 2 / 1000,
    y: position.y + post_size / 2 / 1000,
    z: position.z - footing_Geo.height / 2,
  }));

  // Gable Footing geometry and positions

  const gable_footing_Geo = {
    radius: Footing_size / 1000 / 2,
    height: Footing_depth / 1000,
  };

  const gable_footing_Positions = [...gable_post_left_Positions, ...gable_post_right_Positions].map((position) => ({
    x: position.x + post_size / 2 / 1000,
    y: position.y + post_size / 2 / 1000,
    z: position.z - footing_Geo.height / 2,
  }));

  // Slab geometry and position

  const slab_Geo = {
    width: span / 1000,
    length: length / 1000,
    thickness: 0.2,
  };

  // Left Lean-to Slab geometry - only create if leftLeanTo is enabled
  const leanto_slab_Geo = leftLeanTo ? {
    width: (leftLeanToSpan ) / 1000, // Width based on lean-to span parameter plus overhang
    length: length / 1000,       // Same length as main slab
    thickness: 0.2,              // Same thickness as main slab
  } : { width: 0, length: 0, thickness: 0 };
  
  // Right Lean-to Slab geometry - only create if rightLeanTo is enabled
  const leanto_right_slab_Geo = rightLeanTo ? {
    width: (rightLeanToSpan ) / 1000, // Width based on lean-to span parameter plus overhang
    length: length / 1000,       // Same length as main slab
    thickness: 0.2,              // Same thickness as main slab
  } : { width: 0, length: 0, thickness: 0 };

  //console.log('leftLeanToSpan', leanto_slab_Geo.width);
  
  const slab_Position = {
    x: span / 2 / 1000 + (2 * overhang) / 2 / 1000,
    y: length / 2 / 1000,
    z: -slab_Geo.thickness / 2, // Position the slab below the posts
  };
  
  // Left Lean-to Slab position - only create if leftLeanTo is enabled
  const leanto_slab_Position = leftLeanTo ? {
    x: overhang /1000 - leanto_slab_Geo.width / 2, // Position to attach to the left post
    y: length / 2 / 1000,      // Same y-position as main slab
    z: -leanto_slab_Geo.thickness / 2, // Same z-position as main slab
  } : { x: 0, y: 0, z: 0 };
  
  // Right Lean-to Slab position - only create if rightLeanTo is enabled
  const leanto_right_slab_Position = rightLeanTo ? {
    x: (overhang + span) /1000 + leanto_right_slab_Geo.width / 2, // Position to attach to the right post
    y: length / 2 / 1000,      // Same y-position as main slab
    z: -leanto_right_slab_Geo.thickness / 2, // Same z-position as main slab
  } : { x: 0, y: 0, z: 0 };

  // Left Lean-to Left EP geometry and positions - only create if leftLeanTo is enabled
  const leanto_left_EP_Geo = leftLeanTo ? {
    geo_x: EP_flange / 1000,
    geo_y: EP_web / 1000,
    geo_z: length / 1000 ,
    flange_thickness: 0.002,
    web_thickness: 0.002,
  } : { geo_x: 0, geo_y: 0, geo_z: 0, flange_thickness: 0, web_thickness: 0 };
  
  // Right Lean-to Right EP geometry and positions - only create if rightLeanTo is enabled
  const leanto_right_EP_Geo = rightLeanTo ? {
    geo_x: EP_flange / 1000,
    geo_y: EP_web / 1000,
    geo_z: length / 1000 ,
    flange_thickness: 0.002,
    web_thickness: 0.002,
  } : { geo_x: 0, geo_y: 0, geo_z: 0, flange_thickness: 0, web_thickness: 0 };

    // Gable  Lean-to Right EP geometry and positions - only create if rightLeanTo is enabled
    const gable_leanto_right_EP_Geo = rightLeanTo ? {
      geo_x: EP_flange / 1000,
      geo_y: EP_web / 1000,
      geo_z: length / 1000 ,
      flange_thickness: 0.002,
      web_thickness: 0.002,
    } : { geo_x: 0, geo_y: 0, geo_z: 0, flange_thickness: 0, web_thickness: 0 };
    const gable_leanto_right_EP_Positions = [];

    if (rightLeanTo) {
      const x = (overhang + span + rightLeanToSpan) / 1000;
      const y = length / 2 / 1000 - (0.25 * Rafter_flange) / 1000;
      const leanto_right_span_drop = (rightLeanToSpan) * Math.tan((2 * Math.PI) / 180); // Using 2 degree pitch
      const z = ((eaveheight  - rightLeanToDropHeight) - 
                (0.5 * Rafter_web) / Math.cos((2 * Math.PI) / 180)) / 1000;
      gable_leanto_right_EP_Positions.push({ x, y, z });
    }


  const leanto_left_EP_Positions = [];
  const leanto_right_EP_Positions = [];
  
  if (leftLeanTo) {
    const x = (overhang-leftLeanToSpan) / 1000;
    const y = length / 2 / 1000 - (0.25 * Rafter_flange) / 1000;
    const leanto_span_drop = (leftLeanToSpan ) * Math.tan((2 * Math.PI) / 180); // Using 2 degree pitch
    const z = ((eaveheight - leftLeanToDropHeight) - 
              (0.5 * Rafter_web) / Math.cos((2 * Math.PI) / 180)) / 1000;
    leanto_left_EP_Positions.push({ x, y, z });
  }
  
  if (rightLeanTo) {
    const x = (overhang + span + rightLeanToSpan) / 1000;
    const y = length / 2 / 1000 - (0.25 * Rafter_flange) / 1000;
    const leanto_right_span_drop = (rightLeanToSpan) * Math.tan((2 * Math.PI) / 180); // Using 2 degree pitch
    const z = ((eaveheight +span_drop - rightLeanToDropHeight) - 
              (0.5 * Rafter_web) / Math.cos((2 * Math.PI) / 180)) / 1000;
    leanto_right_EP_Positions.push({ x, y, z });
  }
  
  // Left Lean-to Flat Purlin geometry and positions - only create if leftLeanTo is enabled
  const leanto_flat_purlin_Geo = leftLeanTo ? {
    geo_x: Rafter_flange / 1000,
    geo_y: Rafter_web / 1000,
    geo_z: length / 1000 - (1.5 * Rafter_flange) / 1000
  } : { geo_x: 0, geo_y: 0, geo_z: 0 };
  
  // Right Lean-to Flat Purlin geometry and positions - only create if rightLeanTo is enabled
  const leanto_right_flat_purlin_Geo = rightLeanTo ? {
    geo_x: Rafter_flange / 1000,
    geo_y: Rafter_web / 1000,
    geo_z: length / 1000 - (1.5 * Rafter_flange) / 1000
  } : { geo_x: 0, geo_y: 0, geo_z: 0 };
  
  const leanto_flat_purlin_Positions = [];
  const leanto_right_flat_purlin_Positions = [];
  
  if (leftLeanTo) {
    // Calculate the number of purlins for the lean-to based on its span
    let leanto_purlin_count;
    if (leftLeanToSpan <= 1000) {
      leanto_purlin_count = 2; // Minimum 2 purlins for small lean-to
    } else if (leftLeanToSpan <= 2000) {
      leanto_purlin_count = 3;
    } else {
      leanto_purlin_count = 4;
    }
    
    // Calculate the spacing between purlins
    const leanto_x_dif = (leftLeanToSpan - post_size) / (leanto_purlin_count - 1);
    const leanto_z_dif = (leftLeanToSpan - post_size) * (Math.tan((2 * Math.PI) / 180)) / (leanto_purlin_count - 1);
    
    for (let j = 1; j < leanto_purlin_count; j++) {
      const x = (overhang - leftLeanToSpan + leanto_x_dif * j + 0.5 * Rafter_flange) / 1000;
      const y = (length / 1000) / 2 - 0.25 * EP_flange / 1000;
      const z = ((eaveheight - leftLeanToDropHeight) + leanto_z_dif * j - 0.42 * EP_web / Math.cos((2 * Math.PI) / 180)) / 1000;
      leanto_flat_purlin_Positions.push({ x, y, z });
    }
  }
  
  if (rightLeanTo) {
    // Calculate the number of purlins for the right lean-to based on its span
    let leanto_right_purlin_count;
    if (rightLeanToSpan <= 1000) {
      leanto_right_purlin_count = 2; // Minimum 2 purlins for small lean-to
    } else if (rightLeanToSpan <= 2000) {
      leanto_right_purlin_count = 3;
    } else {
      leanto_right_purlin_count = 4;
    }
    
    // Calculate the spacing between purlins
    const leanto_right_x_dif = (rightLeanToSpan - post_size) / (leanto_right_purlin_count - 1);
    const leanto_right_z_dif = (rightLeanToSpan - post_size) * (Math.tan((2 * Math.PI) / 180)) / (leanto_right_purlin_count - 1);
    
    for (let j = 0; j < leanto_right_purlin_count-1; j++) {
      const x = (overhang + span + leanto_right_x_dif * j - 0.5 * Rafter_flange) / 1000;
      const y = (length / 1000) / 2 - 0.25 * EP_flange / 1000;
      const z = ((eaveheight +span_drop - rightLeanToDropHeight ) + leanto_right_z_dif * (leanto_right_purlin_count-j) - 0.7 * EP_web / Math.cos((2 * Math.PI) / 180)) / 1000;
      leanto_right_flat_purlin_Positions.push({ x, y, z });
      
      // Gable lean-to right purlin positions
      // No pitch height increase needed, matching the gable right lean-to rafter height
      const pitchHeightIncrease = 0;
      // Adjust the z-position to match the gable right lean-to rafter height
      const gable_z = ((eaveheight - rightLeanToDropHeight) + leanto_right_z_dif * (leanto_right_purlin_count-j) - 0.7 * EP_web / Math.cos((2 * Math.PI) / 180)) / 1000;
      gable_leanto_right_flat_purlin_Positions.push({ x, y, z: gable_z });
    }
  }
  
  // Left Lean-to Footing geometry and positions - only create if leftLeanTo is enabled
  const leanto_footing_Geo = leftLeanTo ? {
    radius: Footing_size / 1000 / 2,
    height: Footing_depth / 1000,
  } : { radius: 0, height: 0 };
  
  const leanto_footing_Positions: { x: number; y: number; z: number }[] = [];
  
  if (leftLeanTo) {
    leanto_post_left_Positions.forEach((position: { x: number; y: number; z: number }) => {
      leanto_footing_Positions.push({
        x: position.x + post_size / 2 / 1000,
        y: position.y + post_size / 2 / 1000,
        z: position.z - footing_Geo.height / 2,
      });
    });
  }
  
  // Right Lean-to Footing geometry and positions - only create if rightLeanTo is enabled
  const leanto_right_footing_Geo = rightLeanTo ? {
    radius: Footing_size / 1000 / 2,
    height: Footing_depth / 1000,
  } : { radius: 0, height: 0 };
  
  const leanto_right_footing_Positions: { x: number; y: number; z: number }[] = [];
  
  if (rightLeanTo) {
    leanto_post_right_Positions.forEach((position: { x: number; y: number; z: number }) => {
      leanto_right_footing_Positions.push({
        x: position.x + post_size / 2 / 1000,
        y: position.y + post_size / 2 / 1000,
        z: position.z - footing_Geo.height / 2,
      });
    });
  }
  
  // Gable Right Lean-to Footing geometry and positions - only create if rightLeanTo is enabled
  const gable_leanto_right_footing_Geo = rightLeanTo ? {
    radius: Footing_size / 1000 / 2,
    height: Footing_depth / 1000,
  } : { radius: 0, height: 0 };
  
  const gable_leanto_right_footing_Positions: { x: number; y: number; z: number }[] = [];
  
  if (rightLeanTo) {
    gable_leanto_post_right_Positions.forEach((position: { x: number; y: number; z: number }) => {
      gable_leanto_right_footing_Positions.push({
        x: position.x + post_size / 2 / 1000,
        y: position.y + post_size / 2 / 1000,
        z: position.z - footing_Geo.height / 2,
      });
    });
  }
  
  // Main Left Drop Sheets - vertical sheets connecting main post height to left lean-to post height
  const main_left_drop_Positions: { x: number; y: number; z: number }[] = [];
  const main_left_drop_Geo: { height: number; thickness: number; singlelength: number; length: number }[] = [];
  
  if (leftLeanTo) {
    const drop_full_sheet_number = Math.floor(length / Roof_sheet_width);
    const drop_last_sheet_width = length - drop_full_sheet_number * Roof_sheet_width;
    
    // Add full-width drop sheets
    for (let i = 0; i < drop_full_sheet_number; i++) {
      const drop_full_sheet_Geo = {
        height: Roof_monoclad_height / 1000,
        thickness: Roof_monoclad_thickness / 1000,
        singlelength: Roof_sheet_width / 1000,
        length: (leftLeanToDropHeight - 75) / 1000, // Height difference between main and lean-to
      };
      main_left_drop_Geo.push(drop_full_sheet_Geo);
      
      const x = overhang / 1000; // Position at the main left post line
      const y = (((i + i + 1) / 2) * Roof_sheet_width) / 1000; // Same y-positioning as roof
      const z = (eaveheight - leftLeanToDropHeight/2) / 1000; // Middle point between main and lean-to heights
      main_left_drop_Positions.push({ x, y, z });
    }
    
    // Add the last (potentially partial width) drop sheet
    if (drop_last_sheet_width > 0) {
      const drop_last_sheet_Geo = {
        height: Roof_monoclad_height / 1000,
        thickness: Roof_monoclad_thickness / 1000,
        singlelength: drop_last_sheet_width / 1000,
        length: leftLeanToDropHeight / 1000,
      };
      main_left_drop_Geo.push(drop_last_sheet_Geo);
      
      const x = overhang / 1000;
      const y = (length - drop_last_sheet_width / 2) / 1000;
      const z = (eaveheight - leftLeanToDropHeight/2) / 1000;
      main_left_drop_Positions.push({ x, y, z });
    }
  }
  
  // Main Right Drop Sheets - vertical sheets connecting main post height to right lean-to post height
  const main_right_drop_Positions: { x: number; y: number; z: number }[] = [];
  const main_right_drop_Geo: { height: number; thickness: number; singlelength: number; length: number }[] = [];
  
  if (rightLeanTo) {
    const drop_full_sheet_number = Math.floor(length / Roof_sheet_width);
    const drop_last_sheet_width = length - drop_full_sheet_number * Roof_sheet_width;
    
    // Add full-width drop sheets
    for (let i = 0; i < drop_full_sheet_number; i++) {
      const drop_full_sheet_Geo = {
        height: Roof_monoclad_height / 1000,
        thickness: Roof_monoclad_thickness / 1000,
        singlelength: Roof_sheet_width / 1000,
        length: (rightLeanToDropHeight - 75) / 1000, // Height difference between main and lean-to
      };
      main_right_drop_Geo.push(drop_full_sheet_Geo);
      
      const x = (overhang + span) / 1000; // Position at the main right post line
      const y = (((i + i + 1) / 2) * Roof_sheet_width) / 1000; // Same y-positioning as roof
      const z = (eaveheight - rightLeanToDropHeight/2) / 1000; // Middle point between main and lean-to heights
      main_right_drop_Positions.push({ x, y, z });
    }
    
    // Add the last (potentially partial width) drop sheet
    if (drop_last_sheet_width > 0) {
      const drop_last_sheet_Geo = {
        height: Roof_monoclad_height / 1000,
        thickness: Roof_monoclad_thickness / 1000,
        singlelength: drop_last_sheet_width / 1000,
        length: rightLeanToDropHeight / 1000,
      };
      main_right_drop_Geo.push(drop_last_sheet_Geo);
      
      const x = (overhang + span) / 1000;
      const y = (length - drop_last_sheet_width / 2) / 1000;
      const z = (eaveheight - rightLeanToDropHeight/2) / 1000;
      main_right_drop_Positions.push({ x, y, z });
    }
  }
  

  // Gable Main Right Drop Sheets - vertical sheets connecting main post height to right lean-to post height
  const gable_main_right_drop_Positions: { x: number; y: number; z: number }[] = [];
  const gable_main_right_drop_Geo: { height: number; thickness: number; singlelength: number; length: number }[] = [];
  
  if (rightLeanTo) {
    const drop_full_sheet_number = Math.floor(length / Roof_sheet_width);
    const drop_last_sheet_width = length - drop_full_sheet_number * Roof_sheet_width;
    
    // Add full-width drop sheets
    for (let i = 0; i < drop_full_sheet_number; i++) {
      const drop_full_sheet_Geo = {
        height: Roof_monoclad_height / 1000,
        thickness: Roof_monoclad_thickness / 1000,
        singlelength: Roof_sheet_width / 1000,
        length: (rightLeanToDropHeight - 75) / 1000, // Height difference between main and lean-to
      };
      gable_main_right_drop_Geo.push(drop_full_sheet_Geo);
      
      const x = (overhang + span) / 1000; // Position at the main right post line
      const y = (((i + i + 1) / 2) * Roof_sheet_width) / 1000; // Same y-positioning as roof
      const z = (eaveheight - rightLeanToDropHeight/2) / 1000; // Middle point between main and lean-to heights
      gable_main_right_drop_Positions.push({ x, y, z });
    }
    
    // Add the last (potentially partial width) drop sheet
    if (drop_last_sheet_width > 0) {
      const drop_last_sheet_Geo = {
        height: Roof_monoclad_height / 1000,
        thickness: Roof_monoclad_thickness / 1000,
        singlelength: drop_last_sheet_width / 1000,
        length: rightLeanToDropHeight / 1000,
      };
      gable_main_right_drop_Geo.push(drop_last_sheet_Geo);
      
      const x = (overhang + span) / 1000;
      const y = (length - drop_last_sheet_width / 2) / 1000;
      const z = (eaveheight - rightLeanToDropHeight/2) / 1000;
      gable_main_right_drop_Positions.push({ x, y, z });
    }
  }
  
  // Left Lean-to Roof geometry and positions - only create if leftLeanTo is enabled
  const leanto_roof_Positions: { x: number; y: number; z: number }[] = [];
  const leanto_roof_Geo: { height: number; thickness: number; singlelength: number; length: number }[] = [];
  
  if (leftLeanTo) {
    const leanto_full_roof_sheet_number = Math.floor(length / Roof_sheet_width);
    const leanto_last_roof_sheet_width = length - leanto_full_roof_sheet_number * Roof_sheet_width;
    const leanto_height_offset = Roof_monoclad_height / 2;
    const leanto_span_drop = leftLeanToSpan * Math.tan((2 * Math.PI) / 180); // Using 2 degree pitch for lean-to
    
    // Add full-width roof sheets
    for (let i = 0; i < leanto_full_roof_sheet_number; i++) {
      const leanto_full_roof_plate_Geo = {
        height: Roof_monoclad_height / 1000,
        thickness: Roof_monoclad_thickness / 1000,
        singlelength: Roof_sheet_width / 1000,
        length: leftLeanToSpan / 1000 / Math.cos((2 * Math.PI) / 180), // No overhang, just the lean-to span
      };
      leanto_roof_Geo.push(leanto_full_roof_plate_Geo);
      
      const x = (overhang - leftLeanToSpan/2) / 1000; // Center of lean-to span
      const y = (((i + i + 1) / 2) * Roof_sheet_width) / 1000; // Same y-positioning as main roof
      const z = ((eaveheight - leftLeanToDropHeight) + 
                leanto_height_offset + 
                leanto_span_drop / 2 - 
                (0.25 * Rafter_web) / Math.cos((2 * Math.PI) / 180)) / 1000+
                50/1000;
      leanto_roof_Positions.push({ x, y, z });
    }
    
    // Add the last (potentially partial width) roof sheet
    if (leanto_last_roof_sheet_width > 0) {
      const leanto_last_roof_plate_Geo = {
        height: Roof_monoclad_height / 1000,
        thickness: Roof_monoclad_thickness / 1000,
        singlelength: leanto_last_roof_sheet_width / 1000,
        length: leftLeanToSpan / 1000 / Math.cos((2 * Math.PI) / 180),
      };
      leanto_roof_Geo.push(leanto_last_roof_plate_Geo);
      
      const x = (overhang - leftLeanToSpan/2) / 1000;
      const y = (length - leanto_last_roof_sheet_width / 2) / 1000;
      const z = ((eaveheight - leftLeanToDropHeight) + 
                leanto_height_offset + 
                leanto_span_drop / 2 - 
                (0.25 * Rafter_web) / Math.cos((2 * Math.PI) / 180)) / 1000 +
                50/1000;
      leanto_roof_Positions.push({ x, y, z });
    }
  }
  
  // Right Lean-to Roof geometry and positions - only create if rightLeanTo is enabled
  const leanto_right_roof_Positions: { x: number; y: number; z: number; rot_y?: number }[] = [];
  const leanto_right_roof_Geo: { height: number; thickness: number; singlelength: number; length: number }[] = [];
  
  if (rightLeanTo) {
    const leanto_right_full_roof_sheet_number = Math.floor(length / Roof_sheet_width);
    const leanto_right_last_roof_sheet_width = length - leanto_right_full_roof_sheet_number * Roof_sheet_width;
    const leanto_right_height_offset = Roof_monoclad_height / 2;
    const leanto_right_span_drop = rightLeanToSpan * Math.tan((2 * Math.PI) / 180); // Using 2 degree pitch for lean-to
    
    // Add full-width roof sheets
    for (let i = 0; i < leanto_right_full_roof_sheet_number; i++) {
      const leanto_right_full_roof_plate_Geo = {
        height: Roof_monoclad_height / 1000,
        thickness: Roof_monoclad_thickness / 1000,
        singlelength: Roof_sheet_width / 1000,
        length: rightLeanToSpan / 1000 / Math.cos((2 * Math.PI) / 180), // No overhang, just the lean-to span
      };
      leanto_right_roof_Geo.push(leanto_right_full_roof_plate_Geo);
      
      const x = (overhang + span + rightLeanToSpan/2) / 1000; // Center of lean-to span
      const y = (((i + i + 1) / 2) * Roof_sheet_width) / 1000; // Same y-positioning as main roof
      const z = ((eaveheight + span_drop - rightLeanToDropHeight) + 
      leanto_right_height_offset + 
      leanto_right_span_drop / 2 - 
      (0.25 * Rafter_web) / Math.cos((2 * Math.PI) / 180)) / 1000 +
      50/1000;
      leanto_right_roof_Positions.push({ x, y, z, rot_y: (2 * Math.PI) / 180 });
    }
    
    // Add the last (potentially partial width) roof sheet
    if (leanto_right_last_roof_sheet_width > 0) {
      const leanto_right_last_roof_plate_Geo = {
        height: Roof_monoclad_height / 1000,
        thickness: Roof_monoclad_thickness / 1000,
        singlelength: leanto_right_last_roof_sheet_width / 1000,
        length: rightLeanToSpan / 1000 / Math.cos((2 * Math.PI) / 180),
      };
      leanto_right_roof_Geo.push(leanto_right_last_roof_plate_Geo);
      
      const x = (overhang + span + rightLeanToSpan/2) / 1000;
      const y = (length - leanto_right_last_roof_sheet_width / 2) / 1000;
      const z = ((eaveheight + span_drop - rightLeanToDropHeight) + 
                leanto_right_height_offset + 
                leanto_right_span_drop / 2 - 
                (0.25 * Rafter_web) / Math.cos((2 * Math.PI) / 180)) / 1000 +
                50/1000;
      leanto_right_roof_Positions.push({ x, y, z, rot_y: (2 * Math.PI) / 180 });
    }
  }
  
  // Gable lean-to right roof calculations
  if (rightLeanTo) {
    // Copy the same roof geometry and positions but adjust for gable pitch height
    for (let i = 0; i < leanto_right_roof_Geo.length; i++) {
      // Copy the geometry
      gable_leanto_right_roof_Geo.push({
        height: leanto_right_roof_Geo[i].height,
        thickness: leanto_right_roof_Geo[i].thickness,
        singlelength: leanto_right_roof_Geo[i].singlelength,
        length: leanto_right_roof_Geo[i].length
      });
      
      // Copy the position but adjust the z-coordinate to match the gable right lean-to rafter height
      const position = leanto_right_roof_Positions[i];
      // Use the same approach as the left lean-to for consistent behavior with span changes
      const leanto_right_height_offset = Roof_monoclad_height / 2;
      const leanto_right_span_drop = rightLeanToSpan * Math.tan((2 * Math.PI) / 180);
      
      // Calculate z-position using the same formula as the left lean-to
      const gable_z = ((eaveheight - rightLeanToDropHeight) + 
                      leanto_right_height_offset + 
                      leanto_right_span_drop / 2 - 
                      (0.25 * Rafter_web) / Math.cos((2 * Math.PI) / 180)) / 1000 +
                      50/1000;
      
      gable_leanto_right_roof_Positions.push({
        x: position.x ,
        y: position.y,
        z: gable_z,
        rot_y: position.rot_y
      });
    }
  }
  
  // flat roof geometry and position
  const full_roof_sheet_number = Math.floor(length / Roof_sheet_width);
  const last_roof_sheet_width = length - full_roof_sheet_number * Roof_sheet_width;
  const height_offset = Roof_monoclad_height / 2;
  const roof_Positions = [];
  const roof_Geo = [];

  for (let i = 0; i < full_roof_sheet_number; i++) {
    const full_roof_plate_Geo = {
      height: Roof_monoclad_height / 1000,
      thickness: Roof_monoclad_thickness / 1000,
      singlelength: Roof_sheet_width / 1000,
      length:
        (2 * overhang + span + 0.5 * Gutter_offset) / 1000 / Math.cos((pitch * Math.PI) / 180), // consider length with rotation
    };
    roof_Geo.push(full_roof_plate_Geo);
    const x = (span + 2 * overhang - 0.5 * Gutter_offset) / 2 / 1000;
    const y = (((i + i + 1) / 2) * Roof_sheet_width) / 1000;
    const z =
      (eaveheight +
        height_offset +
        span_drop / 2 -
        (0.25 * Rafter_web) / Math.cos((pitch * Math.PI) / 180) +
        0.5 * EP_web) /
      1000+10/1000;
    roof_Positions.push({ x, y, z });
  }

  //for the last plate, might be cropped
  const last_roof_plate_Geo = {
    height: Roof_monoclad_height / 1000,
    thickness: Roof_monoclad_thickness / 1000,
    singlelength: last_roof_sheet_width / 1000,
    length: (2 * overhang + span + 0.5 * Gutter_offset) / 1000 / Math.cos((pitch * Math.PI) / 180),
  };

  roof_Geo.push(last_roof_plate_Geo);
  const x = (span + 2 * overhang - 0.5 * Gutter_offset) / 2 / 1000;
  const y = (length + full_roof_sheet_number * Roof_sheet_width) / 1000 / 2; //start
  const z =
    (eaveheight +
      height_offset +
      span_drop / 2 -
      (0.25 * Rafter_web) / Math.cos((pitch * Math.PI) / 180) +
      0.5 * EP_web) /
    1000+10/1000;
  roof_Positions.push({ x, y, z });


  // gable roof geometry
  const gable_roof_Positions = [];
  const gable_roof_Geo_full = {
    height: Roof_monoclad_height / 1000,
    thickness: Roof_monoclad_thickness / 1000,
    singlelength: Roof_sheet_width / 1000,
    length:
      (0.5* span 
        + 0.5 * Gutter_offset 
        - 0.5*Rafter_web*Math.sin((pitch * Math.PI) / 180)
      ) / 1000 / Math.cos((pitch * Math.PI) / 180), // consider length with rotation
  };
  //for the last plate, might be cropped
  const gable_last_roof_plate_Geo = {
    height: Roof_monoclad_height / 1000,
    thickness: Roof_monoclad_thickness / 1000,
    singlelength: last_roof_sheet_width / 1000,
    length:
      (0.5* span 
        + 0.5 * Gutter_offset 
        - 0.5*Rafter_web*Math.sin((pitch * Math.PI) / 180)
      ) / 1000 / Math.cos((pitch * Math.PI) / 180), // consider length with rotation
  };
  const gable_roof_height_offset = 40;

  for (let i = 0; i < full_roof_sheet_number; i++) {
    const x_left = (0.5* span 
        - 0.5 * Gutter_offset 
        - 0.5*Rafter_web*Math.sin((pitch * Math.PI) / 180)
      )/2/1000;
    const x_right = (1.5* span 
        + 0.5 * Gutter_offset 
        + 0.5*Rafter_web*Math.sin((pitch * Math.PI) / 180)
      )/2/1000;
    const y = (((i + i + 1) / 2) * Roof_sheet_width) / 1000;
    // todo: adjust
    const z =
      (eaveheight +
        gable_roof_height_offset
        + span_drop / 4 
        + EP_flange * Math.tan((pitch * Math.PI) / 180)
      )/ 1000;
    gable_roof_Positions.push({ x:x_left, y, z, rot_y:-(pitch * Math.PI) / 180});
    gable_roof_Positions.push({ x:x_right, y, z, rot_y:(pitch * Math.PI) / 180});
  }
  //last plate positions
  const last_x_left = (0.5* span 
    - 0.5 * Gutter_offset 
    - 0.5*Rafter_web*Math.sin((pitch * Math.PI) / 180)
  )/2/1000;
  const last_x_right = (1.5* span 
    + 0.5 * Gutter_offset 
    + 0.5*Rafter_web*Math.sin((pitch * Math.PI) / 180)
  )/2/1000;
  const last_y = (length + full_roof_sheet_number * Roof_sheet_width) / 1000 / 2; //start
  const last_z =
    (eaveheight +
      gable_roof_height_offset
      + span_drop / 4 
      + EP_flange * Math.tan((pitch * Math.PI) / 180)
    )/ 1000;
  gable_roof_Positions.push({ x:last_x_left, y:last_y, z:last_z, rot_y:-(pitch * Math.PI) / 180});
  gable_roof_Positions.push({ x:last_x_right, y:last_y, z:last_z, rot_y:(pitch * Math.PI) / 180});



  return {
    //Post
    post_left_Positions,
    post_left_Geo,
    post_right_Positions,
    post_right_Geo,
    //Wall for Skillion Attached
    wall_Geo,
    wall_Positions,
    gable_post_left_Positions,
    gable_post_left_Geo,
    gable_post_right_Positions,
    gable_post_right_Geo,
    leanto_post_left_Positions,
    leanto_post_left_Geo,
    leanto_post_right_Positions,
    leanto_post_right_Geo,
    gable_leanto_post_right_Positions,
    gable_leanto_post_right_Geo,
    gable_leanto_rafter_right_Positions,
    gable_leanto_rafter_right_Geo,
    //Rafter
    rafter_Geo,
    rafter_Positions,
    leanto_rafter_Geo,
    leanto_rafter_Positions,
    leanto_rafter_right_Geo,
    leanto_rafter_right_Positions,
    gable_left_rafter_Geo,
    gable_left_rafter_Positions,
    gable_right_rafter_Geo,
    gable_right_rafter_Positions,
    //Purlin
    flat_purlin_Geo,
    flat_purlin_Positions,
    leanto_flat_purlin_Geo,
    leanto_flat_purlin_Positions,
    leanto_right_flat_purlin_Geo,
    leanto_right_flat_purlin_Positions,
    gable_left_purlin_Geo,
    gable_left_purlin_Positions,
    gable_right_purlin_Geo,
    gable_right_purlin_Positions,
    gable_leanto_right_flat_purlin_Geo,
    gable_leanto_right_flat_purlin_Positions,
    //Eave Purlin
    left_EP_Geo,
    left_EP_Positions,
    right_EP_Geo,
    right_EP_Positions,
    gable_left_EP_Geo,
    gable_left_EP_Positions,
    gable_right_EP_Geo,
    gable_right_EP_Positions,
    leanto_left_EP_Geo,
    leanto_left_EP_Positions,
    leanto_right_EP_Geo,
    leanto_right_EP_Positions,
    gable_leanto_right_EP_Geo,
    gable_leanto_right_EP_Positions,
    //Footing
    footing_Geo,
    footing_Positions,
    gable_footing_Geo,
    gable_footing_Positions,
    leanto_footing_Geo,
    leanto_footing_Positions,
    leanto_right_footing_Geo,
    leanto_right_footing_Positions,
    gable_leanto_right_footing_Geo,
    gable_leanto_right_footing_Positions,
    //Slab
    slab_Geo,
    slab_Position,
    leanto_slab_Geo,
    leanto_slab_Position,
    leanto_right_slab_Geo,
    leanto_right_slab_Position,
    //Pitch
    pitch,
    // Roof
    roof_Geo,
    roof_Positions,
    leanto_roof_Geo,
    leanto_roof_Positions,
    leanto_right_roof_Geo,
    leanto_right_roof_Positions,
    main_left_drop_Geo,
    main_left_drop_Positions,
    main_right_drop_Geo,
    main_right_drop_Positions,
    gable_roof_Geo_full,
    gable_last_roof_plate_Geo,
    gable_roof_Positions,
    gable_leanto_right_roof_Geo,
    gable_leanto_right_roof_Positions,
    gable_main_right_drop_Geo,
    gable_main_right_drop_Positions
  };
}

type CarportConfig = ReturnType<typeof calculateGeometriesAndPositions>;

export default calculateGeometriesAndPositions;
export type { CarportConfig };
