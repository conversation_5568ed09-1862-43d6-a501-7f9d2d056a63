import { post_size } from '@/constants/carport/CarportParams';
import { createCorro2DSection } from '@/domain/helpers/SectionHelper';
import { WallMatrixType } from './girt.config';
import { CarportMaterialType } from '@/constants/materials/Material';
import { brickWallMaterial, roofMaterial, getMaterial } from '@/domain/materials/colorManager';
import { THREE } from 'expo-three';

// Define interfaces for wall sheet geometries and positions
interface WallSheetGeometry {
  geo_x: number;  // width of sheet
  geo_y: number;  // height of sheet
  geo_z: number;  // length of sheet
  thickness: number;  // thickness of the sheet material
  topEdge?: Array<[number, number]>;  // optional array of [x,y] points defining the top edge
  material?: THREE.MeshStandardMaterial; // Material for sheet color
}

interface WallSheetPosition {
  x: number;  // x position in scene
  y: number;  // y position in scene
  z: number;  // z position in scene
  rotation?: {  // optional rotation values
    x: number;
    y: number;
    z: number;
  };
}

interface WallSheetReturn {
  // Main carport wall sheets
  mainLeftWallSheet: {
    geometries: WallSheetGeometry[];
    positions: WallSheetPosition[];
  };
  mainRightWallSheet: {
    geometries: WallSheetGeometry[];
    positions: WallSheetPosition[];
  };
  mainFrontWallSheet: {
    geometries: WallSheetGeometry[];
    positions: WallSheetPosition[];
  };
  mainBackWallSheet: {
    geometries: WallSheetGeometry[];
    positions: WallSheetPosition[];
  };
  mainDividerWallSheet: {
    geometries: WallSheetGeometry[];
    positions: WallSheetPosition[];
  };
  
  // Left lean-to wall sheets
  leftLeanToWallSheet: {
    geometries: WallSheetGeometry[];
    positions: WallSheetPosition[];
  };
  leftLeanToFrontWallSheet: {
    geometries: WallSheetGeometry[];
    positions: WallSheetPosition[];
  };
  leftLeanToBackWallSheet: {
    geometries: WallSheetGeometry[];
    positions: WallSheetPosition[];
  };
  leftLeanToDividerWallSheet: {
    geometries: WallSheetGeometry[];
    positions: WallSheetPosition[];
  };
  
  // Right lean-to wall sheets
  rightLeanToWallSheet: {
    geometries: WallSheetGeometry[];
    positions: WallSheetPosition[];
  };
  rightLeanToFrontWallSheet: {
    geometries: WallSheetGeometry[];
    positions: WallSheetPosition[];
  };
  rightLeanToBackWallSheet: {
    geometries: WallSheetGeometry[];
    positions: WallSheetPosition[];
  };
  rightLeanToDividerWallSheet: {
    geometries: WallSheetGeometry[];
    positions: WallSheetPosition[];
  };
}

// Helper function to get wall sheet dimensions
function getWallSheetDimensions() {
  // Standard dimensions for corrugated wall sheet
  return {
    width: 50,         // width of a single sheet in mm
    height: 20,        // height of the profile in mm
    thickness: 0.42,   // thickness of sheet in mm
  };
}

// Main function to calculate wall sheet geometries and positions
export default function calculateWallSheetGeometriesAndPositions(
  length: number = 12000,
  span: number = 6000,
  height: number = 2700,
  leftLeanToSpan: number = 0,
  leftLeanToDropHeight: number = 0,
  rightLeanToSpan: number = 0,
  rightLeanToDropHeight: number = 0,
  overhang: number = 0,
  pitch: number = 0, // Not used for flat roofs
  wallMatrix: WallMatrixType = {
    mainLeftWall: [],
    mainRightWall: [],
    mainDividerWall: [],
    mainFrontWall: false,
    mainBackWall: false,
    leftLeanToWall: [],
    leftLeanToFrontWall: null,
    leftLeanToBackWall: null,
    leftLeanToDividerWall: [],
    rightLeanToWall: [],
    rightLeanToFrontWall: null,
    rightLeanToBackWall: null,
    rightLeanToDividerWall: [],
  }
): WallSheetReturn {
  // Initialize arrays to store geometries and positions
  const mainLeftWallSheet_Geo: WallSheetGeometry[] = [];
  const mainLeftWallSheet_Positions: WallSheetPosition[] = [];
  
  const mainRightWallSheet_Geo: WallSheetGeometry[] = [];
  const mainRightWallSheet_Positions: WallSheetPosition[] = [];
  
  const mainFrontWallSheet_Geo: WallSheetGeometry[] = [];
  const mainFrontWallSheet_Positions: WallSheetPosition[] = [];
  
  const mainBackWallSheet_Geo: WallSheetGeometry[] = [];
  const mainBackWallSheet_Positions: WallSheetPosition[] = [];
  
  const mainDividerWallSheet_Geo: WallSheetGeometry[] = [];
  const mainDividerWallSheet_Positions: WallSheetPosition[] = [];
  
  // Left lean-to sheet arrays
  const leftLeanToWallSheet_Geo: WallSheetGeometry[] = [];
  const leftLeanToWallSheet_Positions: WallSheetPosition[] = [];
  
  const leftLeanToFrontWallSheet_Geo: WallSheetGeometry[] = [];
  const leftLeanToFrontWallSheet_Positions: WallSheetPosition[] = [];
  
  const leftLeanToBackWallSheet_Geo: WallSheetGeometry[] = [];
  const leftLeanToBackWallSheet_Positions: WallSheetPosition[] = [];
  
  const leftLeanToDividerWallSheet_Geo: WallSheetGeometry[] = [];
  const leftLeanToDividerWallSheet_Positions: WallSheetPosition[] = [];
  
  // Right lean-to sheet arrays
  const rightLeanToWallSheet_Geo: WallSheetGeometry[] = [];
  const rightLeanToWallSheet_Positions: WallSheetPosition[] = [];
  
  const rightLeanToFrontWallSheet_Geo: WallSheetGeometry[] = [];
  const rightLeanToFrontWallSheet_Positions: WallSheetPosition[] = [];
  
  const rightLeanToBackWallSheet_Geo: WallSheetGeometry[] = [];
  const rightLeanToBackWallSheet_Positions: WallSheetPosition[] = [];
  
  const rightLeanToDividerWallSheet_Geo: WallSheetGeometry[] = [];
  const rightLeanToDividerWallSheet_Positions: WallSheetPosition[] = [];
  
  // Calculate number of bays based on length
  const bay_no = Math.max(1, Math.ceil(length / 4000));
  const sectionLength = length / bay_no / 1000; // Convert to meters
  
  // Wall sheet standard dimensions
  const sheetWidth = 840; // Standard sheet width in mm
  const overlap = 76; // Overlap between sheets in mm
  
  /**
   * Process Main Left Wall Sheets
   */
  if (wallMatrix.mainLeftWall && wallMatrix.mainLeftWall.length > 0) {
    // Calculate standard sheet dimensions
    const { width, height: sheetHeight, thickness } = getWallSheetDimensions();
    
    // We'll process each wall segment defined in the matrix
    wallMatrix.mainLeftWall.forEach((segment, segmentIndex) => {
      if (!segment) return; // Skip if segment is not enabled
      
      // Calculate segment position along the carport length
      const segmentStart = (length / wallMatrix.mainLeftWall.length) * segmentIndex;
      const segmentEnd = (length / wallMatrix.mainLeftWall.length) * (segmentIndex + 1);
      const segmentLength = segmentEnd - segmentStart+.15;
      
      // Wall sheet width typically follows standard sizes (e.g., 762mm)
      const standardSheetWidth = 762; // Standard sheet width in mm
      const numSheets = Math.ceil(segmentLength / standardSheetWidth);
      const actualSheetWidth = segmentLength / numSheets;
      
      // Add wall sheets for this segment
      for (let i = 0; i < numSheets; i++) {
        const sheetStart = segmentStart + (i * actualSheetWidth);
        
        // Create the geometry
        mainLeftWallSheet_Geo.push({
          geo_x: 0.001,      // Thickness in meters
          geo_y: height / 1000, // Height in meters
          geo_z: actualSheetWidth / 1000, // Width in meters
          thickness: 0.001,              // Material thickness in meters
          material: roofMaterial        // Use roof material to match other walls
        });
        
        // Calculate position (adjusted for main wall position)
        const x = overhang/1000-0.01; // Left edge of main wall
        const y = length/1000-(sheetStart + (actualSheetWidth/2)) / 1000-.03; // Position along length
        const z = height / 2000; // Center vertically
        
        // Add position
        mainLeftWallSheet_Positions.push({
          x, y, z,
          rotation: { x: 0, y: 0, z: 0 } // Rotation for left wall
        });
      }
    });
  }
  
  /**
   * Process Main Right Wall Sheets
   */
  if (wallMatrix.mainRightWall && wallMatrix.mainRightWall.length > 0) {
    // Calculate standard sheet dimensions
    const { width, height: sheetHeight, thickness } = getWallSheetDimensions();
    
    // We'll process each wall segment defined in the matrix
    wallMatrix.mainRightWall.forEach((segment, segmentIndex) => {
      if (!segment) return; // Skip if segment is not enabled
      
      // Calculate segment position along the carport length
      const segmentStart = (length / wallMatrix.mainRightWall.length) * segmentIndex;
      const segmentEnd = (length / wallMatrix.mainRightWall.length) * (segmentIndex + 1);
      const segmentLength = segmentEnd - segmentStart+.15;
      
      // Wall sheet width typically follows standard sizes (e.g., 762mm)
      const standardSheetWidth = 762; // Standard sheet width in mm
      const numSheets = Math.ceil(segmentLength / standardSheetWidth);
      const actualSheetWidth = segmentLength / numSheets;
          // Calculate the effective height with drop, accounting for roof pitch
    // For right lean-to, we need to account for height increase due to span and pitch (span*tan(pitch))
    const pitchRadians = pitch*Math.PI/180; // Convert pitch to radians
    const heightIncreaseDueToSpan = (span + overhang) * Math.tan(pitchRadians); // Calculate the height increase
    const effectiveHeight = height + heightIncreaseDueToSpan +50;
    
      // Add wall sheets for this segment
      for (let i = 0; i < numSheets; i++) {
        const sheetStart = segmentStart + (i * actualSheetWidth);
        
        // Create the geometry
        mainRightWallSheet_Geo.push({
          geo_x: 0.001,      // Thickness in meters
          geo_y: effectiveHeight / 1000, // Height in meters
          geo_z: actualSheetWidth / 1000, // Width in meters
          thickness: 0.001,              // Material thickness in meters
          material: roofMaterial        // Use roof material to match other walls
        });
        
        // Calculate position (adjusted for main wall position)
        const x = (overhang + span) / 1000+0.01; // Right edge of main wall
        const y = length/1000-(sheetStart + (actualSheetWidth/2)) / 1000-.03; // Position along length
        const z = effectiveHeight / 2000; // Center vertically
        
        // Add position
        mainRightWallSheet_Positions.push({
          x, y, z,
          rotation: { x: 0, y: 0, z: 0 } // Rotation for right wall
        });
      }
    });
  }
  
  /**
   * Process Front Wall Sheet (if enabled)
   */
  if (wallMatrix.mainFrontWall) {
    // Calculate the standard sheet dimensions
    const { width, height: sheetHeight, thickness } = getWallSheetDimensions();
    
    // Calculate the skillion roof height increase due to pitch
    const pitchRadians = pitch*Math.PI/180; // Convert pitch to radians
    const heightIncreaseDueToSpan = (span + overhang) * Math.tan(pitchRadians); // Calculate the height increase
    const peakHeight = height + heightIncreaseDueToSpan; // Height at the higher side
    
    // Standard corrugated steel dimensions
    const corrugationDepth = 16; // Depth of corrugation in mm
    const corrugationWidth = 76; // Width of one corrugation wave in mm
    
    // Create a single drop sheet for the front wall with corrugation pattern
    
    // Import the THREE.js for the corrugation pattern
    const { THREE } = require('expo-three');
    
    // Define the skillion outline shape (points defining the entire wall)
    // For a skillion roof, the top is sloped from lower to higher side
    const wallOutline: Array<[number, number]> = [
      [0, 0],                // Bottom left corner
      [span, 0],             // Bottom right corner
      [span, peakHeight],    // Top right corner (higher side)
      [0, height],           // Top left corner (lower side)
    ];
    
    // Create a shape to hold the corrugated profile
    const wallShape = new THREE.Shape();
    // Start from the first point of the wall outline
    wallShape.moveTo(wallOutline[0][0] / 1000, wallOutline[0][1] / 1000);
    
    // Add corrugation along the bottom edge
    const numWaves = Math.floor(span / corrugationWidth);
    const actualWaveWidth = span / numWaves;
    
    // Start at bottom left corner
    wallShape.lineTo(0, 0);
    
    // Add corrugation waves along the bottom (alternating up and down)
    for (let i = 0; i < numWaves; i++) {
      const waveStartX = i * actualWaveWidth / 1000;
      const wavePeakX = (i * actualWaveWidth + actualWaveWidth / 2) / 1000;
      const waveEndX = ((i + 1) * actualWaveWidth) / 1000;
      
      // Valley to peak
      wallShape.lineTo(wavePeakX, corrugationDepth / 1000);
      // Peak to valley
      wallShape.lineTo(waveEndX, 0);
    }
    
    // Continue with the right edge, top edge and left edge of wall
    wallShape.lineTo(wallOutline[1][0] / 1000, wallOutline[1][1] / 1000); // Bottom right
    wallShape.lineTo(wallOutline[2][0] / 1000, wallOutline[2][1] / 1000); // Top right (higher side)
    wallShape.lineTo(wallOutline[3][0] / 1000, wallOutline[3][1] / 1000); // Top left (lower side)
    wallShape.lineTo(wallOutline[0][0] / 1000, wallOutline[0][1] / 1000); // Back to start
    
    // Extract points from the shape for rendering
    const extractedPoints = wallShape.extractPoints(12); // Higher resolution
    const corrugatedWallPoints = extractedPoints.shape.map((point: { x: number; y: number }) => [
      point.x, // Already in meters
      point.y  // Already in meters
    ] as [number, number]);
    
    // Add the front wall as a single sheet with the trapezoidal shape defined by the corrugated wall points
    mainFrontWallSheet_Geo.push({
      geo_x: 0.001,                  // Thickness in meters
      geo_y: peakHeight / 1000,     // Total height including pitch in meters
      geo_z: .001,                  // Width of the entire wall in meters
      thickness: 0.001,             // Material thickness in meters
      topEdge: corrugatedWallPoints, // The corrugated wall outline defines the trapezoidal shape
      material: roofMaterial        // Use the roof material
    });
    
    // Position the sheet at the front edge of the carport
    const x = overhang/1000+span / 2000;  // Center horizontally, accounting for overhang
    const y = 0.03;                       // Front edge with slight offset
    const z = (height + heightIncreaseDueToSpan/2) / 2000+.1;  // Center vertically
    
    // Add position data
    mainFrontWallSheet_Positions.push({
      x: x,
      y: y-.038,
      z: z,
      rotation: { x: Math.PI/2, y: -Math.PI/2, z: 0 } // Rotation for front wall
    });
  }
  
  /**
   * Process Back Wall Sheet (if enabled)
   */
  if (wallMatrix.mainBackWall) {
    // Calculate the standard sheet dimensions
    const { width, height: sheetHeight, thickness } = getWallSheetDimensions();
    
    // Calculate the skillion roof height increase due to pitch
    const pitchRadians = pitch*Math.PI/180; // Convert pitch percentage to radians
    const heightIncreaseDueToSpan = (span +overhang)* Math.tan(pitchRadians); // Calculate the height increase
    const peakHeight = height + heightIncreaseDueToSpan; // Height at the higher side
    
    // Standard corrugated steel dimensions
    const corrugationDepth = 16; // Depth of corrugation in mm
    const corrugationWidth = 76; // Width of one corrugation wave in mm
    
    // Create a single drop sheet for the back wall with corrugation pattern
    
    // Import the THREE.js for the corrugation pattern
    const { THREE } = require('expo-three');
    
    // Define the skillion outline shape (points defining the entire wall)
    // For a skillion roof, the top is sloped from lower to higher side
    const wallOutline: Array<[number, number]> = [
      [0, 0],                // Bottom left corner
      [span, 0],             // Bottom right corner
      [span, peakHeight],    // Top right corner (higher side)
      [0, height],           // Top left corner (lower side)
    ];
    
    // Create a shape to hold the corrugated profile
    const wallShape = new THREE.Shape();
    // Start from the first point of the wall outline
    wallShape.moveTo(wallOutline[0][0] / 1000, wallOutline[0][1] / 1000);
    
    // Add corrugation along the bottom edge
    const numWaves = Math.floor(span / corrugationWidth);
    const actualWaveWidth = span / numWaves;
    
    // Start at bottom left corner
    wallShape.lineTo(0, 0);
    
    // Add corrugation waves along the bottom (alternating up and down)
    for (let i = 0; i < numWaves; i++) {
      const waveStartX = i * actualWaveWidth / 1000;
      const wavePeakX = (i * actualWaveWidth + actualWaveWidth / 2) / 1000;
      const waveEndX = ((i + 1) * actualWaveWidth) / 1000;
      
      // Valley to peak
      wallShape.lineTo(wavePeakX, corrugationDepth / 1000);
      // Peak to valley
      wallShape.lineTo(waveEndX, 0);
    }
    
    // Continue with the right edge, top edge and left edge of wall
    wallShape.lineTo(wallOutline[1][0] / 1000, wallOutline[1][1] / 1000); // Bottom right
    wallShape.lineTo(wallOutline[2][0] / 1000, wallOutline[2][1] / 1000); // Top right (higher side)
    wallShape.lineTo(wallOutline[3][0] / 1000, wallOutline[3][1] / 1000); // Top left (lower side)
    wallShape.lineTo(wallOutline[0][0] / 1000, wallOutline[0][1] / 1000); // Back to start
    
    // Extract points from the shape for rendering
    const extractedPoints = wallShape.extractPoints(12); // Higher resolution
    const corrugatedWallPoints = extractedPoints.shape.map((point: { x: number; y: number }) => [
      point.x, // Already in meters
      point.y  // Already in meters
    ] as [number, number]);
    
    // Add the back wall as a single sheet with the trapezoidal shape defined by the corrugated wall points
    mainBackWallSheet_Geo.push({
      geo_x: 0.001,                  // Thickness in meters
      geo_y: peakHeight / 1000,     // Total height including pitch in meters
      geo_z: .001,           // Width of the entire wall in meters
      thickness: 0.001,             // Material thickness in meters
      topEdge: corrugatedWallPoints, // The corrugated wall outline defines the trapezoidal shape
      material: roofMaterial        // Use the roof material
    });
    
    // Position the sheet at the back edge of the carport
    const x = overhang/1000+span / 2000;               // Center horizontally
    const y = length / 1000-0.03 ;      // Back edge with slight offset
    const z = (height + heightIncreaseDueToSpan/2) / 2000+.1;  // Center vertically
    
    // Add position data
    mainBackWallSheet_Positions.push({
      x: x,
      y: y+.038,
      z: z,
      rotation: { x: Math.PI/2, y: Math.PI/2, z: 0 } // Rotation for back wall
    });
  }
  
  /**
   * Process Left Lean-to Wall Sheets (for each section defined in matrix)
{{ ... }}
   */
  if (wallMatrix.leftLeanToWall && wallMatrix.leftLeanToWall.length > 0 && leftLeanToSpan > 0) {
    // Calculate standard sheet dimensions
    const { width, height: sheetHeight, thickness } = getWallSheetDimensions();
    
    // Calculate the effective height with drop
    const effectiveHeight = height - leftLeanToDropHeight;
    
    // We'll process each wall segment defined in the matrix
    wallMatrix.leftLeanToWall.forEach((segment, segmentIndex) => {
      if (!segment) return; // Skip if segment is not enabled
      
      // Calculate segment position along the carport length
      const segmentStart = (length / wallMatrix.leftLeanToWall.length) * segmentIndex;
      const segmentEnd = (length / wallMatrix.leftLeanToWall.length) * (segmentIndex + 1);
      const segmentLength = segmentEnd - segmentStart+.15;
      
      // Wall sheet width typically follows standard sizes (e.g., 762mm)
      const standardSheetWidth = 762; // Standard sheet width in mm
      const numSheets = Math.ceil(segmentLength / standardSheetWidth);
      const actualSheetWidth = segmentLength / numSheets;
      
      // Add wall sheets for this segment
      for (let i = 0; i < numSheets; i++) {
        const sheetStart = segmentStart + (i * actualSheetWidth);
        
        // Create the geometry
        leftLeanToWallSheet_Geo.push({
          geo_x: 0.001,      // Thickness in meters
          geo_y: effectiveHeight / 1000, // Height in meters
          geo_z: actualSheetWidth / 1000, // Width in meters
          thickness: 0.001,              // Material thickness in meters
          material: roofMaterial        // Use roof material to match other walls
        });
        
        // Calculate position (adjusted for lean-to position)
        const x = (overhang - leftLeanToSpan + post_size/2) / 1000-.09; // Left edge of lean-to
        const y = length/1000-(segmentStart + (i + 0.5) * actualSheetWidth) / 1000-.03; // Position along length
        const z = effectiveHeight / 2000; // Center vertically
        
        // Add position
        leftLeanToWallSheet_Positions.push({
          x, y, z,
          rotation: { x: 0, y: 0, z: Math.PI/2   } // Rotation for left wall
        });
      }
    });
  }
  
  /**
   * Process Left Lean-to Front Wall Sheet (if enabled)
   */
  if (wallMatrix.leftLeanToFrontWall && leftLeanToSpan > 0) {
    // Calculate standard sheet dimensions
    const { width, height: sheetHeight, thickness } = getWallSheetDimensions();
    
    // Calculate the effective height with drop
    const effectiveHeight = height - leftLeanToDropHeight;
    
    // Create a single sheet for the front wall
    leftLeanToFrontWallSheet_Geo.push({
      geo_x: 0.001,      // Thickness in meters
      geo_y: effectiveHeight / 1000, // Height in meters
      geo_z: leftLeanToSpan / 1000,  // Width in meters
      thickness: 0.001,              // Material thickness in meters
      material: roofMaterial        // Use roof material to match other walls
    });
    
    // Calculate position
    const x = (overhang - leftLeanToSpan/2) / 1000+.02; // Center of lean-to span
    const y = -0.01;                    // Front edge
    const z = effectiveHeight / 2000;               // Center vertically
    
    // Add position
    leftLeanToFrontWallSheet_Positions.push({
      x, y, z,
      rotation: { x: 0, y: 0, z: 0 } // Rotation for front wall
    });
  }
  
  /**
   * Process Left Lean-to Back Wall Sheet (if enabled)
   */
  if (wallMatrix.leftLeanToBackWall && leftLeanToSpan > 0) {
    // Calculate standard sheet dimensions
    const { width, height: sheetHeight, thickness } = getWallSheetDimensions();
    
    // Calculate the effective height with drop
    const effectiveHeight = height - leftLeanToDropHeight;
    
    // Create a single sheet for the back wall
    leftLeanToBackWallSheet_Geo.push({
      geo_x: .001,      // Thickness in meters
      geo_y: effectiveHeight / 1000, // Height in meters
      geo_z: leftLeanToSpan / 1000,  // Width in meters
      thickness: 0.001,              // Material thickness in meters
      material: roofMaterial        // Use roof material to match other walls
    });
    
    // Calculate position
    const x = (overhang - leftLeanToSpan/2) / 1000+.01; // Center of lean-to span
    const y = (length) / 1000+.01;        // Back edge
    const z = effectiveHeight / 2000;               // Center vertically
    
    // Add position
    leftLeanToBackWallSheet_Positions.push({
      x, y, z,
      rotation: { x: 0, y: 0, z: 0 } // Rotation for back wall
    });
  }
  
  /**
   * Process Left Lean-to Divider Wall Sheets
   */
  if (wallMatrix.leftLeanToDividerWall && wallMatrix.leftLeanToDividerWall.length > 0 && leftLeanToSpan > 0) {
    // Calculate standard sheet dimensions
    const { width, height: sheetHeight, thickness } = getWallSheetDimensions();
    
    // Calculate the effective height with drop
    const effectiveHeight = height - leftLeanToDropHeight;
    
    // Get the bay division
    const bay_no = wallMatrix.leftLeanToDividerWall.length+1; // Number of bays
    
    for (let i = 0; i < wallMatrix.leftLeanToDividerWall.length; i++) {
      if (!wallMatrix.leftLeanToDividerWall[i]) continue; // Skip if this divider is not enabled
      
      // Create a geometry for the divider wall
      leftLeanToDividerWallSheet_Geo.push({
        geo_x: 0.001,      // Thickness in meters
        geo_y: effectiveHeight / 1000, // Height in meters
        geo_z: leftLeanToSpan / 1000,  // Width in meters
        thickness: 0.001,              // Material thickness in meters
        material: roofMaterial        // Use roof material to match other walls
      });
      
      // Position the sheet at the divider location
      const x = (overhang - leftLeanToSpan/2) / 1000+.02; // Center of lean-to span
      const y = length/1000-(i+1)*length/1000/bay_no-.09; // Divider position
      const z = effectiveHeight / 2000; // Center vertically
      
      // Add position data
      leftLeanToDividerWallSheet_Positions.push({
        x, y, z,
        rotation: { x: 0, y: 0, z: 0 } // Rotation for divider wall
      });
    }
  }
  
  /**
   * Process Right Lean-to Wall Sheets (for each section defined in matrix)
   */
  if (wallMatrix.rightLeanToWall && wallMatrix.rightLeanToWall.length > 0 && rightLeanToSpan > 0) {
    // Calculate standard sheet dimensions
    const { width, height: sheetHeight, thickness } = getWallSheetDimensions();
    
    // Calculate the effective height with drop, accounting for roof pitch
    // For right lean-to, we need to account for height increase due to span and pitch (span*tan(pitch))
    const pitchRadians = Math.atan(pitch / 100); // Convert pitch percentage to radians
    const heightIncreaseDueToSpan = span * Math.tan(pitchRadians); // Calculate the height increase
    const effectiveHeight = height + heightIncreaseDueToSpan - rightLeanToDropHeight+50;
    
    // We'll process each wall segment defined in the matrix
    wallMatrix.rightLeanToWall.forEach((segment, segmentIndex) => {
      if (!segment) return; // Skip if segment is not enabled
      
      // Calculate segment position along the carport length
      const segmentStart = (length / wallMatrix.rightLeanToWall.length) * segmentIndex;
      const segmentEnd = (length / wallMatrix.rightLeanToWall.length) * (segmentIndex + 1);
      const segmentLength = segmentEnd - segmentStart+.15;
      
      // Wall sheet width typically follows standard sizes (e.g., 762mm)
      const standardSheetWidth = 762; // Standard sheet width in mm
      const numSheets = Math.ceil(segmentLength / standardSheetWidth);
      const actualSheetWidth = segmentLength / numSheets;
      
      // Add wall sheets for this segment
      for (let i = 0; i < numSheets; i++) {
        const sheetStart = segmentStart + (i * actualSheetWidth);
        
        // Create the geometry
        rightLeanToWallSheet_Geo.push({
          geo_x: 0.001,      // Thickness in meters
          geo_y: effectiveHeight / 1000, // Height in meters
          geo_z: actualSheetWidth / 1000, // Width in meters
          thickness: 0.001,              // Material thickness in meters
          material: roofMaterial        // Use roof material to match other walls
        });
        
        // Calculate position (adjusted for lean-to position)
        const x = (overhang + span + rightLeanToSpan - post_size/2) / 1000+.09; // Right edge of lean-to
        const y = length/1000-(segmentStart + (i + 0.5) * actualSheetWidth) / 1000-.03; // Position along length
        const z = effectiveHeight / 2000; // Center vertically
        
        // Add position
        rightLeanToWallSheet_Positions.push({
          x, y, z,
          rotation: { x: 0, y: 0, z: Math.PI/2 } // Rotation for right wall
        });
      }
    });
  }
  
  /**
   * Process Right Lean-to Front Wall Sheet (if enabled)
   */
  if (wallMatrix.rightLeanToFrontWall && rightLeanToSpan > 0) {
    // Calculate standard sheet dimensions
    const { width, height: sheetHeight, thickness } = getWallSheetDimensions();
    
    // Calculate the effective height with drop, accounting for roof pitch
    // For right lean-to, we need to account for height increase due to span and pitch (span*tan(pitch))
    const pitchRadians = Math.atan(pitch / 100); // Convert pitch percentage to radians
    const heightIncreaseDueToSpan = span * Math.tan(pitchRadians); // Calculate the height increase
    const effectiveHeight = height + heightIncreaseDueToSpan - rightLeanToDropHeight+50;
   
    // Create a single sheet for the front wall
    rightLeanToFrontWallSheet_Geo.push({
      geo_x: 0.001,      // Thickness in meters
      geo_y: effectiveHeight / 1000, // Height in meters
      geo_z: rightLeanToSpan / 1000,  // Width in meters
      thickness: 0.001,              // Material thickness in meters
      material: roofMaterial        // Use roof material to match other walls
    });
    
    // Calculate position
    const x = (overhang + span + rightLeanToSpan/2) / 1000-.02; // Center of lean-to span
    const y = -0.01;                    // Front edge
    const z = effectiveHeight / 2000;               // Center vertically
    
    // Add position
    rightLeanToFrontWallSheet_Positions.push({
      x, y, z,
      rotation: { x: 0, y: 0, z: 0 } // Rotation for front wall
    });
  }
  
  /**
   * Process Right Lean-to Back Wall Sheet (if enabled)
   */
  if (wallMatrix.rightLeanToBackWall && rightLeanToSpan > 0) {
    // Calculate standard sheet dimensions
    const { width, height: sheetHeight, thickness } = getWallSheetDimensions();
    
     // Calculate the effective height with drop, accounting for roof pitch
    // For right lean-to, we need to account for height increase due to span and pitch (span*tan(pitch))
    const pitchRadians = Math.atan(pitch / 100); // Convert pitch percentage to radians
    const heightIncreaseDueToSpan = span * Math.tan(pitchRadians); // Calculate the height increase
    const effectiveHeight = height + heightIncreaseDueToSpan - rightLeanToDropHeight+50;
   
    // Create a single sheet for the back wall
    rightLeanToBackWallSheet_Geo.push({
      geo_x: 0.001,      // Thickness in meters
      geo_y: effectiveHeight / 1000, // Height in meters
      geo_z: rightLeanToSpan / 1000,  // Width in meters
      thickness: 0.001,              // Material thickness in meters
      material: roofMaterial        // Use roof material to match other walls
    });
    
    // Calculate position
    const x = (overhang + span + rightLeanToSpan/2) / 1000-.01; // Center of lean-to span
    const y = (length) / 1000+.01;        // Back edge
    const z = effectiveHeight / 2000;               // Center vertically
    
    // Add position
    rightLeanToBackWallSheet_Positions.push({
      x, y, z,
      rotation: { x: 0, y: 0, z: 0 } // Rotation for back wall
    });
  }
  
  /**
   * Process Right Lean-to Divider Wall Sheets
   */
  if (wallMatrix.rightLeanToDividerWall && wallMatrix.rightLeanToDividerWall.length > 0 && rightLeanToSpan > 0) {
    // Calculate standard sheet dimensions
    const { width, height: sheetHeight, thickness } = getWallSheetDimensions();
    
    // Calculate the effective height with drop, accounting for roof pitch
    // For right lean-to, we need to account for height increase due to span and pitch (span*tan(pitch))
    const pitchRadians = Math.atan(pitch / 100); // Convert pitch percentage to radians
    const heightIncreaseDueToSpan = span * Math.tan(pitchRadians); // Calculate the height increase
    const effectiveHeight = height + heightIncreaseDueToSpan - rightLeanToDropHeight+50;
   
    // Get the bay division
    const bay_no = wallMatrix.rightLeanToDividerWall.length+1; // Number of bays
    
    for (let i = 0; i < wallMatrix.rightLeanToDividerWall.length; i++) {
      if (!wallMatrix.rightLeanToDividerWall[i]) continue; // Skip if this divider is not enabled
      
      // Create a geometry for the divider wall
      rightLeanToDividerWallSheet_Geo.push({
        geo_x: 0.001,      // Thickness in meters
        geo_y: effectiveHeight / 1000, // Height in meters
        geo_z: rightLeanToSpan / 1000,  // Width in meters
        thickness: 0.001,              // Material thickness in meters
        material: roofMaterial        // Use roof material to match other walls
      });
      
      // Position the sheet at the divider location
      const x = (overhang + span + rightLeanToSpan/2) / 1000-.02; // Center of lean-to span
      const y = length / 1000 - ((i + 1) * length / bay_no) / 1000-.09; // Divider position
      const z = effectiveHeight / 2000; // Center vertically
      
      // Add position data
      rightLeanToDividerWallSheet_Positions.push({
        x, y, z,
        rotation: { x: 0, y: 0, z: 0 } // Rotation for divider wall
      });
    }
  }
  
  /**
   * Process Divider Wall Sheets
   */
  if (wallMatrix.mainDividerWall && wallMatrix.mainDividerWall.length > 0) {
    // Process each divider wall
    for (let i = 0; i < wallMatrix.mainDividerWall.length; i++) {
      if (wallMatrix.mainDividerWall[i]) {
        // Calculate the standard sheet dimensions
        const { width, height: sheetHeight, thickness } = getWallSheetDimensions();
        
        // Calculate the skillion roof height increase due to pitch
        const pitchRadians = pitch*Math.PI/180; // Convert pitch to radians
        const heightIncreaseDueToSpan = (span + overhang) * Math.tan(pitchRadians); // Calculate the height increase
        const peakHeight = height + heightIncreaseDueToSpan; // Height at the higher side
        
        // Standard corrugated steel dimensions
        const corrugationDepth = 16; // Depth of corrugation in mm
        const corrugationWidth = 76; // Width of one corrugation wave in mm
        
        // Create a single drop sheet for the divider wall with corrugation pattern
        
        // Import the THREE.js for the corrugation pattern
        const { THREE } = require('expo-three');
        
        // Define the skillion outline shape (points defining the entire wall)
        // For a skillion roof, the top is sloped from lower to higher side
        const wallOutline: Array<[number, number]> = [
          [0, 0],                // Bottom left corner
          [span, 0],             // Bottom right corner
          [span, peakHeight],    // Top right corner (higher side)
          [0, height],           // Top left corner (lower side)
        ];
        
        // Create a shape to hold the corrugated profile
        const wallShape = new THREE.Shape();
        // Start from the first point of the wall outline
        wallShape.moveTo(wallOutline[0][0] / 1000, wallOutline[0][1] / 1000);
        
        // Add corrugation along the bottom edge
        const numWaves = Math.floor(span / corrugationWidth);
        const actualWaveWidth = span / numWaves;
        
        // Start at bottom left corner
        wallShape.lineTo(0, 0);
        
        // Add corrugation waves along the bottom (alternating up and down)
        for (let j = 0; j < numWaves; j++) {
          const waveStartX = j * actualWaveWidth / 1000;
          const wavePeakX = (j * actualWaveWidth + actualWaveWidth / 2) / 1000;
          const waveEndX = ((j + 1) * actualWaveWidth) / 1000;
          
          // Valley to peak
          wallShape.lineTo(wavePeakX, corrugationDepth / 1000);
          // Peak to valley
          wallShape.lineTo(waveEndX, 0);
        }
        
        // Continue with the right edge, top edge and left edge of wall
        wallShape.lineTo(wallOutline[1][0] / 1000, wallOutline[1][1] / 1000); // Bottom right
        wallShape.lineTo(wallOutline[2][0] / 1000, wallOutline[2][1] / 1000); // Top right (higher side)
        wallShape.lineTo(wallOutline[3][0] / 1000, wallOutline[3][1] / 1000); // Top left (lower side)
        wallShape.lineTo(wallOutline[0][0] / 1000, wallOutline[0][1] / 1000); // Back to start
        
        // Extract points from the shape for rendering
        const extractedPoints = wallShape.extractPoints(12); // Higher resolution
        const corrugatedWallPoints = extractedPoints.shape.map((point: { x: number; y: number }) => [
          point.x, // Already in meters
          point.y  // Already in meters
        ] as [number, number]);
        
        // Add the divider wall as a single sheet with the trapezoidal shape defined by the corrugated wall points
        mainDividerWallSheet_Geo.push({
          geo_x: 0.001,                  // Thickness in meters
          geo_y: peakHeight / 1000,     // Total height including pitch in meters
          geo_z: .001,                  // Width of the entire wall in meters
          thickness: 0.001,             // Material thickness in meters
          topEdge: corrugatedWallPoints, // The corrugated wall outline defines the trapezoidal shape
          material: roofMaterial        // Use the roof material
        });
        
        // Calculate the position for this divider wall
        // x: center of the span
        // y: position along the length axis of the carport
        // z: position at the base
        const x = overhang/1000+span / 2000;  // Center horizontally, accounting for overhang
        const y = length / 1000 - ((i + 1) * length / (wallMatrix.mainDividerWall.length + 1)) / 1000; // Divider position
        const z = (height + heightIncreaseDueToSpan/2) / 2000+.1;  // Center vertically
        
        // Add position data
        mainDividerWallSheet_Positions.push({
          x: x,
          y: y-.09,
          z: z,
          rotation: { x: Math.PI/2, y: Math.PI/2, z: 0 } // Rotation for divider wall
        });
      }
    }
  }
  
  // Combine all wall sheet data for return
  return {
    // Main carport wall sheets
    mainLeftWallSheet: {
      geometries: mainLeftWallSheet_Geo,
      positions: mainLeftWallSheet_Positions,
    },
    mainRightWallSheet: {
      geometries: mainRightWallSheet_Geo,
      positions: mainRightWallSheet_Positions,
    },
    mainFrontWallSheet: {
      geometries: mainFrontWallSheet_Geo,
      positions: mainFrontWallSheet_Positions,
    },
    mainBackWallSheet: {
      geometries: mainBackWallSheet_Geo,
      positions: mainBackWallSheet_Positions,
    },
    mainDividerWallSheet: {
      geometries: mainDividerWallSheet_Geo,
      positions: mainDividerWallSheet_Positions,
    },
    
    // Left lean-to wall sheets
    leftLeanToWallSheet: {
      geometries: leftLeanToWallSheet_Geo,
      positions: leftLeanToWallSheet_Positions,
    },
    leftLeanToFrontWallSheet: {
      geometries: leftLeanToFrontWallSheet_Geo,
      positions: leftLeanToFrontWallSheet_Positions,
    },
    leftLeanToBackWallSheet: {
      geometries: leftLeanToBackWallSheet_Geo,
      positions: leftLeanToBackWallSheet_Positions,
    },
    leftLeanToDividerWallSheet: {
      geometries: leftLeanToDividerWallSheet_Geo,
      positions: leftLeanToDividerWallSheet_Positions,
    },
    
    // Right lean-to wall sheets
    rightLeanToWallSheet: {
      geometries: rightLeanToWallSheet_Geo,
      positions: rightLeanToWallSheet_Positions,
    },
    rightLeanToFrontWallSheet: {
      geometries: rightLeanToFrontWallSheet_Geo,
      positions: rightLeanToFrontWallSheet_Positions,
    },
    rightLeanToBackWallSheet: {
      geometries: rightLeanToBackWallSheet_Geo,
      positions: rightLeanToBackWallSheet_Positions,
    },
    rightLeanToDividerWallSheet: {
      geometries: rightLeanToDividerWallSheet_Geo,
      positions: rightLeanToDividerWallSheet_Positions,
    },
  };
}
