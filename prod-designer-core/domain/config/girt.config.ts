import {
  post_size,
  EP_web,
  EP_flange,
  Rafter_web,
  Rafter_flange,
  Girt_web,
  Girt_flangeE,
  Girt_flangeF,
  Girt_lip,
  Girt_thickness
} from '@/constants/carport/CarportParams';

import { createStandardZeeSectionShape } from '@/domain/helpers/SectionHelper';
import { RoofType } from '@/types/carport';

// Define types for girt configuration
export type GirtGeometry = {
  geo_x: number; // width
  geo_y: number; // height
  geo_z: number; // length
  thickness?: number;
};

export type GirtPosition = {
  x: number;
  y: number;
  z: number;
  rotation?: {
    x: number;
    y: number;
    z: number;
  };
};

export type WallMatrixType = {
  // Main carport walls
  mainLeftWall: boolean[];
  mainRightWall: boolean[];
  mainDividerWall: boolean[];
  mainFrontWall: boolean;
  mainBackWall: boolean;
  // Lean-to walls
  leftLeanToWall: boolean[];
  rightLeanToWall: boolean[];
  leftLeanToDividerWall: boolean[];
  rightLeanToDividerWall: boolean[];
  // Front/back walls for lean-tos
  leftLeanToFrontWall: boolean | null;
  leftLeanToBackWall: boolean | null;
  rightLeanToFrontWall: boolean | null;
  rightLeanToBackWall: boolean | null;
};

/**
 * Calculate girt geometries and positions based on carport dimensions and wall configuration
 * 
 * @param span Width of the main carport section in mm
 * @param pitch Roof pitch in degrees
 * @param eaveheight Height at the eave in mm
 * @param overhang Overhang distance in mm
 * @param length Length of the carport in mm
 * @param leftLeanTo Whether left lean-to is enabled
 * @param leftLeanToSpan Width of the left lean-to in mm
 * @param leftLeanToDropHeight Height drop of the left lean-to roof in mm
 * @param rightLeanTo Whether right lean-to is enabled
 * @param rightLeanToSpan Width of the right lean-to in mm
 * @param rightLeanToDropHeight Height drop of the right lean-to roof in mm
 * @param wallMatrix Wall configuration matrix for the entire structure
 * @returns Object containing girt geometries and positions
 */
function calculateGirtGeometriesAndPositions(
  span: number = 6000,
  pitch: number = 15,
  eaveheight: number = 2400,
  overhang: number = 1000,
  length: number = 12000,
  leftLeanTo: boolean = false,
  leftLeanToSpan: number = 1000,
  leftLeanToDropHeight: number = 500,
  rightLeanTo: boolean = false,
  rightLeanToSpan: number = 1000,
  rightLeanToDropHeight: number = 500,
  wallMatrix: WallMatrixType,
  roofType: RoofType = 'Skillion'
) {
  // Debugging: log the wall matrix being used for girt calculations
  // Use the imported parameters directly from CarportParams.ts
  // The girt dimensions will be used with the createStandardZeeSectionShape function
  // for Z-section profile creation
  
  // Helper function to get dimensions in meters (with scale factor)
  const getGirtDimensions = () => {
    const scaleFactor = 1; // Scale factor for 3D visualization
    return {
      // The girt section shape will be created by createStandardZeeSectionShape
      // but we still need basic dimensions for position/orientation calculations
      width: (Girt_web * scaleFactor),      // Convert to meters 
      height: (Girt_flangeE+Girt_flangeF) * scaleFactor, // Convert to meters
      thickness: Girt_thickness,           // Convert to meters
      // Original dimensions in mm (for the Z-section shape creation)
      web: Girt_web,
      flangeE: Girt_flangeE,
      flangeF: Girt_flangeF,
      lip: Girt_lip
    };
  };
  
  // Calculate number of bays based on length
  const bay_no = Math.max(1, Math.floor(length / 4000));
  
  // Initialize arrays for geometries and positions
  const mainLeftGirt_Geo: GirtGeometry[] = [];
  const mainLeftGirt_Positions: GirtPosition[] = [];
  
  const mainRightGirt_Geo: GirtGeometry[] = [];
  const mainRightGirt_Positions: GirtPosition[] = [];
  
  const mainFrontGirt_Geo: GirtGeometry[] = [];
  const mainFrontGirt_Positions: GirtPosition[] = [];
  
  const mainBackGirt_Geo: GirtGeometry[] = [];
  const mainBackGirt_Positions: GirtPosition[] = [];
  
  const mainDividerGirt_Geo: GirtGeometry[] = [];
  const mainDividerGirt_Positions: GirtPosition[] = [];
  
  // Lean-to left girts
  const leftLeanToGirt_Geo: GirtGeometry[] = [];
  const leftLeanToGirt_Positions: GirtPosition[] = [];
  
  const leftLeanToFrontGirt_Geo: GirtGeometry[] = [];
  const leftLeanToFrontGirt_Positions: GirtPosition[] = [];
  
  const leftLeanToBackGirt_Geo: GirtGeometry[] = [];
  const leftLeanToBackGirt_Positions: GirtPosition[] = [];
  
  const leftLeanToDividerGirt_Geo: GirtGeometry[] = [];
  const leftLeanToDividerGirt_Positions: GirtPosition[] = [];
  
  // Lean-to right girts
  const rightLeanToGirt_Geo: GirtGeometry[] = [];
  const rightLeanToGirt_Positions: GirtPosition[] = [];
  
  const rightLeanToFrontGirt_Geo: GirtGeometry[] = [];
  const rightLeanToFrontGirt_Positions: GirtPosition[] = [];
  
  const rightLeanToBackGirt_Geo: GirtGeometry[] = [];
  const rightLeanToBackGirt_Positions: GirtPosition[] = [];
  
  const rightLeanToDividerGirt_Geo: GirtGeometry[] = [];
  const rightLeanToDividerGirt_Positions: GirtPosition[] = [];
  
  // Calculate girt positions - start at 200mm from ground, then every 600mm, up to eaveheight-200mm
  const girtZPositions: number[] = [];
  const leftLeanToGirtZPositions: number[] = [];
  const rightLeanToGirtZPositions: number[] = [];
  const girtSpacing = 600; // 600mm between girts
  const firstGirtHeight = 200; // 200mm from ground
  const lastGirtOffset = 200; // 200mm from eaveheight
  
  // Calculate how many girts we need for main carport
  let currentHeight = firstGirtHeight;
  while (currentHeight < eaveheight - lastGirtOffset) {
    // Convert mm to meters for Three.js
    girtZPositions.push(currentHeight / 1000);
    currentHeight += girtSpacing;
  }
  
  // Calculate girt positions for left lean-to (adjusted for drop height)
  if (leftLeanTo) {
    currentHeight = firstGirtHeight;
    const leftLeanToMaxHeight = eaveheight - leftLeanToDropHeight - lastGirtOffset;
    while (currentHeight < leftLeanToMaxHeight) {
      // Convert mm to meters for Three.js
      leftLeanToGirtZPositions.push(currentHeight / 1000);
      currentHeight += girtSpacing;
    }
  }
  
  // Calculate girt positions for right lean-to (adjusted for drop height)
  if (rightLeanTo) {
    currentHeight = firstGirtHeight;
    const rightLeanToMaxHeight = eaveheight - rightLeanToDropHeight - lastGirtOffset;
    while (currentHeight < rightLeanToMaxHeight) {
      // Convert mm to meters for Three.js
      rightLeanToGirtZPositions.push(currentHeight / 1000);
      currentHeight += girtSpacing;
    }
  }
  
  
  // Section length in meters
  const sectionLength = length / bay_no / 1000;
  
  // --------------------------
  // Main carport left wall girts
  // --------------------------
  for (let i = 0; i <bay_no; i++) {
    
    if (wallMatrix.mainLeftWall[i]) {
      
      // Only create girts for sections where walls are enabled
      
      // Calculate girt length - matches the section length
      const girtLength = sectionLength;
      
      // Get girt dimensions using helper function
      const { width, height, thickness } = getGirtDimensions();
      
      // Calculate base position for this wall section
      const x = (overhang + post_size / 2 + Rafter_flange) / 1000 - post_size / 2 / 1000;
      const y = (length - (i * length) / bay_no)/1000-sectionLength/2;
      
      
      
      // Create multiple girts at different heights for this wall section
      for (const zPosition of girtZPositions) {
        // Create geometry for this girt
        mainLeftGirt_Geo.push({
          geo_x: width, // Width in meters
          geo_y: height, // Height in meters
          geo_z: girtLength, // Length in meters
          thickness: thickness, // Thickness in meters
        });
        
        // Create position object (including rotation to correctly orient the girt)
        mainLeftGirt_Positions.push({
          x: x-.032,
          y: y, 
          z: zPosition, // Use the height from our calculated array
          rotation: { x: 0, y: -Math.PI/2, z: Math.PI } // Rotate to align with wall
        });
      }
    } else {
    }
  }


  // --------------------------
  // Main carport right wall girts
  // --------------------------
  for (let i = 0; i < bay_no; i++) {
    
    if (wallMatrix.mainRightWall[i]) {
      
      // Only create girts for sections where walls are enabled
      
      // Calculate girt length - matches the section length
      const girtLength = sectionLength;
      
      // Get girt dimensions using helper function
      const { width, height, thickness } = getGirtDimensions();

      // Calculate base position for this wall section
      const x = (overhang + span - Rafter_flange - post_size / 2) / 1000 - post_size / 2 / 1000;
      const y = (length - (i * length) / bay_no)/1000-sectionLength/2;
      // Create multiple girts at different heights for this wall section
      for (const zPosition of girtZPositions) {
        // Create geometry for this girt
        mainRightGirt_Geo.push({
          geo_x: width,
          geo_y: height,
          geo_z: girtLength,
          thickness: thickness,
        });
        
        // Create position object
        mainRightGirt_Positions.push({
          x: x+.11,
          y: y,
          z: zPosition, // Use the height from our calculated array
          rotation: { x: 0, y: Math.PI/2, z: 0 } // Rotate to align with wall
        });
      }
    } else {
    }
  }
  
  // --------------------------
  // Main carport divider walls
  // --------------------------  
  for (let i = 0; i < bay_no; i++) {
    
    if (wallMatrix.mainDividerWall[i]) {
      
      // Only create girts for sections where walls are enabled
      
      // Calculate girt length - will span the full width
      const girtLength = span / 1000; // Span in meters
      
      // Get girt dimensions using helper function
      const { width, height, thickness } = getGirtDimensions();

      // Calculate base position based on bay number
      const x = (overhang + span / 2) / 1000; // Centered on the span
      const y = length/1000-((i+1) * length / bay_no) / 1000; // Position based on bay
      
      // Create multiple girts at different heights
      for (const zPosition of girtZPositions) {
        // Create geometry for this girt
        mainDividerGirt_Geo.push({
          geo_x: width,
          geo_y: height,
          geo_z: girtLength-.13,
          thickness: thickness,
        });
        
        // Create position object
        mainDividerGirt_Positions.push({
          x: x,
          y: y-.032,
          z: zPosition, // Use the height from our calculated array
          rotation: roofType === 'Skillion' || roofType === 'Skillion Overhang' || roofType === 'Skillion Attached'
          ? { x: Math.PI/2, y: 0, z: Math.PI/2} // Rotation for skillion roof type
          : { x: Math.PI/2, y: 0, z: 0 } // Rotation for Gable
        });
      }
    } else {
    }
  }
  
  // --------------------------
  // Main carport front wall girt
  // --------------------------
  if (wallMatrix.mainFrontWall) {
    
    // Calculate girt length
    const girtLength = span / 1000; // Span in meters
    
    // Get girt dimensions using helper function
    const { width, height, thickness } = getGirtDimensions();

    // Calculate base position
    const x = (overhang + span / 2) / 1000;
    const y = EP_flange / 2 / 1000; // Y position at the front of the carport
    
    // Create multiple girts at different heights
    for (const zPosition of girtZPositions) {
      // Create geometry for this girt
      mainFrontGirt_Geo.push({
        geo_x: width,
        geo_y: height,
        geo_z: girtLength-.25,
        thickness: thickness,
      });
      
      // Create position object with rotation based on roof type
      mainFrontGirt_Positions.push({
        x: x,
        y: y+.01,
        z: zPosition, // Use the height from our calculated array
        rotation: roofType === 'Skillion' || roofType === 'Skillion Overhang' || roofType === 'Skillion Attached'
          ? { x: 0, y: 0, z: 0} // Rotation for skillion roof type
          : { x: Math.PI/2, y: 0, z: 0 } // Rotation for Gable
      });
    }
  } else {
  }
  
  // --------------------------
  // Main carport back wall girt
  // --------------------------
  if (wallMatrix.mainBackWall) {
    
    // Back wall girt spans the full width
    const girtLength = (span - post_size) / 1000; // Convert to meters
    
    // Get girt dimensions using helper function
    const { width, height, thickness } = getGirtDimensions();

    // Calculate base position
    const x = (overhang + span/2) / 1000; // Centered on the span
    const y = (length - EP_flange - post_size / 2) / 1000;
    
    // Create multiple girts at different heights
    for (const zPosition of girtZPositions) {
      // Create geometry for this girt
      mainBackGirt_Geo.push({
        geo_x: width,
        geo_y: height,
        geo_z: girtLength-.15,
        thickness: thickness,
      });
      
      // Create position object
      mainBackGirt_Positions.push({
        x: x, 
        y: y+.06,
        z: zPosition, // Use the height from our calculated array
        rotation: roofType === 'Skillion' || roofType === 'Skillion Overhang' || roofType === 'Skillion Attached'
          ? { x: 0, y: 0, z: 0} // Rotation for skillion roof type
          : { x: Math.PI/2, y: 0, z: 0 } // Rotation for Gable
      });
    }
  } else {
  }
  
  // --------------------------
  // Left Lean-to girts (if enabled)
  // --------------------------
  if (leftLeanTo && wallMatrix.leftLeanToWall.length > 0) {
    
    // Iterate through each section
    for (let i = 0; i < bay_no; i++) {
      
      if (wallMatrix.leftLeanToWall[i]) {
        
        // Only create girts for sections where walls are enabled
        
        // Calculate girt length - matches the section length
        const girtLength = sectionLength;
        
        // Get girt dimensions using helper function
        const { width, height, thickness } = getGirtDimensions();
        
        // Calculate base position
        const x = (overhang - leftLeanToSpan) / 1000; // Centered on left lean-to span
        const y = (length - (i * length) / bay_no)/1000-sectionLength/2;
        
        // Create multiple girts at different heights
        for (const zPosition of leftLeanToGirtZPositions) {
          // Create geometry for each girt
          leftLeanToGirt_Geo.push({
            geo_x: width,
            geo_y: height,
            geo_z: girtLength,
            thickness: thickness,
          });
          
          // Create position object for each girt
          leftLeanToGirt_Positions.push({
            x: x,
            y: y,
            z: zPosition, // Use the position from the array
            rotation: { x: Math.PI, y: 0, z: -Math.PI/2 } // Rotate to align with wall
          });
        }
      }
    }
    
    // Left lean-to divider walls
    for (let i = 0; i < bay_no - 1; i++) {
      if (wallMatrix.leftLeanToDividerWall[i]) {
        // Only create girts for divider walls that are enabled
        
        // Calculate girt length - matches the lean-to span
        const girtLength = leftLeanToSpan / 1000; // Lean-to span in meters
        
        // Get girt dimensions using helper function
        const { width, height, thickness } = getGirtDimensions();
        
        // Calculate base position
        const dividerY = length/1000-((i) * length) / bay_no/1000 -sectionLength;
        const dividerX = (overhang - leftLeanToSpan/2+64) / 1000; // Centered on left lean-to
        
        // Create multiple girts at different heights
        for (const zPosition of leftLeanToGirtZPositions) {
          // Create geometry for each girt
          leftLeanToDividerGirt_Geo.push({
            geo_x: width,
            geo_y: height,
            geo_z: girtLength,
            thickness: thickness,
          });
          
          // Create position object for each girt
          leftLeanToDividerGirt_Positions.push({
            x: dividerX,
            y: dividerY-.04,
            z: zPosition, // Use the position from the array
            rotation: { x: -Math.PI/2, y: 0, z: Math.PI } // Rotate for lean-to divider
          });
        }
      }
    }
    
    // Left lean-to front wall
    if (wallMatrix.leftLeanToFrontWall) {
      // Front wall girt spans the lean-to width
      const girtLength = leftLeanToSpan / 1000; // Lean-to span in meters
      
      // Get girt dimensions using helper function
      const { width, height, thickness } = getGirtDimensions();
      
      // Calculate base position
      const x = (overhang - leftLeanToSpan/2+64) / 1000; // Centered on lean-to
      const y = post_size / 2 / 1000; // Front position
      
      // Create multiple girts at different heights
      for (const zPosition of leftLeanToGirtZPositions) {
        // Create geometry for each girt
        leftLeanToFrontGirt_Geo.push({
          geo_x: width,
          geo_y: height,
          geo_z: girtLength,
          thickness: thickness,
        });
        
        // Create position object for each girt
        leftLeanToFrontGirt_Positions.push({
          x: x,
          y: y,
          z: zPosition, // Use the position from the array
          rotation: { x: -Math.PI/2, y: 0, z: Math.PI } // Rotate for lean-to front
        });
      }
    }
    
    // Left lean-to back wall
    if (wallMatrix.leftLeanToBackWall) {
      // Back wall girt spans the lean-to width
      const girtLength = leftLeanToSpan / 1000; // Lean-to span in meters
      
      // Get girt dimensions using helper function
      const { width, height, thickness } = getGirtDimensions();
      
      // Calculate base position
      const x = (overhang - leftLeanToSpan/2+64) / 1000; // Centered on lean-to
      const y = length / 1000 - post_size / 2 / 1000; // Back position
      
      // Create multiple girts at different heights
      for (const zPosition of leftLeanToGirtZPositions) {
        // Create geometry for each girt
        leftLeanToBackGirt_Geo.push({
          geo_x: width,
          geo_y: height,
          geo_z: girtLength,
          thickness: thickness,
        });
        
        // Create position object for each girt
        leftLeanToBackGirt_Positions.push({
          x: x,
          y: y,
          z: zPosition, // Use the position from the array
          rotation: { x: -Math.PI/2, y: 0, z: 0 } // Rotate for lean-to back
        });
      }
    }
  }
  
  // --------------------------
  // Right Lean-to girts (if enabled)
  // --------------------------
  if (rightLeanTo && wallMatrix.rightLeanToWall.length > 0) {
    // Iterate through each section
    for (let i = 0; i < bay_no; i++) {
      if (wallMatrix.rightLeanToWall[i]) {
        // Only create girts for sections where walls are enabled
        
        // Calculate girt length - matches the section length
        const girtLength = sectionLength;
        
        // Get girt dimensions using helper function
        const { width, height, thickness } = getGirtDimensions();
        
        // Calculate base position
        const x = (overhang + span + rightLeanToSpan) / 1000; // Right edge position
        const y = (length - (i * length) / bay_no)/1000-sectionLength/2;
        
        // Create multiple girts at different heights
        for (const zPosition of rightLeanToGirtZPositions) {
          // Create geometry for each girt
          rightLeanToGirt_Geo.push({
            geo_x: width,
            geo_y: height,
            geo_z: girtLength,
            thickness: thickness,
          });
          
          // Create position object for each girt
          rightLeanToGirt_Positions.push({
            x: x,
            y: y,
            z: zPosition, // Use the position from the array
            rotation: { x: 0, y: Math.PI, z: -Math.PI/2 } // Rotate to align with wall
          });
        }
      }
    }
    
    // Right lean-to divider walls
    for (let i = 0; i < bay_no - 1; i++) {
      if (wallMatrix.rightLeanToDividerWall[i]) {
        // Only create girts for divider walls that are enabled
        
        // Calculate girt length - matches the lean-to span
        const girtLength = rightLeanToSpan / 1000; // Lean-to span in meters
        
        // Get girt dimensions using helper function
        const { width, height, thickness } = getGirtDimensions();
        
        // Calculate base position
        const dividerY = length/1000-((i) * length) / bay_no/1000 -sectionLength;
        const dividerX = (overhang + span + rightLeanToSpan/2-64) / 1000; // Position for right lean-to
        
        // Create multiple girts at different heights
        for (const zPosition of rightLeanToGirtZPositions) {
          // Create geometry for each girt
          rightLeanToDividerGirt_Geo.push({
            geo_x: width,
            geo_y: height,
            geo_z: girtLength,
            thickness: thickness,
          });
          
          // Create position object for each girt
          rightLeanToDividerGirt_Positions.push({
            x: dividerX,
            y: dividerY-.04,
            z: zPosition, // Use the position from the array
            rotation: { x: -Math.PI/2, y: 0, z: Math.PI } // Rotate for lean-to divider
          });
        }
      }
    }
    
    // Right lean-to front wall
    if (wallMatrix.rightLeanToFrontWall) {
      // Front wall girt spans the lean-to width
      const girtLength = rightLeanToSpan / 1000; // Lean-to span in meters
      
      // Get girt dimensions using helper function
      const { width, height, thickness } = getGirtDimensions();
      
      // Calculate base position
      const x = (overhang + span + rightLeanToSpan/2-64) / 1000; // Position for right lean-to
      const y = post_size / 2 / 1000; // Front position
      
      // Create multiple girts at different heights
      for (const zPosition of rightLeanToGirtZPositions) {
        // Create geometry for each girt
        rightLeanToFrontGirt_Geo.push({
          geo_x: width,
          geo_y: height,
          geo_z: girtLength,
          thickness: thickness,
        });
        
        // Create position object for each girt
        rightLeanToFrontGirt_Positions.push({
          x: x,
          y: y,
          z: zPosition, // Use the position from the array
          rotation: { x: -Math.PI/2, y: 0, z: Math.PI } // Rotate for lean-to front
        });
      }
    }
    
    // Right lean-to back wall
    if (wallMatrix.rightLeanToBackWall) {
      // Back wall girt spans the lean-to width
      const girtLength = rightLeanToSpan / 1000; // Lean-to span in meters
      
      // Get girt dimensions using helper function
      const { width, height, thickness } = getGirtDimensions();
      
      // Calculate base position
      const x = (overhang + span + rightLeanToSpan/2-64) / 1000; // Position for right lean-to
      const y = length / 1000 - post_size / 2 / 1000; // Back position
      
      // Create multiple girts at different heights
      for (const zPosition of rightLeanToGirtZPositions) {
        // Create geometry for each girt
        rightLeanToBackGirt_Geo.push({
          geo_x: width,
          geo_y: height,
          geo_z: girtLength,
          thickness: thickness,
        });
        
        // Create position object for each girt
        rightLeanToBackGirt_Positions.push({
          x: x,
          y: y,
          z: zPosition, // Use the position from the array
          rotation: { x: -Math.PI/2, y: 0, z: 0 } // Rotate for lean-to back
        });
      }
    }
  }
  
  // Combine all girt data for return
  return {
    // Main carport girts
    mainLeftGirt: {
      geometries: mainLeftGirt_Geo,
      positions: mainLeftGirt_Positions,
    },
    mainRightGirt: {
      geometries: mainRightGirt_Geo,
      positions: mainRightGirt_Positions,
    },
    mainFrontGirt: {
      geometries: mainFrontGirt_Geo,
      positions: mainFrontGirt_Positions,
    },
    mainBackGirt: {
      geometries: mainBackGirt_Geo,
      positions: mainBackGirt_Positions,
    },
    mainDividerGirt: {
      geometries: mainDividerGirt_Geo,
      positions: mainDividerGirt_Positions,
    },
    
    // Left lean-to girts
    leftLeanToGirt: {
      geometries: leftLeanToGirt_Geo,
      positions: leftLeanToGirt_Positions,
    },
    leftLeanToFrontGirt: {
      geometries: leftLeanToFrontGirt_Geo,
      positions: leftLeanToFrontGirt_Positions,
    },
    leftLeanToBackGirt: {
      geometries: leftLeanToBackGirt_Geo,
      positions: leftLeanToBackGirt_Positions,
    },
    leftLeanToDividerGirt: {
      geometries: leftLeanToDividerGirt_Geo,
      positions: leftLeanToDividerGirt_Positions,
    },
    
    // Right lean-to girts
    rightLeanToGirt: {
      geometries: rightLeanToGirt_Geo,
      positions: rightLeanToGirt_Positions,
    },
    rightLeanToFrontGirt: {
      geometries: rightLeanToFrontGirt_Geo,
      positions: rightLeanToFrontGirt_Positions,
    },
    rightLeanToBackGirt: {
      geometries: rightLeanToBackGirt_Geo,
      positions: rightLeanToBackGirt_Positions,
    },
    rightLeanToDividerGirt: {
      geometries: rightLeanToDividerGirt_Geo,
      positions: rightLeanToDividerGirt_Positions,
    },
  };
}

export type GirtConfig = ReturnType<typeof calculateGirtGeometriesAndPositions>;

export default calculateGirtGeometriesAndPositions;
