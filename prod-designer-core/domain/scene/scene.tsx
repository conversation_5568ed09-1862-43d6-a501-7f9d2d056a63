import { useRef } from 'react';
import { Platform } from 'react-native';
import { use<PERSON>rame, useThree } from '@react-three/fiber/native';
import { OrbitControls, OrthographicCamera, PerspectiveCamera } from '@react-three/drei/native';
import Carport from '@/domain/carport-models/Carport';
import { WallMatrixType } from '@/domain/config/girt.config';
import GableCarport from '@/domain/carport-models/gable_Carport';
import { THREE } from 'expo-three';
import Grass from '@/components/3d-environment/Grass';
import SkyBox from '@/components/3d-environment/SkyBox';
import { setSizeValue, setSceneSetting, useSceneContext } from '@/redux/context';
import { PivotControls } from '@/components/3d-controls/pivotControls';
import { useCanvasScissor } from '@/hooks/useCanvasScissor';
import MeasurementText from '@/components/3d-measurement/measurement-text';
import Car from '@/domain/3d-models/car-model';
import { updateMaterialColor, materialsUpdated } from '@/domain/materials/materials-pool';
import { CarportMaterialType } from '@/constants/materials/Material';
import { useEffect } from 'react';
import { PriceIndicator } from '@/components/indicators/PriceIndicator';
import { Box3, Vector3 } from 'three';

let l0 = 0;
let s0 = 0;
let h0 = 0;

function Scene({ wallMatrix }: { wallMatrix?: WallMatrixType }) {
  const centerGroup = useRef<THREE.Group>(null!);
  const perspectiveCameraRef = useRef<THREE.PerspectiveCamera>(null!);
  const orthoCameraRef = useRef<THREE.OrthographicCamera>(null!);
  const controlsRef = useRef<any>(null);
  const { dimensions, dispatch, sceneSettings } = useSceneContext();
  const { height, span, length, pitch, overhang, roofType } = dimensions;
  const { 
    leftLeanTo, leftLeanToSpan, leftLeanToDropHeight, 
    rightLeanTo, rightLeanToSpan, rightLeanToDropHeight,
    roofColor, wallColor, trimColor, doorColor,
    cameraOrientation, viewMode 
  } = sceneSettings;
  const { invalidate } = useThree();
  useEffect(() => {
    if (!centerGroup.current || !perspectiveCameraRef.current) return;

    const box = new Box3().setFromObject(centerGroup.current);
    const size = new Vector3();
    const center = new Vector3();
    box.getSize(size);
    box.getCenter(center);

    // Add a minimum dimension to prevent over-zoom
    const safeSize = {
      x: Math.max(size.x, 2),
      y: Math.max(size.y, 2),
      z: Math.max(size.z, 2),
    };

    const maxDim = Math.max(safeSize.x, safeSize.y, safeSize.z);
    const fov = perspectiveCameraRef.current.fov * (Math.PI / 180);
    const baseZ = Math.abs(maxDim / 2 / Math.tan(fov / 2));

    const distanceMultiplier = 1.8;
    const cameraZ = baseZ * distanceMultiplier;

    const camera = perspectiveCameraRef.current;
    camera.position.set(center.x + cameraZ * 0.4, center.y + cameraZ * 0.5, center.z + cameraZ);
    camera.lookAt(center);
    camera.updateProjectionMatrix();

    invalidate();
  }, [span, length, height, leftLeanToSpan, rightLeanToSpan, overhang]);

  // Apply default colors on initial load
  useEffect(() => {
    // Apply the default colors from scene settings
    updateMaterialColor(CarportMaterialType.Roof, roofColor || 'NightSky');
    updateMaterialColor(CarportMaterialType.BrickWall, wallColor || 'Classic Cream');
    updateMaterialColor(CarportMaterialType.Cap, trimColor || 'Surfmist');
    // Note: Door color is set in context but not applied to a specific material yet
  }, []);  // Empty dependency array ensures this runs only once on mount

  function onDragStart({ axis }: { axis: 0 | 1 | 2 }) {
    l0 = axis === 1 ? length : 0;
    s0 = axis === 0 ? span : 0;
    h0 = axis === 2 ? height : 0;
  }

  function onDragEnd() {
    l0 = 0;
    s0 = 0;
    h0 = 0;
  }

  function onDrag(
    local: THREE.Matrix4,
    deltaL: THREE.Matrix4,
    world: THREE.Matrix4,
    deltaW: THREE.Matrix4,
  ) {
    const v = new THREE.Vector3();
    v.setFromMatrixPosition(deltaL);
    const [x, y, z] = v.toArray().map((x) => Math.floor(x * 10) / 10);

    // Calculate adjustments based on roof type
    const heightAdjustment =
      roofType === 'Gable'
        ? (y * 1000) / Math.cos((pitch * Math.PI) / 180) // Adjust for roof pitch
        : y * 1000;

    if (y && h0) {
      dispatch(setSizeValue({ height: h0 + heightAdjustment }));
      l0 = 0;
      s0 = 0;
    }
    if (z && l0) {
      const lengthAdjustment = l0 - z * 1000;
      dispatch(setSizeValue({ length: Math.max(1000, lengthAdjustment) })); // Minimum 1m length
      s0 = 0;
      h0 = 0;
    }
    if (x && s0) {
      const spanAdjustment = s0 + x * 1000;
      dispatch(setSizeValue({ span: Math.max(1000, spanAdjustment) })); // Minimum 1m span
      l0 = 0;
      h0 = 0;
    }
  }

  useFrame(() => {
    if (!centerGroup.current) {
      return;
    }
    
    // Calculate total width including lean-tos
    const leftLeanToWidth = leftLeanTo ? (leftLeanToSpan || 0) : 0;
    const rightLeanToWidth = rightLeanTo ? (rightLeanToSpan || 0) : 0;
    const totalWidth = span + leftLeanToWidth + rightLeanToWidth + (overhang * 2);
    
    centerGroup.current.matrixWorld.identity();
    centerGroup.current.position.set(-totalWidth / 2000, 0, length / 2000);
    
    // Handle camera orientation changes from ViewCube
    // Only process orientation changes once by checking for a specific orientation value
    if (cameraOrientation && controlsRef.current) {
      const camera = sceneSettings.defaultCamera === 'perspective' ? 
        perspectiveCameraRef.current : orthoCameraRef.current;
      
      if (camera && controlsRef.current.reset) {
        // Apply the camera orientation
        switch (cameraOrientation) {
          case 'front':
            camera.position.set(0, 0, 10);
            camera.lookAt(0, 0, 0);
            break;
          case 'back':
            camera.position.set(0, 0, -10);
            camera.lookAt(0, 0, 0);
            break;
          case 'left':
            camera.position.set(-10, 0, 0);
            camera.lookAt(0, 0, 0);
            break;
          case 'right':
            camera.position.set(10, 0, 0);
            camera.lookAt(0, 0, 0);
            break;
          case 'top':
            camera.position.set(0, 10, 0);
            camera.lookAt(0, 0, 0);
            break;
          case 'bottom':
            camera.position.set(0, -10, 0);
            camera.lookAt(0, 0, 0);
            break;
          case 'iso':
          default:
            camera.position.set(10, 10, 10);
            camera.lookAt(0, 0, 0);
            break;
        }
        
        // Clear the orientation immediately to prevent continuous reapplication
        // This is critical to avoid the infinite refresh loop
        // Use a more direct approach to avoid the infinite refresh loop
        // We'll use a local variable to track if we've already handled this orientation
        const currentOrientation = cameraOrientation;
        if (currentOrientation) {
          // Clear the orientation by dispatching an action
          dispatch(setSceneSetting('cameraOrientation', undefined));
        }
      }
    }
  });

  useCanvasScissor(sceneSettings.gridView);

  // Update materials when color selections change
  useEffect(() => {
    if (roofColor) {
      updateMaterialColor(CarportMaterialType.Roof, roofColor);
    }
  }, [roofColor]);

  useEffect(() => {
    if (wallColor) {
      updateMaterialColor(CarportMaterialType.BrickWall, wallColor);
    }
  }, [wallColor]);

  useEffect(() => {
    if (trimColor) {
      // Update only the Cap material for trim color (ridge caps and barge caps)
      updateMaterialColor(CarportMaterialType.Cap, trimColor);
    }
  }, [trimColor]);

  // Check if we're in plan view mode
  if (sceneSettings.isPlanView) {
    // Return early with just a container for the PlanView component
    // The actual PlanView component will be rendered separately
    return null;
  }
  
  return (
    <>
      <ambientLight intensity={0.5} />
      <directionalLight
        position={[10, 10, 10]}
        intensity={1}
        castShadow
        shadow-mapSize-width={2048}
        shadow-mapSize-height={2048}
      />
      <SkyBox visible={!sceneSettings.exportMode && sceneSettings.defaultCamera === 'perspective'} />
      {/* <Grass visible={!sceneSettings.exportMode && sceneSettings.defaultCamera === 'perspective'} /> */}
      {sceneSettings.defaultCamera === 'perspective' ? (
        <PerspectiveCamera
          ref={perspectiveCameraRef}
          makeDefault
          position={[10, 10, 10]}
          fov={75}
          near={0.1}
          far={1000}
        />
      ) : (
        <OrthographicCamera
          ref={orthoCameraRef}
          makeDefault
          position={[10, 10, 10]}
          zoom={40}
          near={0.1}
          far={1000}
        />
      )}
      <OrbitControls
        ref={controlsRef}
        enableZoom={true}
        enableRotate={true}
        enablePan={Platform.OS === 'web'}
        enableDamping={false}
        dampingFactor={0.03}
        makeDefault
        maxPolarAngle={Math.PI / 2}
        onChange={() => {
          // Clear the active orientation when user manually rotates
          if (sceneSettings.activeOrientation) {
            dispatch(setSceneSetting('activeOrientation', undefined));
          }
        }}
      />
      <group ref={centerGroup}>
        {/* PivotControls commented out to disable on-screen drag
        <PivotControls
          visible={false}
          onDrag={onDrag}
          onDragStart={onDragStart}
          onDragEnd={onDragEnd}
          scale={2}
          anchor={[1, -0.7, -1]}
          activeAxes={[true, true, true]}
          disableSliders
          disableRotations
          rotation={[-Math.PI / 2, 0, 0]}
        >*/}
          {/* Debug roof type - using a self-executing function to avoid JSX issues */}
          {/* Roof type debugging removed for production */}
          {roofType === 'Gable' ? (
            <GableCarport
              length={length}
              span={span}
              height={height}
              pitch={pitch}
              overhang={overhang}
              leftLeanTo={leftLeanTo}
              leftLeanToSpan={leftLeanToSpan}
              leftLeanToDropHeight={leftLeanToDropHeight}
              rightLeanTo={rightLeanTo}
              rightLeanToSpan={rightLeanToSpan}
              rightLeanToDropHeight={rightLeanToDropHeight}
              viewMode={viewMode}
              showSlab={sceneSettings.showSlab}
            />
          ) : (
            <Carport
              length={length}
              span={span}
              height={height}
              pitch={pitch}
              overhang={overhang}
              viewMode={viewMode}
              showSlab={sceneSettings.showSlab}
              wallMatrix={wallMatrix}
            />
          )}
        {/*</PivotControls>*/}
      </group>
      <MeasurementText
        length={length / 1000}
        span={span / 1000}
        height={height / 1000}
        pitch={pitch}
        overhang={overhang / 1000}
        leftLeanTo={leftLeanTo}
        leftLeanToSpan={leftLeanToSpan ? leftLeanToSpan / 1000 : 0}
        leftLeanToDropHeight={leftLeanToDropHeight ? leftLeanToDropHeight / 1000 : 0}
        rightLeanTo={rightLeanTo}
        rightLeanToSpan={rightLeanToSpan ? rightLeanToSpan / 1000 : 0}
        rightLeanToDropHeight={sceneSettings.rightLeanToDropHeight ? sceneSettings.rightLeanToDropHeight / 1000 : 0}
        lineColor="white"
        //bayNum={Math.floor(length / 4000)}
        visible={!sceneSettings.exportMode && sceneSettings.defaultCamera === 'perspective' && sceneSettings.showMeasurements !== false}
        roofType={roofType}
      />
      <Car />
    </>
  );
}

export default Scene;
