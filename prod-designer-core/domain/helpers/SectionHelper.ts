import { THREE } from 'expo-three';

export function createCeeSectionShape(geo_x: number, geo_y: number, flangeThickness: number) {
  const shape = new THREE.Shape();

  shape.moveTo(0, 0);
  shape.lineTo(geo_x, 0);
  shape.lineTo(geo_x, 0.015);
  shape.lineTo(geo_x - flangeThickness, 0.015);
  shape.lineTo(geo_x - flangeThickness, flangeThickness);
  shape.lineTo(flangeThickness, flangeThickness);
  shape.lineTo(flangeThickness, geo_y - flangeThickness);
  shape.lineTo(geo_x - flangeThickness, geo_y - flangeThickness);
  shape.lineTo(geo_x - flangeThickness, geo_y - 0.015);
  shape.lineTo(geo_x, geo_y - 0.015);
  shape.lineTo(geo_x, geo_y);
  shape.lineTo(0, geo_y);
  shape.lineTo(0, 0);

  return shape;
}

/**
 * Creates a 2D shape for a gable roof ridge cap based on roof pitch
 * @param width - Width of the ridge cap (typically 200-300mm)
 * @param thickness - Material thickness
 * @param pitch - Roof pitch in degrees
 * @returns THREE.Shape for the ridge cap
 */
export function createGableRidgeCap2DShape(width: number, thickness: number, pitch: number) {
  const shape = new THREE.Shape();
  
  // Convert pitch to radians
  const pitchRadians = (pitch * Math.PI) / 180;
  
  // Calculate the height based on pitch angle
  const height = Math.tan(pitchRadians) * (width / 2);
  
  // Create ridge cap shape
  shape.moveTo(0, 0);
  shape.lineTo(width, 0);
  shape.lineTo(width, -thickness);
  shape.lineTo(width - 20/1000, -thickness - 10/1000); // Add a small bend for water runoff
  shape.lineTo(width / 2, -thickness - height); // Center point
  shape.lineTo(20/1000, -thickness - 10/1000); // Matching bend on the other side
  shape.lineTo(0, -thickness);
  shape.lineTo(0, 0);
  
  return shape;
}

export function createTHSection(width: number, height: number, thickness: number) {
  const shape = new THREE.Shape();
  shape.moveTo(0, 0);
  shape.lineTo(0, thickness);
  shape.lineTo(width / 6, thickness);
  shape.lineTo((2 * width) / 6, height + thickness);
  shape.lineTo((4 * width) / 6, height + thickness);
  shape.lineTo((5 * width) / 6, thickness);
  shape.lineTo(width, thickness);
  shape.lineTo(width, -thickness);
  shape.lineTo((5 * width) / 6, -thickness);
  shape.lineTo((4 * width) / 6, height - thickness);
  shape.lineTo((2 * width) / 6, height - thickness);
  shape.lineTo(width / 6, -thickness);
  shape.lineTo(0, -thickness);
  shape.lineTo(0, 0);
  return shape;
}

/**
 * Creates a 2D shape for a gable roof ridge cap based on roof pitch
 * @param width - Width of the ridge cap (typically 200-300mm)
 * @param thickness - Material thickness
 * @param pitch - Roof pitch in degrees
 * @returns THREE.Shape for the ridge cap
 */


export function createZeeSectionShape(
  geo_x: number,
  geo_y: number,
  webThickness: number,
  flangeThickness: number,
) {
  const shape = new THREE.Shape();
  
  // Create a proper Z-section with realistic proportions
  // Starting at bottom left
  shape.moveTo(0, 0);
  // Bottom flange (extends right)
  shape.lineTo(flangeThickness * 3, 0);
  // Up the diagonal web
  shape.lineTo(geo_x - flangeThickness * 3, geo_y - webThickness);
  // Top flange (extends right)
  shape.lineTo(geo_x, geo_y - webThickness);
  shape.lineTo(geo_x, geo_y);
  // Back to the web
  shape.lineTo(geo_x - flangeThickness * 3, geo_y);
  // Back down the diagonal web
  shape.lineTo(flangeThickness * 3, webThickness);
  // Bottom flange end
  shape.lineTo(0, webThickness);
  // Close shape
  shape.lineTo(0, 0);

  return shape;
}



export function createStandardZeeSectionShape(
  web: number,
  flangeE: number,
  flangeF: number,
  lip: number,
  thickness: number
) {
  const shape = new THREE.Shape();
  shape.moveTo(-flangeF, lip);
  shape.lineTo(-flangeF, 0);
  shape.lineTo(0, 0);
  shape.lineTo(0, web);
  shape.lineTo(flangeE, web);
  shape.lineTo(flangeE, web-lip);
  shape.lineTo(flangeE+thickness, web-lip);
  shape.lineTo(flangeE+thickness, web+thickness);
  shape.lineTo(-thickness, web+thickness);
  shape.lineTo(-thickness, thickness);
  shape.lineTo(-flangeF+thickness, thickness);
  shape.lineTo(-flangeF+thickness, lip);
  
  return shape
}

export function createSHSShape(geo_x: number, geo_y: number) {
  const shape = new THREE.Shape();
  const wallThickness = 0.002;

  // Outer square
  shape.moveTo(0, 0);
  shape.lineTo(geo_x, 0);
  shape.lineTo(geo_x, geo_y);
  shape.lineTo(0, geo_y);
  shape.lineTo(0, 0);

  // Inner square (hollow part)
  const hole = new THREE.Path();
  hole.moveTo(wallThickness, wallThickness);
  hole.lineTo(geo_x - wallThickness, wallThickness);
  hole.lineTo(geo_x - wallThickness, geo_y - wallThickness);
  hole.lineTo(wallThickness, geo_y - wallThickness);
  hole.lineTo(wallThickness, wallThickness);

  shape.holes.push(hole);

  return shape;
}


// Zee section shape is already defined above

export function createMonoClad2DSection(height: number, thickness: number, singlelength: number) {
  const shape = new THREE.Shape();
  const monoPoints = [
    [7.3 / 1000, 0],
    [19.7 / 1000, height],
    [51.0 / 1000, height],
    [63.4 / 1000, 0],
    [106.3 / 1000, 0],
    [109.2 / 1000, 3.2 / 1000],
    [130.7 / 1000, 3.2 / 1000],
    [152.2 / 1000, 3.2 / 1000],
    [155.1 / 1000, 0],
    [187.9 / 1000, 0],
  ];

  let currentLength = 0;
  const connectPoints = [];

  while (currentLength < singlelength) {
    for (const point of monoPoints) {
      const newX = point[0] + currentLength;
      if (newX >= singlelength) {
        connectPoints.push([singlelength, point[1]]);
        currentLength = singlelength;
        break;
      }
      connectPoints.push([newX, point[1]]);
    }
    currentLength += monoPoints[monoPoints.length - 1][0];
  }

  const half_length = connectPoints.length;
  for (let i = half_length - 1; i >= 0; i--) {
    connectPoints.push([connectPoints[i][0], connectPoints[i][1] + thickness]);
  }

  shape.moveTo(0, 0);
  for (let j = 0; j < connectPoints.length; j++) {
    shape.lineTo(connectPoints[j][0], connectPoints[j][1]);
  }

  return shape;
}


export function createCorro2DSection(height: number, thickness: number, singlelength: number) {
  const shape = new THREE.Shape();
  
  // Define the points for a single corrugated wave pattern
  // Standard corrugated profile has a wave pattern with regular peaks and valleys
  // Typical corrugated sheet has a pitch of 76mm and a height of 16-18mm
  const corroWaveWidth = 76 / 1000; // 76mm standard width of one corrugated wave
  const corroWaveHeight = 17 / 1000; // 17mm standard height of corrugation
  
  // Define points for a single corrugated wave (5 points per wave)
  const corroPoints = [
    [0, 0], // Start at bottom
    [corroWaveWidth * 0.25, corroWaveHeight], // Rise to peak
    [corroWaveWidth * 0.5, 0], // Down to valley
    [corroWaveWidth * 0.75, corroWaveHeight], // Rise to next peak
    [corroWaveWidth, 0], // Down to next valley
  ];

  let currentLength = 0;
  const connectPoints = [];

  // Generate points along the length
  while (currentLength < singlelength) {
    for (const point of corroPoints) {
      const newX = point[0] + currentLength;
      if (newX >= singlelength) {
        connectPoints.push([singlelength, point[1]]);
        currentLength = singlelength;
        break;
      }
      connectPoints.push([newX, point[1]]);
    }
    currentLength += corroWaveWidth;
  }

  // Create the top surface by mirroring points with added thickness
  const half_length = connectPoints.length;
  for (let i = half_length - 1; i >= 0; i--) {
    connectPoints.push([connectPoints[i][0], connectPoints[i][1] + thickness]);
  }

  // Draw the shape
  shape.moveTo(0, 0);
  for (let j = 0; j < connectPoints.length; j++) {
    shape.lineTo(connectPoints[j][0], connectPoints[j][1]);
  }

  return shape;
}

/**
 * Creates a 2D shape for a barge cap flashing (for the left edge of the roof)
 * @param plateWidth - Width of each plate (typically around 170mm)
 * @param thickness - Material thickness (typically 1mm)
 * @param pitch - Roof pitch in degrees
 * @returns THREE.Shape for the barge cap
 */
export function createLeftBargeCapFlashing2DShape(plateWidth: number, thickness: number, pitch: number) {
  const shape = new THREE.Shape();
  
  // Convert pitch to radians
  const pitchRadians = (pitch * Math.PI) / 180;
  
  // Calculate the angle between the two plates (90 degrees plus pitch)
  const bendAngle = Math.PI / 2 + pitchRadians;
  
  // Calculate the horizontal and vertical components of the second plate
  const secondPlateHorizontal = plateWidth * Math.cos(bendAngle);
  const secondPlateVertical = plateWidth * Math.sin(bendAngle);
  
  // Create barge cap shape with two plates welded together
  // Start at bottom left of first plate
  shape.moveTo(0, 0);
  // Bottom edge of first plate
  shape.lineTo(plateWidth, 0);
  // Right edge of first plate
  shape.lineTo(plateWidth, thickness);
  // Top edge of first plate
  shape.lineTo(0, thickness);
  // Bend point (weld)
  shape.lineTo(0, 0);
  // Second plate starting from the bend point
  shape.lineTo(secondPlateHorizontal, secondPlateVertical);
  // Right edge of second plate
  shape.lineTo(secondPlateHorizontal + thickness * Math.sin(bendAngle), secondPlateVertical - thickness * Math.cos(bendAngle));
  // Top edge of second plate
  shape.lineTo(thickness * Math.sin(bendAngle), -thickness * Math.cos(bendAngle));
  // Back to bend point
  shape.lineTo(0, 0);
  
  return shape;
}
export function createGableLeftBargeCapFlashing2DShape(plateWidth: number, thickness: number, pitch: number) {
  const shape = new THREE.Shape();
  
  // Convert pitch to radians
  const pitchRadians = (pitch * Math.PI) / 180;
  
  // Calculate the angle between the two plates (90 degrees plus pitch)
  const bendAngle = Math.PI / 2 + pitchRadians;
  
  // Calculate the horizontal and vertical components of the second plate
  const secondPlateHorizontal = plateWidth * Math.cos(bendAngle);
  const secondPlateVertical = plateWidth *0.5* Math.sin(bendAngle);
  
  // Create barge cap shape with two plates welded together
  // Start at bottom left of first plate
  shape.moveTo(0, 0);
  // Bottom edge of first plate
  shape.lineTo(plateWidth, 0);
  // Right edge of first plate
  shape.lineTo(plateWidth, thickness);
  // Top edge of first plate
  shape.lineTo(0, thickness);
  // Bend point (weld)
  shape.lineTo(0, 0);
  // Second plate starting from the bend point
  shape.lineTo(secondPlateHorizontal, secondPlateVertical);
  // Right edge of second plate
  shape.lineTo(secondPlateHorizontal + thickness * Math.sin(bendAngle), secondPlateVertical - thickness * Math.cos(bendAngle));
  // Top edge of second plate
  shape.lineTo(thickness * Math.sin(bendAngle), -thickness * Math.cos(bendAngle));
  // Back to bend point
  shape.lineTo(0, 0);
  
  return shape;
}

export function createGableRightBargeCapFlashing2DShape(plateWidth: number, thickness: number, pitch: number) {
  const shape = new THREE.Shape();
  
  // Convert pitch to radians
  const pitchRadians = (pitch * Math.PI) / 180;
  
  // Calculate the angle between the two plates (90 degrees plus pitch)
  const bendAngle = Math.PI / 2 + pitchRadians;
  
  // Calculate the horizontal and vertical components of the second plate
  const secondPlateHorizontal = -plateWidth * Math.cos(bendAngle);
  const secondPlateVertical = plateWidth * 0.5 * Math.sin(bendAngle);
  
  // Create barge cap shape with two plates welded together
  // Start at bottom right of first plate (mirror of left barge)
  shape.moveTo(0, 0);
  // Bottom edge of first plate
  shape.lineTo(-plateWidth, 0);
  // Left edge of first plate
  shape.lineTo(-plateWidth, thickness);
  // Top edge of first plate
  shape.lineTo(0, thickness);
  // Bend point (weld)
  shape.lineTo(0, 0);
  // Second plate starting from the bend point
  shape.lineTo(secondPlateHorizontal, secondPlateVertical);
  // Left edge of second plate
  shape.lineTo(secondPlateHorizontal - thickness * Math.sin(bendAngle), secondPlateVertical - thickness * Math.cos(bendAngle));
  // Top edge of second plate
  shape.lineTo(-thickness * Math.sin(bendAngle), -thickness * Math.cos(bendAngle));
  // Back to bend point
  shape.lineTo(0, 0);
  
  return shape;
}

/**
 * Creates a 2D shape for a barge cap flashing (for the right edge of the roof)
 * @param plateWidth - Width of each plate (typically around 170mm)
 * @param thickness - Material thickness (typically 1mm)
 * @param pitch - Roof pitch in degrees
 * @param isGable - Whether this is for a gable roof (affects the angle)
 * @returns THREE.Shape for the right barge cap
 */
export function createRightBargeCapFlashing2DShape(plateWidth: number, thickness: number, pitch: number, isGable: boolean = false) {
  const shape = new THREE.Shape();
  
  // Convert pitch to radians
  const pitchRadians = (pitch * Math.PI) / 180;
  
  // Calculate the angle between the two plates
  // For gable roofs: 90 degrees plus pitch (same as left barge)
  // For other roof types: 90 degrees minus pitch
  const bendAngle = isGable ? (Math.PI / 2 + pitchRadians) : (Math.PI / 2 - pitchRadians);
  
  // Calculate the horizontal and vertical components of the second plate
  const secondPlateHorizontal = -plateWidth * Math.cos(bendAngle);
  const secondPlateVertical = plateWidth * Math.sin(bendAngle);
  
  // Create barge cap shape with two plates welded together
  // Start at bottom right of first plate (mirror of left barge)
  shape.moveTo(0, 0);
  // Bottom edge of first plate
  shape.lineTo(-plateWidth, 0);
  // Left edge of first plate
  shape.lineTo(-plateWidth, thickness);
  // Top edge of first plate
  shape.lineTo(0, thickness);
  // Bend point (weld)
  shape.lineTo(0, 0);
  // Second plate starting from the bend point
  shape.lineTo(secondPlateHorizontal, secondPlateVertical);
  // Left edge of second plate
  shape.lineTo(secondPlateHorizontal - thickness * Math.sin(bendAngle), secondPlateVertical - thickness * Math.cos(bendAngle));
  // Top edge of second plate
  shape.lineTo(-thickness * Math.sin(bendAngle), -thickness * Math.cos(bendAngle));
  // Back to bend point
  shape.lineTo(0, 0);
  
  return shape;
}

// Alias for backward compatibility
export function createBargeCapFlashing2DShape(plateWidth: number, thickness: number, pitch: number) {
  return createLeftBargeCapFlashing2DShape(plateWidth, thickness, pitch);
}



