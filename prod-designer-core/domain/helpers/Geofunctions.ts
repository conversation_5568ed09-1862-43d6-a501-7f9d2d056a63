import { THREE } from 'expo-three';

export const processGeometry = (
  geometry: THREE.BufferGeometry,
  rotationX: number,
  rotationY: number,
  rotationZ: number,
) => {
  // Compute the centroid of the geometry
  geometry.computeBoundingBox();
  const boundingBox = geometry.boundingBox!;
  const centroid = new THREE.Vector3();
  boundingBox.getCenter(centroid);
  //console.log('Geometry CENTROID:', centroid);

  // Translate the geometry to the origin based on its centroid
  const translationMatrix = new THREE.Matrix4().makeTranslation(
    -centroid.x,
    -centroid.y,
    -centroid.z,
  );
  geometry.applyMatrix4(translationMatrix);

  // Apply the rotations around the origin
  const rotationMatrixX = new THREE.Matrix4().makeRotationX(rotationX); // Rotation around X-axis
  const rotationMatrixY = new THREE.Matrix4().makeRotationY(rotationY); // Rotation around Y-axis
  const rotationMatrixZ = new THREE.Matrix4().makeRotationZ(rotationZ); // Rotation around Z-axis
  geometry.applyMatrix4(rotationMatrixX);
  geometry.applyMatrix4(rotationMatrixY);
  geometry.applyMatrix4(rotationMatrixZ);

  // console.log('Processed Geometry:', geometry);

  return geometry;
};
