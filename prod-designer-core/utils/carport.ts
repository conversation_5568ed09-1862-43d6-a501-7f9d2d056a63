import { CarportState, roofTypes } from '@/types/carport';
import { includes, isInteger } from './tsUtils';
import { fromBase64 } from './base64Utils';

// Import the decryption utility
import { decryptText } from '@/components/designer-frame/en-cry-decry';

export function decodeCarport(s?: string, modelParam?: string): CarportState | undefined {
  // First, try to use the modelParam if provided (new shared link format)
  if (modelParam) {
    try {
      // Decoding from model parameter
      // Decrypt and parse the model parameter
      const decodedParam = decodeURIComponent(modelParam);
      const decryptedData = decryptText(decodedParam);
      const modelData = JSON.parse(decryptedData);
      
      // Check if we have dimensions data
      if (modelData && modelData.dimensions) {
        // Successfully decoded model data
        
        // Create a CarportState with the dimension values
        const state: CarportState = {
          height: modelData.dimensions.height || 2400,
          length: modelData.dimensions.length || 12000,
          span: modelData.dimensions.span || 6000,
          overhang: modelData.dimensions.overhang || 200,
          pitch: modelData.dimensions.pitch || 11,
          roofType: modelData.dimensions.roofType || 'Gable',
          
          // Add lean-to properties if present
          _sceneLeanProperties: {
            leftLeanTo: modelData.sceneSettings?.leftLeanTo || false,
            leftLeanToSpan: modelData.sceneSettings?.leftLeanToSpan || 0,
            leftLeanToDropHeight: modelData.sceneSettings?.leftLeanToDropHeight || 0,
            rightLeanTo: modelData.sceneSettings?.rightLeanTo || false,
            rightLeanToSpan: modelData.sceneSettings?.rightLeanToSpan || 0,
            rightLeanToDropHeight: modelData.sceneSettings?.rightLeanToDropHeight || 0,
            rightLeanToAttached: modelData.sceneSettings?.rightLeanToAttached || false,
          },
          
          // Include scene settings for initialization
          _sceneSettings: modelData.sceneSettings
        };
        
        return state;
      }
    } catch (error) {
      console.error('Error decoding model parameter:', error);
    }
  }
  
  // Fall back to the legacy 'd' parameter if model param failed
  if (s) {
    try {
      const decoded = s ? (fromBase64(s) as Partial<Record<keyof CarportState, unknown>>) : undefined;

      const shared: Partial<CarportState> = {
        height: isInteger(decoded?.height) ? decoded?.height : undefined,
        length: isInteger(decoded?.length) ? decoded?.length : undefined,
        span: isInteger(decoded?.span) ? decoded?.span : undefined,
        overhang: isInteger(decoded?.overhang) ? decoded?.overhang : undefined,
        pitch: isInteger(decoded?.pitch) ? decoded?.pitch : undefined,
        roofType: includes(roofTypes, decoded?.roofType) ? decoded?.roofType : undefined,
      };

      const valid = !Object.values(shared).some((v) => v === undefined);

      return valid ? (shared as CarportState) : undefined;
    } catch (error) {
      console.error('Error decoding legacy parameter:', error);
      return undefined;
    }
  }
  
  return undefined;
}
