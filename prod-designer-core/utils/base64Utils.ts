/**
 * Base64 utility functions
 */

/**
 * Convert a string to base64
 * @param s The string to convert
 * @returns The base64 encoded string
 */
export function toBase64(s: string) {
  return btoa(s);
}

/**
 * Convert a base64 string back to its original form
 * @param base64 The base64 string to convert
 * @returns The decoded object
 */
export function fromBase64(base64: string): unknown {
  let r = undefined;
  try {
    r = JSON.parse(atob(base64));
  } catch (e) {
    console.error(e);
  }
  return r;
}
