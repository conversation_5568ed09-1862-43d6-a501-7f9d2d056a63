import * as THREE from 'three';
import { useSceneContext } from '@/redux/context';

// Type definitions for React Three Fiber ref
interface R3FRef {
  current: {
    gl: THREE.WebGLRenderer;
    scene: THREE.Scene;
    camera: THREE.Camera;
    [key: string]: any;
  } | null;
}

/**
 * Sets up the global window objects needed for plan view export
 * This connects the Three.js scene with the export functionality
 */
export const setupPlanViewExport = (
  renderer: THREE.WebGLRenderer,
  scene: THREE.Scene,
  camera: THREE.OrthographicCamera,
  dimensions: any
) => {
  console.log('setupPlanViewExport - Starting setup with dimensions:', dimensions);
  console.log('setupPlanViewExport - Renderer available:', !!renderer);
  console.log('setupPlanViewExport - Scene available:', !!scene);
  console.log('setupPlanViewExport - Camera available:', !!camera);
  
  try {
    // Create a bounding box for the model
    const boundingBox = new THREE.Box3();
    let meshCount = 0;
    
    scene.traverse((object: THREE.Object3D) => {
      if (object instanceof THREE.Mesh) {
        boundingBox.expandByObject(object);
        meshCount++;
      }
    });
    
    console.log(`setupPlanViewExport - Found ${meshCount} meshes in scene`);
    console.log('setupPlanViewExport - Bounding box:', boundingBox.min, boundingBox.max);

    // Calculate model size
    const size = new THREE.Vector3();
    boundingBox.getSize(size);
    console.log('setupPlanViewExport - Model size:', size);
    
    // Set up the global objects needed for plan view export
    (window as any).__planview_gl = renderer;
    (window as any).__planview_scene = scene;
    (window as any).__planview_camera = camera;
    (window as any).__planview_sizeModel = size;
    (window as any).__planview_formData = {
      span: dimensions.span || 6000,
      length: dimensions.length || 12000,
      height: dimensions.height || 2400,
    };
    (window as any).__planview_boundingBox = boundingBox;
    (window as any).__planview_margin = 500;
    (window as any).__planview_minMargin = 200;
    (window as any).__planview_scaleFactor = 1.5;
    (window as any).__planview_imageHeight = 1200;
    (window as any).__planview_size = { width: 2000, height: 2000 };
    
    console.log('setupPlanViewExport - Plan view export objects set up successfully');
    return true;
  } catch (error) {
    console.error('setupPlanViewExport - Error setting up plan view export:', error);
    return false;
  }
};

/**
 * Hook to prepare the scene for export
 * Use this in components that need to trigger exports
 */
export const usePlanViewExport = () => {
  const { ref, dimensions } = useSceneContext();
  
  const preparePlanViewExport = () => {
    console.log('preparePlanViewExport - Starting preparation');
    console.log('preparePlanViewExport - Ref:', ref);
    console.log('preparePlanViewExport - Dimensions:', dimensions);
    
    if (!ref) {
      console.error('preparePlanViewExport - Scene ref object is not available');
      return false;
    }
    
    if (!ref.current) {
      console.error('preparePlanViewExport - Scene ref.current is not available');
      return false;
    }
    
    console.log('preparePlanViewExport - Ref current:', ref.current);
    
    // Try to access the gl, scene, and camera properties directly
    try {
      // Cast the ref to the R3FRef type to access its properties
      const r3fRef = ref as unknown as R3FRef;
      
      if (!r3fRef.current) {
        console.error('preparePlanViewExport - R3F ref current is null');
        return false;
      }
      
      console.log('preparePlanViewExport - R3F current properties:', {
        hasGL: !!r3fRef.current.gl,
        hasScene: !!r3fRef.current.scene,
        hasCamera: !!r3fRef.current.camera,
      });
      
      const renderer = r3fRef.current.gl;
      const scene = r3fRef.current.scene;
      const camera = r3fRef.current.camera as THREE.OrthographicCamera;
      
      if (!renderer) {
        console.error('preparePlanViewExport - WebGL renderer not available');
        return false;
      }
      
      if (!scene) {
        console.error('preparePlanViewExport - Scene not available');
        return false;
      }
      
      if (!camera) {
        console.error('preparePlanViewExport - Camera not available');
        return false;
      }
      
      // If we got here, we have all the required objects
      return setupPlanViewExport(renderer, scene, camera, dimensions);
    } catch (error) {
      console.error('preparePlanViewExport - Error accessing R3F properties:', error);
      return false;
    }
  };
  
  return { preparePlanViewExport };
};
