import React, { useEffect, useState } from 'react';
import { View, StyleSheet, useWindowDimensions, Dimensions, TouchableOpacity, Text } from 'react-native';
import { router, useLocalSearchParams, useRootNavigationState } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

import '@/i18n';
import i18n from '@/i18n';

// Import security components
import { SecurityManager, type SecurityStatus } from '@/components/security';
import { createSecurityConfig } from '@/components/security/SecurityConfig';

import Canvas3D from '@/components/designer-frame/Canvas3D';
import { NewCanvasControls } from '@/components/designer-frame/NewCanvasControls';
import { UtilityContainer } from '@/components/designer-frame/UtilityContainer';
import { SceneContextProvider, useSceneContext, setSizeValue } from '@/redux/context';
import { decodeCarport } from '@/utils/carport';
import { decryptText } from '@/components/designer-frame/en-cry-decry';
import { ViewsPanel } from '@/components/designer-frame/panels/ViewsPanel';
import { ExportPanel } from '@/components/designer-frame/panels/ExportPanel';

function Root() {
  const { width: windowWidth, height: windowHeight } = useWindowDimensions();
  const rootNavigationState = useRootNavigationState();
  const { d, model } = useLocalSearchParams<{ d?: string; model?: string }>();
  const [sharedModel, setSharedModel] = useState<any>(null);
  const [panelExpanded, setPanelExpanded] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);

  const sharedCarport = decodeCarport(d);

  if (windowWidth === 0 || windowHeight === 0) {
    return null;
  }

  useEffect(() => {
    async function parseModelParam() {
      if (!model) return;
      try {
        const decryptedData = decryptText(decodeURIComponent(model));
        setSharedModel(JSON.parse(decryptedData));
      } catch (error) {
        console.error('Error parsing model parameter:', error);
      }
    }
    parseModelParam();
  }, [model]);

  useEffect(() => {
    if (d !== undefined && sharedCarport === undefined && rootNavigationState?.key) {
      router.setParams({ d: undefined });
    }
  }, [d, rootNavigationState?.key, sharedCarport]);

  useEffect(() => {
    const listener = (event: MessageEvent) => {
      if (event.data?.type === 'SET_LANGUAGE') {
        const supported = ['en', 'zh', 'ko', 'ja'];
        const lang = event.data.lang;
        if (supported.includes(lang)) {
          i18n.changeLanguage(lang);
        }
      }
    };

    window.addEventListener('message', listener);
    return () => window.removeEventListener('message', listener);
  }, []);

  const defaultState = sharedModel?.dimensions || sharedCarport;

  const isMobile = windowWidth <= 600;
  const isTablet = windowWidth > 600 && windowWidth <= 1024;
  const isDesktop = windowWidth > 1024;

  const outerContainerStyle = [
    styles.outerContainer,
    (isMobile || isTablet) && { margin: 16 },
    isDesktop && { margin: 0 },
  ];

  const containerStyle = [
    styles.container,
    isFullscreen && { maxWidth: '100%', maxHeight: '100%', backgroundColor: '#000' },
    !isFullscreen && isMobile && styles.mobile,
    !isFullscreen && isTablet && styles.tablet,
    !isFullscreen && isDesktop && styles.desktop,
  ];

  return (
    <View style={outerContainerStyle}>
      <View style={containerStyle}>
        <SceneContextProvider defaultState={defaultState}>
          {sharedModel && <SharedModelLoader model={sharedModel} />}
          <View style={[styles.innerLayout, (isMobile || isTablet) && styles.innerLayoutColumn]}>
            {isDesktop ? (
              <>
                <View>
                  <ConditionalControls
                    isMobile={isMobile}
                    isTablet={isTablet}
                    onPanelExpandChange={setPanelExpanded}
                  />
                </View>
                <View style={styles.rightPanel}>
                  <FloatingControls
                    isMobile={isMobile}
                    isTablet={isTablet}
                    isFullscreen={isFullscreen}
                    onToggleFullscreen={() => setIsFullscreen((prev) => !prev)}
                  />
                  <ConditionalView
                    isMobile={isMobile}
                    isTablet={isTablet}
                    panelExpanded={panelExpanded}
                  />
                </View>
              </>
            ) : (
              <>
                <View style={styles.rightPanel}>
                  <FloatingControls
                    isMobile={isMobile}
                    isTablet={isTablet}
                    isFullscreen={isFullscreen}
                    onToggleFullscreen={() => setIsFullscreen((prev) => !prev)}
                  />
                  <ConditionalView
                    isMobile={isMobile}
                    isTablet={isTablet}
                    panelExpanded={panelExpanded}
                  />
                </View>
                <View>
                  <ConditionalControls
                    isMobile={isMobile}
                    isTablet={isTablet}
                    onPanelExpandChange={setPanelExpanded}
                  />
                </View>
              </>
            )}
          </View>
        </SceneContextProvider>
      </View>
    </View>
  );
}

function SharedModelLoader({ model }: { model: any }) {
  const { setSceneSettings, dispatch } = useSceneContext();

  useEffect(() => {
    const settings = model.sceneSettings;
    const dimensions = model.dimensions;

    if (!settings) return;

    setTimeout(() => {
      if (dimensions?.roofType && dimensions.roofType !== 'Gable') {
        dispatch(
          setSizeValue({
            roofType: dimensions.roofType,
            overhang: dimensions.overhang || 0,
            pitch: dimensions.pitch || 2,
          })
        );
      }

      setSceneSettings((current) => ({
        ...current,
        roofType: dimensions?.roofType,
        overhang: dimensions?.overhang || 0,
        pitch: dimensions?.pitch || 11,
        leftLeanTo: settings.leftLeanTo || false,
        leftLeanToSpan: settings.leftLeanToSpan || 0,
        leftLeanToDropHeight: settings.leftLeanToDropHeight || 0,
        rightLeanTo: settings.rightLeanTo || false,
        rightLeanToSpan: settings.rightLeanToSpan || 0,
        rightLeanToDropHeight: settings.rightLeanToDropHeight || 0,
        roofColor: settings.roofColor || 'NightSky',
        wallColor: settings.wallColor || 'Classic Cream',
        trimColor: settings.trimColor || 'Surfmist',
        doorColor: settings.doorColor || 'Surfmist',
        viewMode: settings.viewMode || 'normal',
        showSlab: settings.showSlab !== undefined ? settings.showSlab : true,
        showMeasurements:
          settings.showMeasurements !== undefined ? settings.showMeasurements : true,
      }));
    }, 500);
  }, [model, setSceneSettings]);

  return null;
}

function ConditionalView({
  isMobile,
  isTablet,
  panelExpanded,
}: {
  isMobile: boolean;
  isTablet: boolean;
  panelExpanded: boolean;
}) {
  const isMobileOrTablet = isMobile || isTablet;

  return (
    <View
      style={[
        styles.conditionalViewContainer,
        isMobileOrTablet && panelExpanded && styles.shrinkedView,
      ]}
    >
      <Canvas3D />
    </View>
  );
}

interface ConditionalControlsProps {
  isMobile: boolean;
  isTablet: boolean;
  onPanelExpandChange?: (isExpanded: boolean) => void;
}

function ConditionalControls({
  isMobile,
  isTablet,
  onPanelExpandChange,
}: ConditionalControlsProps) {
  const [panelExpanded, setPanelExpanded] = useState(false);

  const handlePanelToggle = (isExpanded: boolean) => {
    setPanelExpanded(isExpanded);
    onPanelExpandChange?.(isExpanded);
  };

  return (
    <View style={styles.conditionalControlsContainer}>
      <NewCanvasControls
        isMobile={isMobile}
        isTablet={isTablet}
        onPanelToggle={handlePanelToggle}
      />
    </View>
  );
}

interface FloatingControlsProps {
  isMobile: boolean;
  isTablet: boolean;
  onToggleFullscreen: () => void;
  isFullscreen: boolean;
}

function FloatingControls({
  isTablet,
  isMobile,
  onToggleFullscreen,
  isFullscreen,
}: FloatingControlsProps) {
  return (
    <>
      <View style={styles.leftControls}>
        <UtilityContainer isMobile={isMobile} isTablet={isTablet} />
        <ExportPanel isMobile={isMobile} isTablet={isTablet} />
      </View>
      <View style={styles.rightControls}>
        <View style={styles.viewControlsWrapper}>
          <ViewsPanel isMobile={isMobile} />
          <TouchableOpacity
            onPress={onToggleFullscreen}
            style={{
              marginTop: 8,
              backgroundColor: 'white',
              borderRadius: 4,
              padding: 6,
            }}
          >
            <Ionicons
              name={isFullscreen ? 'contract-outline' : 'expand-outline'}
              size={20}
              color="red"
            />
          </TouchableOpacity>
        </View>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  outerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  container: {
    maxWidth: 1200,
    maxHeight: 800,
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  innerLayout: {
    flexDirection: 'row',
    width: '100%',
    height: '100%',
    display: 'flex',
  },
  innerLayoutColumn: {
    flexDirection: 'column',
  },
  rightPanel: {
    position: 'relative',
    flex: 1,
  },
  conditionalControlsContainer: {
    flex: 1,
  },
  mobile: {
    maxWidth: '100%',
    maxHeight: '100%',
    paddingLeft: 16,
    paddingRight: 16,
  },
  tablet: {
    maxWidth: 900,
    maxHeight: 800,
    paddingLeft: 16,
    paddingRight: 16,
  },
  desktop: {
    maxWidth: 1200,
    maxHeight: 800,
  },
  leftControls: {
    position: 'absolute',
    top: 10,
    left: 10,
    zIndex: 1000,
  },
  rightControls: {
    position: 'absolute',
    top: 10,
    right: 10,
    alignItems: 'flex-end',
    zIndex: 1000,
  },
  planSwitchWrapper: {
    marginBottom: 20,
  },
  viewControlsWrapper: {
    marginTop: 10,
    alignItems: 'center',
  },
  floatingControls: {
    position: 'absolute',
    top: 10,
    left: 10,
    zIndex: 1000,
  },
  controlBox: {
    borderRadius: 8,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.1)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 6,
    elevation: 3,
    width: 64,
  },
  conditionalViewContainer: {
    flex: 1,
  },
  shrinkedView: {
    maxHeight: Dimensions.get('window').height - 400,
    zIndex: 1000,
  },
  securityFallback: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    padding: 20,
  },
  securityFallbackContent: {
    alignItems: 'center',
    padding: 40,
    backgroundColor: 'white',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
    maxWidth: 400,
  },
  securityFallbackTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#d32f2f',
    marginBottom: 16,
    textAlign: 'center',
  },
  securityFallbackMessage: {
    fontSize: 16,
    color: '#333',
    textAlign: 'center',
    marginBottom: 12,
    lineHeight: 22,
  },
  securityFallbackSubtext: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    lineHeight: 20,
  },
});

// Security fallback component
function SecurityFallback() {
  return (
    <View style={styles.securityFallback}>
      <View style={styles.securityFallbackContent}>
        <Text style={styles.securityFallbackTitle}>🔒 Access Restricted</Text>
        <Text style={styles.securityFallbackMessage}>
          This application must be accessed through an authorized parent application.
        </Text>
        <Text style={styles.securityFallbackSubtext}>
          Please access this designer through the main application interface.
        </Text>
      </View>
    </View>
  );
}

export default function App() {
  const [securityStatus, setSecurityStatus] = useState<SecurityStatus | null>(null);

  // Create security configuration from environment variables and defaults
  const envSecurityConfig = createSecurityConfig();

  // Security configuration based on environment
  const securityConfig = {
    // Environment will be auto-detected
    accessControl: envSecurityConfig, // Use environment-based configuration
    developmentOverrides: {
      allowDirectAccess: process.env.NODE_ENV === 'development', // Allow direct access in development
      bypassAccessControl: false, // Still run security checks for testing
      bypassReferrerValidation: false
    },
    enableLogging: true,
    enableMonitoring: true
  };

  const handleSecurityStatus = (status: SecurityStatus) => {
    setSecurityStatus(status);

    // Log security status for monitoring
    if (process.env.NODE_ENV === 'development') {
      console.log('🔒 Application Security Status:', {
        secure: status.overall.secure,
        issues: status.overall.issues,
        accessControl: status.accessControl.authorized,
        referrerValidation: status.referrerValidation.valid,
        securityHeaders: status.securityHeaders.applied
      });
    }
  };

  return (
    <SecurityManager
      config={securityConfig}
      onSecurityStatus={handleSecurityStatus}
      fallback={<SecurityFallback />}
    >
      <Root />
    </SecurityManager>
  );
}
