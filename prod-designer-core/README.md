# DesignerCore

DesignerCore is a React Native application built with Expo, focused on a 3D carport designer tool. The app allows users to design and customize carports in a 3D environment, with support for both flat and gable roof types.

## Obfuscation and Deployment

The application includes a robust obfuscation and deployment solution to protect sensitive business logic and intellectual property. Key features include:

- **Code Obfuscation**: Strong protection for business logic using techniques like control flow flattening, string array encoding, and debug protection
- **Gable Carport Implementation**: Fully protected implementation of gable roof carports with proper 3D mesh rendering
- **Deployment Automation**: Scripts for building, obfuscating, and deploying to Firebase Hosting
- **Original UI Preservation**: Maintains the exact UI layout from the original application

## Codebase Structure

- **app**: Contains application-specific configurations and entry points.
- **assets**: Stores static assets like images, fonts, and icons.
- **components**: Houses reusable UI components, organized by functionality.
- **constants**: Defines application-wide constants and configurations, organized by category.
- **domain**: Contains domain models and business logic for the carport designer.
- **hooks**: Custom React hooks for shared logic.
- **redux**: State management setup using Redux.
- **scripts**: Utility scripts for development tasks.
- **types**: TypeScript type definitions.
- **utils**: Utility functions and helpers.
- **not-used**: Contains files that are not currently being used in the project.

## Detailed Directory Structure and Purposes

### Root Files
- `package.json` - Project configuration, dependencies, and scripts
- `app.json` - Expo configuration file
- `tsconfig.json` - TypeScript configuration
- `metro.config.js` - Metro bundler configuration for React Native
- `.env` - Environment variables
- `.prettierrc` - Code formatting rules
- `.gitignore` - Git ignore rules
- `custom.d.ts` & `expo-env.d.ts` - TypeScript declaration files

### App Directory
- `app/index.tsx` - Main entry point for the application using Expo Router, sets up the 3D canvas and main UI components

### Components Directory
The components directory is organized into several subdirectories by purpose:

#### Themed Components (`components/themed/`)
- `ThemedText.tsx` - Text component with theme support
- `ThemedView.tsx` - View component with theme support
- `ThemedInputText.tsx` - Input text component with theme support
- `ThemedPicker.tsx` - Picker component with theme support
- `ThemedIcon.tsx` - Icon component with theme support
- `ThemedIconButton.tsx` - Icon button component with theme support

#### Platform-Specific Components (`components/platform-specific/`)
- `IconSymbol.tsx` & `IconSymbol.ios.tsx` - Platform-specific icon implementations
- `TabBarBackground.tsx` & `TabBarBackground.ios.tsx` - Platform-specific tab bar backgrounds

#### 3D Environment Components (`components/3d-environment/`)
- `Grass.tsx` - 3D grass component for the environment
- `SkyBox.tsx` - 3D skybox for the environment background
- `Environment.tsx` - Overall 3D environment setup

#### 3D Measurement Components (`components/3d-measurement/`)
- `measurement-text.tsx` - Text for displaying measurements in 3D space

#### 3D Controls (`components/3d-controls/`)
- `pivotControls/` - Controls for manipulating 3D objects in the scene

#### UI Controls (`components/ui-controls/`)
- `ColorPicker.tsx` - Component for selecting colors
- `ObjectPicker.tsx` - Component for selecting objects
- `Slider.tsx` - Custom slider component for adjusting values

#### Indicators (`components/indicators/`)
- `PriceIndicator.tsx` - Component for displaying price information

#### Designer Frame (`components/designer-frame/`)
- `Canvas3D.tsx` - Main 3D canvas component
- `CanvasControls.tsx` - Controls for the 3D canvas
- `Toolbar.tsx` - Toolbar with design tools

### Constants Directory
Organized by category:

#### Colors (`constants/colors/`)
- `Colors.ts` - Base color definitions
- `colorbondColors.ts` - Colorbond color options for materials

#### Materials (`constants/materials/`)
- `Material.ts` - Material definitions and properties

#### Carport (`constants/carport/`)
- `CarportParams.ts` - Parameters for carport configurations

#### Objects (`constants/objects/`)
- `objectOptions.ts` - Options for objects in the scene

### Domain Directory
Contains the core business logic and models, organized by functionality:

#### Carport Models (`domain/carport-models/`)
- `Carport.tsx` - Main carport model implementation
- `gable_Carport.tsx` - Gable carport model implementation

#### Configuration (`domain/config/`)
- `Carportconfig.ts` - Carport configuration options and calculations

#### Helpers (`domain/helpers/`)
- `Geofunctions.ts` - Geometric utility functions
- `SectionHelper.ts` - Helper functions for section calculations

#### 3D Models (`domain/3d-models/`)
- `car-model.tsx` - 3D car model for scene placement

#### Materials (`domain/materials/`)
- `colorManager.ts` - Color management for materials
- `materials-pool.ts` - Materials management and pooling

#### Scene (`domain/scene/`)
- `scene.tsx` - Scene management and rendering

### Hooks Directory
- `useCanvasScissor.ts` - Hook for canvas scissoring
- `useColorScheme.ts` & `useColorScheme.web.ts` - Platform-specific color scheme hooks
- `useScreenshot.ts` & `useScreenshot.web.ts` - Platform-specific screenshot hooks
- `useThemeColor.ts` - Hook for theme colors

### Redux Directory
- `context.tsx` - Context provider for state management

### Utils Directory
- `carport.ts` - Utility functions for carport
- `math.ts` - Math utility functions
- `tsUtils.ts` - TypeScript utility functions
- `windowUtils.ts` & `windowUtils.web.ts` - Platform-specific window utilities

### Unused Files
- `domain/models/SectionHelper.ts.bak` - Backup file, likely not in active use
- `hooks/useScreenshot.ts` - Empty native version (web version is used)

## Consolidation Opportunities
Based on the file structure, here are some consolidation opportunities:

1. **Themed Components**: Consolidate `ThemedText.tsx`, `ThemedView.tsx`, `ThemedInputText.tsx`, etc. into a single theming system
2. **Platform-specific files**: Consider using React Native's platform-specific import system instead of separate files
3. **3D Components**: The 3D components could potentially be organized better based on their functionality
4. **Models**: The carport models (`Carport.tsx` and `gable_Carport.tsx`) are quite large and might benefit from being broken down into smaller, more focused components

## UI Functions and Components

### Core UI Components
1. **ThemedIconButton**
   - **Purpose**: Interactive button with themed icon
   - **Properties**:
     - `visible`: Toggle button visibility
     - `name`: Icon identifier
     - `onPress`: Click handler
     - `style`: Custom styling
   - **Behavior**: 
     - Applies theme-based background color
     - Standard padding of 8 units
     - Icon size of 24 units

2. **ThemedText**
   - **Purpose**: Text display with theme support
   - **Usage**: Base component for all text elements
   - **Features**: Customizable styling and theme integration

3. **ThemedInputText**
   - **Purpose**: User input field
   - **Features**: Theme-aware styling
   - **Use Cases**: Form inputs, search fields

4. **ThemedPicker**
   - **Purpose**: Dropdown selection component
   - **Features**: Themed dropdown options
   - **Use Cases**: Selection menus, filters

5. **Slider**
   - **Purpose**: Value range selection
   - **Features**: 
     - Interactive sliding control
     - Visual feedback
     - Customizable range

## 3D Design System

### 1. Measurement System
**Component**: `measurement-text.tsx`
- **Core Functions**:
  1. Single Measurement Display
     - Shows distance with unit (meters)
     - Displays labels (Front, Back, Left, Right)
     - Configurable text size and color
     - Automatic scaling based on measurement size

  2. Dimension Indicators
     - Horizontal measurements (span, length)
     - Vertical measurements (height)
     - Bay spacing when applicable
     - Overhang measurements

  3. Visual Elements
     - Line indicators with configurable color
     - Edge markers for measurement points
     - 3D text labels with automatic positioning
     - Rotation handling for different viewing angles

### 2. Environment Setup
**Component**: `environment.tsx`
- **Features**:
  1. Scene Configuration
     - Lighting setup
     - Camera positioning
     - Scene background
  2. Render Settings
     - Material configurations
     - Shadow mapping
     - Performance optimizations

### 3. Visual Elements
1. **Skybox** (`SkyBox.tsx`)
   - Realistic sky rendering
   - Environmental lighting
   - Atmosphere effects

2. **Ground** (`Grass.tsx`)
   - Textured ground plane
   - Material properties
   - Lighting interaction

### Interactive Design Workflow
1. **Initial Setup**
   - Environment initialization
   - Camera positioning
   - Lighting configuration

2. **Measurement System**
   - Place measurement points
   - Configure display options
   - Update measurements in real-time

3. **Visual Enhancement**
   - Apply skybox
   - Add ground texture
   - Configure environmental effects

4. **User Interaction**
   - Dimension modification
   - View angle adjustment
   - Real-time updates

## Getting Started

1. Clone the repository.
2. Install dependencies using `npm install`.
3. Run the application with `npx expo start`.

## Contributing

Contributions are welcome! Please follow the contribution guidelines in the repository.
