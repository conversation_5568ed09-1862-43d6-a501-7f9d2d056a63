#!/bin/bash

# Local Testing Setup Script
# Sets up both applications for local iframe security testing

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "🧪 Setting up Local Testing Environment"
echo "======================================"

# Check if required tools are installed
check_requirements() {
    print_status "Checking requirements..."
    
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js first."
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed. Please install npm first."
        exit 1
    fi
    
    print_success "Requirements check passed"
}

# Get local IP address
get_local_ip() {
    if command -v ipconfig &> /dev/null; then
        # Windows
        LOCAL_IP=$(ipconfig | grep -o "IPv4.*: [0-9.]*" | grep -o "[0-9.]*$" | head -1)
    elif command -v ifconfig &> /dev/null; then
        # Mac/Linux
        LOCAL_IP=$(ifconfig | grep -o "inet [0-9.]*" | grep -v "127.0.0.1" | head -1 | cut -d' ' -f2)
    else
        LOCAL_IP="*************"
        print_warning "Could not detect local IP. Using default: $LOCAL_IP"
    fi
    
    if [ -z "$LOCAL_IP" ]; then
        LOCAL_IP="*************"
        print_warning "Could not detect local IP. Using default: $LOCAL_IP"
    fi
    
    print_status "Detected local IP: $LOCAL_IP"
}

# Setup prod-designer-core
setup_prod_designer_core() {
    print_status "Setting up prod-designer-core..."
    
    if [ ! -d "prod-designer-core" ]; then
        print_error "prod-designer-core directory not found"
        exit 1
    fi
    
    cd prod-designer-core
    
    # Install dependencies
    print_status "Installing prod-designer-core dependencies..."
    npm install
    
    # Setup Cloud Functions
    print_status "Installing Cloud Functions dependencies..."
    cd functions
    npm install
    cd ..
    
    # Create local environment file
    if [ ! -f ".env.local" ]; then
        print_status "Creating .env.local file..."
        cat > .env.local << EOF
# Local Development Environment Configuration
NODE_ENV=development
SECURITY_ENABLED=true
DEVELOPMENT_MODE=true

# Local Network Configuration
ALLOWED_IPS=127.0.0.1,::1,localhost,${LOCAL_IP},***********/24,10.0.0.0/24

# Local Domains
ALLOWED_DOMAINS=localhost,127.0.0.1,${LOCAL_IP}

# Local Origins
ALLOWED_ORIGINS=http://localhost:8080,http://localhost:3000,http://localhost:8084,http://127.0.0.1:8080,http://127.0.0.1:3000,http://127.0.0.1:8084,http://${LOCAL_IP}:8080,http://${LOCAL_IP}:3000,http://${LOCAL_IP}:8084

# Admin Configuration
ADMIN_KEY=local-test-admin-key

# Firebase Configuration
FIREBASE_PROJECT_ID=designmycarport
FIREBASE_REGION=us-central1

# Logging Configuration
LOG_LEVEL=debug
ENABLE_ACCESS_LOGS=true

# Rate Limiting
RATE_LIMIT_WINDOW=60000
RATE_LIMIT_MAX_REQUESTS=1000

# Development Settings
ALLOW_LOCALHOST=true
ENABLE_SECURITY_TESTS=true
TEST_MODE=true
EOF
        print_success "Created .env.local for prod-designer-core"
    else
        print_warning ".env.local already exists for prod-designer-core"
    fi
    
    cd ..
}

# Setup spark-oasis-main
setup_spark_oasis_main() {
    print_status "Setting up spark-oasis-main..."
    
    if [ ! -d "spark-oasis-main" ]; then
        print_error "spark-oasis-main directory not found"
        exit 1
    fi
    
    cd spark-oasis-main
    
    # Install dependencies
    print_status "Installing spark-oasis-main dependencies..."
    npm install
    
    # Create local environment file
    if [ ! -f ".env.local" ]; then
        print_status "Creating .env.local file..."
        cat > .env.local << EOF
# Local Development Environment Configuration

# Iframe URLs for Local Testing
VITE_CARPORT_DESIGNER_URL_PROD=http://localhost:8084/
VITE_CARPORT_DESIGNER_URL_DEV=http://localhost:8084/
VITE_CARPORT_DESIGNER_URL_FALLBACK=http://localhost:8084/

# Allowed iframe origins
VITE_ALLOWED_IFRAME_ORIGINS=http://localhost:8084,http://127.0.0.1:8084,http://${LOCAL_IP}:8084

# Content Security Policy Configuration
VITE_CSP_FRAME_SRC=http://localhost:8084 http://127.0.0.1:8084 http://${LOCAL_IP}:8084
VITE_CSP_CONNECT_SRC=http://localhost:8084 http://127.0.0.1:8084 http://${LOCAL_IP}:8084

# Security Headers
VITE_ENABLE_SECURITY_HEADERS=true
VITE_X_FRAME_OPTIONS=SAMEORIGIN
VITE_X_CONTENT_TYPE_OPTIONS=nosniff

# Development Settings
VITE_DEVELOPMENT_MODE=true
VITE_ENABLE_DEBUG_LOGS=true

# Error Reporting
VITE_ENABLE_ERROR_REPORTING=false

# Local Firebase Emulator URLs
VITE_FIREBASE_FUNCTIONS_URL=http://localhost:5001/designmycarport/us-central1

# Testing Configuration
VITE_TEST_MODE=true
VITE_ADMIN_KEY=local-test-admin-key
EOF
        print_success "Created .env.local for spark-oasis-main"
    else
        print_warning ".env.local already exists for spark-oasis-main"
    fi
    
    cd ..
}

# Create test runner script
create_test_runner() {
    print_status "Creating test runner script..."
    
    cat > run-local-tests.sh << 'EOF'
#!/bin/bash

# Local Testing Runner Script
# Runs automated tests for the security implementation

echo "🧪 Running Local Security Tests"
echo "==============================="

# Test 1: Basic connectivity
echo "Testing basic connectivity..."
curl -s http://localhost:8084/ > /dev/null && echo "✅ prod-designer-core is accessible" || echo "❌ prod-designer-core is not accessible"
curl -s http://localhost:3000/ > /dev/null && echo "✅ spark-oasis-main is accessible" || echo "❌ spark-oasis-main is not accessible"

# Test 2: Security headers
echo -e "\nTesting security headers..."
HEADERS=$(curl -s -I http://localhost:8084/)
echo "$HEADERS" | grep -q "x-frame-options" && echo "✅ X-Frame-Options header present" || echo "❌ X-Frame-Options header missing"
echo "$HEADERS" | grep -q "content-security-policy" && echo "✅ CSP header present" || echo "❌ CSP header missing"

# Test 3: CORS
echo -e "\nTesting CORS..."
curl -s -H "Origin: http://localhost:3000" http://localhost:8084/ > /dev/null && echo "✅ CORS allows localhost:3000" || echo "❌ CORS blocks localhost:3000"

# Test 4: Cloud Functions (if running)
echo -e "\nTesting Cloud Functions..."
if curl -s http://localhost:5001/designmycarport/us-central1/healthCheck > /dev/null 2>&1; then
    echo "✅ Cloud Functions emulator is running"
    curl -s -H "X-Admin-Key: local-test-admin-key" http://localhost:5001/designmycarport/us-central1/testSecurity > /dev/null && echo "✅ Security tests endpoint accessible" || echo "❌ Security tests endpoint not accessible"
else
    echo "⚠️  Cloud Functions emulator not running"
fi

echo -e "\n🎉 Local testing complete!"
echo "Open http://localhost:3000/carport-designer to test iframe embedding"
EOF

    chmod +x run-local-tests.sh
    print_success "Created run-local-tests.sh script"
}

# Create startup script
create_startup_script() {
    print_status "Creating startup script..."
    
    cat > start-local-development.sh << 'EOF'
#!/bin/bash

# Local Development Startup Script
# Starts all required services for local testing

echo "🚀 Starting Local Development Environment"
echo "========================================"

# Function to start a service in background
start_service() {
    local name=$1
    local command=$2
    local dir=$3
    
    echo "Starting $name..."
    cd "$dir"
    $command &
    local pid=$!
    echo "$name started with PID $pid"
    cd - > /dev/null
}

# Start prod-designer-core web server
start_service "prod-designer-core" "npm run web" "prod-designer-core"

# Wait a bit for the first service to start
sleep 3

# Start Firebase Functions emulator
start_service "Firebase Functions" "firebase emulators:start --only functions" "prod-designer-core"

# Wait a bit for functions to start
sleep 3

# Start spark-oasis-main
start_service "spark-oasis-main" "npm run dev" "spark-oasis-main"

echo ""
echo "🎉 All services started!"
echo ""
echo "Services:"
echo "- prod-designer-core: http://localhost:8084"
echo "- spark-oasis-main: http://localhost:3000"
echo "- Firebase Functions: http://localhost:5001"
echo ""
echo "Test iframe embedding at: http://localhost:3000/carport-designer"
echo ""
echo "Press Ctrl+C to stop all services"

# Wait for user to stop
wait
EOF

    chmod +x start-local-development.sh
    print_success "Created start-local-development.sh script"
}

# Main setup process
main() {
    check_requirements
    get_local_ip
    
    echo ""
    setup_prod_designer_core
    echo ""
    setup_spark_oasis_main
    echo ""
    create_test_runner
    create_startup_script
    
    echo ""
    print_success "🎉 Local testing environment setup complete!"
    echo ""
    echo "Next steps:"
    echo "1. Start all services: ./start-local-development.sh"
    echo "2. Run tests: ./run-local-tests.sh"
    echo "3. Open browser: http://localhost:3000/carport-designer"
    echo "4. Check the LOCAL_TESTING_GUIDE.md for detailed testing instructions"
    echo ""
    echo "Your local IP address: $LOCAL_IP"
    echo "You can test from other devices on your network using this IP"
}

# Run main function
main "$@"
