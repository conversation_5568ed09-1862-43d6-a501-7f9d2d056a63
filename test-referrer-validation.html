<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Referrer Validation Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-title {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .status.warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: 2px solid #007bff;
            border-radius: 4px;
        }
        .test-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-left: 4px solid #007bff;
            margin: 15px 0;
        }
        .test-info h4 {
            margin-top: 0;
            color: #007bff;
        }
        .test-results {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        .test-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            border: 1px solid #dee2e6;
        }
        .test-item h5 {
            margin-top: 0;
            color: #495057;
        }
        .code {
            background: #f1f3f4;
            padding: 8px 12px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">🔒 Referrer Validation Test Suite</h1>
        
        <div class="status info">
            <strong>Test Environment:</strong> Development Mode<br>
            <strong>Current URL:</strong> <span id="currentUrl"></span><br>
            <strong>Referrer:</strong> <span id="referrerInfo"></span>
        </div>

        <div class="test-info">
            <h4>Security Configuration Status</h4>
            <p><strong>Environment Variables Loaded:</strong></p>
            <div class="code">
                ALLOWED_REFERRERS=http://localhost:8080/,http://localhost:8080/carport-designer,http://localhost:3000/,http://localhost:8084/,http://127.0.0.1:8080/,http://127.0.0.1:8080/carport-designer,http://127.0.0.1:3000/,http://127.0.0.1:8084/
            </div>
            <p><strong>Expected Behavior:</strong></p>
            <ul>
                <li>✅ spark-oasis-main (port 8080) should be recognized as authorized referrer</li>
                <li>✅ Specific carport-designer path should be allowed</li>
                <li>⚠️ Development mode allows direct access with security warnings</li>
                <li>🔍 Security status should be logged to browser console</li>
            </ul>
        </div>

        <div class="test-results">
            <div class="test-item">
                <h5>🌐 Direct Access Test</h5>
                <p>Access prod-designer-core directly:</p>
                <div class="code">http://localhost:8081</div>
                <p><strong>Expected:</strong> Loads with security warnings in development mode</p>
                <button onclick="testDirectAccess()">Test Direct Access</button>
                <div id="directAccessResult"></div>
            </div>

            <div class="test-item">
                <h5>🖼️ Iframe Embedding Test</h5>
                <p>Access through spark-oasis-main iframe:</p>
                <div class="code">http://localhost:8080/carport-designer</div>
                <p><strong>Expected:</strong> Loads successfully with proper referrer validation</p>
                <button onclick="testIframeEmbedding()">Test Iframe Embedding</button>
                <div id="iframeResult"></div>
            </div>
        </div>
    </div>

    <div class="test-container">
        <h2 class="test-title">🖼️ Live Iframe Embedding Test</h2>
        
        <div class="status success">
            <strong>Testing:</strong> prod-designer-core embedded from spark-oasis-main<br>
            <strong>Referrer:</strong> http://localhost:8080/carport-designer<br>
            <strong>Target:</strong> http://localhost:8081
        </div>

        <div class="test-info">
            <h4>What to Look For:</h4>
            <ul>
                <li>🔍 Check browser console for security status logs</li>
                <li>✅ Application should load without "Access Restricted" message</li>
                <li>🔒 Security headers should be applied</li>
                <li>📊 Referrer validation should pass</li>
            </ul>
        </div>

        <iframe 
            src="http://localhost:8081" 
            title="Carport Designer - Referrer Validation Test"
            onload="handleIframeLoad()"
            onerror="handleIframeError()">
        </iframe>

        <div id="iframeStatus" class="status info">
            <strong>Status:</strong> Loading iframe...
        </div>
    </div>

    <script>
        // Update current page info
        document.getElementById('currentUrl').textContent = window.location.href;
        document.getElementById('referrerInfo').textContent = document.referrer || 'None';

        function testDirectAccess() {
            const resultDiv = document.getElementById('directAccessResult');
            resultDiv.innerHTML = '<div class="status info">Opening direct access test...</div>';
            
            // Open in new tab to test direct access
            const newWindow = window.open('http://localhost:8081', '_blank');
            
            setTimeout(() => {
                resultDiv.innerHTML = `
                    <div class="status warning">
                        Direct access test opened in new tab.<br>
                        <strong>Check:</strong> Should show security warnings in console but still load in development mode.
                    </div>
                `;
            }, 1000);
        }

        function testIframeEmbedding() {
            const resultDiv = document.getElementById('iframeResult');
            resultDiv.innerHTML = '<div class="status info">Testing iframe embedding...</div>';
            
            // This page IS the iframe embedding test
            resultDiv.innerHTML = `
                <div class="status success">
                    This page demonstrates iframe embedding!<br>
                    <strong>Current referrer:</strong> ${document.referrer || 'None'}<br>
                    <strong>Status:</strong> Referrer validation should pass for embedded iframe below.
                </div>
            `;
        }

        function handleIframeLoad() {
            const statusDiv = document.getElementById('iframeStatus');
            statusDiv.className = 'status success';
            statusDiv.innerHTML = `
                <strong>✅ Iframe Loaded Successfully!</strong><br>
                Referrer validation passed. The prod-designer-core application is properly embedded.<br>
                <em>Check browser console for detailed security logs.</em>
            `;
        }

        function handleIframeError() {
            const statusDiv = document.getElementById('iframeStatus');
            statusDiv.className = 'status warning';
            statusDiv.innerHTML = `
                <strong>⚠️ Iframe Load Issue</strong><br>
                There may be an issue with the iframe loading. Check browser console for details.<br>
                <em>This could indicate referrer validation or other security restrictions.</em>
            `;
        }

        // Log test environment info
        console.log('🔒 Referrer Validation Test Environment');
        console.log('=====================================');
        console.log('Current URL:', window.location.href);
        console.log('Referrer:', document.referrer);
        console.log('User Agent:', navigator.userAgent);
        console.log('Test Time:', new Date().toISOString());
        console.log('');
        console.log('Expected Security Behavior:');
        console.log('• spark-oasis-main (localhost:8080) should be authorized referrer');
        console.log('• Iframe embedding should pass referrer validation');
        console.log('• Security status should be logged by prod-designer-core');
        console.log('• Development mode allows direct access with warnings');
    </script>
</body>
</html>
